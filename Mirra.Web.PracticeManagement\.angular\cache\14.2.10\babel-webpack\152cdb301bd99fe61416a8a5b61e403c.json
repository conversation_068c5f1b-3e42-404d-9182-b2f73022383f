{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/Practice Management/Web/Mirra.Web.PracticeManagement/Mirra.Web.PracticeManagement/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { FormBuilder, FormControl, Validators } from '@angular/forms';\nimport { faxNumberValidator, noWhitespaceValidator, trimSpaces, zipCodeValidator } from 'src/app/shared/functions/customFormValidators';\nimport Swal from 'sweetalert2';\nimport { NavTabFromDetails, Tabs } from 'src/app/common/nav-constant';\nimport * as JSLZString from 'lz-string';\nimport { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';\nimport { DebounceTime } from 'src/app/common/common-static';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"src/app/services/ProviderManagement/provider-management.service\";\nimport * as i3 from \"src/app/services/ClaimForm/all-states-by-searchstring.service\";\nimport * as i4 from \"src/app/shared/services/subject.service\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"src/app/services/Notification/notification.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@ng-select/ng-select\";\nimport * as i9 from \"../../../shared/directives/numbers-only.directive\";\n\nfunction AddFacilityComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\")(2, \"h3\", 43);\n    i0.ɵɵtext(3, \"Add Facility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\")(5, \"img\", 44);\n    i0.ɵɵlistener(\"click\", function AddFacilityComponent_div_0_Template_img_click_5_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.close());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction AddFacilityComponent_label_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.mainHeading);\n  }\n}\n\nfunction AddFacilityComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1, \" Facility name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddFacilityComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1, \" Tax ID is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddFacilityComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1, \" NPI is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddFacilityComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"uppercase\");\n    i0.ɵɵpipe(2, \"uppercase\");\n  }\n\n  if (rf & 2) {\n    const item_r18 = ctx.item;\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(1, 2, item_r18.taxonomyCode), \" - \", i0.ɵɵpipeBind1(2, 4, item_r18.taxonomyDescription), \"\");\n  }\n}\n\nfunction AddFacilityComponent_ng_template_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r21 = ctx.item;\n    i0.ɵɵtextInterpolate(item_r21.cityStateCounty);\n  }\n}\n\nfunction AddFacilityComponent_ng_template_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"uppercase\");\n    i0.ɵɵpipe(2, \"uppercase\");\n  }\n\n  if (rf & 2) {\n    const item_r24 = ctx.item;\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(1, 2, item_r24.stateCode), \" - \", i0.ɵɵpipeBind1(2, 4, item_r24.stateName), \"\");\n  }\n}\n\nfunction AddFacilityComponent_div_107_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AddFacilityComponent_div_107_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.addFacility(true));\n    });\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddFacilityComponent_div_107_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AddFacilityComponent_div_107_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.editFacility());\n    });\n    i0.ɵɵelementStart(1, \"i\", 54);\n    i0.ɵɵtext(2, \"save_as\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Update\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddFacilityComponent_div_107_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AddFacilityComponent_div_107_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.openEditFacility());\n    });\n    i0.ɵɵelementStart(1, \"i\", 55);\n    i0.ɵɵtext(2, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Edit\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddFacilityComponent_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"div\", 48);\n    i0.ɵɵelementStart(2, \"div\", 49);\n    i0.ɵɵtemplate(3, AddFacilityComponent_div_107_button_3_Template, 3, 0, \"button\", 50);\n    i0.ɵɵtemplate(4, AddFacilityComponent_div_107_button_4_Template, 4, 0, \"button\", 50);\n    i0.ɵɵtemplate(5, AddFacilityComponent_div_107_button_5_Template, 4, 0, \"button\", 50);\n    i0.ɵɵelementStart(6, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function AddFacilityComponent_div_107_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.cancel());\n    });\n    i0.ɵɵtext(7, \"Cancel\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.mode == \"add\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.mode == \"edit\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.mode == \"view\");\n  }\n}\n\nfunction AddFacilityComponent_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function AddFacilityComponent_div_108_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.addFacility(false));\n    });\n    i0.ɵɵelement(2, \"i\", 53);\n    i0.ɵɵtext(3, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 58);\n    i0.ɵɵtext(5, \"Cancel\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nconst _c0 = function (a0, a1) {\n  return {\n    \"dialog-content\": a0,\n    \"view-form\": a1\n  };\n};\n\nconst _c1 = function (a0) {\n  return {\n    \"mat-card\": a0\n  };\n};\n\nconst _c2 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nconst _c3 = function (a0) {\n  return {\n    \"invalid-fc\": a0\n  };\n};\n\nexport let AddFacilityComponent = /*#__PURE__*/(() => {\n  class AddFacilityComponent {\n    constructor(dialogRef, providerManagementService, stateService, subService, builder, notificationService) {\n      this.dialogRef = dialogRef;\n      this.providerManagementService = providerManagementService;\n      this.stateService = stateService;\n      this.subService = subService;\n      this.builder = builder;\n      this.notificationService = notificationService;\n      this.citySearchSubject = new Subject();\n      this.destroy$ = new Subject();\n      this.addTab = false;\n      this.mode = 'add';\n      this.mainHeading = 'Add Facility';\n\n      if (!!dialogRef && Object.keys(dialogRef).length > 0) {\n        dialogRef.disableClose = true;\n      }\n    }\n\n    ngOnInit() {\n      this.getAllDropdownData();\n      this.createFormGroup();\n\n      if (!!this.subService.currentTab && this.subService.currentTab.name == 'Add Facility') {\n        this.addTab = true;\n      }\n\n      this.subService.getSelectedFacilityInfoForView().pipe(takeUntil(this.destroy$)).subscribe(res => {\n        if (!!res && !this.addTab) {\n          if (this.mode == 'add') {\n            this.mode = res.mode;\n          }\n\n          if (this.mode == 'edit') {\n            this.mainHeading = 'Edit Facility123';\n          } else if (this.mode == 'view') {\n            this.addForm.disable();\n            this.mainHeading = 'View Facility';\n          }\n\n          if (!!!this.tabName) {\n            this.tabName = this.providerManagementService.getTabNameForProvider(res.data);\n          }\n\n          this.contentNeededForEdit = JSON.parse(JSON.stringify(res.data));\n          this.providerManagementService.fetchFacilityInfo(res.data.uniqueProviderId).subscribe(data => {\n            data = !!data.content ? data.content[0] : null;\n\n            if (!!!data) {\n              this.notificationService.showWarning('', 'Issue getting Facility Details!', 4000);\n            } else {\n              if (!!!this.facilityDataForView) {\n                this.facilityDataForView = data;\n                this.patchValueToForm(data);\n              }\n            }\n\n            this.subService.resetSelectedFacilityInfoForView();\n          });\n        }\n      });\n      this.subService.getRefreshFacilityViewAfterEdit().pipe(takeUntil(this.destroy$)).subscribe(res => {\n        if (this.mode == 'view' && !!this.facilityDataForView && !!res && this.facilityDataForView.uniqueOrgCode == res.uniqueOrgCode) {\n          this.providerManagementService.fetchFacilityInfo(res.uniqueOrgCode.toString()).subscribe(data => {\n            data = !!data.content ? data.content[0] : null;\n\n            if (!!!data) {\n              this.notificationService.showWarning('', 'Issue getting Facility Details!', 4000);\n            } else {\n              this.facilityDataForView = data;\n              this.patchValueToForm(data);\n              this.addForm.disable();\n            }\n\n            this.subService.resetRefreshFacilityViewAfterEdit();\n          });\n        }\n      });\n      this.citySearchSubject.pipe(debounceTime(DebounceTime.citiesDebounceTime), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.searchCities(searchTerm);\n      });\n    }\n\n    searchCities(searchTerm) {\n      // Only search if user has typed 2 or more characters\n      if (!searchTerm || searchTerm.length < 2) {\n        this.citiesData = [];\n        return;\n      }\n\n      this.providerManagementService.fetchCitiesBySearchTerm(searchTerm).subscribe(res => {\n        if (!!res && !!res.content) {\n          this.citiesData = res.content;\n        } else {\n          this.citiesData = [];\n        }\n      }, error => {\n        this.citiesData = [];\n      });\n    }\n\n    checkIfDialog() {\n      return !!this.dialogRef && Object.keys(this.dialogRef).length > 0;\n    }\n\n    patchValueToForm(data) {\n      this.addForm.patchValue({\n        facilityName: data.facilityName,\n        facilityType: data.facilityType,\n        taxID: data.providerTaxId,\n        organizationNPI: data.organizationNPI,\n        taxonomyDescription: data.taxonomyDescription,\n        taxonomyMDMCode: data.taxonomyMDMCode,\n        taxonomy: data.taxonomy,\n        taxonomyCode: data.taxonomy\n      });\n      let contact;\n\n      if (!!data.contactPerson && data.contactPerson.length > 0) {\n        contact = JSON.parse(JSON.stringify(data.contactPerson[0]));\n      }\n\n      if (!!contact) {\n        this.addForm.controls.contactPerson.patchValue({\n          contactPersonName: contact.contactPersonName,\n          contactPersonPhone: contact.contactPersonPhone,\n          contactPersonFax: contact.contactPersonFax,\n          contactPersonEmail: contact.contactPersonEmail\n        });\n      }\n\n      if (!!data.address) {\n        this.addForm.controls.addressForm.patchValue({\n          addressLine1: data.address.addressLine1,\n          addressLine2: data.address.addressLine2,\n          city: data.address.city,\n          state: data.address.state,\n          county: data.address.county,\n          countryCode: data.address.countryCode,\n          postalCode: data.address.postalCode\n        });\n      }\n    }\n\n    createContactPersonForm() {\n      let contactPersonForm = this.builder.group({\n        contactPersonName: new FormControl(null),\n        contactPersonPhone: new FormControl(null, faxNumberValidator),\n        contactPersonFax: new FormControl(null, faxNumberValidator),\n        contactPersonEmail: new FormControl(null, Validators.email)\n      });\n      return contactPersonForm;\n    }\n\n    createAddressForm() {\n      let addressForm = this.builder.group({\n        addressLine1: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        addressLine2: new FormControl(null),\n        city: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        state: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        county: new FormControl(null),\n        countryCode: new FormControl(null),\n        postalCode: new FormControl(null, [Validators.required, zipCodeValidator])\n      });\n      return addressForm;\n    }\n\n    get f() {\n      return this.addForm.controls;\n    }\n\n    createFormGroup() {\n      this.addForm = this.builder.group({\n        requestUserUUID: new FormControl(JSON.parse(localStorage.getItem('uuid'))),\n        requestedByEmail: new FormControl(JSON.parse(localStorage.getItem('email'))),\n        requestedByName: new FormControl(localStorage.getItem('userName')),\n        facilityName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        facilityType: new FormControl(null),\n        taxID: new FormControl(null, this.taxIdValidator),\n        organizationNPI: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.minLength(10), Validators.maxLength(10)]),\n        taxonomyDescription: new FormControl(null),\n        taxonomyCode: new FormControl(null),\n        taxonomyMDMCode: new FormControl(null),\n        taxonomy: new FormControl(null),\n        taxonomyCodeDescription: new FormControl(null),\n        contactPerson: this.createContactPersonForm(),\n        addressForm: this.createAddressForm()\n      });\n    }\n\n    get contactPersonForm() {\n      return this.addForm.controls.contactPerson.controls;\n    }\n\n    get addressForm() {\n      return this.addForm.controls.addressForm.controls;\n    }\n\n    taxIdValidator(control) {\n      const isValid = !!control.value && control.value.trim().length == 9 || control.value == null || control.value == '';\n      return isValid ? null : {\n        'isInvalid': true\n      };\n    }\n\n    cancel() {\n      if (this.mode == 'add') {\n        this.subService.setCloseTabRefresh('Add Facility');\n      }\n\n      if (this.mode == 'edit') {\n        this.subService.setCloseTabRefresh('Edit Facility - ' + this.tabName);\n      }\n\n      if (this.mode == 'view') {\n        this.subService.setCloseTabRefresh('View Facility - ' + this.tabName);\n      }\n    }\n\n    close() {\n      this.dialogRef.close(true);\n    }\n\n    getAllDropdownData() {\n      this.citiesData = [];\n      this.providerManagementService.fetchAllFacilityTypes().subscribe(res => {\n        this.facilityTypeData = !!res ? res : [];\n      });\n      let taxonomy = localStorage.getItem('allSpecialityTaxonomyCodes');\n\n      if (!!taxonomy) {\n        this.taxonomyData = JSON.parse(JSLZString.decompress(taxonomy));\n      } else {\n        this.providerManagementService.fetchSpecialityData('').subscribe(res => {\n          this.taxonomyData = !!res ? res : [];\n        });\n      }\n\n      let state = localStorage.getItem('allStates');\n\n      if (!!state) {\n        this.statesData = JSON.parse(JSLZString.decompress(state));\n      } else {\n        this.stateService.fetchchAllStatesBySearchstring().subscribe(res => {\n          this.statesData = res;\n        });\n      }\n\n      let countries = localStorage.getItem('allCountries');\n\n      if (!!countries) {\n        this.countriesData = JSON.parse(JSLZString.decompress(countries));\n      } else {\n        this.providerManagementService.fetchCountriesData(\"\").subscribe(res => {\n          this.countriesData = !!res.content ? res.content : [];\n        });\n      }\n\n      let counties = localStorage.getItem('allCounties');\n\n      if (!!counties) {\n        this.countiesData = JSON.parse(JSLZString.decompress(counties));\n      } else {\n        this.providerManagementService.fetchCountiesData(\"\").subscribe(res => {\n          this.countiesData = !!res.content ? res.content : [];\n        });\n      }\n    }\n\n    changeTaxonomyDescription(selectedTaxonomy) {\n      if (!!selectedTaxonomy) {\n        this.addForm.controls['taxonomyCode'].setValue(selectedTaxonomy.taxonomyCode);\n        this.addForm.controls['taxonomyMDMCode'].setValue(selectedTaxonomy.mdmCode);\n        this.addForm.controls['taxonomy'].setValue(selectedTaxonomy.taxonomyCode);\n        this.addForm.controls['taxonomyCodeDescription'].setValue(selectedTaxonomy.taxonomyDescription);\n      } else {\n        this.addForm.controls['taxonomyCode'].setValue(null);\n        this.addForm.controls['taxonomyMDMCode'].setValue(null);\n        this.addForm.controls['taxonomy'].setValue(null);\n        this.addForm.controls['taxonomyCodeDescription'].setValue(null);\n      }\n    }\n\n    addFacility(withoutDialog) {\n      var _this = this;\n\n      if (this.addForm.invalid) {\n        return;\n      }\n\n      let addFacilityObj = JSON.parse(JSON.stringify(this.addForm.value));\n      addFacilityObj.contactPerson = [JSON.parse(JSON.stringify(this.addForm.controls.contactPerson.value))];\n      addFacilityObj.address = JSON.parse(JSON.stringify(this.addForm.controls.addressForm.value));\n      addFacilityObj = trimSpaces(addFacilityObj); // let requestBody = {\n      //   addressLineOne: addFacilityObj.address.addressLine1,\n      //   city: addFacilityObj.address.city,\n      //   state: addFacilityObj.address.state,\n      //   zipcode: addFacilityObj.address.postalCode\n      // };\n      // this.providerManagementService.GetCorrectAddress(requestBody).subscribe((res) => {\n      //   console.log(res);\n      // })\n\n      this.providerManagementService.AddFacility(addFacilityObj).subscribe(res => {\n        if (res.statusCode == 200) {\n          this.notificationService.showSuccess('', 'Facility Added Successfully.', 4000);\n          this.subService.setRefreshProviderInfo(true);\n          this.subService.setRefreshFacilityList(true);\n          this.addForm.reset();\n\n          if (withoutDialog) {\n            this.cancel();\n          } else {\n            Swal.fire({\n              title: 'Confirm!',\n              text: \"Do you want to continue adding provider or do you want to close the current tab?\",\n              icon: 'warning',\n              showCancelButton: true,\n              confirmButtonText: 'Continue',\n              cancelButtonText: 'Close',\n              reverseButtons: true\n            }).then( /*#__PURE__*/function () {\n              var _ref = _asyncToGenerator(function* (result) {\n                if (result.value) {\n                  _this.close();\n                }\n\n                if (result.dismiss && result.dismiss == 'cancel') {\n                  _this.close();\n\n                  _this.subService.setCloseTabRefresh('Add New Provider');\n                }\n              });\n\n              return function (_x) {\n                return _ref.apply(this, arguments);\n              };\n            }());\n          }\n        } else {\n          this.notificationService.showError('', 'Facility could not be added. Please try again later.', 4000);\n        }\n      }, error => {\n        this.notificationService.showError('', 'Facility could not be added. Please try again later.', 4000);\n      });\n    }\n\n    changeCitySelect(selectedCity) {\n      if (!!selectedCity) {\n        this.addForm.controls.addressForm.controls.state.setValue(selectedCity.stateCode);\n        this.addForm.controls.addressForm.controls.county.setValue(selectedCity.countyName);\n        this.addForm.controls.addressForm.controls.countryCode.setValue(selectedCity.countryCode);\n      }\n    }\n\n    openEditFacility() {\n      const dataNeeded = {\n        data: this.contentNeededForEdit,\n        mode: 'edit'\n      };\n      this.subService.setSelectedFacilityInfoForView(dataNeeded);\n      let nav = new NavTabFromDetails();\n      nav.name = Tabs.editFacility;\n      nav.data = this.tabName;\n      this.subService.passValue(nav);\n      this.cancel();\n    }\n\n    editFacility() {\n      if (this.addForm.invalid) {\n        return;\n      }\n\n      let requestBody = this.getRequestBodyForEdit();\n      requestBody = trimSpaces(requestBody);\n      this.providerManagementService.updateFacility(requestBody).subscribe(res => {\n        if (res.statusCode == 200) {\n          this.notificationService.showSuccess('', 'Facility Updated Successfully.', 4000);\n          this.subService.setRefreshProviderInfo(true);\n          this.subService.setRefreshFacilityList(true);\n          let data = {\n            uniqueOrgCode: this.facilityDataForView.uniqueOrgCode\n          };\n          this.subService.setRefreshFacilityViewAfterEdit(data);\n          this.addForm.reset();\n          this.cancel();\n        } else {\n          this.notificationService.showError('', 'Facility could not be updated. Please try again later.', 4000);\n        }\n      }, error => {\n        this.notificationService.showError('', 'Facility could not be updated. Please try again later.', 4000);\n      });\n    }\n\n    getRequestBodyForEdit() {\n      let requestBody = {};\n      requestBody.requestedByName = this.addForm.value.requestedByName;\n      requestBody.requestedByEmail = this.addForm.value.requestedByEmail;\n      requestBody.requestUserUUID = this.addForm.value.requestUserUUID;\n      requestBody.demographic = {};\n      requestBody.demographic.facilityName = this.addForm.value.facilityName;\n      requestBody.demographic.npi = this.addForm.value.organizationNPI;\n      requestBody.contract = {};\n      requestBody.contract.facilityType = this.addForm.value.facilityType;\n      requestBody.contract.taxonomyCode = this.addForm.value.taxonomyCode;\n      requestBody.contract.taxonomyMDMCode = this.addForm.value.taxonomyMDMCode;\n      requestBody.contract.taxonomyDescription = this.addForm.value.taxonomyDescription;\n      requestBody.contract.taxonomyDescription = this.addForm.value.taxonomyDescription;\n      requestBody.contract.addressLine1 = this.addForm.controls.addressForm.value.addressLine1;\n      requestBody.contract.addressLine2 = this.addForm.controls.addressForm.value.addressLine2;\n      requestBody.contract.city = this.addForm.controls.addressForm.value.city;\n      requestBody.contract.state = this.addForm.controls.addressForm.value.state;\n      requestBody.contract.county = this.addForm.controls.addressForm.value.county;\n      requestBody.contract.country = this.addForm.controls.addressForm.value.countryCode;\n      requestBody.contract.zipCode = this.addForm.controls.addressForm.value.postalCode;\n      requestBody.contract.effectiveDate = this.facilityDataForView.effectiveDate;\n      requestBody.contract.termDate = this.facilityDataForView.terminationDate;\n      requestBody.contract.bridgeQueueStatus = this.facilityDataForView.bridgeQueueStatus;\n      requestBody.contract.isPreferred = this.facilityDataForView.isPreferred != null && this.facilityDataForView.isPreferred != undefined ? this.facilityDataForView.isPreferred.toString() : this.facilityDataForView.isPreferred;\n      requestBody.contract.isINNetwork = this.facilityDataForView.inNetwork;\n      requestBody.contract.providerRosterID = this.facilityDataForView.providerRosterID;\n      requestBody.contract.module = this.facilityDataForView.module;\n      requestBody.contract.taxID = this.addForm.value.taxID;\n      requestBody.contract.contactName = this.addForm.controls.contactPerson.value.contactPersonName;\n      requestBody.contract.telephoneNumber = this.addForm.controls.contactPerson.value.contactPersonPhone;\n      requestBody.contract.faxNumber = this.addForm.controls.contactPerson.value.contactPersonFax;\n      requestBody.contract.email = this.addForm.controls.contactPerson.value.contactPersonEmail;\n      requestBody.contract.planCode = this.facilityDataForView.planCode;\n      requestBody.contract.facilityID = this.facilityDataForView.facilityInformationID;\n      return requestBody;\n    }\n\n    ngOnDestroy() {\n      this.destroy$.next(true);\n      this.destroy$.unsubscribe();\n    }\n\n  }\n\n  AddFacilityComponent.ɵfac = function AddFacilityComponent_Factory(t) {\n    return new (t || AddFacilityComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef, 8), i0.ɵɵdirectiveInject(i2.ProviderManagementService), i0.ɵɵdirectiveInject(i3.AllStatesBySearchstringService), i0.ɵɵdirectiveInject(i4.SubjectService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.NotificationService));\n  };\n\n  AddFacilityComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddFacilityComponent,\n    selectors: [[\"app-add-facility\"]],\n    decls: 109,\n    vars: 67,\n    consts: [[\"class\", \"dailog-header\", 4, \"ngIf\"], [1, \"container-fluid\", \"add-facility\", 3, \"ngClass\"], [1, \"mt-3\", \"create-claim-form-styles\", 3, \"ngClass\"], [\"class\", \"create-claim-title mt-15\", \"style\", \"color: #023781;font-weight: bold;margin-left: 21px; font-size: 22px;\", 4, \"ngIf\"], [3, \"formGroup\"], [1, \"mt-3\", \"internal-div\"], [1, \"f-size16\", \"menuitemschaild\"], [1, \"row\", \"menuitemschaild\", \"mt-8\"], [1, \"col-md-12\"], [1, \"row\", \"f-size13\"], [1, \"col-md-4\"], [1, \"dashboard-label\"], [\"type\", \"text\", \"placeholder\", \"Name\", \"name\", \"name\", \"id\", \"name\", \"autocomplete\", \"off\", \"formControlName\", \"facilityName\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"bindValue\", \"name\", \"bindLabel\", \"name\", \"placeholder\", \"Select Facility Type\", \"formControlName\", \"facilityType\", 1, \"form-control\", 3, \"virtualScroll\", \"items\"], [\"facilityTypeSelect\", \"\"], [\"type\", \"text\", \"numbersOnly\", \"\", \"autocomplete\", \"off\", \"placeholder\", \"Tax ID\", \"name\", \"taxId\", \"id\", \"taxId\", \"formControlName\", \"taxID\", \"maxlength\", \"10\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-md-12\", \"mt-2\"], [\"type\", \"text\", \"numbersOnly\", \"\", \"autocomplete\", \"off\", \"placeholder\", \"NPI\", \"name\", \"npi\", \"id\", \"npi\", \"formControlName\", \"organizationNPI\", 1, \"form-control\", 3, \"ngClass\"], [\"bindLabel\", \"taxonomyDescription\", \"bindValue\", \"taxonomyDescription\", \"placeholder\", \"Select Taxonomy\", \"formControlName\", \"taxonomyDescription\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"change\"], [\"taxonomyDescriptionSelect\", \"\"], [\"ng-option-tmp\", \"\"], [1, \"row\", \"f-size13\", 3, \"formGroup\"], [1, \"col-md-3\"], [\"type\", \"text\", \"placeholder\", \"Contact Name\", \"formControlName\", \"contactPersonName\", \"name\", \"contactName\", \"id\", \"contactName\", \"autocomplete\", \"off\", 1, \"form-control\"], [\"type\", \"text\", \"numbersOnly\", \"\", \"autocomplete\", \"off\", \"placeholder\", \"Telephone Number\", \"formControlName\", \"contactPersonPhone\", \"name\", \"telNum\", \"id\", \"telNum\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"numbersOnly\", \"\", \"autocomplete\", \"off\", \"placeholder\", \"Fax Number\", \"formControlName\", \"contactPersonFax\", \"name\", \"faxNum\", \"id\", \"faxNum\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"email\", \"autocomplete\", \"off\", \"placeholder\", \"Email\", \"formControlName\", \"contactPersonEmail\", \"name\", \"email\", \"id\", \"email\", 1, \"form-control\", 3, \"ngClass\"], [1, \"mt-3\", \"internal-div\", 3, \"formGroup\"], [\"type\", \"text\", \"placeholder\", \"Address Line 1\", \"name\", \"address1\", \"id\", \"address1\", \"autocomplete\", \"off\", \"formControlName\", \"addressLine1\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"placeholder\", \"Address Line 2\", \"name\", \"address2\", \"id\", \"address2\", \"autocomplete\", \"off\", \"formControlName\", \"addressLine2\", 1, \"form-control\"], [\"bindValue\", \"cityName\", \"bindLabel\", \"cityName\", \"appendTo\", \"body\", \"formControlName\", \"city\", \"placeholder\", \"Select City\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"typeahead\", \"minTermLength\", \"ngClass\", \"change\"], [\"citySelect\", \"\"], [\"bindLabel\", \"stateCode\", \"bindValue\", \"stateCode\", \"placeholder\", \"Select State\", \"formControlName\", \"state\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"ngClass\"], [\"stateSelect\", \"\"], [\"bindValue\", \"countyName\", \"bindLabel\", \"countyName\", \"formControlName\", \"county\", \"placeholder\", \"Select County\", 1, \"form-control\", 3, \"virtualScroll\", \"items\"], [\"countySelect\", \"\"], [\"bindValue\", \"countryCode\", \"bindLabel\", \"name\", \"formControlName\", \"countryCode\", \"placeholder\", \"Select Country\", 1, \"form-control\", 3, \"virtualScroll\", \"items\"], [\"countrySelect\", \"\"], [\"type\", \"text\", \"placeholder\", \"Zip Code\", \"numbersOnly\", \"\", \"name\", \"zipCode\", \"id\", \"zipCode\", \"autocomplete\", \"off\", \"formControlName\", \"postalCode\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"row menuitemschaild mt-8 bottom-div\", 4, \"ngIf\"], [\"mat-dialog-actions\", \"\", \"class\", \"mat-dialog-actions-custom\", 4, \"ngIf\"], [1, \"dailog-header\"], [\"mat-dialog-title\", \"\"], [\"src\", \"../../../../assets//images/Modals/SaveModal/CloseIcon.png\", \"id\", \"ModalClose\", \"alt\", \"Close\", \"title\", \"Close\", 1, \"Modal-Close-Image\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"create-claim-title\", \"mt-15\", 2, \"color\", \"#023781\", \"font-weight\", \"bold\", \"margin-left\", \"21px\", \"font-size\", \"22px\"], [1, \"invalid-feedback\"], [1, \"row\", \"menuitemschaild\", \"mt-8\", \"bottom-div\"], [1, \"col-md-6\"], [1, \"col-md-6\", \"text-align-R\"], [\"type\", \"button\", \"class\", \"btn btn-primary primary-btn btn-height\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn-white\", \"btn-common\", \"common-btn\", \"btn-height\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"primary-btn\", \"btn-height\", 3, \"click\"], [1, \"fa\", \"fa-floppy-o\", \"icon-margin\"], [1, \"material-icons\", \"icon-margin\", \"icon-height\", \"icon-align\"], [1, \"material-icons\", \"icon-margin\"], [\"mat-dialog-actions\", \"\", 1, \"mat-dialog-actions-custom\"], [1, \"btn\", \"btn-primary\", \"primary-btn\", \"btn-height\", 2, \"font-size\", \"1rem\", \"padding-top\", \"0px\", 3, \"click\"], [\"mat-dialog-close\", \"\", 1, \"btn-white\", \"btn-common\", \"common-btn\", \"btn-height\", 2, \"font-size\", \"1rem\"]],\n    template: function AddFacilityComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AddFacilityComponent_div_0_Template, 6, 0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵtemplate(3, AddFacilityComponent_label_3_Template, 2, 1, \"label\", 3);\n        i0.ɵɵelementStart(4, \"form\", 4)(5, \"div\", 5)(6, \"label\", 6);\n        i0.ɵɵtext(7, \" General Information \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"div\", 10)(12, \"label\", 11);\n        i0.ɵɵtext(13, \" Name* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(14, \"input\", 12);\n        i0.ɵɵtemplate(15, AddFacilityComponent_div_15_Template, 2, 0, \"div\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"div\", 10)(17, \"label\", 11);\n        i0.ɵɵtext(18, \" Facility Type \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(19, \"ng-select\", 14, 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 10)(22, \"label\", 11);\n        i0.ɵɵtext(23, \" Tax ID \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(24, \"input\", 16);\n        i0.ɵɵtext(25);\n        i0.ɵɵpipe(26, \"json\");\n        i0.ɵɵtemplate(27, AddFacilityComponent_div_27_Template, 2, 0, \"div\", 13);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(28, \"div\", 17)(29, \"div\", 9)(30, \"div\", 10)(31, \"label\", 11);\n        i0.ɵɵtext(32, \" NPI* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(33, \"input\", 18);\n        i0.ɵɵtext(34);\n        i0.ɵɵpipe(35, \"json\");\n        i0.ɵɵtemplate(36, AddFacilityComponent_div_36_Template, 2, 0, \"div\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"div\", 10)(38, \"label\", 11);\n        i0.ɵɵtext(39, \" Taxonomy Description \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"ng-select\", 19, 20);\n        i0.ɵɵlistener(\"change\", function AddFacilityComponent_Template_ng_select_change_40_listener($event) {\n          return ctx.changeTaxonomyDescription($event);\n        });\n        i0.ɵɵtemplate(42, AddFacilityComponent_ng_template_42_Template, 3, 6, \"ng-template\", 21);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(43, \"div\", 5)(44, \"label\", 6);\n        i0.ɵɵtext(45, \" Contact Information \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(46, \"div\", 7)(47, \"div\", 8)(48, \"div\", 22)(49, \"div\", 23)(50, \"label\", 11);\n        i0.ɵɵtext(51, \" Contact Name \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(52, \"input\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"div\", 23)(54, \"label\", 11);\n        i0.ɵɵtext(55, \" Telephone Number \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(56, \"input\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"div\", 23)(58, \"label\", 11);\n        i0.ɵɵtext(59, \" Fax Number \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(60, \"input\", 26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"div\", 23)(62, \"label\", 11);\n        i0.ɵɵtext(63, \" Email \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(64, \"input\", 27);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(65, \"div\", 28)(66, \"label\", 6);\n        i0.ɵɵtext(67, \" Address Information \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"div\", 7)(69, \"div\", 8)(70, \"div\", 9)(71, \"div\", 23)(72, \"label\", 11);\n        i0.ɵɵtext(73, \" Address Line 1* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(74, \"input\", 29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(75, \"div\", 23)(76, \"label\", 11);\n        i0.ɵɵtext(77, \" Address Line 2 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(78, \"input\", 30);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(79, \"div\", 23)(80, \"label\", 11);\n        i0.ɵɵtext(81, \" City* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(82, \"ng-select\", 31, 32);\n        i0.ɵɵlistener(\"change\", function AddFacilityComponent_Template_ng_select_change_82_listener($event) {\n          return ctx.changeCitySelect($event);\n        });\n        i0.ɵɵtemplate(84, AddFacilityComponent_ng_template_84_Template, 1, 1, \"ng-template\", 21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(85, \"div\", 23)(86, \"label\", 11);\n        i0.ɵɵtext(87, \" State* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(88, \"ng-select\", 33, 34);\n        i0.ɵɵtemplate(90, AddFacilityComponent_ng_template_90_Template, 3, 6, \"ng-template\", 21);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(91, \"div\", 17)(92, \"div\", 9)(93, \"div\", 23)(94, \"label\", 11);\n        i0.ɵɵtext(95, \" County \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(96, \"ng-select\", 35, 36);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(98, \"div\", 23)(99, \"label\", 11);\n        i0.ɵɵtext(100, \" Country \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(101, \"ng-select\", 37, 38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"div\", 23)(104, \"label\", 11);\n        i0.ɵɵtext(105, \" Zip Code* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(106, \"input\", 39);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵtemplate(107, AddFacilityComponent_div_107_Template, 8, 3, \"div\", 40);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(108, AddFacilityComponent_div_108_Template, 6, 0, \"div\", 41);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.checkIfDialog());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(42, _c0, ctx.checkIfDialog(), ctx.mode == \"view\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(45, _c1, !ctx.checkIfDialog()));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.checkIfDialog());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"formGroup\", ctx.addForm);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(47, _c2, ctx.f[\"facilityName\"].invalid && (ctx.f[\"facilityName\"].dirty || ctx.f[\"facilityName\"].touched) && (ctx.f[\"facilityName\"].errors == null ? null : ctx.f[\"facilityName\"].errors.required)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"facilityName\"].invalid && ctx.f[\"facilityName\"].errors.required);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.facilityTypeData);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(49, _c2, ctx.f[\"taxID\"].invalid && (ctx.f[\"taxID\"].dirty || ctx.f[\"taxID\"].touched) && ctx.f[\"taxID\"].errors));\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(26, 38, ctx.f[\"taxID\"].errors), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"taxID\"].invalid && (ctx.f[\"taxID\"].errors == null ? null : ctx.f[\"taxID\"].errors.isInvalid));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(51, _c2, ctx.f[\"organizationNPI\"].invalid && (ctx.f[\"organizationNPI\"].dirty || ctx.f[\"organizationNPI\"].touched) && (ctx.f[\"organizationNPI\"].errors == null ? null : ctx.f[\"organizationNPI\"].errors.required)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(35, 40, ctx.f[\"organizationNPI\"].errors), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"organizationNPI\"].invalid && ctx.f[\"organizationNPI\"].errors.required);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.taxonomyData);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"formGroup\", ctx.addForm.controls.contactPerson);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c3, ctx.contactPersonForm[\"contactPersonPhone\"].invalid && (ctx.contactPersonForm[\"contactPersonPhone\"].dirty || ctx.contactPersonForm[\"contactPersonPhone\"].touched) && ctx.contactPersonForm[\"contactPersonPhone\"].errors));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(55, _c3, ctx.contactPersonForm[\"contactPersonFax\"].invalid && (ctx.contactPersonForm[\"contactPersonFax\"].dirty || ctx.contactPersonForm[\"contactPersonFax\"].touched) && ctx.contactPersonForm[\"contactPersonFax\"].errors));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(57, _c3, ctx.contactPersonForm[\"contactPersonEmail\"].invalid && (ctx.contactPersonForm[\"contactPersonEmail\"].dirty || ctx.contactPersonForm[\"contactPersonEmail\"].touched) && ctx.contactPersonForm[\"contactPersonEmail\"].errors));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"formGroup\", ctx.addForm.controls.addressForm);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(59, _c3, ctx.addressForm[\"addressLine1\"].invalid && (ctx.addressForm[\"addressLine1\"].dirty || ctx.addressForm[\"addressLine1\"].touched) && ctx.addressForm[\"addressLine1\"].errors));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.citiesData)(\"typeahead\", ctx.citySearchSubject)(\"minTermLength\", 2)(\"ngClass\", i0.ɵɵpureFunction1(61, _c3, ctx.addressForm[\"city\"].invalid && (ctx.addressForm[\"city\"].dirty || ctx.addressForm[\"city\"].touched) && ctx.addressForm[\"city\"].errors));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.statesData)(\"ngClass\", i0.ɵɵpureFunction1(63, _c3, ctx.addressForm[\"state\"].invalid && (ctx.addressForm[\"state\"].dirty || ctx.addressForm[\"state\"].touched) && ctx.addressForm[\"state\"].errors));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.countiesData);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.countriesData);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(65, _c3, ctx.addressForm[\"postalCode\"].invalid && (ctx.addressForm[\"postalCode\"].dirty || ctx.addressForm[\"postalCode\"].touched) && ctx.addressForm[\"postalCode\"].errors));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.checkIfDialog());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.checkIfDialog());\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.MaxLengthValidator, i5.FormGroupDirective, i5.FormControlName, i8.NgSelectComponent, i8.NgOptionTemplateDirective, i9.OnlyNumberDirective, i1.MatDialogClose, i1.MatDialogTitle, i1.MatDialogActions, i7.UpperCasePipe, i7.JsonPipe],\n    styles: [\".mt-15[_ngcontent-%COMP%]{margin-top:15px}.mt-8[_ngcontent-%COMP%]{margin-top:8px}.wdt-35[_ngcontent-%COMP%]{width:35px!important}.mt-26[_ngcontent-%COMP%]{margin-top:26px}.text-align-R[_ngcontent-%COMP%]{text-align:right}.ml-10[_ngcontent-%COMP%]{margin-left:10px}.btn-white[_ngcontent-%COMP%]{padding:0 10px 0 8px!important}.f-size16[_ngcontent-%COMP%]{font-size:16px!important}.f-size13[_ngcontent-%COMP%]{font-size:13px!important}.internal-div[_ngcontent-%COMP%]{background:#ECF4FC 0% 0% no-repeat padding-box;border:1px solid #ECF4FC;border-radius:6px;opacity:1;padding:20px 20px 20px 0;font-family:Poppins-SemiBold;margin:15px 15px 10px}.bottom-div[_ngcontent-%COMP%]{margin-bottom:10px;padding:2px 11px 10px 0}  .custom-frame{& .mat-checkbox-background,.mat-checkbox-frame{border-radius:70%!important}}.add-provider[_ngcontent-%COMP%]{input[type=\\\"checkbox\\\"] {cursor: pointer;} ::ng-deep .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {white-space: break-spaces !important;} .form-control[readonly] {pointer-events: none !important;} ::ng-deep .mat-radio-checked .mat-radio-inner-circle{background-color: #1059E3 !important;} ::ng-deep .mat-radio-checked .mat-radio-outer-circle{border-color: #1059E3 !important;} ::ng-deep .mat-radio-button .mat-ripple-element{background-color: #1059E3 !important;} ::ng-deep .ng-select-container {height: 100% !important;}}.form-control[_ngcontent-%COMP%]{height:2.36rem!important}.radio-form-control[_ngcontent-%COMP%]{border:none!important;background:inherit!important;padding-left:0!important}.mat-card2[_ngcontent-%COMP%]{background:white!important;margin:1rem;padding:.5rem;border:1px solid lightgrey;cursor:pointer}.invalid-radio[_ngcontent-%COMP%]{::ng-deep .mat-radio-outer-circle {border-color: red !important;}}.view-form[_ngcontent-%COMP%]{.form-control,.form-select {background-color: transparent !important; border: none !important; padding: 0 !important; font-style: italic !important; pointer-events: none !important; text-transform: uppercase; word-wrap: break-word;} input::placeholder {color: transparent !important;} select::placeholder {color: transparent !important;} input[type=\\\"date\\\"]::-webkit-calendar-picker-indicator {color: transparent;} .row {margin-top: 1rem;}}.primary-location[_ngcontent-%COMP%]{border:4px solid #1059E3!important;font-weight:600}.current-location[_ngcontent-%COMP%]{box-shadow:4px 5px 4px 4px #0003,0 1px 1px 7px #00000024,0 1px 3px #0000001f;border:2px solid darkgrey}.dailog-header[_ngcontent-%COMP%]{padding-bottom:0!important}.mat-dialog-actions-custom[_ngcontent-%COMP%]{border-top:1px solid #bdbdbd!important;padding-right:1rem!important;padding-left:1rem!important;justify-content:end!important;padding-top:0;padding-bottom:0;margin-bottom:0}.dialog-content[_ngcontent-%COMP%]{max-height:80%!important;overflow-y:auto!important}.add-facility[_ngcontent-%COMP%]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{white-space:break-spaces!important}.add-facility[_ngcontent-%COMP%]     .ng-select-container{height:100%!important}.view-form[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], .view-form[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]{background-color:transparent!important;border:none!important;padding:0!important;font-style:italic!important;pointer-events:none!important;text-transform:uppercase;word-wrap:break-word}.view-form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:transparent!important}.view-form[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]::placeholder{color:transparent!important}.view-form[_ngcontent-%COMP%]   input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent}.view-form[_ngcontent-%COMP%]   .mt-2[_ngcontent-%COMP%]{margin-top:1rem}.material-icons[_ngcontent-%COMP%]{font-size:12px!important;padding:unset!important}.btn[_ngcontent-%COMP%]{height:26px}\"]\n  });\n  return AddFacilityComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}