.body {
    background-color: white;
    border-radius: 8px;
    margin-top: 10px;
    height: 1000px;
    box-shadow: 0px 0px 8px #0000001a;
}

.heading {
    color: #0074bc;
    font-size: 22px;
    padding-left: 10px;
    padding-top: 20px;
    font-family: "Poppins-SemiBold";
}

.customInputForProvider {
    width: 100% !important;
}

input[type="text"],
input[type="number"] {
    padding-left: 5px;
    border-radius: 6px;
    border: 1px solid #cccccc !important;
    height: 30px;
    outline: none;
    min-width: 40px;
    width: 65%;
    font-size: 10px;
    font-family: "Poppins-SemiBold";
    background-color: #fff;
}

input[type="text"]:focus,
input[type="number"]:focus {
    border: 1px solid #61abd4 !important;
}

input[type="radio"] {
    color: white !important;
    border-radius: 50%;
    appearance: none;
    border: 1px solid #d3d3d3;
    width: 16px;
    height: 16px;
    content: none;
    outline: none;
    margin: 0;
    padding: 0px !important;
    margin-right: 2px;
    box-shadow: none;
}

input[type="radio"]:checked {
    width: 16px;
    height: 16px;
    appearance: none;
    outline: none;
    padding: 0;
    content: none;
    border: none;
    box-shadow: none;
}

input[type="radio"]:checked::before {
    position: absolute;
    background-color: #0074bc !important;
    color: white !important;
    content: "\00A0\2713\00A0" !important;
    border: 1px solid #d3d3d3;
    font-weight: bold;
    font-size: 10px;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    box-shadow: none;
    border: none;
    padding: 0px;
    margin-left: -1px;
    margin-top: -1px;
    padding-left: 1px;
}

input[type="date"] {
    background-color: white;
    padding-left: 5px;
    outline: none;
    border: 1px solid #d9dade;
    border-radius: 8px;
    height: 30px;
    opacity: 1;
    width: 90%;
    color: #000000;
    font-size: 12px;
    font-family: "Poppins-Medium";
}

input[type="date"]::-webkit-calendar-picker-indicator {
    color: rgba(0, 0, 0, 0);
    opacity: 1;
    display: block;
    background: url("../../Images/Claims/CalendarIcon.svg") no-repeat;
    width: 9px;
    height: 15px;
    border-width: thin;
}

input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

select {
    box-shadow: none !important;
    background-color: white !important;
    padding: 0px;
    border: 1px solid #cccccc !important;
    border-radius: 6px;
    font-size: 10px;
    font-family: "Poppins-SemiBold";
    height: 30px;
    width: 65%;
    cursor: pointer;
    padding-left: 5px;
    outline: none;
}

select.ng-invalid.ng-touched {
    border: 1px solid red !important;
}

.PaddingRight {
    padding-right: 17px !important;
}

.inner-addon {
    position: relative;
}

.inner-addon .ArrowDown {
    position: absolute;
    padding: 3%;
}

.right-addon .ArrowDown {
    pointer-events: none;
    right: 8%;
    margin-right: 28%;
}

.right-addon-for-provider .ArrowDown {
    pointer-events: none;
    right: 4%;
}

.btn {
    border: none;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
}

.btn:hover {
    background-color: #005488;
}

.backBtn {
    border-color: #0074bc !important;
    background-color: white;
    margin-right: 3%;
    color: #0074bc;
    border: 1px solid;
    font-size: 12px;
    margin-left: 77%;
}

.backBtn:hover {
    border-color: #0074bc !important;
    background-color: white;
}

.border {
    border: 1px solid #cccccc !important;
    border-radius: 6px;
    opacity: 1;
    margin: 1%;
    padding: 1%;
}

.practiceInformationBorder {
    border: 1px solid #cccccc !important;
    border-radius: 6px;
    opacity: 1;
    padding: 1%;
}

.billingInformationBorder {
    border: 1px solid #cccccc !important;
    border-radius: 6px;
    opacity: 1;
    padding: 1%;
}

.TextRight {
    text-align: end;
}

.textRight {
    text-align: end;
    justify-content: flex-end;
}

.MarginRight {
    margin-right: 10px;
}

.MarginLeft {
    margin-left: 8px;
}

.btnMargin {
    margin-left: 80px;
}

a {
    font-family: "Poppins-SemiBold";
    color: #646464;
    font-size: 12px;
    cursor: pointer;
}

.demographicForm {
    margin-left: 2%;
}

.practiceInformationForm {
    margin-left: 2%;
}

.billingInformationForm {
    margin-left: 2%;
}

.billingInformationPartialForm {
    margin-left: 2%;
}

.SpinnerCustomDesign {
    height: 12px;
    width: 12px;
    margin-left: 10%;
}

.iconMargin {
    margin-left: 3%;
}

.descList {
    background-color: #e3f2fd;
}

.section-heading {
    color: #5d5d5d;
    font-family: "Poppins-SemiBold";
    font-size: 15px;
    padding: 0;
}

.col {
    color: #646464;
    font-size: 12px;
    font-family: "Poppins-semibold";
}

 

 

input.ng-invalid.ng-touched,
.RedBorder::before {
    border: 1px solid red !important;
}

select.ng-invalid.ng-touched {
    border: 1px solid red !important;
}
