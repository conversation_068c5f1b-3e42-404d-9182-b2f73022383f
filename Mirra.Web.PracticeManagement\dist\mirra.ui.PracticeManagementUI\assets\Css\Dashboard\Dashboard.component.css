.radioClass {
    height: 16px;
    width: 16px;
}

.radiopClass {
    height: 16px;
    width: 16px;
}

input[type=checkbox] {
    /* Hide original inputs */
    opacity: 0.001;
    visibility: visible;
    position: absolute;
    z-index: 1000;
}

input[type=checkbox]+label+p {
    height: 16px;
    width: 16px;
}

input[type=checkbox]+label>p {
    transition: 100ms all;
    height: 16px;
    width: 16px;
}

input[type=checkbox]:checked+label>p {
    z-index: -1000;
    background-repeat: no-repeat;
    border-style: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16.17' height='16.505' viewBox='0 0 14.17 13.505'%3E%3Cg id='Checkbox' transform='translate(0 0.001)'%3E%3Crect id='Rectangle' width='14.17' height='13.505' rx='3' transform='translate(0 -0.001)' fill='%230074bc'/%3E%3Cpath id='check' d='M10.97,4.97a.75.75,0,1,1,1.071,1.05L8.049,11.01a.75.75,0,0,1-1.08.02L4.324,8.384a.75.75,0,1,1,1.06-1.06L7.478,9.417l3.473-4.425.02-.022Z' transform='translate(-1.085 -0.745)' fill='%23fff' fill-rule='evenodd'/%3E%3C/g%3E%3C/svg%3E%0A");
}

input[type=checkbox]+label>p {
    width: 16px;
    height: 16px;
    border: #999;
    border-width: 1px;
    border-style: solid;
    border-radius: 5px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16.17' height='16.505' viewBox='0 0 8.17 6.505'%3E%3Cpath id='check' d='M10.97,4.97a.75.75,0,1,1,1.071,1.05L8.049,11.01a.75.75,0,0,1-1.08.02L4.324,8.384a.75.75,0,1,1,1.06-1.06L7.478,9.417l3.473-4.425.02-.022Z' transform='translate(-4.085 -4.745)' fill='%23f4f7fc'/%3E%3C/svg%3E%0A");
}


/* some styling for tabs header shown on header */

::ng-deep .mat-tab-list .mat-tab-labels .mat-tab-label-active {
    color: white;
    background-color: #0D3B57;
}

::ng-deep .mat-tab-list .mat-tab-label {
    opacity: 1;
    color: #5a607f;
    background-color: lightblue;
    border: 1px solid #D5D7E3;
    border-radius: 4px 4px 0 0;
    /* border-top-right-radius: 10px; */
    /* border-top-left-radius: 10px; */
    margin-left: 5px;
    height: 38px;
}

::ng-deep .mat-tab-list {
    background-color: transparent;
}


/* styling for width for max width 600px */

@media screen and (max-width: 600px) {
    .column {
        width: auto;
        display: block;
        margin-bottom: 20px;
        overflow: hidden;
    }
}


/* Regular Poppins font for text */

.Regular {
    font-family: 'Poppins';
}


/* stylinf for making font bold */

.Bold-Weight {
    font-weight: bold;
}


/* Style the counter cards */

.card {
    border-color: transparent;
    box-shadow: 0px 0px 8px #0000001A;
    min-width: 102%;
    padding: 7px;
    background-color: white;
    border-radius: 15px;
}


/* for hiding extra content in daterangepicker */

.e-range-header {
    display: none;
}


/* for setting width of daterangepicker already added dates (This Week This Month..)*/

.e-presets {
    width: 300px;
}


/* some styling for daterangepicker wrapper */

.e-daterangepicker.e-popup.e-preset-wrapper,
.e-bigger.e-small .e-daterangepicker.e-popup.e-preset-wrapper {
    min-width: 800px;
    left: 500px;
}


/* styling for daterange input */

#control_wrapper {
    display: flex;
    max-width: 500px;
}


/* styling for labels */

label {
    font-family: "Poppins-SemiBold", Arial, Helvetica, sans-serif;
    font-size: 0.84vw;
}


/* to display content in div left side */

.top-left {
    text-align: left;
}


/* to display content in div right side */

.top-right {
    text-align: right;
}


/* to make select placeholder */

.Selected-Option {
    color: lightgrey;
}


/* styling for select having this class paticularly */

.sel1,
.sel2 {
    display: flex;
    /* width: 200px; */
}


/* styling for select*/

select {
    width: 10.00vw;
    height: 35px;
    border-radius: 8px;
    box-shadow: 0px 0px 8px #0000001A;
    ;
}


/* for setting each card 's column */

.column {
    float: left;
    max-width: 105%;
    min-width: 100%;
    min-height: 283px;
    margin-bottom: 25px;
    padding: 10px 5px;
}


/* for setting background color of daterangepicker */

.e-input-group {
    border-color: transparent !important;
}


/* to give prroperty of flex space between to div */

.Flex-Sp-Btwn-Pd {
    display: flex!important;
    justify-content: space-between;
    padding: 3px;
}


/* property for custom select */

.Width13 {
    width: 13%;
}

.Width14 {
    width: 14%;
}

.Width15 {
    width: 15%;
}

.Width18 {
    width: 18%;
}

.custom-select {
    /* width: 135px; */
    width: 90%;
    color: #565656;
    border: 1px solid #D5D7E3;
    outline: none;
    box-shadow: none !important;
}


/* styles of dateranegepicker label */

.Label-Of-Date-Selector {
    width: 100px;
    font-size: 12px;
    font-family: "Poppins-SemiBold", Arial, Helvetica, sans-serif;
    margin-left: -10px;
}

.Date-Selector {
    width: 90%;
    border: 1px solid #D5D7E3;
    /* box-shadow: 0px 0px 8px #0000001A; */
    height: 37px;
    border-radius: 10px;
    background-color: white;
}

.Header-Input-Row {
    margin-right: 30px;
}


/* for setting text and placeholder inside daaterangepicker */

.e-input-group:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left),
.e-input-group.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error) .e-input-in-wrap,
.e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left),
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error) .e-input-in-wrap,
.e-float-input.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error) .e-input-in-wrap,
.e-float-input.e-control-wrapper.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error) .e-input-in-wrap {
    margin-bottom: -15px;
}


/* styling for common status of each card i.e. initiated,submited.. */

.StatusSize {
    font-size: 14px;
    font-family: "Poppins-Medium", Arial, Helvetica, sans-serif;
}


/* styling for total count having # */

.LargestStatus {
    color: #000;
    font-size: 19px;
}


/* text in blue color */

.Blue-Color {
    color: #00A9D1;
}


/* styles for heading in blue color */

.Blue-Color-Heading {
    color: #0074BC;
}


/* text in orange color */

.Orange-Color {
    color: #994272;
}


/* heading in green color */

.Green-Color-Heading {
    color: #077E25;
}


/* text in red color */

.Red-Color {
    color: #C7003D;
}


/* heading in red color */

.Red-Color-Heading {
    color: #FF2366;
}


/* for text having font Bold */

.Bolder-Font {
    font-size: 16px;
    font-family: "Poppins-Bold", Arial, Helvetica, sans-serif;
}


/* Main top left status of each card */

.Status {
    font-size: 12px;
    color: #061058;
    margin-right: 10px;
    font-family: "Poppins", Arial, Helvetica, sans-serif;
}


/* styling for nested status of each card like open dispatched */

.Nested-Status {
    /* width: 72px; */
    padding-left: 5px;
    color: #061058;
    font-size: 8px;
    font-family: "Poppins-SemiBold";
}


/* styling for nested status count of each card like open's count dispatched count */

.Nested-Status-Count {
    padding-left: 5px!important;
    color: #061058;
    font-size: 12px;
    font-family: "Poppins-Bold";
}


/* border for bottom of div */

.Border-Bottom {
    border-bottom: 0.5px solid #D5D7E3;
}


/* border for top of div */

.Border-Top {
    border-top: 0.5px solid #D5D7E3;
}


/* border for right of div */

.BorderRight {
    border-right: 0.5px solid #D5D7E3;
}


/* if td is empty don't collapse it this is used to add some space*/

td:empty::after {
    content: "\00a0";
}


/* Remove extra left and right margins, due to padding */

.row {
    margin: 0 -5px;
    display: contents !important;
}


/* Clear floats after the columns */

.row:after {
    /* content: ""; */
    display: table;
    clear: both;
}

.card-deck {
    margin-top: 10px;
    margin-left: auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    grid-gap: .5rem;
}

.Margin-Right {
    margin-right: 5px;
}

.FilterBox {
    font-family: 'Poppins';
    position: absolute;
    right: 2.5%;
    top: 110%;
    background-color: white;
    color: #000000;
    z-index: 2;
    box-shadow: 0px 0px 8px #0000001A;
    border-radius: 8px;
    opacity: 1;
}

.FilterFlexBox {
    display: flex;
}

.Width190 {
    text-align: left;
    width: 190px;
}

.FilterLabelBox {
    padding-left: 15px;
}

.MarginTopFilterRows {
    margin-top: -20px;
}

.MarginBetweenFilterRows {
    margin-top: -10px;
}

.FilterLabels {
    text-align: left;
    font: normal normal normal 14px/21px Poppins;
    letter-spacing: 0px;
    color: #1D2634;
    opacity: 1;
}

.FlexButtonsRow {
    margin-top: -10px;
    margin-right: 25px;
    display: flex;
    justify-content: flex-end;
}

.FilterSubmitBtn {
    width: 100px!important;
    height: 32px!important;
    padding-top: 4px;
    font-size: 16px!important;
    font-family: 'Poppins'!important;
}


/* property common for all buttons */


/* .btn {
    border-color: #0074BC;
    box-shadow: 0px 0px 8px #0000001A;
    height: 37px;
    background-color: #0074BC;
    color: white;
    border-radius: 8px;
    font-size: 14px;
    font-family: "Poppins-SemiBold";
    width: max-content;
} */

.btn {
    border-color: #0074BC;
    box-shadow: 0px 0px 8px #0000001a;
    height: 100%;
    background-color: #0074BC;
    color: white;
    border-radius: 8px;
    font-size: 14px;
    font-family: "Poppins-SemiBold";
    width: -webkit-max-content;
    width: 97%;
}


/* .active, .btn:hover {
    outline: none !important;
    box-shadow: none!important;
    background-color: white!important;
    color: #0074bc !important;
} */


/* ::ng-deep.filterButton .btn :active{
    background-color: white!important;
    color: #0074bc !important;
} */

.FilterButtons {
    margin-right: 15px;
    font-size: 16px;
    color: #0074BC;
    background-color: white;
    width: 100px;
    height: 32px;
    border: 1px solid #0074BC;
    border-radius: 8px;
}

.FilterInputs {
    outline: none;
    margin: 0 0.7rem!important;
    width: 150px;
    height: 28px;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #D5D7E3;
    border-radius: 8px;
    opacity: 1;
}


/* property common for all buttons when hovered*/


/* 
 .btn:active {
border: none!important;
    background-color: #0D3B57;
}  */


/* to increase size of content in button */

.Count {
    font-size: 12px;
    font-family: "Poppins-Bold", Arial, Helvetica, sans-serif;
}

.Flex-Wrap {
    flex-wrap: wrap;
}


/* property of cards */

.Grid-Styling {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr)) !important;
    grid-gap: 0.5rem !important;
}


/* for alignmentof items */

.AlignCenter {
    align-items: center;
}


/* these widths are given to labels row's div */

.Width193 {
    width: 193px;
}

.Width125 {
    width: 125px!important;
}

.Width95 {
    width: 95px!important;
}

.Width100 {
    width: 100px!important;
}


/* for setting margin bottom */

.MarginForLabelRow {
    margin-bottom: -15px;
    margin-top: -60px
}

.MarginBetweenCheckBoxes {
    margin-top: 5px;
    margin-bottom: -3px;
}

.WidthForEmptyColInputRow {
    width: 16vw;
}

.Width84 {
    width: 84px!important;
}


/* @media screen and (min-width: 992px) {
    .btn {
        background-color: #03090c;
        color: white;
        border-radius: 10px;
        font-size: 16px;
        font-family: "Poppins-SemiBold", Arial, Helvetica, sans-serif;
    }
} */

.showMore {
    margin-left: 25%;
    font-size: 14px;
    opacity: 1;
    cursor: pointer;
    transition: .1s all ease;
}

.showMore+input {
    opacity: 0;
    transition: .1s all ease;
}

.showMore+input+* {
    z-index: -1!important;
    opacity: 0;
    transition: .1s all ease;
}

.showMore+input:checked+* {
    cursor: default;
    z-index: 2!important;
    opacity: 1;
    transition: .1s all ease;
}

.EachStatusTable {
    cursor: pointer;
    position: relative;
    z-index: 1!important;
}

object {
    position: relative;
    z-index: -1!important;
    height: 40px;
    width: 40px;
}

.MarginLeft {
    margin-left: -1%;
}

.MarginLeftDOS {
    margin-left: -2%;
}

.MarginLeftFullDashboard {
    margin-left: -11px;
}

.MarginTopCheckBox {
    margin-top: 10px;
}

::ng-deep .mat-date-range-input-separator {
    font-size: 9px;
    margin-top: 8px!important;
    color: black;
}

.mat-date-range-input-inner {
    font-family: 'Poppins';
    font-size: 9px;
    margin-top: 8px;
    color: black;
}

.mat-datepicker-toggle,
.mat-datepicker-content .mat-calendar-next-button,
.mat-datepicker-content .mat-calendar-previous-button {
    margin-top: -6px;
}

.DOCDateRangePicker {
    width: 150px;
    height: 28px;
}

.DOCDateRangePicker .e-input-group {
    margin-top: -10px;
}

.DOCInputBox {
    width: 150px;
    height: 28px;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #D5D7E3;
    border-radius: 8px;
    opacity: 1;
}

.MarginLeftDOCDiv {
    margin-left: 13px;
}

.PlusIcon{
    font-size: 16px;
}

/* 
::ng-deep .e-btn.e-flat.e-primary, .e-css.e-btn.e-flat.e-primary:hover{
    background-color: transparent!important;
} */


/* only for start date */


/* styling for daterangepicker */

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day {
    background-color: #0074BC!important;
    color: white!important;
}

::ng-deep .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected.e-focused-date span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected.e-focused-date span.e-day {
    background-color: #0074BC!important;
    color: white!important;
}

::ng-deep .e-calendar .e-content td.e-today.e-selected span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-today.e-selected span.e-day {
    background-color: #0074BC!important;
    border: 1px solid #0074BC!important;
    box-shadow: inset 0 0 0 2px #fff!important;
    color: #fff!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-today span.e-day {
    background-color: #0074BC!important;
    color: white!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-today:hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-focused-date.e-today span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-start-date.e-selected.e-today span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-end-date.e-selected.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-today:hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-focused-date.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-start-date.e-selected.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-end-date.e-selected.e-today span.e-day {
    border: 1px solid #0074BC!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-content.e-month .e-today.e-range-hover span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content.e-month .e-today.e-range-hover span {
    background-color: #eee!important;
    border: 1px solid #0074BC!important;
    color: #0074BC!important;
}

::ng-deep .e-calendar .e-content td.e-today span.e-day,
.e-calendar[_ngcontent-kbk-c96] .e-content[_ngcontent-kbk-c96] td.e-focused-date.e-today[_ngcontent-kbk-c96] span.e-day[_ngcontent-kbk-c96],
.e-bigger.e-small[_ngcontent-kbk-c96] .e-calendar[_ngcontent-kbk-c96] .e-content[_ngcontent-kbk-c96] td.e-today[_ngcontent-kbk-c96] span.e-day[_ngcontent-kbk-c96],
.e-bigger.e-small[_ngcontent-kbk-c96] .e-calendar[_ngcontent-kbk-c96] .e-content[_ngcontent-kbk-c96] td.e-focused-date.e-today[_ngcontent-kbk-c96] span.e-day[_ngcontent-kbk-c96] {
    background: none;
    border: 1px solid #0074BC!important;
    border-radius: 50%;
    color: #0074BC!important;
}

::ng-deep .e-calendar .e-content td.e-today span.e-day,
.e-calendar .e-content td.e-focused-date.e-today span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-today span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-focused-date.e-today span.e-day {
    background: none;
    border: 1px solid #0074BC!important;
    border-radius: 50%;
    color: #0074BC!important;
}

::ng-deep .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected.e-focused-date span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected.e-focused-date span.e-day {
    background-color: #0074BC!important;
    color: #fff!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day {
    background-color: #0074BC!important;
    color: #fff!important;
}

::ng-deep .e-daterangepicker.e-popup .e-presets .e-list-item.e-active,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-list-item.e-active {
    color: #0074BC!important;
}

::ng-deep .e-date-range-wrapper .e-input-group-icon.e-icons.e-active {
    color: #0074BC!important;
}

::ng-deep .e-input-group:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after {
    background-color: transparent!important;
}

::ng-deep .e-btn.e-flat.e-primary,
.e-css.e-btn.e-flat.e-primary {
    color: white!important;
    border-color: transparent;
    background-color: #0074BC!important;
}


/* ::ng-deep .e-btn.e-flat.e-primary:focus,
.e-css.e-btn.e-flat.e-primary:focus {
    background-color: rgba(0, 116, 188, 0.12)!important;
    border-color: transparent;
    color: #0074BC!important
} */

::ng-deep .e-btn.e-flat.e-primary:active,
.e-css.e-btn.e-flat.e-primary:active {
    background-color: rgba(0, 116, 188, 0.12)!important;
    border-color: transparent;
    color: #0074BC!important
}


/* only for start date */

::ng-deep .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected.e-focused-date span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected.e-focused-date span.e-day {
    background-color: #0074BC!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day {
    background-color: #0074BC!important;
}

::ng-deep .e-calendar .e-content td.e-selected span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected span.e-day {
    background-color: #0074BC!important;
}

:ng-deep .e-daterangepicker.e-popup .e-calendar-container .e-left-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container .e-left-container .e-left-calendar {
    overflow: hidden!important;
}

:ng-deep .e-calendar,
.e-bigger.e-small .e-calendar {
    overflow: hidden!important;
}

:ng-deep .e-daterangepicker.e-popup,
.e-bigger.e-small .e-daterangepicker.e-popup {
    box-shadow: 0px 0px 8px #0000001A!important;
    border-radius: 8px!important;
}

::ng-deep .e-daterangepicker.e-popup.e-preset-wrapper,
.e-bigger.e-small .e-daterangepicker.e-popup.e-preset-wrapper {
    box-shadow: 0px 0px 8px #0000001A!important;
    border-radius: 8px!important;
}

::ng-deep .e-daterangepicker.e-popup .e-footer,
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer {
    border-radius: 8px 0;
}

.FilterOpened {
    color: #0074BC;
    background-color: white;
    border-color: white;
}

@media only screen and (min-width: 768px) {
    /* For desktop: */
    .col-1 {
        width: 8.33%;
    }
    .col-2 {
        width: 16.66%;
    }
    .col-3 {
        width: 25%;
    }
    .col-4 {
        width: 33.33%;
    }
    .col-5 {
        width: 41.66%;
    }
    .col-6 {
        width: 50%;
    }
    .col-7 {
        width: 58.33%;
    }
    .col-8 {
        width: 66.66%;
    }
    .col-9 {
        width: 75%;
    }
    .col-10 {
        width: 83.33%;
    }
    .col-11 {
        width: 91.66%;
    }
    .col-12 {
        width: 100%;
    }
}

.originalIcon{
    height: 16px; 
    width: 16px;
    margin-right: 5px;
}

.customIcon{
    height: 16px;
    width: 16px; 
    margin-right: 5px;
    margin-top:3px
}

.tileAlign{
    min-height: 150px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}