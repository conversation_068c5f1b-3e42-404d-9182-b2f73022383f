.closeAllButton {
    cursor: pointer;
    color: cornflowerblue;
    font-weight: bold;
    text-decoration: underline;
    right: 0;
}

::ng-deep .mat-tab-list .mat-tab-labels .mat-tab-label-active {
    color: white;
    background-color: #0D3B57 !important;
}

::ng-deep .mat-tab-list .mat-tab-labels {
    height: 40px;
}

::ng-deep.mat-tab-label.mat-tab-label-active:not(.mat-tab-disabled),
::ng-deep.mat-tab-label.mat-tab-label-active.cdk-keyboard-focused:not(.mat-tab-disabled) {
    background-color: #005488 !important;
    opacity: 1;
    height: 40px;
    border-color: #005488;
}

::ng-deep .mat-tab-list .mat-tab-label {
    font-family: 'Poppins';
    color: #0D3B57;
    background-color: #ffffff !important;
    border: 1px solid gray;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    margin-left: 5px;
    font-size: 12px;
}

::ng-deep .mat-tab-list .mat-tab-labels .mat-tab-label-active .Close-Icon {
    background-color: white;
    color: #0D3B57;
}

.closeButton {
    margin-left: -12px;
    margin-right: -3px;
}

.Close-Icon {
    background-color: #D5D7E3;
    color: #005488;
    border-radius: 50%;
    font-size: 12px;
    height: 14px;
    width: 14px;
    margin-bottom: 3px;
    line-height: 15px!important;
}

:host:ng-deep .mat-tab-body .mat-tab-body-active {
    overflow-y: hidden!important;
    background-color: #001488 !important;
}

::ng-deep .mat-tab-list {
    background-color: transparent;
}

.Dashboard-Box {
    padding-top: 10px !important;
    padding-left: 0px !important;
    padding-right: 0px !important;
    --bs-gutter-x: 0rem !important;
}

.Black-Btn {
    color: black
}

.TabName {
    width: 50%;
    /* margin-right: 1px; */
}

#sel1,
#sel2 {
    width: 200px;
}

div,
span {
    align-items: center;
    /* justify-content: space-between; */
    padding: 10px;
}

label+select {
    margin-left: 15px;
}

label+button {
    margin-left: 15px;
}

#control_wrapper {
    max-width: 600px;
    margin: 30px auto;
    padding-top: 50px;
}

.container-fluid1 {
    display: flex;
}

.top-left {
    text-align: left;
}

.top-right {
    text-align: right;
}

.sel1,
.sel2 {
    display: flex;
    width: 400px;
}

select {
    width: 150px;
    height: 40px;
    border-radius: 8px;
    border-color: silver;
}

.column {
    float: left;
    width: 25%;
    padding: 0 10px;
}


/* Remove extra left and right margins, due to padding */

.row {
    margin: 0 -5px;
}


/* Clear floats after the columns */

.row:after {
    content: "";
    display: table;
    clear: both;
}


/* Responsive columns */

@media screen and (max-width: 600px) {
    .column {
        width: 100%;
        display: block;
        margin-bottom: 20px;
    }
}


/* Style the counter cards */

.card {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    padding: 16px;
    text-align: center;
    background-color: white;
    border-radius: 15px;
}

.Bolder-Font {
    font-weight: bold;
}

.StatusSize {
    font-size: 20px;
}

.CountSize {
    font-size: 30px;
}

.SubmittedCount {
    margin-left: 24px;
}

.PendingCount {
    margin-left: 46px;
}

tr:nth-child(3) {
    border-bottom: solid thin rgb(182, 181, 181);
}

tr:nth-child(4) {
    border-bottom: solid thin rgb(182, 181, 181);
}

tr:nth-child(5) {
    border-bottom: solid thin rgb(182, 181, 181);
}

.BorderRight {
    border-right: solid thin rgb(182, 181, 181);
}

ejs-daterangepicker {
    background-color: white;
    border-radius: 15px;
}

.control-section {
    margin-top: -45px;
    margin-left: -150px;
}

.display-none {
    font-size: 32px;
}

td:empty::after {
    content: "\00a0";
}

.e-range-header {
    display: none;
}

.e-presets {
    width: 300px;
}

.e-daterangepicker.e-popup.e-preset-wrapper,
.e-bigger.e-small .e-daterangepicker.e-popup.e-preset-wrapper {
    min-width: 800px;
    left: 500px;
}