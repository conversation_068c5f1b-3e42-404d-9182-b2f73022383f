{"ast": null, "code": "import { Form<PERSON><PERSON>er, FormControl, FormGroup } from '@angular/forms';\nimport { Member } from 'src/app/classmodels/Member/MemberProfile';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/shared/services/subject.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../../../../shared/directives/numbers-and-alphabets-with-space.directive\";\n\nfunction PatientInfoComponent_input_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 13);\n  }\n}\n\nfunction PatientInfoComponent_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r1.f.nm103PatientLastOrOrganizationName.value && ctx_r1.f.nm103PatientLastOrOrganizationName.value.length > 0 ? ctx_r1.f.nm103PatientLastOrOrganizationName.value : \"-\");\n  }\n}\n\nfunction PatientInfoComponent_input_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 15);\n  }\n}\n\nfunction PatientInfoComponent_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r3.f.nm104PatientFirst.value && ctx_r3.f.nm104PatientFirst.value.length > 0 ? ctx_r3.f.nm104PatientFirst.value : \"-\");\n  }\n}\n\nfunction PatientInfoComponent_input_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 16);\n  }\n}\n\nfunction PatientInfoComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r5.f.nm105PatientMiddle.value && ctx_r5.f.nm105PatientMiddle.value.length > 0 ? ctx_r5.f.nm105PatientMiddle.value : \"-\");\n  }\n}\n\nexport let PatientInfoComponent = /*#__PURE__*/(() => {\n  class PatientInfoComponent {\n    constructor(payerform, subjectService) {\n      this.payerform = payerform;\n      this.subjectService = subjectService;\n      this.subjectService.getInsureInfo().subscribe(res => {\n        if (res) {\n          if (!res.isCopyInsure) {\n            this.patientForm.patchValue({\n              nm105PatientMiddle: res.nm105SubscriberMiddle,\n              nm104PatientFirst: res.nm104SubscriberFirst,\n              nm103PatientLastOrOrganizationName: res.nm103SubscriberLastOrOrganizationName\n            });\n\n            if (!!this.patientForm.controls.nm105PatientMiddle.value) {\n              this.patientForm.controls.nm105PatientMiddle.setValue(this.patientForm.controls.nm105PatientMiddle.value.trim().replace(/[^a-z A-Z0-9]/g, ''));\n            }\n\n            if (!!this.patientForm.controls.nm104PatientFirst.value) {\n              this.patientForm.controls.nm104PatientFirst.setValue(this.patientForm.controls.nm104PatientFirst.value.trim().replace(/[^a-z A-Z0-9]/g, ''));\n            }\n\n            if (!!this.patientForm.controls.nm103PatientLastOrOrganizationName.value) {\n              this.patientForm.controls.nm103PatientLastOrOrganizationName.setValue(this.patientForm.controls.nm103PatientLastOrOrganizationName.value.trim().replace(/[^a-z A-Z0-9]/g, ''));\n            }\n          } else {\n            this.clearValue();\n          }\n\n          submitValidateAllFields.validateDisable(this.patientForm, [\"nm104PatientFirst\", \"nm103PatientLastOrOrganizationName\"]);\n          this.subjectService.resetInsureInfo();\n        }\n      });\n    }\n\n    ngOnInit() {\n      this.patchValue();\n    }\n\n    createForm() {\n      this.patientForm = this.payerform.group({\n        nm105PatientMiddle: new FormControl(''),\n        nm104PatientFirst: new FormControl(''),\n        nm103PatientLastOrOrganizationName: new FormControl('')\n      });\n      return this.patientForm;\n    }\n\n    patchValue() {\n      if (this.claimFormData.isAddClaim) {\n        this.patientForm.patchValue({\n          nm105PatientMiddle: !this.memberResult?.middleName ? '' : this.memberResult?.middleName.trim(),\n          nm104PatientFirst: !this.memberResult?.firstName ? '' : this.memberResult?.firstName.trim(),\n          nm103PatientLastOrOrganizationName: !this.memberResult?.lastName ? '' : this.memberResult?.lastName.trim()\n        });\n        this.patientForm.disable(); //submitValidateAllFields.validateDisableControl(this.patientForm, [\"\"]);\n      } else if (this.claimFormData.isEditClaim || this.claimFormData.isViewClaim) {\n        this.patientForm.patchValue({\n          nm105PatientMiddle: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.nm105PatientMiddle,\n          nm104PatientFirst: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.nm104PatientFirst,\n          nm103PatientLastOrOrganizationName: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.nm103PatientLastOrOrganizationName\n        });\n        this.patientForm.disable();\n      }\n\n      if (!!this.patientForm.controls.nm105PatientMiddle.value) {\n        this.patientForm.controls.nm105PatientMiddle.setValue(this.patientForm.controls.nm105PatientMiddle.value.trim().replace(/[^a-z A-Z0-9]/g, ''));\n      }\n\n      if (!!this.patientForm.controls.nm104PatientFirst.value) {\n        this.patientForm.controls.nm104PatientFirst.setValue(this.patientForm.controls.nm104PatientFirst.value.trim().replace(/[^a-z A-Z0-9]/g, ''));\n      }\n\n      if (!!this.patientForm.controls.nm103PatientLastOrOrganizationName.value) {\n        this.patientForm.controls.nm103PatientLastOrOrganizationName.setValue(this.patientForm.controls.nm103PatientLastOrOrganizationName.value.trim().replace(/[^a-z A-Z0-9]/g, ''));\n      }\n    }\n\n    clearValue() {\n      this.patientForm.patchValue({\n        nm105PatientMiddle: '',\n        nm104PatientFirst: '',\n        nm103PatientLastOrOrganizationName: ''\n      });\n      this.patientForm.enable();\n    }\n\n    get f() {\n      return this.patientForm.controls;\n    }\n\n  }\n\n  PatientInfoComponent.ɵfac = function PatientInfoComponent_Factory(t) {\n    return new (t || PatientInfoComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SubjectService));\n  };\n\n  PatientInfoComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PatientInfoComponent,\n    selectors: [[\"app-patient-info\"]],\n    inputs: {\n      memberResult: \"memberResult\",\n      claimFormData: \"claimFormData\",\n      isShowInsureDetails: \"isShowInsureDetails\"\n    },\n    decls: 26,\n    vars: 7,\n    consts: [[3, \"formGroup\"], [1, \"2\", \"bd\"], [1, \"row\", \"mt-2\"], [1, \"col\"], [1, \"form-title\"], [\"for\", \"PatientLastName\", 1, \"create-claims-labels\"], [\"for\", \"PatientFirstName\", 1, \"create-claims-labels\"], [\"for\", \"PatientMiddleInitial\", 1, \"create-claims-labels\"], [1, \"row\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm103PatientLastOrOrganizationName\", \"id\", \"PatientLastName\", \"placeholder\", \"Last Name\", 4, \"ngIf\"], [\"class\", \"form-control\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm104PatientFirst\", \"id\", \"PatientFirstName\", \"placeholder\", \"First Name\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm105PatientMiddle\", \"id\", \"PatientMiddleInitial\", \"placeholder\", \"Middle Name\", 4, \"ngIf\"], [\"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm103PatientLastOrOrganizationName\", \"id\", \"PatientLastName\", \"placeholder\", \"Last Name\", 1, \"form-control\", \"form-control-sm\"], [1, \"form-control\"], [\"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm104PatientFirst\", \"id\", \"PatientFirstName\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm105PatientMiddle\", \"id\", \"PatientMiddleInitial\", \"placeholder\", \"Middle Name\", 1, \"form-control\", \"form-control-sm\"]],\n    template: function PatientInfoComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0)(1, \"section\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"p\", 4);\n        i0.ɵɵtext(5, \"2. Patient Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"label\", 5);\n        i0.ɵɵtext(9, \"Last Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 3)(11, \"label\", 6);\n        i0.ɵɵtext(12, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 3)(14, \"label\", 7);\n        i0.ɵɵtext(15, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(16, \"div\", 8)(17, \"div\", 3);\n        i0.ɵɵtemplate(18, PatientInfoComponent_input_18_Template, 1, 0, \"input\", 9);\n        i0.ɵɵtemplate(19, PatientInfoComponent_span_19_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"div\", 3);\n        i0.ɵɵtemplate(21, PatientInfoComponent_input_21_Template, 1, 0, \"input\", 11);\n        i0.ɵɵtemplate(22, PatientInfoComponent_span_22_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"div\", 3);\n        i0.ɵɵtemplate(24, PatientInfoComponent_input_24_Template, 1, 0, \"input\", 12);\n        i0.ɵɵtemplate(25, PatientInfoComponent_span_25_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.patientForm);\n        i0.ɵɵadvance(18);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n      }\n    },\n    dependencies: [i3.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i4.NumbersAndAlphabetsWithSpaceDirective],\n    styles: [\".create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.search-icon-alignment[_ngcontent-%COMP%]{position:relative}.search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%], .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}\"]\n  });\n  return PatientInfoComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}