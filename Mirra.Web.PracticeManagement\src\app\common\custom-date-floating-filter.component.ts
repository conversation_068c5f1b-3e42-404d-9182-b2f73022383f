import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { IFloatingFilter, IFloatingFilterParams, AgFrameworkComponent } from 'ag-grid-angular';

@Component({
  selector: 'app-custom-date-floating-filter',
  template: `
    <input #input type="text" (input)="onInput($event)" [value]="currentValue" placeholder="yyyy-mm-dd"/>
    <div *ngIf="errorMsg" style="color: red; font-size: 11px;">{{ errorMsg }}</div>
  `
})
export class CustomDateFloatingFilterComponent implements IFloatingFilter, OnInit, AgFrameworkComponent<IFloatingFilterParams> {
  @ViewChild('input', { static: true }) input: ElementRef<HTMLInputElement>;
  params: IFloatingFilterParams;
  currentValue: string = '';
  errorMsg: string = '';

  agInit(params: IFloatingFilterParams): void {
    this.params = params;
  }

  ngOnInit() { }

  onInput(event: any) {
    const value = event.target.value;
    this.currentValue = value;
    if (value && !/^\d{4}-\d{2}-\d{2}$/.test(value)) {
      this.errorMsg = 'Incorrect format. Use yyyy-mm-dd';
      this.params.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged('equals', null); // Remove filter
      });
      return;
    }

    // Try parsing the date
    if (value) {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        this.errorMsg = 'Invalid date.';
        this.params.parentFilterInstance(instance => {
          instance.onFloatingFilterChanged('equals', null); // Remove filter
        });
        return;
      }
    }
    this.errorMsg = '';
    this.params.parentFilterInstance(instance => {
      instance.onFloatingFilterChanged('equals', value || null);
    });
  }

  onParentModelChanged(parentModel: any): void {
    // Not used in this simple version
  }
}
