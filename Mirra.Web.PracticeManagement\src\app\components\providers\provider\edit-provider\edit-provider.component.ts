import { Component, OnInit } from '@angular/core';
import { SubjectService } from 'src/app/shared/services/subject.service';
import { MasterdataService } from 'src/app/shared/services/masterdata.service';
import { ConfigurationMaster } from 'src/app/classmodels/ResponseModel/MasterData/ConfigurationMaster';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { ProviderManagementService } from 'src/app/services/ProviderManagement/provider-management.service';
import { NotificationService } from 'src/app/services/Notification/notification.service';
import { MatDialog } from '@angular/material/dialog';
import { zipCodeValidator, faxNumberValidator, noWhitespaceValidator } from 'src/app/shared/functions/customFormValidators';
import { DatePipe } from '@angular/common';
import { PREVILEGES } from 'src/app/common/common-static';
import { GlobalService } from 'src/app/shared/services/global.service';
import { AddFacilityPopComponent } from '../../facility/add-facility-pop/add-facility-pop.component';
import { MasterSharedService } from 'src/app/shared/services/mastershares.service';
import { CacheService } from 'src/app/services/cache-service/cache.service';
import { LocalStorageKey } from 'src/app/shared/constant/constatnt';
@Component({
  selector: 'app-edit-provider',
  templateUrl: './edit-provider.component.html',
  styleUrls: ['./edit-provider.component.scss']
})
export class EditProviderComponent implements OnInit {
  genderData: ConfigurationMaster[] = [];
  isSubmitted: boolean = false;
  insuranceData: any[] = [];
  addForm;
  providerSpecialtyData;
  planData;
  ipaData;
  groupsData;
  specialtyData;
  today = new Date();
  citiesData;
  statesData;
  facilityData;
  selectedFacilityInfo: any = null;
  addTab = false;
  providerDataForView;
  isEditMode = true;
  addFacilityView = false;
  selectedFacilityInfoCopy;
  tabName;
  isAddNewFacilityBtnShow: boolean = false;
  isCreateProviderBtnShow: boolean = false;
  isViewEditProviderBtnShow: boolean = false;
  selectedFacilityId: string = "";
  practiceLocationData: any = null;
  constructor(private subService: SubjectService,
    private masterDataService: MasterdataService,
    private builder: FormBuilder,
    private cacheService: CacheService,
    private providerMgmtService: ProviderManagementService,
    private notificationService: NotificationService,
    private masterSharedService: MasterSharedService,
    private dialog: MatDialog,
    public datepipe: DatePipe,
    private readonly globalApi: GlobalService) { }
  ngOnInit(): void {
    this.createDemographicForm();
    this.getPrivilegesByRole();
    this.getAllMasterData();
    this.specialtyData = this.cacheService.localStorageGetItem(LocalStorageKey.allSpecialityTaxonomyCodes);
    this.subService.getRefreshFacilityList().subscribe((res) => {
      if (!!res) {
        this.getFacilityData();
        this.subService.resetRefreshFacilityList();
      }
    });
    this.subService.getSelectedProviderInfoForView().subscribe((res) => {
      this.tabName = this.providerMgmtService.getTabNameForProvider(res.data);
      if (res.mode === 'view') {
        this.isEditMode = false;
      }
      this.selectedFacilityId = res.data.facilityId;
      this.providerMgmtService.fetchProviderData(res.data.uniqueProviderId).subscribe((data) => {
        data = !!data.content ? data.content : null;
        this.providerDataForView = data;        
        data.practiceInformationList.forEach((practiceInfo) => {
          if (practiceInfo.facilityId == this.selectedFacilityId && this.practiceLocationData == null) {
            this.practiceLocationData = practiceInfo;
          }
        });
        this.fillProvider(data);

      });
    });

    if (!this.isEditMode) {
      this.addForm.disable();
    }

  }

  fillProvider(data: any) {
    this.addForm.patchValue({
      npiNumber: data?.demographicInformation?.npiNumber,
      firstName: data?.demographicInformation?.firstName,
      lastName: data?.demographicInformation?.lastName,
      middleName: data?.demographicInformation?.middleName,
      gender: data?.demographicInformation?.gender,
      isMidLevelPhysician: data?.demographicInformation?.isMidLevelPhysician,
      planName: this.practiceLocationData?.planName,
      planCode_CMS: this.practiceLocationData?.planCode_CMS,
      ipA_AMCode: this.practiceLocationData?.ipA_AMCode,
      ipaName: this.practiceLocationData?.ipaName,
      billingProviderType: this.practiceLocationData?.billingProviderType,
      contractType: this.practiceLocationData?.contractType,
      contractId: this.practiceLocationData?.contractId,
      effectiveDate: this.practiceLocationData?.effectiveDate,
      terminationDate: this.practiceLocationData?.terminationDate,
      specialtyName: this.practiceLocationData?.specialtyName,
      groupName: this.practiceLocationData?.groupName,
      groupTaxId: this.practiceLocationData?.groupTaxId,
      groupSpecialty: this.practiceLocationData?.groupSpecialty,
      groupTaxonomyCode: this.practiceLocationData?.groupTaxonomyCode,
      groupTaxonomyDescription: this.practiceLocationData?.groupTaxonomyDescription,
      groupSpecialtyMDMCode: this.practiceLocationData?.groupSpecialtyMDMCode,
      groupCode_CMS: this.practiceLocationData?.groupCode_CMS,
      groupNPI: this.practiceLocationData?.groupNPI,
      groupId: this.practiceLocationData?.groupId,
      billingProviderName: this.practiceLocationData?.billingProviderName,
      billingTaxId: this.practiceLocationData?.billingTaxId,
      printOnClaim: this.practiceLocationData?.printOnClaim,
      billingProviderAddressLine1: this.practiceLocationData?.billingProviderAddressLine1,
      billingProviderAddressLine2: this.practiceLocationData?.billingProviderAddressLine2,
      billingProviderCity: this.practiceLocationData?.billingProviderCity,
      billingProviderState: this.practiceLocationData?.billingProviderState,
      billingProviderZipCode: this.practiceLocationData?.billingProviderZipCode,
      billingProviderContactPersonName: this.practiceLocationData?.billingProviderContactPersonName,
      billingProviderContactPhone: this.practiceLocationData?.billingProviderContactPhone,
      billingProviderContactFax: this.practiceLocationData?.billingProviderContactFax,
      billingProviderContactEmail: this.practiceLocationData?.billingProviderContactEmail,
      useSameAsFacility: this.practiceLocationData?.useSameAsFacility,
      facilityDescription: this.practiceLocationData?.facilityDescription,
      facilityAddress1: this.practiceLocationData?.facilityAddressLine1,
      facilityAddress2: this.practiceLocationData?.facilityAddressLine2,
      facilityCity: this.practiceLocationData?.facilityAddressCity,
      facilityState: this.practiceLocationData?.facilityAddressState,
      facilityCounty: this.practiceLocationData?.facilityAddressCounty,
      facilityCountry: this.practiceLocationData?.facilityAddressCountry,
      facilityZipCode: this.practiceLocationData?.facilityAddressZipCode,
      billingProviderCode: this.practiceLocationData?.billingProviderCode,
      billingProviderNpi: '',
      facilityId: this.practiceLocationData?.facilityId,
      facilityName: this.practiceLocationData?.facilityName,
      specialtyCode_CMS: this.practiceLocationData?.specialtyCode_CMS,
      taxonomyCode: this.practiceLocationData?.taxonomyCode,
      uniqueContractId: this.practiceLocationData?.uniqueContractId,
      providerProfileID: data?.providerProfileID,
      uniqueProviderID: data?.uniqueProviderID,
      providerTitle: this.practiceLocationData?.providerTitle,
      recordId: this.practiceLocationData?.recordId,
      providerBridgeRequestId: this.practiceLocationData?.providerBridgeRequestId,
      providerBridgeContractInformationId: this.practiceLocationData?.providerBridgeContractInformationId,
      isBillingSelfUpdated: this.practiceLocationData?.isBillingSelfUpdated,
      isFacilityPrimary: this.practiceLocationData?.isFacilityPrimary,
    });
  }

  cancel() {
    if (!this.isEditMode) {
      this.subService.setCloseTabRefresh('View Provider - ' + this.tabName);
    }
    this.subService.setCloseTabRefresh('Edit Provider - ' + this.tabName);
  }


  createDemographicForm() {
    this.addForm = this.builder.group({
      npiNumber: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(10), Validators.minLength(10)]),
      firstName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      lastName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      middleName: new FormControl(null),
      gender: new FormControl(null),
      isMidLevelPhysician: new FormControl(false),
      planName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      ipaName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      billingProviderType: new FormControl('Group', Validators.required),
      contractType: new FormControl("CAP"),
      contractId: new FormControl(null),
      effectiveDate: new FormControl(null),
      terminationDate: new FormControl(null),
      specialtyName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      groupName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      groupTaxId: new FormControl(null),
      billingProviderName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      billingTaxId: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(9), Validators.minLength(9)]),
      printOnClaim: new FormControl(null),
      billingProviderAddressLine1: new FormControl('', [Validators.required, noWhitespaceValidator]),
      billingProviderAddressLine2: new FormControl(null),
      billingProviderCity: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      billingProviderState: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      billingProviderZipCode: new FormControl(null, [Validators.required, noWhitespaceValidator, zipCodeValidator]),
      billingProviderContactPersonName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      billingProviderContactPhone: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(10), Validators.minLength(10)]),
      billingProviderContactFax: new FormControl(null, faxNumberValidator),
      billingProviderContactEmail: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.email]),
      useSameAsFacility: new FormControl(null),
      facilityDescription: new FormControl(null, [Validators.required]),
      facilityAddress1: new FormControl(null),
      facilityAddress2: new FormControl(null),
      facilityCity: new FormControl(null),
      facilityState: new FormControl(null),
      facilityCounty: new FormControl(null),
      facilityCountry: new FormControl(null),
      facilityZipCode: new FormControl(null),
      billingProviderCode: new FormControl(null),
      groupSpecialty: new FormControl(null),
      groupTaxonomyCode: new FormControl(null),
      groupTaxonomyDescription: new FormControl(null),
      groupSpecialtyMDMCode: new FormControl(null),
      groupCode_CMS: new FormControl(null),
      groupNPI: new FormControl(null),
      groupId: new FormControl(null),
      billingProviderNpi: new FormControl(null),
      facilityId: new FormControl(null),
      facilityName: new FormControl(null),
      ipA_AMCode: new FormControl(null),
      planCode_CMS: new FormControl(null),
      specialtyCode_CMS: new FormControl(null),
      taxonomyCode: new FormControl(null),
      uniqueContractId: new FormControl(null),
      specialtyDetailId: new FormControl(null),
      providerTitle: new FormControl(null),
      recordId: new FormControl(null),
      providerBridgeRequestId: new FormControl(null),
      providerBridgeContractInformationId: new FormControl(null),
      isBillingSelfUpdated: new FormControl(null),
      isFacilityPrimary: new FormControl(null),
      providerProfileID: new FormControl(null),
      uniqueProviderID: new FormControl(null)
    })

  }
  createFacilityForm() {
    let facilityForm = this.builder.group({
      facilityDescription: new FormControl(null, [Validators.required, noWhitespaceValidator]),
    })
    return facilityForm;
  }


  changePlanNameSelect(selectedPlan) {
    if (!!selectedPlan) {
      this.addForm.controls['planCode_CMS'].setValue(selectedPlan.mdmCode);
    } else {
      this.addForm.controls['planCode_CMS'].setValue(null);
    }
  }
  changeIPASelect(selectedIPA) {
    if (!!selectedIPA) {
      this.addForm.controls['ipA_AMCode'].setValue(selectedIPA.mdmCode);
    } else {
      this.addForm.controls['ipA_AMCode'].setValue(null);
    }
  }
  get f() {
    return this.addForm.controls;
  }

  getAllMasterData() {
    this.masterDataService.fetchchMasterData('Gender').subscribe((res) => {
      this.genderData = res;
    })

    this.providerMgmtService.fetchGroupsData('').subscribe((res) => {
      this.groupsData = !!res && res.statusCode == 200 && !!res.content ? res.content : [];
    })

    this.providerMgmtService.fetchIPAData('').subscribe((res) => {
      this.ipaData = !!res.content ? res.content : [];
    })

    this.getFacilityData();
    this.getFacilityMasterData();
  }

  getFacilityMasterData() {
    this.masterSharedService.getFacilityMasterData().subscribe({
      next: (res) => {

        
        this.statesData = res.states;
        this.insuranceData = res.insurance;

      },
      error: (err) => {
        console.error('Error loading facility master data:', err);
      }
    });
  }
  getFacilityData() {
    this.providerMgmtService.fetchFacilityData('').subscribe((res) => {
      this.facilityData = !!res.content ? res.content : [];
    })
  }
  changeSpecialtySelect(selectedSpecialty) {
    if (selectedSpecialty) {
      this.addForm.patchValue({
        taxonomyCode: selectedSpecialty.taxonomyCode ?? null,
        specialtyCode_CMS: selectedSpecialty.mdmCode ?? null
      });
    } else {
      this.addForm.patchValue({
        taxonomyCode: null,
        specialtyCode_CMS: null
      });
    }

  }
  changeGroupSelect(selectedGroup) {
    if (!!selectedGroup) {
      this.addForm.controls['groupTaxId'].setValue(!!selectedGroup.taxID ? selectedGroup.taxID : null);
      this.addForm.controls['groupCode_CMS'].setValue(!!selectedGroup.groupMDMCode ? selectedGroup.groupMDMCode : null);
      this.addForm.controls['groupNPI'].setValue(!!selectedGroup.npiNumber ? selectedGroup.npiNumber : null);
      this.addForm.controls['groupId'].setValue(!!selectedGroup.groupID ? selectedGroup.groupID : null);
    } else {
      this.addForm.controls['groupTaxId'].setValue(null);
      this.addForm.controls['groupCode_CMS'].setValue(null);
      this.addForm.controls['groupNPI'].setValue(null);
      this.addForm.controls['groupId'].setValue(null);
    }
    //  this.addForm.controls['groupTaxId'].setValue(this.addProvider.practiceInformationList.groupTaxId);
  }
  changeBillingProviderSelect(selectedBillingProvider: any): void {
    const provider = selectedBillingProvider || {};

    const safeValue = (val: any, formatFn?: (v: any) => any) =>
      val != null ? (formatFn ? formatFn(val) : val) : null;

    this.addForm.patchValue({
      billingProviderCode: safeValue(provider.groupMDMCode),
      groupSpecialty: safeValue(provider.specialtyName),
      groupTaxonomyCode: safeValue(provider.taxonomyDesc),
      groupTaxonomyDescription: safeValue(provider.groupMDMCode),
      groupSpecialtyMDMCode: safeValue(provider.specTaxonomyMDMCode),
      billingProviderName: safeValue(provider.groupname),
      billingTaxId: safeValue(provider.taxID),
      printOnClaim: safeValue(provider.groupname),
      billingProviderAddressLine1: safeValue(provider.street),
      billingProviderAddressLine2: safeValue(provider.building),
      billingProviderCity: safeValue(provider.city),
      billingProviderState: safeValue(provider.state),
      billingProviderZipCode: safeValue(provider.zipCode),
      billingProviderContactPersonName: safeValue(provider.contactPersonName),
      billingProviderContactPhone: safeValue(provider.contactPersonPhone, this.replacePhoneAndFax.bind(this)),
      billingProviderContactFax: safeValue(provider.contactPersonFax, this.replacePhoneAndFax.bind(this)),
      billingProviderContactEmail: safeValue(provider.contactPersonEmail)
    });
  }

  resetBillingProviderGroupVariables() {
    this.addForm.controls['billingProviderCode'].setValue(null);
    this.addForm.controls['groupSpecialtyMDMCode'].setValue(null);
    this.addForm.controls['groupSpecialty'].setValue(null);
    this.addForm.controls['groupTaxonomyCode'].setValue(null);
    this.addForm.controls['groupTaxonomyDescription'].setValue(null);
  }
  changeBillingProviderCitySelect(selectedCity) {
    if (!!selectedCity) {
      this.addForm.controls['billingProviderState'].setValue(selectedCity.stateCode);
    }
  }
  changeBillingProviderType(e) {
    this.resetBillingProviderGroupVariables();
    if (e.value == 'Group') {
      this.addForm.patchValue({
        printOnClaim: '',
        billingTaxId: '',
        billingProviderName: null,
        billingProviderContactPersonName: null,
        billingProviderContactPhone: null,
        billingProviderContactFax: null,
        billingProviderContactEmail: null,
      })
    } else {
      let name = this.addForm.controls['firstName'].value + ' ' + this.addForm.controls['lastName'].value;
      this.addForm.patchValue({
        printOnClaim: name,
        billingTaxId: this.addForm.controls['groupTaxId'].value,
        billingProviderName: name,
        billingProviderContactPersonName: null,
        billingProviderContactPhone: null,
        billingProviderContactFax: null,
        billingProviderContactEmail: null,
      })
    }
  }
  replacePhoneAndFax(number) {
    if (!!number) {
      number = number.replace('(', '');
      number = number.replace(')', '');
      number = number.replace('-', '');
      number = number.replace(' ', '');
    }
    return number;
  }

  getName(lastName, firstName) {
    let result = [];
    if (lastName) {
      result.push(lastName)
    }
    if (firstName) {
      result.push(firstName)
    }
    return result.join(' ');
  }
  changeFacilitySelect(facilitySelected: any): void {
    const address = facilitySelected?.address;
    this.selectedFacilityInfo = JSON.parse(JSON.stringify(facilitySelected));
    this.addForm.patchValue({
      facilityAddress1: address?.addressLine1 || null,
      facilityAddress2: address?.addressLine2 || null,
      facilityCity: address?.city || null,
      facilityState: address?.state || null,
      facilityCounty: address?.county || null,
      facilityCountry: address?.countryCode || null,
      facilityZipCode: address?.postalCode || null,
      facilityId: facilitySelected?.facilityID || null,
      facilityName: facilitySelected?.facilityName || null,
    });
  }

  useSameAsFacility(event: any): void {
    const isChecked = event.target.checked;
    this.addForm.controls['useSameAsFacility'].setValue(isChecked ? 'ON' : 'OFF');
    const facilityControls = this.addForm.controls;
    const patchData = isChecked
      ? {
        billingProviderAddressLine1: facilityControls['facilityAddress1'].value || null,
        billingProviderAddressLine2: facilityControls['facilityAddress2'].value || null,
        billingProviderCity: facilityControls['facilityCity'].value || null,
        billingProviderState: facilityControls['facilityState'].value || null,
        billingProviderZipCode: facilityControls['facilityZipCode'].value || null,
        billingProviderContactPersonName: this.providerDataForView.practiceInformationList[0]?.facilityContactName,
        billingProviderContactPhone: this.providerDataForView.practiceInformationList[0]?.facilityContactPhone,
        billingProviderContactFax: this.providerDataForView.practiceInformationList[0]?.facilityContactFax,
        billingProviderContactEmail: this.providerDataForView.practiceInformationList[0]?.facilityContactEmail,
      }
      : {
        billingProviderAddressLine1: null,
        billingProviderAddressLine2: null,
        billingProviderCity: null,
        billingProviderState: null,
        billingProviderZipCode: null,
        billingProviderContactPersonName: null,
        billingProviderContactPhone: null,
        billingProviderContactFax: null,
        billingProviderContactEmail: null,
      };
    this.addForm.patchValue(patchData);
  }

  checkIfNPIExists() {
    this.addForm.controls['npiNumber'].markAsTouched();
    this.addForm.controls['npiNumber'].updateValueAndValidity();
    if (this.addForm.controls['npiNumber'].invalid) {
      return;
    }
    this.providerMgmtService.CheckNpi(this.addForm.controls['npiNumber'].value).subscribe((res) => {
      if (!!res && !!res.content && res.content) {
        this.addForm.controls['npiNumber'].setErrors({ invalidFormat: true });
        this.isCheckNpi = true;
      } else {
        this.updateProvider();
      }
    })
  }
  isCheckNpi: boolean = false;
  saveProvider() {
    this.isCheckNpi = true;
    if (this.addForm.controls['npiNumber'].dirty) {
      this.isCheckNpi = true;
      this.checkIfNPIExists();
      return;
    }
    this.updateProvider();
  }


  updateProvider() {
    this.isSubmitted = true;

    if (this.addForm.invalid) {
      this.addForm.markAllAsTouched();
      return;
    }


    this.providerMgmtService.UpdatePracticeLocation(this.getProperRequestBody()).subscribe((res) => {
      if (res.statusCode == 200) {
        this.notificationService.showSuccess('', 'Provider Updated Successfully.', 4000);
        this.subService.setRefreshProviderInfo(true);
        this.addForm.reset();
        this.cancel();
      } else {
        this.notificationService.showError('', 'Provider could not be added. Please try again later.', 4000);
      }
    }, (error) => {
      this.notificationService.showError('', 'Provider could not be added. Please try again later.', 4000);
    })
  }
  getProperRequestBody() {
    let insuranceInfo = this.insuranceData.find(t => t.mdmCode === this.addForm.controls["planCode_CMS"].value);
     
    let groupInfo = this.groupsData.find(t => t.groupID === this.addForm.controls["groupId"].value);
    let ipaInfo = this.ipaData.find(t => t.mdmCode === this.addForm.controls["ipA_AMCode"].value);
    let specialityInfo = this.specialtyData.find(t => t.taxonomyCode === this.addForm.controls["taxonomyCode"].value);

    return {
      uniqueProviderId: this.addForm.controls["uniqueProviderID"].value,
      uniqueContractId: this.addForm.controls["uniqueContractId"].value,
      contractId: String(this.addForm.controls["contractId"].value),
      providerProfileId: this.addForm.controls["providerProfileID"].value,
      providerIdOrCenterId: null,
      module: null,
      lobCode: null,
      status: "Active",
      uUID: String(JSON.parse(localStorage.getItem("uuid"))),
      planBenefitPackage: null,
      transactionResult: true,
      requestedByEmail: String(localStorage.getItem("email")),
      requestedByName: String(localStorage.getItem("userName")),
      requestedByUUID: String(JSON.parse(localStorage.getItem("uuid"))),
      requestedByAccountId: null,
      queueStatus: null,
      approvedByEmail: null,
      approvedByName: null,
      approvedByUUID: null,
      demographicInformation: {
        id: 0,
        firstName: this.addForm.controls["firstName"].value,
        middleName: this.addForm.controls["middleName"].value,
        lastName: this.addForm.controls["lastName"].value,
        gender: this.addForm.controls["gender"].value,
        providerType: null,
        nPI: this.addForm.controls["npiNumber"].value,
        recordID: null,
        updateTrackerId: null,
        providerTypeCode_CMS: null,
        module: null,
        lastUpdatedByEmail: null,
        transactionResult: true
      },
      practiceInformationList: {
        id: String(this.addForm.controls["facilityId"].value),
        name: null,
        nPI: this.addForm.controls["npiNumber"].value,
        providerNetwork: null,
        effectiveDate: this.addForm.controls["effectiveDate"].value,
        terminationDate: this.addForm.controls["terminationDate"].value,
        addressLine1: this.addForm.controls["facilityAddress1"].value,
        addressLine2: this.addForm.controls["facilityAddress2"].value,
        city: this.addForm.controls["facilityCity"].value,
        state: this.addForm.controls["facilityState"].value,
        county: this.addForm.controls["facilityCounty"].value,
        country: this.addForm.controls["facilityCountry"].value,
        zipCode: this.addForm.controls["facilityZipCode"].value,
        contactName: groupInfo?.contactPersonName,
        contactPhone: groupInfo?.contactPersonPhone,
        contactFax: groupInfo?.contactPersonFax,
        contactEmail: groupInfo?.contactPersonEmail,
        groupTaxId: groupInfo?.taxID,
        taxId: groupInfo?.taxID,
        groupName: groupInfo?.groupname,
        groupNPI: this.addForm.controls["groupNPI"].value,
        groupCMSCode: groupInfo?.groupMDMCode,
        pCP: null,
        useSameAsFacility: this.addForm.controls["useSameAsFacility"].value,
        isFacilityPrimary: String(this.addForm.controls["isFacilityPrimary"].value),
        referralCentral: null,
        referralCentralContactName: null,
        referralCentralFaxNumber: null,
        referralCentralPhoneNumber: null,
        billingInformationList: {
          id: '0',
          type: this.addForm.controls["billingProviderType"].value,
          code: null,
          name: this.addForm.controls["billingProviderName"].value,
          nPI: this.addForm.controls["npiNumber"].value,
          taxId: this.addForm.controls["billingTaxId"].value,
          printOnClaim: this.addForm.controls["printOnClaim"].value,
          addressLine1: this.addForm.controls["billingProviderAddressLine1"].value,
          addressLine2: this.addForm.controls["billingProviderAddressLine2"].value,
          city: this.addForm.controls["billingProviderCity"].value,
          state: this.addForm.controls["billingProviderState"].value,
          county: null,
          country: null,
          zipCode: this.addForm.controls["billingProviderZipCode"].value,
          contactPersonName: this.addForm.controls["billingProviderContactPersonName"].value,
          contactPhone: this.addForm.controls["billingProviderContactPhone"].value,
          contactFax: this.addForm.controls["billingProviderContactFax"].value,
          contactEmail: this.addForm.controls["billingProviderContactEmail"].value,
        },
        specialtyName: this.addForm.controls["specialtyName"].value,
        specialtyMDMCode: null,
        taxonomyCode: specialityInfo?.taxonomyCode,
        planName: insuranceInfo.payerName,
        planCMSCode: insuranceInfo.mdmCode,
        iPA: this.addForm.controls["ipaName"].value,
        iPAMDMCode: ipaInfo?.mdmCode,
        transactionResult: true,
        updateTrackerId: null,
        contractType: this.addForm.controls["contractType"].value,
        centerNumber: null,
        isPreferred: null
      }
    }
  }


  getLargerDate(date) {
    const date1 = new Date(date).getTime();
    const date2 = this.today.getTime();
    if (date1 < date2) {
      return this.today;
    } else if (date2 < date1) {
      return date;
    } else {
      return this.today;
    }
  }
  addNewFacility() {
    const dialogRef = this.dialog.open(AddFacilityPopComponent, { position: { right: '0%' }, height: '100%', width: '50%' });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.getFacilityData();
      }
    });
  }
  addNewFacilityInEdit() {
    this.selectedFacilityInfoCopy = JSON.parse(JSON.stringify(this.selectedFacilityInfo));
    this.selectedFacilityInfo = null;
    this.addForm.controls.practiceInformationList.reset();
    this.addFacilityView = true;
    this.addForm.controls.practiceInformationList.controls.billingProviderType.setValue('Group');
    this.addForm.markAsUntouched();
    this.addForm.controls.demographicInformation.markAsUntouched();
    this.addForm.controls.practiceInformationList.markAsUntouched();
    this.addForm.controls.practiceInformationList.controls.facilityDetails.markAsUntouched();
  }


  getPrivilegesByRole() {
    const privielagesDetails = this.globalApi.getPrivilegesByRole();
    this.isAddNewFacilityBtnShow = privielagesDetails.privilegeCodes.filter((x: any) => x == PREVILEGES.CLAIMS_BillingManagement_AddFacility).length > 0 ? true : false;
    this.isCreateProviderBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_AddProvider).length > 0 ? true : false;
    this.isViewEditProviderBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.ProviderVilla_ProviderBridge_EditProvider).length > 0 ? true : false;

  }
  setSelectedPracticeInfo(practiceInfoData) {
    if (!this.isEditMode) {
      return;
    }
    this.selectedFacilityInfo = practiceInfoData;
    this.addForm.patchValue({
      planName: practiceInfoData.planName,
      ipaName: practiceInfoData.ipaName,
      billingProviderType: practiceInfoData.billingProviderType,
      contractType: practiceInfoData.contractType,
      contractId: practiceInfoData.contractId,
      effectiveDate: !!practiceInfoData.effectiveDate ? this.datepipe.transform(practiceInfoData.effectiveDate, 'yyyy-MM-dd') : practiceInfoData.effectiveDate,
      terminationDate: !!practiceInfoData.terminationDate ? this.datepipe.transform(practiceInfoData.terminationDate, 'yyyy-MM-dd') : practiceInfoData.terminationDate,
      specialtyName: practiceInfoData.specialtyName,
      groupName: practiceInfoData.groupName,
      groupTaxId: practiceInfoData.groupTaxId,
      billingProviderName: practiceInfoData.billingProviderName,
      billingTaxId: practiceInfoData.billingTaxId,
      printOnClaim: practiceInfoData.printOnClaim,
      billingProviderAddressLine1: practiceInfoData.billingProviderAddressLine1,
      billingProviderAddressLine2: practiceInfoData.billingProviderAddressLine2,
      billingProviderCity: practiceInfoData.billingProviderCity,
      billingProviderState: practiceInfoData.billingProviderState,
      billingProviderZipCode: practiceInfoData.billingProviderZipCode,
      billingProviderContactPersonName: practiceInfoData.billingProviderContactPersonName,
      billingProviderContactPhone: practiceInfoData.billingProviderContactPhone,
      billingProviderContactFax: practiceInfoData.billingProviderContactFax,
      billingProviderContactEmail: practiceInfoData.billingProviderContactEmail,
      useSameAsFacility: practiceInfoData.useSameAsFacility,
      isFacilityPrimary: practiceInfoData.isFacilityPrimary,
      facilityDescription: practiceInfoData?.facilityDescription,
      facilityAddress1: practiceInfoData?.facilityAddressLine1,
      facilityAddress2: practiceInfoData?.facilityAddressLine2,
      facilityCity: practiceInfoData?.facilityAddressCity,
      facilityState: practiceInfoData?.facilityAddressState,
      facilityCounty: practiceInfoData?.facilityAddressCounty,
      facilityCountry: practiceInfoData?.facilityAddressCountry,
      facilityZipCode: practiceInfoData?.facilityAddressZipCode,
      facilityId: practiceInfoData?.facilityId,
      facilityName: practiceInfoData?.facilityName,
    })
    this.addForm.patchValue({
      facilityDescription: practiceInfoData.facilityDescription
    })

  }

}
