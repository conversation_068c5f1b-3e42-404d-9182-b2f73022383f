{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AgGridModule } from 'ag-grid-angular';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { InputDirectivesModule } from 'src/app/shared/directives/input-directives.module';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { ClaimReportServiceService } from 'src/app/services/Dashboard/claim-report-service.service';\nimport { MatRangeDatePickerModule } from 'src/app/shared/components/form-inputs/mat-range-date-picker/mat-range-date-picker.module';\nimport { AllPlaceOfServicesService } from 'src/app/services/ClaimForm/all-place-of-services.service';\nimport { ExportService } from 'src/app/services/export-service/export.service';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MasterdataService } from 'src/app/services/Shared/masterdata.service';\nimport { CustomDateFilterModule } from '../../shared/components/custom-date-filter/custom-date-filter.module';\nimport * as i0 from \"@angular/core\";\nexport let SearchClaimModule = /*#__PURE__*/(() => {\n  class SearchClaimModule {}\n\n  SearchClaimModule.ɵfac = function SearchClaimModule_Factory(t) {\n    return new (t || SearchClaimModule)();\n  };\n\n  SearchClaimModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SearchClaimModule\n  });\n  SearchClaimModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [ClaimReportServiceService, AllPlaceOfServicesService, ExportService, MasterdataService],\n    imports: [CommonModule, ReactiveFormsModule, MatRadioModule, MatTooltipModule, AgGridModule, NgSelectModule, FormsModule, InputDirectivesModule, MatDialogModule, NgMultiSelectDropDownModule, MatDatepickerModule, MatNativeDateModule, MatRangeDatePickerModule, DragDropModule, MatSidenavModule, MatCheckboxModule, CustomDateFilterModule]\n  });\n  return SearchClaimModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}