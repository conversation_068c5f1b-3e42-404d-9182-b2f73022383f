{"ast": null, "code": "import { FormControl, Validators } from '@angular/forms';\nimport { FilePreviewDialogComponent } from 'src/app/shared/components/file-preview-dialog/file-preview-dialog.component';\nimport { checkIfFileTypeValid } from 'src/app/shared/functions/checkIfFileTypeValid';\nimport { submitValidateAllFields } from 'src/app/shared/functions/submitFormRelatedFunctions';\nimport { MemberAlreadyExistsComponent } from '../member-already-exists/member-already-exists.component';\nimport { debounceTime, distinctUntilChanged, first, Subject, takeUntil } from 'rxjs';\nimport { NavTabFromDetails, Tabs } from 'src/app/common/nav-constant';\nimport { noWhitespaceValidator, trimSpaces } from 'src/app/shared/functions/customFormValidators';\nimport * as JSLZString from 'lz-string';\nimport { DebounceTime, PREVILEGES } from 'src/app/common/common-static';\nimport * as moment from 'moment';\nimport { AddressMismatchComponent } from 'src/app/shared/components/address-mismatch/address-mismatch.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"src/app/shared/services/masterdata.service\";\nimport * as i4 from \"src/app/services/ProviderManagement/provider-management.service\";\nimport * as i5 from \"src/app/services/ClaimForm/all-states-by-searchstring.service\";\nimport * as i6 from \"src/app/services/Notification/notification.service\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"src/app/services/Member/member.service\";\nimport * as i9 from \"src/app/shared/services/subject.service\";\nimport * as i10 from \"src/app/services/Providers/provider.service\";\nimport * as i11 from \"src/app/shared/services/global.service\";\nimport * as i12 from \"@ng-select/ng-select\";\nimport * as i13 from \"../../../shared/directives/numbers-only.directive\";\nimport * as i14 from \"@angular/material/datepicker\";\nimport * as i15 from \"@angular/material/input\";\nimport * as i16 from \"../member-activity-log/member-activity-log.component\";\n\nfunction AddMemberComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function AddMemberComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r71 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r71.openActivityLog());\n    });\n    i0.ɵɵelement(1, \"i\", 61);\n    i0.ɵɵtext(2, \"View Activity Log\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function AddMemberComponent_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.closeActivityLog());\n    });\n    i0.ɵɵtext(1, \"Close Activity Log\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"app-member-activity-log\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"memberDetails\", ctx_r2.memberDataForView);\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"invalid-fc\": a0\n  };\n};\n\nfunction AddMemberComponent_input_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 64);\n    i0.ɵɵlistener(\"blur\", function AddMemberComponent_input_18_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.checkIfSubscriberIdAlreadyExists());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r3.f[\"subscriberID\"].invalid && (ctx_r3.f[\"subscriberID\"].dirty || ctx_r3.f[\"subscriberID\"].touched) && ctx_r3.f[\"subscriberID\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Subscriber Id is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.memberDataForView.subscriberID);\n  }\n}\n\nfunction AddMemberComponent_input_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 67);\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r6.f[\"lastName\"].invalid && (ctx_r6.f[\"lastName\"].dirty || ctx_r6.f[\"lastName\"].touched) && ctx_r6.f[\"lastName\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Last Name is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.memberDataForView.lastName);\n  }\n}\n\nfunction AddMemberComponent_input_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 68);\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r9.f[\"firstName\"].invalid && (ctx_r9.f[\"firstName\"].dirty || ctx_r9.f[\"firstName\"].touched) && ctx_r9.f[\"firstName\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" First Name is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.memberDataForView.firstName);\n  }\n}\n\nfunction AddMemberComponent_input_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 69);\n  }\n}\n\nfunction AddMemberComponent_span_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r13.memberDataForView.middleName);\n  }\n}\n\nfunction AddMemberComponent_select_41_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const genderOption_r78 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(genderOption_r78.keyItem);\n  }\n}\n\nfunction AddMemberComponent_select_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"select\", 70)(1, \"option\", 71);\n    i0.ɵɵtext(2, \"SELECT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AddMemberComponent_select_41_option_3_Template, 2, 1, \"option\", 72);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r14.f[\"gender\"].invalid && (ctx_r14.f[\"gender\"].dirty || ctx_r14.f[\"gender\"].touched) && ctx_r14.f[\"gender\"].errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.genderData);\n  }\n}\n\nfunction AddMemberComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Gender is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r16.memberDataForView.gender);\n  }\n}\n\nfunction AddMemberComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Date of Birth is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Mbr Eff Date is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" MBR Term Date is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_input_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 73);\n  }\n\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r23.f[\"addressLine1\"].invalid && (ctx_r23.f[\"addressLine1\"].dirty || ctx_r23.f[\"addressLine1\"].touched) && ctx_r23.f[\"addressLine1\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Address Line 1 is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r25.memberDataForView.addressLine1);\n  }\n}\n\nfunction AddMemberComponent_input_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 74);\n  }\n}\n\nfunction AddMemberComponent_span_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r27.memberDataForView.addressLine2);\n  }\n}\n\nfunction AddMemberComponent_ng_select_88_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r81 = ctx.item;\n    i0.ɵɵtextInterpolate(item_r81.cityStateCounty);\n  }\n}\n\nfunction AddMemberComponent_ng_select_88_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r85 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 75, 76);\n    i0.ɵɵlistener(\"change\", function AddMemberComponent_ng_select_88_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r85);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.changeCitySelect($event));\n    });\n    i0.ɵɵtemplate(2, AddMemberComponent_ng_select_88_ng_template_2_Template, 1, 1, \"ng-template\", 77);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"items\", ctx_r28.citiesData)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r28.f[\"city\"].invalid && (ctx_r28.f[\"city\"].dirty || ctx_r28.f[\"city\"].touched) && ctx_r28.f[\"city\"].errors))(\"typeahead\", ctx_r28.citySearchSubject)(\"minTermLength\", 2);\n  }\n}\n\nfunction AddMemberComponent_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" City is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r30.memberDataForView.city);\n  }\n}\n\nfunction AddMemberComponent_ng_select_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ng-select\", 78, 76);\n  }\n\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r31.statesData)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r31.f[\"state\"].invalid && (ctx_r31.f[\"state\"].dirty || ctx_r31.f[\"state\"].touched) && ctx_r31.f[\"state\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" State is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r33.memberDataForView.state);\n  }\n}\n\nfunction AddMemberComponent_ng_select_103_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 79, 80);\n    i0.ɵɵlistener(\"change\", function AddMemberComponent_ng_select_103_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r88 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r88.changeCountySelect($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r34.countiesData)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r34.f[\"county\"].invalid && (ctx_r34.f[\"county\"].dirty || ctx_r34.f[\"county\"].touched) && ctx_r34.f[\"county\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" County is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r36.memberDataForView.county);\n  }\n}\n\nfunction AddMemberComponent_ng_select_109_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 81, 82);\n    i0.ɵɵlistener(\"change\", function AddMemberComponent_ng_select_109_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r92);\n      const ctx_r91 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r91.changeCountrySelect($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r37.countriesData)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r37.f[\"country\"].invalid && (ctx_r37.f[\"country\"].dirty || ctx_r37.f[\"country\"].touched) && ctx_r37.f[\"country\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Country is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r39.memberDataForView.country);\n  }\n}\n\nfunction AddMemberComponent_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Zip Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_div_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Invalid Zip Code. Please enter only 9 digit number. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_div_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Phone Number is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_div_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Please enter only 10 digit number. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Addr Eff Date is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_div_140_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Addr Term Date Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_ng_select_154_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r95 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 83, 84);\n    i0.ɵɵlistener(\"change\", function AddMemberComponent_ng_select_154_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r95);\n      const ctx_r94 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r94.changeInsuranceCompanySelect($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r48.insuranceCompaniesData)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r48.f[\"insuranceCompanyName\"].invalid && (ctx_r48.f[\"insuranceCompanyName\"].dirty || ctx_r48.f[\"insuranceCompanyName\"].touched) && ctx_r48.f[\"insuranceCompanyName\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_155_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Insurance Company is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_156_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r50.memberDataForView.insuranceCompanyName);\n  }\n}\n\nfunction AddMemberComponent_ng_select_160_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r98 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 85, 86);\n    i0.ɵɵlistener(\"click\", function AddMemberComponent_ng_select_160_Template_ng_select_click_0_listener() {\n      i0.ɵɵrestoreView(_r98);\n      const ctx_r97 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r97.checkIfPlanNameCanBeSelected());\n    })(\"change\", function AddMemberComponent_ng_select_160_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r98);\n      const ctx_r99 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r99.changePlanNameSelect($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r51.planData)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r51.f[\"planName\"].invalid && (ctx_r51.f[\"planName\"].dirty || ctx_r51.f[\"planName\"].touched) && ctx_r51.f[\"planName\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_161_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Plan Name is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_162_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r53.memberDataForView.planName);\n  }\n}\n\nfunction AddMemberComponent_div_169_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Plan Eff Date is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_div_176_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Plan Term Date is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_ng_select_186_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r102 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 87, 88);\n    i0.ɵɵlistener(\"change\", function AddMemberComponent_ng_select_186_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r101 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r101.changeIPASelect($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r58.ipaData)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r58.f[\"ipaName\"].invalid && (ctx_r58.f[\"ipaName\"].dirty || ctx_r58.f[\"ipaName\"].touched) && ctx_r58.f[\"ipaName\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_187_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" IPA Name is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_188_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r60 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r60.memberDataForView.ipaName);\n  }\n}\n\nfunction AddMemberComponent_ng_select_192_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r105 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 89, 90);\n    i0.ɵɵlistener(\"click\", function AddMemberComponent_ng_select_192_Template_ng_select_click_0_listener() {\n      i0.ɵɵrestoreView(_r105);\n      const ctx_r104 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r104.checkIfPCPNameCanBeSelected());\n    })(\"change\", function AddMemberComponent_ng_select_192_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r105);\n      const ctx_r106 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r106.changePCPNameSelect($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r61 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r61.pcpData)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r61.f[\"pcpName\"].invalid && (ctx_r61.f[\"pcpName\"].dirty || ctx_r61.f[\"pcpName\"].touched) && ctx_r61.f[\"pcpName\"].errors));\n  }\n}\n\nfunction AddMemberComponent_div_193_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" PCP Name is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_span_194_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r63 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r63.memberDataForView.pcpName);\n  }\n}\n\nfunction AddMemberComponent_div_201_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" PCP Eff From is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_div_208_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" PCP Eff To is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_button_212_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r108 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function AddMemberComponent_button_212_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r108);\n      const ctx_r107 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r107.addMember());\n    });\n    i0.ɵɵelement(1, \"i\", 92);\n    i0.ɵɵtext(2, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_button_213_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r110 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function AddMemberComponent_button_213_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r109 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r109.addMember());\n    });\n    i0.ɵɵelementStart(1, \"i\", 93);\n    i0.ɵɵtext(2, \"save_as\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Update\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddMemberComponent_button_214_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r112 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function AddMemberComponent_button_214_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r112);\n      const ctx_r111 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r111.openEditMember());\n    });\n    i0.ɵɵelementStart(1, \"i\", 93);\n    i0.ɵɵtext(2, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Edit\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"view-form\": a0\n  };\n};\n\nexport let AddMemberComponent = /*#__PURE__*/(() => {\n  class AddMemberComponent {\n    constructor(formBuilder, datepipe, masterDataService, providerManagementService, stateService, notificationService, dialog, memberService, subService, providerService, globalService) {\n      this.formBuilder = formBuilder;\n      this.datepipe = datepipe;\n      this.masterDataService = masterDataService;\n      this.providerManagementService = providerManagementService;\n      this.stateService = stateService;\n      this.notificationService = notificationService;\n      this.dialog = dialog;\n      this.memberService = memberService;\n      this.subService = subService;\n      this.providerService = providerService;\n      this.globalService = globalService;\n      this.citySearchSubject = new Subject();\n      this.today = new Date();\n      this.genderData = [];\n      this.attachments = [];\n      this.memberToAdd = {};\n      this.mainHeading = 'Add Member';\n      this.mode = 'add';\n      this.showActivityLog = false;\n      this.addTab = false;\n      this.isViewEditBtnShow = false;\n      this.destroy$ = new Subject();\n      this.getPrivileges();\n    }\n\n    ngOnInit() {\n      this.createFormGroup();\n      this.getAllMasterData();\n\n      if (!!this.subService.currentTab && this.subService.currentTab.name == 'Add New Member') {\n        this.addTab = true;\n      }\n\n      this.subService.getSelectedMemberInfoForView().pipe(first()).subscribe(res => {\n        if (!!res && !this.addTab) {\n          if (this.mode == 'add') {\n            this.mode = res.mode;\n          }\n\n          if (this.mode == 'edit') {\n            this.mainHeading = 'Edit Member';\n          } else if (this.mode == 'view') {\n            this.mainHeading = 'View Member';\n          }\n\n          this.memberService.fetchParticularMember(res.data.uniqueMemberID, res.data.planCode, res.data.effectiveDate, this.mode).subscribe(data => {\n            if (!!!data) {\n              this.notificationService.showWarning('', 'Issue getting Member Details!', 4000);\n            } else {\n              if (!!!this.memberDataForView) {\n                this.memberDataForView = data; //this.attachments = JSON.parse(JSON.stringify(data.documents));\n\n                this.patchValueToForm(data);\n                this.memberToAdd = JSON.parse(JSON.stringify(this.memberDataForView));\n\n                if (this.mode == 'view') {\n                  // this.setDatesInViewMode(data);\n                  this.addForm.disable();\n                }\n\n                if (this.mode == 'edit') {\n                  this.getPCPDetails();\n                  this.getPlanData();\n                  this.addForm.controls.subscriberID.disable(); // if (!(!!this.attachments) || this.attachments.length == 0) {\n                  //   this.attachments = [];\n                  //   this.addNewAttachment();\n                  // }\n                }\n              }\n            }\n\n            this.subService.resetSelectedMemberInfoForView();\n          });\n        }\n      });\n      this.subService.getRefreshMemberViewAfterEdit().pipe(takeUntil(this.destroy$)).subscribe(res => {\n        if (this.mode == 'view' && !!this.memberDataForView && !!res && this.memberDataForView.memberprofileId == res.memberprofileId && this.memberDataForView.subscriberID == res.subscriberID) {\n          this.memberService.fetchParticularMember(res.memberprofileId.toString(), res.planCode, res.effectiveDate, this.mode).subscribe(data => {\n            if (!!!data) {\n              this.notificationService.showWarning('', 'Issue getting Member Details!', 4000);\n            } else {\n              this.memberDataForView = data; // this.attachments = JSON.parse(JSON.stringify(data.documents));\n\n              this.patchValueToForm(data);\n              this.memberToAdd = JSON.parse(JSON.stringify(this.memberDataForView));\n              this.addForm.disable();\n            }\n\n            this.subService.resetRefreshMemberViewAfterEdit();\n          });\n        }\n      }); // Set up city search subscription\n\n      this.citySearchSubject.pipe(debounceTime(DebounceTime.citiesDebounceTime), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.searchCities(searchTerm);\n      });\n    }\n\n    get f() {\n      return this.addForm.controls;\n    }\n\n    createFormGroup() {\n      this.addForm = this.formBuilder.group({\n        subscriberID: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        lastName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        firstName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        middleName: new FormControl(null),\n        gender: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        dateOfBirth: new FormControl(null, [Validators.required]),\n        personEffectiveDate: new FormControl(null, [Validators.required]),\n        personTerminationDate: new FormControl(this.datepipe.transform('12/31/9999', 'yyyy-MM-dd'), [Validators.required]),\n        addressLine1: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        addressLine2: new FormControl(null),\n        addressEffectiveDate: new FormControl(null, [Validators.required]),\n        addressTerminationDate: new FormControl(this.datepipe.transform('12/31/9999', 'yyyy-MM-dd'), [Validators.required]),\n        relationshipName: new FormControl({\n          value: 'SELF',\n          disabled: true\n        }, [Validators.required, noWhitespaceValidator]),\n        planEffectiveDate: new FormControl(null, [Validators.required]),\n        planTerminationDate: new FormControl(this.datepipe.transform('12/31/9999', 'yyyy-MM-dd'), [Validators.required]),\n        pcpEffectiveDate: new FormControl(null, [Validators.required]),\n        pcpTerminationDate: new FormControl(this.datepipe.transform('12/31/9999', 'yyyy-MM-dd'), [Validators.required]),\n        city: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        state: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        country: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        county: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        zipCode: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(9)]),\n        number: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(10)]),\n        ipaName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        insuranceCompanyName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        planName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        pcpName: new FormControl(null, [Validators.required, noWhitespaceValidator])\n      });\n      this.dateValidatorForDates();\n    }\n\n    patchValueToForm(data) {\n      this.addForm.patchValue({\n        subscriberID: data.subscriberID,\n        lastName: data.lastName,\n        firstName: data.firstName,\n        middleName: data.middleName,\n        gender: !!data.gender ? data.gender.toUpperCase() : data.gender,\n        dateOfBirth: !!data.dateOfBirth ? this.datepipe.transform(data.dateOfBirth, 'yyyy-MM-dd') : data.dateOfBirth,\n        personEffectiveDate: !!data.personEffectiveDate ? this.datepipe.transform(data.personEffectiveDate, 'yyyy-MM-dd') : data.personEffectiveDate,\n        personTerminationDate: !!data.personTerminationDate ? this.datepipe.transform(data.personTerminationDate, 'yyyy-MM-dd') : data.personTerminationDate,\n        addressLine1: data.addressLine1,\n        addressLine2: data.addressLine2,\n        addressEffectiveDate: !!data.addressEffectiveDate ? this.datepipe.transform(data.addressEffectiveDate, 'yyyy-MM-dd') : data.addressEffectiveDate,\n        addressTerminationDate: !!data.addressTerminationDate ? this.datepipe.transform(data.addressTerminationDate, 'yyyy-MM-dd') : data.addressTerminationDate,\n        relationshipName: data.relationshipName,\n        planEffectiveDate: !!data.planEffectiveDate ? this.datepipe.transform(data.planEffectiveDate, 'yyyy-MM-dd') : data.planEffectiveDate,\n        planTerminationDate: !!data.planTerminationDate ? this.datepipe.transform(data.planTerminationDate, 'yyyy-MM-dd') : data.planTerminationDate,\n        pcpEffectiveDate: !!data.pcpEffectiveDate ? this.datepipe.transform(data.pcpEffectiveDate, 'yyyy-MM-dd') : data.pcpEffectiveDate,\n        pcpTerminationDate: !!data.pcpTerminationDate ? this.datepipe.transform(data.pcpTerminationDate, 'yyyy-MM-dd') : data.pcpTerminationDate,\n        city: data.city,\n        state: data.state,\n        country: data.country,\n        county: data.county,\n        zipCode: data.zipCode,\n        number: data.number,\n        ipaName: data.ipaName,\n        insuranceCompanyName: data.insuranceCompanyName,\n        planName: data.planName,\n        pcpName: data.pcpName\n      });\n    }\n\n    setDatesInViewMode(data) {\n      this.addForm.controls.dateOfBirth.setValue(!!data.dateOfBirth ? this.datepipe.transform(data.dateOfBirth, 'MM-dd-yyyy') : data.dateOfBirth);\n      this.addForm.controls.personEffectiveDate.setValue(!!data.personEffectiveDate ? this.datepipe.transform(data.personEffectiveDate, 'MM-dd-yyyy') : data.personEffectiveDate);\n      this.addForm.controls.personTerminationDate.setValue(!!data.personTerminationDate ? this.datepipe.transform(data.personTerminationDate, 'MM-dd-yyyy') : data.personTerminationDate);\n      this.addForm.controls.addressEffectiveDate.setValue(!!data.addressEffectiveDate ? this.datepipe.transform(data.addressEffectiveDate, 'MM-dd-yyyy') : data.addressEffectiveDate);\n      this.addForm.controls.addressTerminationDate.setValue(!!data.addressTerminationDate ? this.datepipe.transform(data.addressTerminationDate, 'MM-dd-yyyy') : data.addressTerminationDate);\n      this.addForm.controls.planEffectiveDate.setValue(!!data.planEffectiveDate ? this.datepipe.transform(data.planEffectiveDate, 'MM-dd-yyyy') : data.planEffectiveDate);\n      this.addForm.controls.planTerminationDate.setValue(!!data.planTerminationDate ? this.datepipe.transform(data.planTerminationDate, 'MM-dd-yyyy') : data.planTerminationDate);\n      this.addForm.controls.pcpEffectiveDate.setValue(!!data.pcpEffectiveDate ? this.datepipe.transform(data.pcpEffectiveDate, 'MM-dd-yyyy') : data.pcpEffectiveDate);\n      this.addForm.controls.pcpTerminationDate.setValue(!!data.pcpTerminationDate ? this.datepipe.transform(data.pcpTerminationDate, 'MM-dd-yyyy') : data.pcpTerminationDate);\n    }\n\n    getAllMasterData() {\n      this.masterDataService.fetchchMasterData('Gender').subscribe(res => {\n        this.genderData = res;\n      }); // Initialize cities data as empty - will be populated by search\n\n      this.citiesData = [];\n      let state = localStorage.getItem('allStates');\n\n      if (!!state) {\n        this.statesData = JSON.parse(JSLZString.decompress(state));\n      } else {\n        this.stateService.fetchchAllStatesBySearchstring().subscribe(res => {\n          this.statesData = res;\n        });\n      }\n\n      let countries = localStorage.getItem('allCountries');\n\n      if (!!countries) {\n        this.countriesData = JSON.parse(JSLZString.decompress(countries));\n      } else {\n        this.providerManagementService.fetchCountriesData(\"\").subscribe(res => {\n          this.countriesData = !!res.content ? res.content : [];\n        });\n      }\n\n      let counties = localStorage.getItem('allCounties');\n\n      if (!!counties) {\n        this.countiesData = JSON.parse(JSLZString.decompress(counties));\n      } else {\n        this.providerManagementService.fetchCountiesData(\"\").subscribe(res => {\n          this.countiesData = !!res.content ? res.content : [];\n        });\n      }\n\n      this.providerManagementService.fetchIPAData('').subscribe(res => {\n        this.ipaData = !!res.content ? res.content : [];\n      });\n      this.masterDataService.fetchchMasterData('AttatchmentTypes').subscribe(data => {\n        this.attachmentTypesData = data;\n        this.addNewAttachment();\n      });\n      let insuranceData = localStorage.getItem('insuranceCompaniesOrAllPlans');\n\n      if (!!insuranceData) {\n        this.insuranceCompaniesData = JSON.parse(JSLZString.decompress(insuranceData));\n      } else {\n        this.providerManagementService.fetchPlansData('').subscribe(res => {\n          this.insuranceCompaniesData = !!res.content ? res.content : [];\n        });\n      }\n    }\n\n    checkIfSubscriberIdAlreadyExists() {\n      let id = this.addForm.controls.subscriberID.value;\n      let toCheck = true;\n\n      if (this.mode == 'edit' && id == this.memberDataForView.subscriberID) {\n        toCheck = false;\n      }\n\n      if (!!id && id.length > 0 && toCheck) {\n        this.memberService.CheckSubscriberIDExists(id).subscribe(res => {\n          if (!!res) {\n            this.notificationService.showError('', 'Subscriber ID already exists', 4000);\n            this.dialog.open(MemberAlreadyExistsComponent, {\n              data: res,\n              width: '-webkit-fill-available'\n            });\n            this.addForm.controls.subscriberID.setValue(null);\n          }\n        });\n      }\n    }\n\n    changeCitySelect(selectedCity) {\n      if (!!selectedCity) {\n        this.addForm.controls.country.setValue(selectedCity.countryName);\n        this.addForm.controls.county.setValue(selectedCity.countyName);\n        this.addForm.controls.state.setValue(selectedCity.stateCode);\n        this.memberToAdd.cityCode = selectedCity.cityCode;\n        this.memberToAdd.countryCode = selectedCity.countryCode;\n        this.memberToAdd.countyCode = selectedCity.countyMDMCode;\n      } else {\n        if (!!this.memberToAdd.cityCode) {\n          this.memberToAdd.cityCode = null;\n        }\n      }\n    }\n\n    searchCities(searchTerm) {\n      // Only search if user has typed 2 or more characters\n      if (!searchTerm || searchTerm.length < 2) {\n        this.citiesData = [];\n        return;\n      }\n\n      this.providerManagementService.fetchCitiesBySearchTerm(searchTerm).subscribe(res => {\n        if (!!res && !!res.content) {\n          this.citiesData = res.content;\n        } else {\n          this.citiesData = [];\n        }\n      }, error => {\n        this.citiesData = [];\n      });\n    }\n\n    changeCountySelect(selectedCounty) {\n      if (!!selectedCounty) {\n        this.memberToAdd.countyCode = selectedCounty.countyMDMCode;\n      } else {\n        this.memberToAdd.countyCode = null;\n      }\n    }\n\n    changeIPASelect(selectedIPA) {\n      if (!!selectedIPA) {\n        this.memberToAdd.ipaCode = selectedIPA.mdmCode;\n        this.resetPCPFields();\n        this.getPCPDetails();\n      } else {\n        this.memberToAdd.ipaCode = null;\n        this.resetPCPFields();\n      }\n    }\n\n    getPCPDetails() {\n      if (!!this.memberToAdd.ipaCode && !!this.memberToAdd.insuranceCompanyCode) {\n        let requestBody = {\n          searchTerm: \"\",\n          ipa: this.memberToAdd.ipaCode,\n          companyName: this.memberToAdd.insuranceCompanyCode\n        };\n        this.providerService.GetAllProviderDetailsByIPA(requestBody).subscribe(res => {\n          if (!!res && !!res.searchResult && res.searchResult.length > 0) {\n            this.pcpData = res.searchResult;\n          } else {\n            this.notificationService.showWarning('', 'No PCP found for selected Insurance Company and IPA.', 4000);\n          }\n        });\n      }\n    }\n\n    changeCountrySelect(selectedCountry) {\n      if (!!selectedCountry) {\n        this.memberToAdd.countryCode = selectedCountry.countryCode;\n      } else {\n        this.memberToAdd.countryCode = null;\n      }\n    }\n\n    getPlanData() {\n      if (!!this.memberToAdd.insuranceCompanyCode) {\n        this.memberService.getPlansByInsuranceCompanyCode(this.memberToAdd.insuranceCompanyCode).subscribe(res => {\n          this.planData = !!res.content ? res.content : [];\n\n          if (!(!!this.planData && this.planData.length > 0)) {\n            this.notificationService.showWarning('', 'No Plan found for selected Insurance Company.', 4000);\n          }\n        });\n      }\n    }\n\n    changeInsuranceCompanySelect(selectedInsuranceCompany) {\n      if (!!selectedInsuranceCompany) {\n        this.memberToAdd.insuranceCompanyCode = selectedInsuranceCompany.mdmCode;\n        this.memberService.getPlansByInsuranceCompanyCode(this.memberToAdd.insuranceCompanyCode).subscribe(res => {\n          this.planData = !!res.content ? res.content : [];\n          this.addForm.controls.planName.reset();\n          this.memberToAdd.planCode = null;\n\n          if (!(!!this.planData && this.planData.length > 0)) {\n            this.notificationService.showWarning('', 'No Plan found for selected Insurance Company.', 4000);\n          }\n        });\n        this.resetPCPFields();\n        this.getPCPDetails();\n      } else {\n        this.memberToAdd.insuranceCompanyCode = null;\n        this.addForm.controls.planName.reset();\n        this.memberToAdd.planCode = null;\n        this.planData = [];\n        this.resetPCPFields();\n      }\n    }\n\n    checkIfPlanNameCanBeSelected() {\n      const selectedInsuranceCompany = this.addForm.controls.insuranceCompanyName.value;\n\n      if (!(!!selectedInsuranceCompany && selectedInsuranceCompany.length > 0)) {\n        this.notificationService.showWarning('Please select \"Insurance Company\" first.', 'Invalid Selection', 4000);\n      }\n    }\n\n    checkIfPCPNameCanBeSelected() {\n      const selectedInsuranceCompany = this.addForm.controls.insuranceCompanyName.value;\n      const selectedIPAName = this.addForm.controls.ipaName.value;\n\n      if (!(!!selectedInsuranceCompany && selectedInsuranceCompany.length > 0 && !!selectedIPAName && selectedIPAName.length > 0)) {\n        this.notificationService.showWarning('Please select \"Insurance Company\" and \"IPA Name\" first.', 'Invalid Selection', 4000);\n      }\n    }\n\n    resetPCPFields() {\n      this.addForm.controls.pcpName.reset();\n      this.pcpData = [];\n      this.memberToAdd.npi = null;\n      this.memberToAdd.uniqueProviderID = null;\n    }\n\n    changePlanNameSelect(selectedPlan) {\n      if (!!selectedPlan) {\n        this.memberToAdd.planCode = selectedPlan.planCode;\n      } else {\n        this.memberToAdd.planCode = null;\n      }\n    }\n\n    changePCPNameSelect(selectedPCP) {\n      if (!!selectedPCP) {\n        this.memberToAdd.npi = selectedPCP.npiNumber;\n        this.memberToAdd.uniqueProviderID = selectedPCP.uniqueProviderId;\n      } else {\n        this.memberToAdd.npi = null;\n        this.memberToAdd.uniqueProviderID = null;\n      }\n    }\n\n    addMember() {\n      if (this.addForm.invalid) {\n        submitValidateAllFields.validateAllFields(this.addForm);\n        let errors = {\n          required: false,\n          zipCodeMaxLength: false,\n          phoneNumberMaxLength: false,\n          invalidEffectiveDate: false,\n          invalidMemberDate: false\n        };\n        Object.keys(this.addForm.controls).forEach(key => {\n          const controlErrors = this.addForm.get(key).errors;\n\n          if (controlErrors != null) {\n            Object.keys(controlErrors).forEach(keyError => {\n              // if (keyError == 'required' || keyError == 'whitespace') {\n              //   errors.required = errors.required || controlErrors[keyError]\n              // };\n              // if (keyError == 'maxlength' && key == 'zipCode') {\n              //   errors.zipCodeMaxLength = true;\n              // }\n              // if (keyError == 'maxlength' && key == 'number') {\n              //   errors.phoneNumberMaxLength = true;\n              // }\n              if (keyError == 'invalidEffectiveDate') {\n                errors.invalidEffectiveDate = true;\n              }\n\n              if (keyError == 'invalidMemberDate') {\n                errors.invalidMemberDate = true;\n              }\n            });\n          }\n        }); // if (errors.required) {\n        //   this.notificationService.showError('', 'Please fill all the required (*) fields.', 4000)\n        // }\n        // if (errors.zipCodeMaxLength) {\n        //   this.notificationService.showError('', \"'Zip Code' should be less than or equal to 9 characters in length.\", 4000);\n        // }\n        // if (errors.phoneNumberMaxLength) {\n        //   this.notificationService.showError('', \"'Phone Number' should be less than or equal to 10 characters in length.\", 4000);\n        // }\n\n        if (errors.invalidEffectiveDate) {\n          this.notificationService.showError('', \"'Addr Eff Date', 'Plan Eff Date' and 'PCP Eff From' should be greater than or equal to 'MBR Eff Date'.\", 4000);\n        }\n\n        if (errors.invalidMemberDate) {\n          this.notificationService.showError('', \"'MBR Eff Date' should be greater than or equal to 'Date of Birth'.\", 4000);\n        }\n\n        return;\n      }\n\n      let patientrequestbody = {\n        addressLineOne: this.addForm.controls[\"addressLine1\"].value,\n        city: this.addForm.controls[\"city\"].value,\n        state: this.addForm.controls[\"state\"].value,\n        givenZipcode: this.addForm.controls[\"zipCode\"].value\n      };\n      this.providerManagementService.GetCorrectAddress(patientrequestbody).subscribe(res => {\n        if (!!res && !!res.content && res.content.isSmartAddressFound && !res.content.isAdderssCorrect) {\n          const dialogRef = this.dialog.open(AddressMismatchComponent, {\n            width: '800px',\n            data: [{\n              entity: 'Member',\n              address: res.content\n            }]\n          });\n          dialogRef.afterClosed().subscribe(resp => {\n            if (!!resp && resp.isMapAddresss) {\n              this.addForm.controls[\"addressLine1\"].setValue(res.content.addressLineOne);\n              this.addForm.controls[\"city\"].setValue(res.content.city);\n              this.addForm.controls[\"state\"].setValue(res.content.state);\n              this.addForm.controls[\"zipCode\"].setValue(res.content.zipcode);\n              this.saveMember();\n            } else if (!!resp && resp.isMapAddresss == false) {\n              this.saveMember();\n            }\n          });\n        } else {\n          this.saveMember();\n        }\n      });\n    }\n\n    saveMember() {\n      let documents = [];\n\n      for (const attachment of this.attachments) {\n        if (!!attachment.document) {\n          documents.push(attachment);\n        }\n      }\n\n      const memberToAddCopy = JSON.parse(JSON.stringify(this.memberToAdd));\n      this.memberToAdd = JSON.parse(JSON.stringify(this.addForm.value));\n      this.memberToAdd.pcpEffDate = this.memberToAdd.pcpEffectiveDate && (moment.isMoment(this.memberToAdd.pcpEffectiveDate) || this.memberToAdd.pcpEffectiveDate.includes('T')) ? moment(this.memberToAdd.pcpEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.pcpEffectiveDate;\n      this.memberToAdd.pcpTermDate = this.memberToAdd.pcpTerminationDate && (moment.isMoment(this.memberToAdd.pcpTerminationDate) || this.memberToAdd.pcpTerminationDate.includes('T')) ? moment(this.memberToAdd.pcpTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.pcpTerminationDate;\n      this.memberToAdd.planEffDate = this.memberToAdd.planEffectiveDate && (moment.isMoment(this.memberToAdd.planEffectiveDate) || this.memberToAdd.planEffectiveDate.includes('T')) ? moment(this.memberToAdd.planEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.planEffectiveDate;\n      this.memberToAdd.planTermDate = this.memberToAdd.planTerminationDate && (moment.isMoment(this.memberToAdd.planTerminationDate) || this.memberToAdd.planTerminationDate.includes('T')) ? moment(this.memberToAdd.planTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.planTerminationDate;\n      this.memberToAdd.dateOfBirth = this.memberToAdd.dateOfBirth && (moment.isMoment(this.memberToAdd.dateOfBirth) || this.memberToAdd.dateOfBirth.includes('T')) ? moment(this.memberToAdd.dateOfBirth).format('YYYY-MM-DD') : this.memberToAdd.dateOfBirth;\n      this.memberToAdd.personEffectiveDate = this.memberToAdd.personEffectiveDate && (moment.isMoment(this.memberToAdd.personEffectiveDate) || this.memberToAdd.personEffectiveDate.includes('T')) ? moment(this.memberToAdd.personEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.personEffectiveDate;\n      this.memberToAdd.personTerminationDate = this.memberToAdd.personTerminationDate && (moment.isMoment(this.memberToAdd.personTerminationDate) || this.memberToAdd.personTerminationDate.includes('T')) ? moment(this.memberToAdd.personTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.personTerminationDate;\n      this.memberToAdd.addressEffectiveDate = this.memberToAdd.addressEffectiveDate && (moment.isMoment(this.memberToAdd.addressEffectiveDate) || this.memberToAdd.addressEffectiveDate.includes('T')) ? moment(this.memberToAdd.addressEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.addressEffectiveDate;\n      this.memberToAdd.addressTerminationDate = this.memberToAdd.addressTerminationDate && (moment.isMoment(this.memberToAdd.addressTerminationDate) || this.memberToAdd.addressTerminationDate.includes('T')) ? moment(this.memberToAdd.addressTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.addressTerminationDate;\n      this.memberToAdd.planEffectiveDate = this.memberToAdd.planEffectiveDate && (moment.isMoment(this.memberToAdd.planEffectiveDate) || this.memberToAdd.planEffectiveDate.includes('T')) ? moment(this.memberToAdd.planEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.planEffectiveDate;\n      this.memberToAdd.planTerminationDate = this.memberToAdd.planTerminationDate && (moment.isMoment(this.memberToAdd.planTerminationDate) || this.memberToAdd.planTerminationDate.includes('T')) ? moment(this.memberToAdd.planTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.planTerminationDate;\n      this.memberToAdd.pcpEffectiveDate = this.memberToAdd.pcpEffectiveDate && (moment.isMoment(this.memberToAdd.pcpEffectiveDate) || this.memberToAdd.pcpEffectiveDate.includes('T')) ? moment(this.memberToAdd.pcpEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.pcpEffectiveDate;\n      this.memberToAdd.pcpTerminationDate = this.memberToAdd.pcpTerminationDate && (moment.isMoment(this.memberToAdd.pcpTerminationDate) || this.memberToAdd.pcpTerminationDate.includes('T')) ? moment(this.memberToAdd.pcpTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.pcpTerminationDate;\n      this.memberToAdd.stateSubdivisionCode = this.memberToAdd.state;\n      this.memberToAdd.stateCode = this.memberToAdd.state;\n      this.memberToAdd.relationshipCode = 'PTR00000001';\n      this.memberToAdd.createdBy = localStorage.getItem('userName');\n      this.memberToAdd.cityCode = memberToAddCopy.cityCode;\n      this.memberToAdd.countryCode = memberToAddCopy.countryCode;\n      this.memberToAdd.countyCode = memberToAddCopy.countyCode;\n      this.memberToAdd.ipaCode = memberToAddCopy.ipaCode;\n      this.memberToAdd.insuranceCompanyCode = memberToAddCopy.insuranceCompanyCode;\n      this.memberToAdd.planCode = memberToAddCopy.planCode;\n      this.memberToAdd.npi = memberToAddCopy.npi;\n      this.memberToAdd.uniqueProviderID = memberToAddCopy.uniqueProviderID;\n      this.memberToAdd.relationshipName = 'SELF';\n      this.memberToAdd.planName = !!this.memberToAdd.planName ? this.memberToAdd.planName.toUpperCase() : this.memberToAdd.planName;\n      this.memberToAdd.documents = documents;\n      const fileArray = [];\n\n      for (const attachment of documents) {\n        fileArray.push(attachment.document);\n      }\n\n      this.memberToAdd = trimSpaces(this.memberToAdd);\n\n      if (this.mode == 'add') {\n        this.memberService.AddMemberDetails(this.memberToAdd, fileArray).subscribe(res => {\n          if (res.statusCode == 200) {\n            this.notificationService.showSuccess('', 'Member Added Successfully.', 4000);\n            this.subService.setRefreshMemberInfo(true);\n            this.memberToAdd = {};\n            this.attachments = [];\n            this.addNewAttachment();\n            this.addForm.reset();\n            this.cancel();\n          } else {\n            this.notificationService.showError('', 'Member could not be added. Please try again later.', 4000);\n          }\n        }, error => {\n          this.notificationService.showError('', 'Member could not be added. Please try again later.', 4000);\n        });\n      }\n\n      if (this.mode == 'edit') {\n        this.memberToAdd.subscriberID = this.memberDataForView.subscriberID;\n        this.memberToAdd.personId = this.memberDataForView.personId;\n        this.memberToAdd.addressId = this.memberDataForView.addressId;\n        this.memberService.UpdateMemberDetails(this.memberToAdd, fileArray).subscribe(res => {\n          if (res.statusCode == 200) {\n            this.notificationService.showSuccess('', 'Member Updated Successfully', 4000);\n            this.subService.setRefreshMemberInfo(true);\n            let data = {\n              memberprofileId: this.memberDataForView.memberprofileId,\n              planCode: this.memberToAdd.planCode,\n              subscriberID: this.memberDataForView.subscriberID\n            };\n            this.subService.setRefreshMemberViewAfterEdit(data);\n            this.memberToAdd = {};\n            this.attachments = [];\n            this.addNewAttachment();\n            this.addForm.reset();\n            this.cancel();\n          } else {\n            this.notificationService.showError('', 'Member could not be updated. Please try again later.', 4000);\n          }\n        }, error => {\n          this.notificationService.showError('', 'Member could not be updated. Please try again later.', 4000);\n        });\n      }\n    }\n\n    addNewAttachment() {\n      let newDocument = {};\n      newDocument.title = this.attachmentTypesData[0].keyItem;\n      newDocument.categoryName = this.attachmentTypesData[0].keyItem;\n      this.attachments.push(newDocument);\n    }\n\n    checkFileValidity(e, attachment) {\n      if (!!e.target.files && e.target.files.length > 0) {\n        if (checkIfFileTypeValid(e, ['jpeg', 'pdf', 'png', 'jpg'])) {\n          attachment.document = e.target.files[0];\n          attachment.description = e.target.files[0].name;\n        } else {\n          e.target.value = null;\n          attachment.document = null;\n          attachment.description = null;\n          this.notificationService.showError('', 'Only jpeg, pdf, png, jpg files are allowed', 4000);\n        }\n      } else {\n        e.target.value = null;\n        attachment.document = null;\n        attachment.description = null;\n      }\n    }\n\n    removeAttachment(index) {\n      this.attachments.splice(index, 1);\n    }\n\n    preview(attachment) {\n      const reader = new FileReader();\n\n      reader.onload = e => {\n        let modalData = {\n          url: e.target.result,\n          messageToDisplay: 'Preview File',\n          messageToDisplayOnButton: 'Close'\n        };\n        this.dialog.open(FilePreviewDialogComponent, {\n          data: modalData,\n          width: '50vw',\n          height: '90vh'\n        });\n      };\n\n      reader.readAsDataURL(attachment.document);\n    }\n\n    cancel() {\n      let tabName = '';\n\n      if (this.mode == 'add') {\n        tabName = 'Add New Member';\n      }\n\n      if (this.mode == 'edit') {\n        tabName = 'Edit Member - ' + this.memberService.getTabNameForViewAndEditMember(this.memberDataForView);\n      }\n\n      if (this.mode == 'view') {\n        tabName = 'View Member - ' + this.memberService.getTabNameForViewAndEditMember(this.memberDataForView);\n      }\n\n      this.subService.setCloseTabRefresh(tabName);\n    }\n\n    getSmallerDate(date) {\n      const date1 = new Date(date).getTime();\n      const date2 = this.today.getTime();\n\n      if (date1 > date2) {\n        return this.today;\n      } else if (date2 > date1) {\n        return date;\n      } else {\n        return this.today;\n      }\n    }\n\n    dateValidatorForDates() {\n      this.addForm.controls['personEffectiveDate'].valueChanges.pipe(distinctUntilChanged()).subscribe(personEffectiveDate => {\n        if (!!personEffectiveDate) {\n          if (!!this.addForm.controls.addressEffectiveDate.value) {\n            if (new Date(personEffectiveDate).getTime() > new Date(this.addForm.controls.addressEffectiveDate.value).getTime()) {\n              this.addForm.controls.addressEffectiveDate.setErrors({\n                invalidEffectiveDate: true\n              });\n            } else {\n              this.addForm.controls.addressEffectiveDate.setErrors(null);\n            }\n          }\n\n          if (!!this.addForm.controls.planEffectiveDate.value) {\n            if (new Date(personEffectiveDate).getTime() > new Date(this.addForm.controls.planEffectiveDate.value).getTime()) {\n              this.addForm.controls.planEffectiveDate.setErrors({\n                invalidEffectiveDate: true\n              });\n            } else {\n              this.addForm.controls.planEffectiveDate.setErrors(null);\n            }\n          }\n\n          if (!!this.addForm.controls.pcpEffectiveDate.value) {\n            if (new Date(personEffectiveDate).getTime() > new Date(this.addForm.controls.pcpEffectiveDate.value).getTime()) {\n              this.addForm.controls.pcpEffectiveDate.setErrors({\n                invalidEffectiveDate: true\n              });\n            } else {\n              this.addForm.controls.pcpEffectiveDate.setErrors(null);\n            }\n          }\n\n          if (!!this.addForm.controls.dateOfBirth.value) {\n            if (new Date(personEffectiveDate).getTime() < new Date(this.addForm.controls.dateOfBirth.value).getTime()) {\n              this.addForm.controls.personEffectiveDate.setErrors({\n                invalidMemberDate: true\n              });\n            } else {\n              this.addForm.controls.personEffectiveDate.setErrors(null);\n            }\n          } else {\n            this.addForm.controls.personEffectiveDate.setErrors(null);\n          }\n        } else {\n          if (!!this.addForm.controls.addressEffectiveDate.value) {\n            this.addForm.controls.addressEffectiveDate.setErrors(null);\n          }\n\n          if (!!this.addForm.controls.planEffectiveDate.value) {\n            this.addForm.controls.planEffectiveDate.setErrors(null);\n          }\n\n          if (!!this.addForm.controls.pcpEffectiveDate.value) {\n            this.addForm.controls.pcpEffectiveDate.setErrors(null);\n          }\n        }\n      });\n      this.addForm.controls['addressEffectiveDate'].valueChanges.pipe(distinctUntilChanged()).subscribe(addressEffectiveDate => {\n        if (!!addressEffectiveDate) {\n          if (!!this.addForm.controls.personEffectiveDate.value) {\n            if (new Date(addressEffectiveDate).getTime() < new Date(this.addForm.controls.personEffectiveDate.value).getTime()) {\n              this.addForm.controls.addressEffectiveDate.setErrors({\n                invalidEffectiveDate: true\n              });\n            } else {\n              this.addForm.controls.addressEffectiveDate.setErrors(null);\n            }\n          } else {\n            this.addForm.controls.addressEffectiveDate.setErrors(null);\n          }\n        }\n      });\n      this.addForm.controls['planEffectiveDate'].valueChanges.pipe(distinctUntilChanged()).subscribe(planEffectiveDate => {\n        if (!!planEffectiveDate) {\n          if (!!this.addForm.controls.personEffectiveDate.value) {\n            if (new Date(planEffectiveDate).getTime() < new Date(this.addForm.controls.personEffectiveDate.value).getTime()) {\n              this.addForm.controls.planEffectiveDate.setErrors({\n                invalidEffectiveDate: true\n              });\n            } else {\n              this.addForm.controls.planEffectiveDate.setErrors(null);\n            }\n          } else {\n            this.addForm.controls.planEffectiveDate.setErrors(null);\n          }\n        }\n      });\n      this.addForm.controls['pcpEffectiveDate'].valueChanges.pipe(distinctUntilChanged()).subscribe(pcpEffectiveDate => {\n        if (!!pcpEffectiveDate) {\n          if (!!this.addForm.controls.personEffectiveDate.value) {\n            if (new Date(pcpEffectiveDate).getTime() < new Date(this.addForm.controls.personEffectiveDate.value).getTime()) {\n              this.addForm.controls.pcpEffectiveDate.setErrors({\n                invalidEffectiveDate: true\n              });\n            } else {\n              this.addForm.controls.pcpEffectiveDate.setErrors(null);\n            }\n          } else {\n            this.addForm.controls.pcpEffectiveDate.setErrors(null);\n          }\n        }\n      });\n      this.addForm.controls['dateOfBirth'].valueChanges.pipe(distinctUntilChanged()).subscribe(dateOfBirth => {\n        if (!!dateOfBirth) {\n          if (!!this.addForm.controls.personEffectiveDate.value) {\n            if (new Date(dateOfBirth).getTime() > new Date(this.addForm.controls.personEffectiveDate.value).getTime()) {\n              this.addForm.controls.personEffectiveDate.setErrors({\n                invalidMemberDate: true\n              });\n            } else {\n              this.addForm.controls.personEffectiveDate.setErrors(null);\n            }\n          }\n        } else {\n          if (!!this.addForm.controls.personEffectiveDate.value) {\n            this.addForm.controls.personEffectiveDate.setErrors(null);\n          }\n        }\n      });\n    }\n\n    checkIfAddAttachmentDisable() {\n      let disable = false;\n\n      for (const attachment of this.attachments) {\n        if (!!!attachment.document) {\n          disable = disable || true;\n        }\n      }\n\n      return disable;\n    }\n\n    openActivityLog() {\n      this.showActivityLog = true;\n    }\n\n    closeActivityLog() {\n      this.showActivityLog = false;\n    }\n\n    openEditMember() {\n      let nav = new NavTabFromDetails();\n      nav.name = Tabs.editMember;\n      nav.data = this.memberService.getTabNameForViewAndEditMember(this.memberDataForView);\n      this.subService.passValue(nav); // this.subService.passValue('Edit Member - ' + this.memberService.getTabNameForViewAndEditMember(this.memberDataForView));\n\n      this.cancel();\n      const dataNeeded = {\n        data: this.memberDataForView,\n        mode: 'edit'\n      };\n      this.subService.setSelectedMemberInfoForView(dataNeeded);\n    }\n\n    getPrivileges() {\n      const privielagesDetails = this.globalService.getPrivilegesByRole();\n      this.isViewEditBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.MemberHouse_MemberManagement_Claims_EditMember).length > 0 ? true : false;\n    }\n\n    zipCodeOnBlur(ev) {\n      if (ev.target.value.length == 5 || ev.target.value.length == 9) {\n        this.getLocationDetailsByAddresLineAndZipcode();\n      }\n    }\n\n    getLocationDetailsByAddresLineAndZipcode() {\n      let request = {\n        payToAddressLine1: this.addForm.controls.addressLine1.value.trim(),\n        payToZipCode: this.addForm.controls.zipCode.value\n      };\n      this.providerManagementService.getLocationDetailsByStreetAndZipCode(request).subscribe(resp => {\n        if (resp.statusCode == 200) {\n          if (!!resp.content) {\n            let countryDetails = this.countriesData.find(country => country.countryCode == \"US\");\n\n            if (!!resp.content.payToCity && !!resp.content.payToState) {\n              this.addForm.patchValue({\n                addressLine1: resp.content.payToAddressLine1,\n                city: resp.content.payToCity,\n                zipCode: resp.content.payToZipCode,\n                state: resp.content.payToState,\n                country: !!countryDetails ? countryDetails.name : null\n              });\n            }\n          }\n        }\n      });\n    }\n\n    ngOnDestroy() {\n      this.destroy$.next(true);\n      this.destroy$.unsubscribe();\n    }\n\n  }\n\n  AddMemberComponent.ɵfac = function AddMemberComponent_Factory(t) {\n    return new (t || AddMemberComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DatePipe), i0.ɵɵdirectiveInject(i3.MasterdataService), i0.ɵɵdirectiveInject(i4.ProviderManagementService), i0.ɵɵdirectiveInject(i5.AllStatesBySearchstringService), i0.ɵɵdirectiveInject(i6.NotificationService), i0.ɵɵdirectiveInject(i7.MatDialog), i0.ɵɵdirectiveInject(i8.MemberService), i0.ɵɵdirectiveInject(i9.SubjectService), i0.ɵɵdirectiveInject(i10.ProviderService), i0.ɵɵdirectiveInject(i11.GlobalService));\n  };\n\n  AddMemberComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddMemberComponent,\n    selectors: [[\"app-add-member\"]],\n    decls: 217,\n    vars: 118,\n    consts: [[1, \"container-fluid\", \"add-member\", 3, \"ngClass\"], [1, \"mat-card\", \"mt-3\", \"create-claim-form-styles\"], [2, \"width\", \"100%\"], [1, \"create-claim-title\", \"mt-15\", 2, \"color\", \"#023781\", \"font-weight\", \"bold\", \"margin-left\", \"21px\", \"font-size\", \"22px\"], [\"type\", \"button\", \"class\", \"btn-primary activity-button primary-btn btn-height\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn-common-danger activity-button danger-btn btn-height\", 3, \"click\", 4, \"ngIf\"], [3, \"formGroup\"], [\"class\", \"mt-3 internal-div\", 4, \"ngIf\"], [1, \"mt-3\", \"internal-div\"], [1, \"f-size16\", \"menuitemschaild\"], [1, \"row\", \"menuitemschaild\", \"mt-8\"], [1, \"col-md-12\"], [1, \"row\", \"f-size13\"], [1, \"col-md-2\"], [1, \"dashboard-label\"], [\"type\", \"text\", \"placeholder\", \"Subscriber Id\", \"class\", \"form-control\", \"name\", \"SubscriberId\", \"id\", \"SId\", \"formControlName\", \"subscriberID\", \"autocomplete\", \"off\", 3, \"ngClass\", \"blur\", 4, \"ngIf\"], [\"class\", \"invalid-feedback invalid-show\", 4, \"ngIf\"], [\"class\", \"form-control\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Last Name\", \"class\", \"form-control\", \"name\", \"Lname\", \"id\", \"LastName\", \"autocomplete\", \"off\", 3, \"ngClass\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"First Name\", \"class\", \"form-control\", \"name\", \"Fname\", \"id\", \"FirstName\", \"autocomplete\", \"off\", 3, \"ngClass\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"middleName\", \"placeholder\", \"Middle Initial\", \"class\", \"form-control\", \"name\", \"mi\", \"id\", \"mi\", \"autocomplete\", \"off\", 4, \"ngIf\"], [\"class\", \"form-select\", \"id\", \"Gender\", \"name\", \"gender\", \"formControlName\", \"gender\", \"aria-label\", \".form-select-sm example\", 3, \"ngClass\", 4, \"ngIf\"], [\"matInput\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"dateOfBirth\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"mat-datepicker-background\", 3, \"max\", \"matDatepicker\", \"ngClass\", \"click\"], [\"dateOfBirth\", \"\"], [\"matInput\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"personEffectiveDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"mat-datepicker-background\", 3, \"max\", \"matDatepicker\", \"ngClass\", \"click\"], [\"personEffectiveDate\", \"\"], [\"matInput\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"personTerminationDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"mat-datepicker-background\", 3, \"min\", \"matDatepicker\", \"ngClass\", \"click\"], [\"personTerminationDate\", \"\"], [1, \"col-md-3\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"formControlName\", \"addressLine1\", \"placeholder\", \"Address Line 1\", \"class\", \"form-control\", \"name\", \"address1\", \"id\", \"address1\", 3, \"ngClass\", 4, \"ngIf\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"formControlName\", \"addressLine2\", \"placeholder\", \"Address Line 2\", \"class\", \"form-control\", \"name\", \"address2\", \"id\", \"address2\", 4, \"ngIf\"], [1, \"col-md-4\"], [\"class\", \"form-control\", \"bindValue\", \"cityName\", \"bindLabel\", \"cityName\", \"placeholder\", \"Type to search city (min 2 characters)\", \"formControlName\", \"city\", 3, \"items\", \"virtualScroll\", \"ngClass\", \"typeahead\", \"minTermLength\", \"change\", 4, \"ngIf\"], [\"class\", \"form-control uppercase-select\", \"bindValue\", \"stateCode\", \"bindLabel\", \"stateName\", \"placeholder\", \"Select State\", \"formControlName\", \"state\", 3, \"virtualScroll\", \"items\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control uppercase-select\", \"bindValue\", \"countyName\", \"bindLabel\", \"countyName\", \"placeholder\", \"Select County\", \"formControlName\", \"county\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\", 4, \"ngIf\"], [\"class\", \"form-control uppercase-select\", \"bindValue\", \"name\", \"bindLabel\", \"name\", \"placeholder\", \"Select Country\", \"formControlName\", \"country\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\", 4, \"ngIf\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Zip Code\", \"minlength\", \"9\", \"maxlength\", \"9\", \"name\", \"zip\", \"id\", \"zip\", \"formControlName\", \"zipCode\", \"numbersOnly\", \"\", 1, \"form-control\", 3, \"ngClass\", \"blur\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"numbersOnly\", \"\", \"placeholder\", \"Phone Number\", \"minlength\", \"10\", \"maxlength\", \"10\", \"name\", \"phone\", \"id\", \"phone\", \"formControlName\", \"number\", 1, \"form-control\", 3, \"ngClass\"], [\"matInput\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"addressEffectiveDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"mat-datepicker-background\", 3, \"max\", \"matDatepicker\", \"ngClass\", \"click\"], [\"addressEffectiveDate\", \"\"], [\"matInput\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"addressTerminationDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"mat-datepicker-background\", 3, \"min\", \"matDatepicker\", \"ngClass\", \"click\"], [\"addressTerminationDate\", \"\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Patient Relationship\", \"formControlName\", \"relationshipName\", \"name\", \"PatientRelationship\", \"id\", \"pRelationship\", 1, \"form-control\"], [\"class\", \"form-control uppercase-select\", \"bindValue\", \"payerName\", \"bindLabel\", \"payerName\", \"placeholder\", \"Select Insurance Company\", \"formControlName\", \"insuranceCompanyName\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\", 4, \"ngIf\"], [\"class\", \"form-control uppercase-select\", \"bindValue\", \"planName\", \"bindLabel\", \"planName\", \"placeholder\", \"Select Plan Name\", \"formControlName\", \"planName\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"click\", \"change\", 4, \"ngIf\"], [\"matInput\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"planEffectiveDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"mat-datepicker-background\", 3, \"max\", \"matDatepicker\", \"ngClass\", \"click\"], [\"planEffectiveDate\", \"\"], [\"matInput\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"planTerminationDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"mat-datepicker-background\", 3, \"min\", \"matDatepicker\", \"ngClass\", \"click\"], [\"planTerminationDate\", \"\"], [\"class\", \"form-control uppercase-select\", \"bindValue\", \"name\", \"bindLabel\", \"name\", \"placeholder\", \"Select Account\", \"formControlName\", \"ipaName\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\", 4, \"ngIf\"], [\"class\", \"form-control uppercase-select\", \"bindValue\", \"fullName\", \"bindLabel\", \"fullName\", \"placeholder\", \"Select PCP Name\", \"formControlName\", \"pcpName\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"click\", \"change\", 4, \"ngIf\"], [\"matInput\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"pcpEffectiveDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"mat-datepicker-background\", 3, \"max\", \"matDatepicker\", \"ngClass\", \"click\"], [\"pcpEffectiveDate\", \"\"], [\"matInput\", \"\", \"autocomplete\", \"off\", \"formControlName\", \"pcpTerminationDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"mat-datepicker-background\", 3, \"min\", \"matDatepicker\", \"ngClass\", \"click\"], [\"pcpTerminationDate\", \"\"], [1, \"row\", \"menuitemschaild\", \"mt-8\", \"bottom-div\"], [1, \"col-md-6\"], [1, \"col-md-6\", \"text-align-R\"], [\"type\", \"button\", \"class\", \"btn btn-primary primary-btn btn-height\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn-white\", \"btn-common\", \"common-btn\", \"btn-height\", 3, \"click\"], [\"type\", \"button\", 1, \"btn-primary\", \"activity-button\", \"primary-btn\", \"btn-height\", 3, \"click\"], [1, \"fa\", \"fa-history\", \"icon-margin\"], [\"type\", \"button\", 1, \"btn-common-danger\", \"activity-button\", \"danger-btn\", \"btn-height\", 3, \"click\"], [3, \"memberDetails\"], [\"type\", \"text\", \"placeholder\", \"Subscriber Id\", \"name\", \"SubscriberId\", \"id\", \"SId\", \"formControlName\", \"subscriberID\", \"autocomplete\", \"off\", 1, \"form-control\", 3, \"ngClass\", \"blur\"], [1, \"invalid-feedback\", \"invalid-show\"], [1, \"form-control\"], [\"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Last Name\", \"name\", \"Lname\", \"id\", \"LastName\", \"autocomplete\", \"off\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"First Name\", \"name\", \"Fname\", \"id\", \"FirstName\", \"autocomplete\", \"off\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"middleName\", \"placeholder\", \"Middle Initial\", \"name\", \"mi\", \"id\", \"mi\", \"autocomplete\", \"off\", 1, \"form-control\"], [\"id\", \"Gender\", \"name\", \"gender\", \"formControlName\", \"gender\", \"aria-label\", \".form-select-sm example\", 1, \"form-select\", 3, \"ngClass\"], [\"hidden\", \"\", \"selected\", \"\", 1, \"Selected-Option\", 3, \"value\"], [4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"formControlName\", \"addressLine1\", \"placeholder\", \"Address Line 1\", \"name\", \"address1\", \"id\", \"address1\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"formControlName\", \"addressLine2\", \"placeholder\", \"Address Line 2\", \"name\", \"address2\", \"id\", \"address2\", 1, \"form-control\"], [\"bindValue\", \"cityName\", \"bindLabel\", \"cityName\", \"placeholder\", \"Type to search city (min 2 characters)\", \"formControlName\", \"city\", 1, \"form-control\", 3, \"items\", \"virtualScroll\", \"ngClass\", \"typeahead\", \"minTermLength\", \"change\"], [\"citySelect\", \"\"], [\"ng-option-tmp\", \"\"], [\"bindValue\", \"stateCode\", \"bindLabel\", \"stateName\", \"placeholder\", \"Select State\", \"formControlName\", \"state\", 1, \"form-control\", \"uppercase-select\", 3, \"virtualScroll\", \"items\", \"ngClass\"], [\"bindValue\", \"countyName\", \"bindLabel\", \"countyName\", \"placeholder\", \"Select County\", \"formControlName\", \"county\", 1, \"form-control\", \"uppercase-select\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"countySelect\", \"\"], [\"bindValue\", \"name\", \"bindLabel\", \"name\", \"placeholder\", \"Select Country\", \"formControlName\", \"country\", 1, \"form-control\", \"uppercase-select\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"countrySelect\", \"\"], [\"bindValue\", \"payerName\", \"bindLabel\", \"payerName\", \"placeholder\", \"Select Insurance Company\", \"formControlName\", \"insuranceCompanyName\", 1, \"form-control\", \"uppercase-select\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"insuranceCompanySelect\", \"\"], [\"bindValue\", \"planName\", \"bindLabel\", \"planName\", \"placeholder\", \"Select Plan Name\", \"formControlName\", \"planName\", 1, \"form-control\", \"uppercase-select\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"click\", \"change\"], [\"planNameSelect\", \"\"], [\"bindValue\", \"name\", \"bindLabel\", \"name\", \"placeholder\", \"Select Account\", \"formControlName\", \"ipaName\", 1, \"form-control\", \"uppercase-select\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"ipaSelect\", \"\"], [\"bindValue\", \"fullName\", \"bindLabel\", \"fullName\", \"placeholder\", \"Select PCP Name\", \"formControlName\", \"pcpName\", 1, \"form-control\", \"uppercase-select\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"click\", \"change\"], [\"pcpNameSelect\", \"\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"primary-btn\", \"btn-height\", 3, \"click\"], [1, \"fa\", \"fa-floppy-o\", \"icon-margin\"], [1, \"material-icons\", \"icon-margin\", \"icon-height\", \"icon-align\"]],\n    template: function AddMemberComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r113 = i0.ɵɵgetCurrentView();\n\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"label\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, AddMemberComponent_button_5_Template, 3, 0, \"button\", 4);\n        i0.ɵɵtemplate(6, AddMemberComponent_button_6_Template, 2, 0, \"button\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"form\", 6);\n        i0.ɵɵtemplate(8, AddMemberComponent_div_8_Template, 2, 1, \"div\", 7);\n        i0.ɵɵelementStart(9, \"div\", 8)(10, \"label\", 9);\n        i0.ɵɵtext(11, \" Member Basic Details \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"label\", 14);\n        i0.ɵɵtext(17, \" Subscriber Id* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(18, AddMemberComponent_input_18_Template, 1, 3, \"input\", 15);\n        i0.ɵɵtemplate(19, AddMemberComponent_div_19_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(20, AddMemberComponent_span_20_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 13)(22, \"label\", 14);\n        i0.ɵɵtext(23, \" Last Name* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(24, AddMemberComponent_input_24_Template, 1, 3, \"input\", 18);\n        i0.ɵɵtemplate(25, AddMemberComponent_div_25_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(26, AddMemberComponent_span_26_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"div\", 13)(28, \"label\", 14);\n        i0.ɵɵtext(29, \" First Name* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(30, AddMemberComponent_input_30_Template, 1, 3, \"input\", 19);\n        i0.ɵɵtemplate(31, AddMemberComponent_div_31_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(32, AddMemberComponent_span_32_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"div\", 13)(34, \"label\", 14);\n        i0.ɵɵtext(35, \" MI \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(36, AddMemberComponent_input_36_Template, 1, 0, \"input\", 20);\n        i0.ɵɵtemplate(37, AddMemberComponent_span_37_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"div\", 13)(39, \"label\", 14);\n        i0.ɵɵtext(40, \" Gender* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(41, AddMemberComponent_select_41_Template, 4, 5, \"select\", 21);\n        i0.ɵɵtemplate(42, AddMemberComponent_div_42_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(43, AddMemberComponent_span_43_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"div\", 13)(45, \"label\", 14);\n        i0.ɵɵtext(46, \" Date of Birth* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(47, \"input\", 22);\n        i0.ɵɵlistener(\"click\", function AddMemberComponent_Template_input_click_47_listener() {\n          i0.ɵɵrestoreView(_r113);\n\n          const _r17 = i0.ɵɵreference(49);\n\n          return i0.ɵɵresetView(_r17.open());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(48, \"mat-datepicker\", null, 23);\n        i0.ɵɵtemplate(50, AddMemberComponent_div_50_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(51, \"div\", 10)(52, \"div\", 11)(53, \"div\", 12)(54, \"div\", 13)(55, \"label\", 14);\n        i0.ɵɵtext(56, \" MBR Eff Date* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"input\", 24);\n        i0.ɵɵlistener(\"click\", function AddMemberComponent_Template_input_click_57_listener() {\n          i0.ɵɵrestoreView(_r113);\n\n          const _r19 = i0.ɵɵreference(59);\n\n          return i0.ɵɵresetView(_r19.open());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(58, \"mat-datepicker\", null, 25);\n        i0.ɵɵtemplate(60, AddMemberComponent_div_60_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"div\", 13)(62, \"label\", 14);\n        i0.ɵɵtext(63, \" MBR Term Date* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(64, \"input\", 26);\n        i0.ɵɵlistener(\"click\", function AddMemberComponent_Template_input_click_64_listener() {\n          i0.ɵɵrestoreView(_r113);\n\n          const _r21 = i0.ɵɵreference(66);\n\n          return i0.ɵɵresetView(_r21.open());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(65, \"mat-datepicker\", null, 27);\n        i0.ɵɵtemplate(67, AddMemberComponent_div_67_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(68, \"div\", 8)(69, \"label\", 9);\n        i0.ɵɵtext(70, \" Member Address Details \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(71, \"div\", 10)(72, \"div\", 11)(73, \"div\", 12)(74, \"div\", 28)(75, \"label\", 14);\n        i0.ɵɵtext(76, \" Address Line 1* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(77, AddMemberComponent_input_77_Template, 1, 3, \"input\", 29);\n        i0.ɵɵtemplate(78, AddMemberComponent_div_78_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(79, AddMemberComponent_span_79_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(80, \"div\", 28)(81, \"label\", 14);\n        i0.ɵɵtext(82, \" Address Line 2 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(83, AddMemberComponent_input_83_Template, 1, 0, \"input\", 30);\n        i0.ɵɵtemplate(84, AddMemberComponent_span_84_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(85, \"div\", 31)(86, \"label\", 14);\n        i0.ɵɵtext(87, \" City* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(88, AddMemberComponent_ng_select_88_Template, 3, 7, \"ng-select\", 32);\n        i0.ɵɵtemplate(89, AddMemberComponent_div_89_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(90, AddMemberComponent_span_90_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(91, \"div\", 13)(92, \"label\", 14);\n        i0.ɵɵtext(93, \" State* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(94, AddMemberComponent_ng_select_94_Template, 2, 5, \"ng-select\", 33);\n        i0.ɵɵtemplate(95, AddMemberComponent_div_95_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(96, AddMemberComponent_span_96_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(97, \"div\", 10)(98, \"div\", 11)(99, \"div\", 12)(100, \"div\", 28)(101, \"label\", 14);\n        i0.ɵɵtext(102, \" County* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(103, AddMemberComponent_ng_select_103_Template, 2, 5, \"ng-select\", 34);\n        i0.ɵɵtemplate(104, AddMemberComponent_div_104_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(105, AddMemberComponent_span_105_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(106, \"div\", 28)(107, \"label\", 14);\n        i0.ɵɵtext(108, \" Country* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(109, AddMemberComponent_ng_select_109_Template, 2, 5, \"ng-select\", 35);\n        i0.ɵɵtemplate(110, AddMemberComponent_div_110_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(111, AddMemberComponent_span_111_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(112, \"div\", 13)(113, \"label\", 14);\n        i0.ɵɵtext(114, \" Zip Code* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(115, \"input\", 36);\n        i0.ɵɵlistener(\"blur\", function AddMemberComponent_Template_input_blur_115_listener($event) {\n          return ctx.zipCodeOnBlur($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(116, AddMemberComponent_div_116_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(117, AddMemberComponent_div_117_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(118, \"div\", 13)(119, \"label\", 14);\n        i0.ɵɵtext(120, \" Phone Number* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(121, \"input\", 37);\n        i0.ɵɵtemplate(122, AddMemberComponent_div_122_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(123, AddMemberComponent_div_123_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(124, \"div\", 13)(125, \"label\", 14);\n        i0.ɵɵtext(126, \" Addr Eff Date* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(127, \"input\", 38);\n        i0.ɵɵlistener(\"click\", function AddMemberComponent_Template_input_click_127_listener() {\n          i0.ɵɵrestoreView(_r113);\n\n          const _r44 = i0.ɵɵreference(129);\n\n          return i0.ɵɵresetView(_r44.open());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(128, \"mat-datepicker\", null, 39);\n        i0.ɵɵtemplate(130, AddMemberComponent_div_130_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(131, \"div\", 10)(132, \"div\", 11)(133, \"div\", 12)(134, \"div\", 13)(135, \"label\", 14);\n        i0.ɵɵtext(136, \" Addr Term Date* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(137, \"input\", 40);\n        i0.ɵɵlistener(\"click\", function AddMemberComponent_Template_input_click_137_listener() {\n          i0.ɵɵrestoreView(_r113);\n\n          const _r46 = i0.ɵɵreference(139);\n\n          return i0.ɵɵresetView(_r46.open());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(138, \"mat-datepicker\", null, 41);\n        i0.ɵɵtemplate(140, AddMemberComponent_div_140_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(141, \"div\", 8)(142, \"label\", 9);\n        i0.ɵɵtext(143, \" Insurance Details \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(144, \"div\", 10)(145, \"div\", 11)(146, \"div\", 12)(147, \"div\", 13)(148, \"label\", 14);\n        i0.ɵɵtext(149, \" Patient Relationship* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(150, \"input\", 42);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(151, \"div\", 28)(152, \"label\", 14);\n        i0.ɵɵtext(153, \" Insurance Company* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(154, AddMemberComponent_ng_select_154_Template, 2, 5, \"ng-select\", 43);\n        i0.ɵɵtemplate(155, AddMemberComponent_div_155_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(156, AddMemberComponent_span_156_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(157, \"div\", 28)(158, \"label\", 14);\n        i0.ɵɵtext(159, \" Plan Name* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(160, AddMemberComponent_ng_select_160_Template, 2, 5, \"ng-select\", 44);\n        i0.ɵɵtemplate(161, AddMemberComponent_div_161_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(162, AddMemberComponent_span_162_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(163, \"div\", 13)(164, \"label\", 14);\n        i0.ɵɵtext(165, \" Plan Eff Date* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(166, \"input\", 45);\n        i0.ɵɵlistener(\"click\", function AddMemberComponent_Template_input_click_166_listener() {\n          i0.ɵɵrestoreView(_r113);\n\n          const _r54 = i0.ɵɵreference(168);\n\n          return i0.ɵɵresetView(_r54.open());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(167, \"mat-datepicker\", null, 46);\n        i0.ɵɵtemplate(169, AddMemberComponent_div_169_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(170, \"div\", 13)(171, \"label\", 14);\n        i0.ɵɵtext(172, \" Plan Term Date* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(173, \"input\", 47);\n        i0.ɵɵlistener(\"click\", function AddMemberComponent_Template_input_click_173_listener() {\n          i0.ɵɵrestoreView(_r113);\n\n          const _r56 = i0.ɵɵreference(175);\n\n          return i0.ɵɵresetView(_r56.open());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(174, \"mat-datepicker\", null, 48);\n        i0.ɵɵtemplate(176, AddMemberComponent_div_176_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(177, \"div\", 8)(178, \"label\", 9);\n        i0.ɵɵtext(179, \" PCP Details \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(180, \"div\", 10)(181, \"div\", 11)(182, \"div\", 12)(183, \"div\", 31)(184, \"label\", 14);\n        i0.ɵɵtext(185, \" IPA Name* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(186, AddMemberComponent_ng_select_186_Template, 2, 5, \"ng-select\", 49);\n        i0.ɵɵtemplate(187, AddMemberComponent_div_187_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(188, AddMemberComponent_span_188_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(189, \"div\", 31)(190, \"label\", 14);\n        i0.ɵɵtext(191, \" PCP Name* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(192, AddMemberComponent_ng_select_192_Template, 2, 5, \"ng-select\", 50);\n        i0.ɵɵtemplate(193, AddMemberComponent_div_193_Template, 2, 0, \"div\", 16);\n        i0.ɵɵtemplate(194, AddMemberComponent_span_194_Template, 2, 1, \"span\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(195, \"div\", 13)(196, \"label\", 14);\n        i0.ɵɵtext(197, \" PCP Eff From* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(198, \"input\", 51);\n        i0.ɵɵlistener(\"click\", function AddMemberComponent_Template_input_click_198_listener() {\n          i0.ɵɵrestoreView(_r113);\n\n          const _r64 = i0.ɵɵreference(200);\n\n          return i0.ɵɵresetView(_r64.open());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(199, \"mat-datepicker\", null, 52);\n        i0.ɵɵtemplate(201, AddMemberComponent_div_201_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(202, \"div\", 13)(203, \"label\", 14);\n        i0.ɵɵtext(204, \" PCP Eff To* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(205, \"input\", 53);\n        i0.ɵɵlistener(\"click\", function AddMemberComponent_Template_input_click_205_listener() {\n          i0.ɵɵrestoreView(_r113);\n\n          const _r66 = i0.ɵɵreference(207);\n\n          return i0.ɵɵresetView(_r66.open());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(206, \"mat-datepicker\", null, 54);\n        i0.ɵɵtemplate(208, AddMemberComponent_div_208_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(209, \"div\", 55);\n        i0.ɵɵelement(210, \"div\", 56);\n        i0.ɵɵelementStart(211, \"div\", 57);\n        i0.ɵɵtemplate(212, AddMemberComponent_button_212_Template, 3, 0, \"button\", 58);\n        i0.ɵɵtemplate(213, AddMemberComponent_button_213_Template, 4, 0, \"button\", 58);\n        i0.ɵɵtemplate(214, AddMemberComponent_button_214_Template, 4, 0, \"button\", 58);\n        i0.ɵɵelementStart(215, \"button\", 59);\n        i0.ɵɵlistener(\"click\", function AddMemberComponent_Template_button_click_215_listener() {\n          return ctx.cancel();\n        });\n        i0.ɵɵtext(216, \"Cancel\");\n        i0.ɵɵelementEnd()()()()()();\n      }\n\n      if (rf & 2) {\n        const _r17 = i0.ɵɵreference(49);\n\n        const _r19 = i0.ɵɵreference(59);\n\n        const _r21 = i0.ɵɵreference(66);\n\n        const _r44 = i0.ɵɵreference(129);\n\n        const _r46 = i0.ɵɵreference(139);\n\n        const _r54 = i0.ɵɵreference(168);\n\n        const _r56 = i0.ɵɵreference(175);\n\n        const _r64 = i0.ɵɵreference(200);\n\n        const _r66 = i0.ɵɵreference(207);\n\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(94, _c1, ctx.mode == \"view\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\"\", ctx.mainHeading, \" \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"add\" && !ctx.showActivityLog && !!ctx.memberDataForView);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"add\" && ctx.showActivityLog);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"formGroup\", ctx.addForm);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showActivityLog);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f[\"subscriberID\"] == null ? null : ctx.f[\"subscriberID\"].invalid) && (ctx.f[\"subscriberID\"].dirty || ctx.f[\"subscriberID\"].touched) && ctx.f[\"subscriberID\"].errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.lastName == null ? null : ctx.f.lastName.invalid) && (ctx.f.lastName.dirty || ctx.f.lastName.touched) && ctx.f.lastName.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.firstName == null ? null : ctx.f.firstName.invalid) && (ctx.f.firstName.dirty || ctx.f.firstName.touched) && ctx.f.firstName.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.gender == null ? null : ctx.f.gender.invalid) && (ctx.f.gender.dirty || ctx.f.gender.touched) && ctx.f.gender.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"max\", ctx.today)(\"matDatepicker\", _r17)(\"ngClass\", i0.ɵɵpureFunction1(96, _c0, ctx.f[\"dateOfBirth\"].invalid && (ctx.f[\"dateOfBirth\"].dirty || ctx.f[\"dateOfBirth\"].touched) && ctx.f[\"dateOfBirth\"].errors));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.dateOfBirth == null ? null : ctx.f.dateOfBirth.invalid) && (ctx.f.dateOfBirth.dirty || ctx.f.dateOfBirth.touched) && ctx.f.dateOfBirth.errors);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"max\", ctx.getSmallerDate(ctx.f[\"personTerminationDate\"].value))(\"matDatepicker\", _r19)(\"ngClass\", i0.ɵɵpureFunction1(98, _c0, ctx.f[\"personEffectiveDate\"].invalid && (ctx.f[\"personEffectiveDate\"].dirty || ctx.f[\"personEffectiveDate\"].touched) && ctx.f[\"personEffectiveDate\"].errors));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.personEffectiveDate == null ? null : ctx.f.personEffectiveDate.invalid) && (ctx.f.personEffectiveDate.dirty || ctx.f.personEffectiveDate.touched) && ctx.f.personEffectiveDate.errors);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"min\", ctx.f[\"personEffectiveDate\"].value)(\"matDatepicker\", _r21)(\"ngClass\", i0.ɵɵpureFunction1(100, _c0, ctx.f[\"personTerminationDate\"].invalid && (ctx.f[\"personTerminationDate\"].dirty || ctx.f[\"personTerminationDate\"].touched) && ctx.f[\"personTerminationDate\"].errors));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.personTerminationDate == null ? null : ctx.f.personTerminationDate.invalid) && (ctx.f.personTerminationDate.dirty || ctx.f.personTerminationDate.touched) && ctx.f.personTerminationDate.errors);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.addressLine1 == null ? null : ctx.f.addressLine1.invalid) && (ctx.f.addressLine1.dirty || ctx.f.addressLine1.touched) && ctx.f.addressLine1.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.city == null ? null : ctx.f.city.invalid) && (ctx.f.city.dirty || ctx.f.city.touched) && ctx.f.city.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.state == null ? null : ctx.f.state.invalid) && (ctx.f.state.dirty || ctx.f.state.touched) && ctx.f.state.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.county == null ? null : ctx.f.county.invalid) && (ctx.f.county.dirty || ctx.f.county.touched) && ctx.f.county.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.country == null ? null : ctx.f.country.invalid) && (ctx.f.country.dirty || ctx.f.country.touched) && ctx.f.country.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(102, _c0, ctx.f[\"zipCode\"].invalid && (ctx.f[\"zipCode\"].dirty || ctx.f[\"zipCode\"].touched) && ctx.f[\"zipCode\"].errors));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.zipCode == null ? null : ctx.f.zipCode.invalid) && (ctx.f.zipCode.dirty || ctx.f.zipCode.touched) && ctx.f.zipCode.errors.required);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.zipCode == null ? null : ctx.f.zipCode.invalid) && (ctx.f.zipCode.dirty || ctx.f.zipCode.touched) && !ctx.f.zipCode.errors.required && ctx.f.zipCode.errors);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(104, _c0, ctx.f[\"number\"].invalid && (ctx.f[\"number\"].dirty || ctx.f[\"number\"].touched) && ctx.f[\"number\"].errors));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.number == null ? null : ctx.f.number.invalid) && (ctx.f.number.dirty || ctx.f.number.touched) && ctx.f.number.errors.required && ctx.f.number.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.number == null ? null : ctx.f.number.invalid) && (ctx.f.number.dirty || ctx.f.number.touched) && !ctx.f.number.errors.required && ctx.f.number.errors);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"max\", ctx.getSmallerDate(ctx.f[\"addressTerminationDate\"].value))(\"matDatepicker\", _r44)(\"ngClass\", i0.ɵɵpureFunction1(106, _c0, ctx.f[\"addressEffectiveDate\"].invalid && (ctx.f[\"addressEffectiveDate\"].dirty || ctx.f[\"addressEffectiveDate\"].touched) && ctx.f[\"addressEffectiveDate\"].errors));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.addressEffectiveDate == null ? null : ctx.f.addressEffectiveDate.invalid) && (ctx.f.addressEffectiveDate.dirty || ctx.f.addressEffectiveDate.touched) && ctx.f.addressEffectiveDate.errors.required && ctx.f.addressEffectiveDate.errors);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"min\", ctx.f[\"addressEffectiveDate\"].value)(\"matDatepicker\", _r46)(\"ngClass\", i0.ɵɵpureFunction1(108, _c0, ctx.f[\"addressTerminationDate\"].invalid && (ctx.f[\"addressTerminationDate\"].dirty || ctx.f[\"addressTerminationDate\"].touched) && ctx.f[\"addressTerminationDate\"].errors));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.addressTerminationDate == null ? null : ctx.f.addressTerminationDate.invalid) && (ctx.f.addressTerminationDate.dirty || ctx.f.addressTerminationDate.touched) && ctx.f.addressTerminationDate.errors.required && ctx.f.addressTerminationDate.errors);\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.insuranceCompanyName == null ? null : ctx.f.insuranceCompanyName.invalid) && (ctx.f.insuranceCompanyName.dirty || ctx.f.insuranceCompanyName.touched) && ctx.f.insuranceCompanyName.errors.required && ctx.f.insuranceCompanyName.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.planName == null ? null : ctx.f.planName.invalid) && (ctx.f.planName.dirty || ctx.f.planName.touched) && ctx.f.planName.errors.required && ctx.f.planName.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"max\", ctx.getSmallerDate(ctx.f[\"planTerminationDate\"].value))(\"matDatepicker\", _r54)(\"ngClass\", i0.ɵɵpureFunction1(110, _c0, ctx.f[\"planEffectiveDate\"].invalid && (ctx.f[\"planEffectiveDate\"].dirty || ctx.f[\"planEffectiveDate\"].touched) && ctx.f[\"planEffectiveDate\"].errors));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.planEffectiveDate == null ? null : ctx.f.planEffectiveDate.invalid) && (ctx.f.planEffectiveDate.dirty || ctx.f.planEffectiveDate.touched) && ctx.f.planEffectiveDate.errors.required && ctx.f.planEffectiveDate.errors);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"min\", ctx.f[\"planEffectiveDate\"].value)(\"matDatepicker\", _r56)(\"ngClass\", i0.ɵɵpureFunction1(112, _c0, ctx.f[\"planTerminationDate\"].invalid && (ctx.f[\"planTerminationDate\"].dirty || ctx.f[\"planTerminationDate\"].touched) && ctx.f[\"planTerminationDate\"].errors));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.planTerminationDate == null ? null : ctx.f.planTerminationDate.invalid) && (ctx.f.planTerminationDate.dirty || ctx.f.planTerminationDate.touched) && ctx.f.planTerminationDate.errors.required && ctx.f.planTerminationDate.errors);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.ipaName == null ? null : ctx.f.ipaName.invalid) && (ctx.f.ipaName.dirty || ctx.f.ipaName.touched) && ctx.f.ipaName.errors.required && ctx.f.ipaName.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.pcpName == null ? null : ctx.f.pcpName.invalid) && (ctx.f.pcpName.dirty || ctx.f.pcpName.touched) && ctx.f.pcpName.errors.required && ctx.f.pcpName.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"view\" && !!ctx.memberDataForView);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"max\", ctx.getSmallerDate(ctx.f[\"pcpTerminationDate\"].value))(\"matDatepicker\", _r64)(\"ngClass\", i0.ɵɵpureFunction1(114, _c0, ctx.f[\"pcpEffectiveDate\"].invalid && (ctx.f[\"pcpEffectiveDate\"].dirty || ctx.f[\"pcpEffectiveDate\"].touched) && ctx.f[\"pcpEffectiveDate\"].errors));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.pcpEffectiveDate == null ? null : ctx.f.pcpEffectiveDate.invalid) && (ctx.f.pcpEffectiveDate.dirty || ctx.f.pcpEffectiveDate.touched) && ctx.f.pcpEffectiveDate.errors.required && ctx.f.pcpEffectiveDate.errors);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"min\", ctx.f[\"pcpEffectiveDate\"].value)(\"matDatepicker\", _r66)(\"ngClass\", i0.ɵɵpureFunction1(116, _c0, ctx.f[\"pcpTerminationDate\"].invalid && (ctx.f[\"pcpTerminationDate\"].dirty || ctx.f[\"pcpTerminationDate\"].touched) && ctx.f[\"pcpTerminationDate\"].errors));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.pcpTerminationDate == null ? null : ctx.f.pcpTerminationDate.invalid) && (ctx.f.pcpTerminationDate.dirty || ctx.f.pcpTerminationDate.touched) && ctx.f.pcpTerminationDate.errors.required && ctx.f.pcpTerminationDate.errors);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"add\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mode == \"edit\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isViewEditBtnShow && ctx.mode == \"view\");\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinLengthValidator, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i12.NgSelectComponent, i12.NgOptionTemplateDirective, i13.OnlyNumberDirective, i14.MatDatepicker, i14.MatDatepickerInput, i15.MatInput, i16.MemberActivityLogComponent],\n    styles: [\".mt-15[_ngcontent-%COMP%]{margin-top:15px}.mt-8[_ngcontent-%COMP%]{margin-top:8px}.wdt-35[_ngcontent-%COMP%]{width:35px!important}.mt-26[_ngcontent-%COMP%]{margin-top:26px}.text-align-R[_ngcontent-%COMP%]{text-align:right}.btn-white[_ngcontent-%COMP%]{padding:0 10px 0 8px!important}.f-size16[_ngcontent-%COMP%]{font-size:16px!important}.f-size13[_ngcontent-%COMP%]{font-size:13px!important}.internal-div[_ngcontent-%COMP%]{background:#ECF4FC 0% 0% no-repeat padding-box;border:1px solid #ECF4FC;border-radius:6px;opacity:1;padding:20px 20px 20px 0;font-family:Poppins-SemiBold;margin:15px 15px 10px}.bottom-div[_ngcontent-%COMP%]{margin-bottom:10px;padding:2px 11px 10px 0}.form-select[_ngcontent-%COMP%]{cursor:pointer}.btn-sm[_ngcontent-%COMP%]{font-size:1rem}[_ngcontent-%COMP%]::placeholder{color:#789!important}button[_ngcontent-%COMP%]:disabled{cursor:auto!important}.add-member[_ngcontent-%COMP%]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{white-space:break-spaces!important}.add-member[_ngcontent-%COMP%]     .ng-select-container{height:100%!important}.dashboard-content[_ngcontent-%COMP%]{font-weight:600;padding:2px 24px}.form-control[_ngcontent-%COMP%]{height:2.36rem!important}.view-form[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], .view-form[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]{background-color:transparent!important;border:none!important;padding:0!important;font-style:italic!important;pointer-events:none!important;text-transform:uppercase;word-wrap:break-word}.view-form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:transparent!important}.view-form[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]::placeholder{color:transparent!important}.view-form[_ngcontent-%COMP%]   input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent}.view-form[_ngcontent-%COMP%]     .mat-datepicker-input{color:#000!important}.view-form[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{height:unset!important}.activity-button[_ngcontent-%COMP%]{float:right;margin-top:12px;margin-right:15px}.attachment-form-control[_ngcontent-%COMP%]{background-color:transparent!important;border:none!important;padding:0!important;pointer-events:none!important}.danger-btn[_ngcontent-%COMP%]:active{border-bottom:0px;padding-top:4px;color:red!important}.danger-btn[_ngcontent-%COMP%]{border-bottom:2px solid #b90202;outline:none!important}.danger-btn[_ngcontent-%COMP%]:hover{background:#b90202;border:0;color:#fff!important}.danger-btn[_ngcontent-%COMP%]:focus{outline:none!important;color:red!important}.mat-datepicker-background[_ngcontent-%COMP%]{background:white}  .uppercase-select .ng-value-label{text-transform:uppercase}  .uppercase-select .ng-option-label{text-transform:uppercase}.invalid-show[_ngcontent-%COMP%]{display:block}\"]\n  });\n  return AddMemberComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}