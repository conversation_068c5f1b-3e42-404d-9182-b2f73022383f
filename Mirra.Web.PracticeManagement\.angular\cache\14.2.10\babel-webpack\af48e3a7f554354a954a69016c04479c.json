{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AgGridModule } from 'ag-grid-angular';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { DispatchClaimService } from 'src/app/services/claim-buckets/dispatch-claim.service';\nimport { ClaimsTrackingService } from 'src/app/services/claims-tracking/claims-tracking.service';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { NotificationService } from 'src/app/services/Notification/notification.service';\nimport { ClaimReportServiceService } from 'src/app/services/Dashboard/claim-report-service.service';\nimport { ClaimService } from 'src/app/services/ClaimForm/claim.service';\nimport { PopoverModule } from 'ngx-bootstrap/popover';\nimport { CustomDateFilterModule } from 'src/app/shared/components/custom-date-filter/custom-date-filter.module';\nimport * as i0 from \"@angular/core\"; // Ensure the path is correct; adjust if necessary\n\nexport let ClaimStatusModule = /*#__PURE__*/(() => {\n  class ClaimStatusModule {}\n\n  ClaimStatusModule.ɵfac = function ClaimStatusModule_Factory(t) {\n    return new (t || ClaimStatusModule)();\n  };\n\n  ClaimStatusModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ClaimStatusModule\n  });\n  ClaimStatusModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [DispatchClaimService, ClaimsTrackingService, NotificationService, ClaimReportServiceService, ClaimService],\n    imports: [ReactiveFormsModule, CommonModule, AgGridModule, NgSelectModule, MatTooltipModule, MatDialogModule, PopoverModule, CustomDateFilterModule]\n  });\n  return ClaimStatusModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}