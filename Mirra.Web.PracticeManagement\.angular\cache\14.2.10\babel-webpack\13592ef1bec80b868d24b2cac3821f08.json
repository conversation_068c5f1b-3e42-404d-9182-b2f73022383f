{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { Member } from 'src/app/classmodels/Member/MemberProfile';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport { zipCodeValidatorWithoutRequired } from 'src/app/shared/functions/customFormValidators';\nimport { stateSearch } from 'src/app/shared/functions/statefunction';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/shared/services/subject.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ng-select/ng-select\";\nimport * as i5 from \"../../../../../shared/directives/numbers-only.directive\";\nimport * as i6 from \"../../../../../shared/directives/address-related-characters.directive\";\n\nfunction PatientAddressComponent_input_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 21);\n  }\n}\n\nfunction PatientAddressComponent_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r1.f.address1.value && ctx_r1.f.address1.value.length > 0 ? ctx_r1.f.address1.value : \"-\");\n  }\n}\n\nfunction PatientAddressComponent_input_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 23);\n  }\n}\n\nfunction PatientAddressComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r3.f.address2.value && ctx_r3.f.address2.value.length > 0 ? ctx_r3.f.address2.value : \"-\");\n  }\n}\n\nfunction PatientAddressComponent_input_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 24);\n  }\n}\n\nfunction PatientAddressComponent_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r5.f.city.value && ctx_r5.f.city.value.length > 0 ? ctx_r5.f.city.value : \"-\");\n  }\n}\n\nfunction PatientAddressComponent_ng_select_37_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"uppercase\");\n    i0.ɵɵpipe(2, \"uppercase\");\n  }\n\n  if (rf & 2) {\n    const item_r16 = ctx.item;\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(1, 2, item_r16.stateCode), \"-\", i0.ɵɵpipeBind1(2, 4, item_r16.stateName), \" \");\n  }\n}\n\nfunction PatientAddressComponent_ng_select_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-select\", 25, 26);\n    i0.ɵɵtemplate(2, PatientAddressComponent_ng_select_37_ng_template_2_Template, 3, 6, \"ng-template\", 27);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"items\", ctx_r6.allStateData)(\"searchFn\", ctx_r6.stateSearch);\n  }\n}\n\nfunction PatientAddressComponent_span_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r7.f.state.value && ctx_r7.f.state.value.length > 0 ? ctx_r7.f.state.value : \"-\");\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction PatientAddressComponent_input_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 28);\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, (ctx_r8.f.zip == null ? null : ctx_r8.f.zip.invalid) && (ctx_r8.f.zip == null ? null : ctx_r8.f.zip.errors)));\n  }\n}\n\nfunction PatientAddressComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \" Invalid Zip Code. Please enter only 9 digit number. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction PatientAddressComponent_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r10.f.zip.value && ctx_r10.f.zip.value.length > 0 ? ctx_r10.f.zip.value : \"-\");\n  }\n}\n\nfunction PatientAddressComponent_input_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 30);\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, (ctx_r11.f.telephone == null ? null : ctx_r11.f.telephone.invalid) && (ctx_r11.f.telephone == null ? null : ctx_r11.f.telephone.errors)));\n  }\n}\n\nfunction PatientAddressComponent_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r12.f.telephone.value && ctx_r12.f.telephone.value.length > 0 ? ctx_r12.f.telephone.value : \"-\");\n  }\n}\n\nfunction PatientAddressComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \" Invalid Telephone. Please enter only 10 digit number. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport let PatientAddressComponent = /*#__PURE__*/(() => {\n  class PatientAddressComponent {\n    constructor(payerform, subjectService) {\n      this.payerform = payerform;\n      this.subjectService = subjectService;\n      this.checkedCopy = false;\n      this.getInsureData = new EventEmitter();\n      this.subjectService.getInsureAddress().subscribe(res => {\n        if (res) {\n          if (!res.isCopyInsure) {\n            this.patientAddressInfo.patchValue({\n              address1: res.n301SubscriberAddr1,\n              address2: res.n302SubscriberAddr2,\n              city: res.n401SubscriberCity,\n              state: res.n402SubscriberState,\n              zip: res.n403SubscriberZip,\n              telephone: res.per04SubscriberPhoneNo\n            });\n\n            if (!!this.patientAddressInfo.controls.zip.value) {\n              this.patientAddressInfo.controls.zip.setValue(this.patientAddressInfo.controls.zip.value.trim().replace(/[^0-9]/g, ''));\n            }\n\n            if (!!this.patientAddressInfo.controls.telephone.value) {\n              this.patientAddressInfo.controls.telephone.setValue(this.patientAddressInfo.controls.telephone.value.trim().replace(/[^a-zA-Z0-9]/g, ''));\n            }\n\n            if (!!this.patientAddressInfo.controls.address1.value) {\n              this.patientAddressInfo.controls.address1.setValue(this.patientAddressInfo.controls.address1.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));\n            }\n\n            if (!!this.patientAddressInfo.controls.address2.value) {\n              this.patientAddressInfo.controls.address2.setValue(this.patientAddressInfo.controls.address2.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));\n            }\n\n            if (!!this.patientAddressInfo.controls.city.value) {\n              this.patientAddressInfo.controls.city.setValue(this.patientAddressInfo.controls.city.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));\n            }\n          } else {}\n\n          submitValidateAllFields.validateDisable(this.patientAddressInfo, [\"address1\", \"city\", \"state\", \"zip\", \"telephone\"]);\n          this.subjectService.resetInsureAddress();\n        }\n      });\n    }\n\n    ngOnInit() {\n      this.patchValue();\n    }\n\n    get f() {\n      return this.patientAddressInfo.controls;\n    }\n\n    createForm() {\n      this.patientAddressInfo = this.payerform.group({\n        address1: new FormControl(''),\n        address2: new FormControl(''),\n        city: new FormControl(''),\n        state: new FormControl(''),\n        zip: new FormControl('', zipCodeValidatorWithoutRequired),\n        telephone: new FormControl(''),\n        copyFromInsure: new FormControl('false')\n      });\n      return this.patientAddressInfo;\n    }\n\n    patchValue() {\n      if (this.claimFormData.isAddClaim) {\n        this.patientAddressInfo.patchValue({\n          address1: this.memberResult?.addressLine1,\n          address2: this.memberResult?.addressLine2,\n          city: this.memberResult?.city,\n          state: this.memberResult?.stateSubdivisionCode,\n          zip: this.memberResult?.zipCode,\n          telephone: this.memberResult?.number,\n          copyFromInsure: false\n        });\n        submitValidateAllFields.validateDisableControl(this.patientAddressInfo, [\"copyFromInsure\"]);\n      } else if (this.claimFormData.isEditClaim || this.claimFormData.isViewClaim) {\n        this.patientAddressInfo.patchValue({\n          address1: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n301PatientAddr1,\n          address2: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n302PatientAddr2,\n          city: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n401PatientCity,\n          state: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n402PatientState,\n          zip: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n403PatientZip,\n          telephone: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.per04PatientPhoneNo,\n          copyFromInsure: false\n        });\n      }\n\n      this.patientAddressInfo.disable();\n\n      if (!!this.patientAddressInfo.controls.zip.value) {\n        this.patientAddressInfo.controls.zip.setValue(this.patientAddressInfo.controls.zip.value.trim().replace(/[^0-9]/g, ''));\n      }\n\n      if (!!this.patientAddressInfo.controls.telephone.value) {\n        this.patientAddressInfo.controls.telephone.setValue(this.patientAddressInfo.controls.telephone.value.trim().replace(/[^a-zA-Z0-9]/g, ''));\n      }\n\n      if (!!this.patientAddressInfo.controls.address1.value) {\n        this.patientAddressInfo.controls.address1.setValue(this.patientAddressInfo.controls.address1.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));\n      }\n\n      if (!!this.patientAddressInfo.controls.address2.value) {\n        this.patientAddressInfo.controls.address2.setValue(this.patientAddressInfo.controls.address2.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));\n      }\n\n      if (!!this.patientAddressInfo.controls.city.value) {\n        this.patientAddressInfo.controls.city.setValue(this.patientAddressInfo.controls.city.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));\n      }\n    }\n\n    copyData(e) {\n      if (e.srcElement.checked) {\n        this.getInsureData.emit(false);\n      } else {\n        this.patientAddressInfo.patchValue({\n          address1: '',\n          address2: '',\n          city: '',\n          state: null,\n          zip: '',\n          telephone: ''\n        });\n        this.getInsureData.emit(true);\n      }\n    }\n\n    validateForm() {\n      if (this.patientAddressInfo.invalid) {\n        submitValidateAllFields.validateAllFields(this.patientAddressInfo);\n        return false;\n      }\n\n      return true;\n    }\n\n    stateSearch(term, item) {\n      return stateSearch.StateSearch(term, item);\n    }\n\n  }\n\n  PatientAddressComponent.ɵfac = function PatientAddressComponent_Factory(t) {\n    return new (t || PatientAddressComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SubjectService));\n  };\n\n  PatientAddressComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PatientAddressComponent,\n    selectors: [[\"app-patient-address\"]],\n    inputs: {\n      memberResult: \"memberResult\",\n      claimFormData: \"claimFormData\",\n      allStateData: \"allStateData\"\n    },\n    outputs: {\n      getInsureData: \"getInsureData\"\n    },\n    decls: 49,\n    vars: 15,\n    consts: [[1, \"pat-addr-info-form\", 3, \"formGroup\"], [1, \"5\", \"bd\"], [1, \"row\", \"mt-2\"], [1, \"form-title\"], [1, \"col-4\"], [\"for\", \"PatientStreetNumber\", 1, \"create-claims-labels\"], [\"for\", \"PatientAddressLine2\", 1, \"create-claims-labels\"], [\"for\", \"PatientCity\", 1, \"create-claims-labels\"], [1, \"row\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"address1\", \"addressRelatedCharacters\", \"\", \"name\", \"Patient street number\", \"id\", \"PatientStreetNumber\", \"placeholder\", \"Address\", 4, \"ngIf\"], [\"class\", \"form-control\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"address2\", \"addressRelatedCharacters\", \"\", \"id\", \"PatientAddressLine2\", \"placeholder\", \"Address\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"city\", \"addressRelatedCharacters\", \"\", \"class\", \"form-control Value-Inside-Input\", \"id\", \"PatientCity\", \"name\", \"Patient city\", \"placeholder\", \"Address\", 4, \"ngIf\"], [\"for\", \"PatientState\", 1, \"create-claims-labels\"], [\"for\", \"PatientZip\", 1, \"create-claims-labels\"], [\"for\", \"PatientTelephone\", 1, \"create-claims-labels\"], [\"class\", \"form-control form-control-sm\", \"placeholder\", \"State\", \"bindLabel\", \"stateName\", \"bindValue\", \"stateCode\", \"formControlName\", \"state\", 3, \"items\", \"searchFn\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"zip\", \"id\", \"PatientZip\", \"numbersOnly\", \"\", \"minlength\", \"9\", \"maxlength\", \"9\", \"placeholder\", \"Zip\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"telephone\", \"minlength\", \"10\", \"maxlength\", \"10\", \"id\", \"PatientTelephone\", \"placeholder\", \"Telephone\", \"numbersOnly\", \"\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"col-12\", 2, \"font-size\", \"12px\"], [\"type\", \"text\", \"formControlName\", \"address1\", \"addressRelatedCharacters\", \"\", \"name\", \"Patient street number\", \"id\", \"PatientStreetNumber\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\"], [1, \"form-control\"], [\"type\", \"text\", \"formControlName\", \"address2\", \"addressRelatedCharacters\", \"\", \"id\", \"PatientAddressLine2\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"city\", \"addressRelatedCharacters\", \"\", \"id\", \"PatientCity\", \"name\", \"Patient city\", \"placeholder\", \"Address\", 1, \"form-control\", \"Value-Inside-Input\"], [\"placeholder\", \"State\", \"bindLabel\", \"stateName\", \"bindValue\", \"stateCode\", \"formControlName\", \"state\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"searchFn\"], [\"assignto\", \"\"], [\"ng-option-tmp\", \"\", \"ng-label-tmp\", \"\"], [\"type\", \"text\", \"formControlName\", \"zip\", \"id\", \"PatientZip\", \"numbersOnly\", \"\", \"minlength\", \"9\", \"maxlength\", \"9\", \"placeholder\", \"Zip\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [1, \"invalid-feedback\"], [\"type\", \"text\", \"formControlName\", \"telephone\", \"minlength\", \"10\", \"maxlength\", \"10\", \"id\", \"PatientTelephone\", \"placeholder\", \"Telephone\", \"numbersOnly\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"]],\n    template: function PatientAddressComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0)(1, \"section\", 1)(2, \"div\", 2)(3, \"p\", 3);\n        i0.ɵɵtext(4, \"5. Patient Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"div\", 2)(6, \"div\", 4)(7, \"label\", 5);\n        i0.ɵɵtext(8, \"Address Line - 1 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 4)(10, \"label\", 6);\n        i0.ɵɵtext(11, \"Address Line - 2\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 4)(13, \"label\", 7);\n        i0.ɵɵtext(14, \"City\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(15, \"div\", 8)(16, \"div\", 4);\n        i0.ɵɵtemplate(17, PatientAddressComponent_input_17_Template, 1, 0, \"input\", 9);\n        i0.ɵɵtemplate(18, PatientAddressComponent_span_18_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 4);\n        i0.ɵɵtemplate(20, PatientAddressComponent_input_20_Template, 1, 0, \"input\", 11);\n        i0.ɵɵtemplate(21, PatientAddressComponent_span_21_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 4);\n        i0.ɵɵtemplate(23, PatientAddressComponent_input_23_Template, 1, 0, \"input\", 12);\n        i0.ɵɵtemplate(24, PatientAddressComponent_span_24_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(25, \"div\", 2)(26, \"div\", 4)(27, \"label\", 13);\n        i0.ɵɵtext(28, \"State \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(29, \"div\", 4)(30, \"label\", 14);\n        i0.ɵɵtext(31, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 4)(33, \"label\", 15);\n        i0.ɵɵtext(34, \"Telephone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(35, \"div\", 8)(36, \"div\", 4);\n        i0.ɵɵtemplate(37, PatientAddressComponent_ng_select_37_Template, 3, 2, \"ng-select\", 16);\n        i0.ɵɵtemplate(38, PatientAddressComponent_span_38_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"div\", 4);\n        i0.ɵɵtemplate(40, PatientAddressComponent_input_40_Template, 1, 3, \"input\", 17);\n        i0.ɵɵtemplate(41, PatientAddressComponent_div_41_Template, 2, 0, \"div\", 18);\n        i0.ɵɵtemplate(42, PatientAddressComponent_span_42_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"div\", 4);\n        i0.ɵɵtemplate(44, PatientAddressComponent_input_44_Template, 1, 3, \"input\", 19);\n        i0.ɵɵtemplate(45, PatientAddressComponent_span_45_Template, 2, 1, \"span\", 10);\n        i0.ɵɵtemplate(46, PatientAddressComponent_div_46_Template, 2, 0, \"div\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(47, \"div\", 2);\n        i0.ɵɵelement(48, \"div\", 20);\n        i0.ɵɵelementEnd()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.patientAddressInfo);\n        i0.ɵɵadvance(17);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.zip == null ? null : ctx.f.zip.invalid) && ctx.f.zip.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.telephone == null ? null : ctx.f.telephone.invalid) && ctx.f.telephone.errors);\n      }\n    },\n    dependencies: [i3.NgClass, i3.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinLengthValidator, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i4.NgSelectComponent, i4.NgOptionTemplateDirective, i4.NgLabelTemplateDirective, i5.OnlyNumberDirective, i6.AddressRelatedCharactersDirective, i3.UpperCasePipe],\n    styles: [\".create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.search-icon-alignment[_ngcontent-%COMP%]{position:relative}.search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%], .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}.pat-addr-info-form[_ngcontent-%COMP%]     .ng-select-container{height:100%!important}\"]\n  });\n  return PatientAddressComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}