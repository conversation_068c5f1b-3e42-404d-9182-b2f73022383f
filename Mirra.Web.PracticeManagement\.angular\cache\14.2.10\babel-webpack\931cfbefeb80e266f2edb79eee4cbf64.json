{"ast": null, "code": "import { formatDate } from '@angular/common';\nimport { ChangeDetectorRef, ElementRef } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { MatDialogConfig } from '@angular/material/dialog';\nimport { Observable, of, Subject } from 'rxjs';\nimport { debounceTime, first, map, startWith } from 'rxjs/operators';\nimport { InsuredIdModalComponent } from 'src/app/modals/insured-id-modal/insured-id-modal.component';\nimport { ReferingProviderModalComponent } from 'src/app/modals/refering-provider-modal/refering-provider-modal.component';\nimport { SaveModalComponent } from 'src/app/modals/save-modal/save-modal.component';\nimport { AddressMismatchModalComponent } from 'src/app/modals/address-mismatch-modal/address-mismatch-modal.component';\nimport { ErrorMessagePopUpComponent } from 'src/app/modals/error-message-pop-up/error-message-pop-up.component';\nimport { UserPermissionEnum } from 'src/app/classmodels/Enums/PermissionEnum';\nimport { SearchInsuredPopupComponent } from '../popups/search-insured-popup/search-insured-popup.component';\nimport { BillingProviderPopupComponent } from '../popups/billing-provider-popup/billing-provider-popup.component';\nimport { RenderingProviderPopupComponent } from '../rendering-provider-popup/rendering-provider-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/BillingProvider/billing-provider.service\";\nimport * as i2 from \"src/app/services/ReferringProvider/referring-provider.service\";\nimport * as i3 from \"src/app/services/Shared/user-data.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"src/app/services/ProviderManagement/provider-management.service\";\nimport * as i6 from \"src/app/services/ClaimForm/PreviewClaimForm/get-claim-info-by-id.service\";\nimport * as i7 from \"ngx-spinner\";\nimport * as i8 from \"src/app/services/UserAuthentication/UserAuthentication.service\";\nimport * as i9 from \"src/app/services/Dashboard/claim-report-service.service\";\nimport * as i10 from \"src/app/services/ClaimForm/claim.service\";\nimport * as i11 from \"src/app/services/Member/member.service\";\nimport * as i12 from \"src/app/services/Navigation/navigation-service.service\";\nimport * as i13 from \"src/app/services/Notification/notification.service\";\nimport * as i14 from \"src/app/services/ClaimForm/get-all-cptcode.service\";\nimport * as i15 from \"src/app/services/ClaimForm/get-all-icdcode.service\";\nimport * as i16 from \"src/app/services/ClaimForm/qualifier-data-by-type.service\";\nimport * as i17 from \"src/app/services/ClaimForm/all-states-by-searchstring.service\";\nimport * as i18 from \"src/app/services/ClaimForm/all-resubmission-code.service\";\nimport * as i19 from \"src/app/services/ClaimForm/all-place-of-services.service\";\nimport * as i20 from \"src/app/services/Shared/masterdata.service\";\nimport * as i21 from \"@angular/common\";\nimport * as i22 from \"@angular/material/dialog\";\nimport * as i23 from \"@angular/forms\";\nimport * as i24 from \"@angular/material/icon\";\nimport * as i25 from \"@angular/material/input\";\nimport * as i26 from \"@angular/material/tooltip\";\n\nfunction NewClaimComponent_tr_864_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"tr\", 295)(1, \"td\")(2, \"div\", 296)(3, \"div\", 297)(4, \"div\", 298);\n    i0.ɵɵelement(5, \"input\", 299);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 300)(7, \"input\", 301);\n    i0.ɵɵlistener(\"change\", function NewClaimComponent_tr_864_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.changeQualDate($event));\n    });\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"div\", 9)(11, \"div\", 46);\n    i0.ɵɵelement(12, \"input\", 302);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"td\")(15, \"div\", 9)(16, \"div\", 46)(17, \"input\", 303);\n    i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_tr_864_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.OnPlaceOfServiceChange($event, \"3\"));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"div\", 9)(20, \"div\", 46)(21, \"select\", 304);\n    i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_tr_864_Template_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.locationOfService6 = $event);\n    });\n    i0.ɵɵelementStart(22, \"option\", 305);\n    i0.ɵɵtext(23, \" Select \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\");\n    i0.ɵɵtext(25, \"Yes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"option\");\n    i0.ɵɵtext(27, \"No\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(28, \"td\")(29, \"div\", 9)(30, \"div\", 46)(31, \"input\", 306);\n    i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_tr_864_Template_input_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.OnCPTCodeChange($event, \"0\", ctx_r7.PlaceOfService3));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"td\")(33, \"div\", 307)(34, \"div\", 46);\n    i0.ɵɵelement(35, \"input\", 308);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 46);\n    i0.ɵɵelement(37, \"input\", 309);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 46);\n    i0.ɵɵelement(39, \"input\", 310);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 46);\n    i0.ɵɵelement(41, \"input\", 311);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"td\")(43, \"div\", 307)(44, \"div\", 46)(45, \"input\", 312);\n    i0.ɵɵlistener(\"keypress\", function NewClaimComponent_tr_864_Template_input_keypress_45_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.DPChanged($event));\n    })(\"input\", function NewClaimComponent_tr_864_Template_input_input_45_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.DPChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 46)(47, \"input\", 313);\n    i0.ɵɵlistener(\"keypress\", function NewClaimComponent_tr_864_Template_input_keypress_47_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.DPChanged($event));\n    })(\"input\", function NewClaimComponent_tr_864_Template_input_input_47_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.DPChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 46)(49, \"input\", 314);\n    i0.ɵɵlistener(\"keypress\", function NewClaimComponent_tr_864_Template_input_keypress_49_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.DPChanged($event));\n    })(\"input\", function NewClaimComponent_tr_864_Template_input_input_49_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.DPChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 46)(51, \"input\", 315);\n    i0.ɵɵlistener(\"keypress\", function NewClaimComponent_tr_864_Template_input_keypress_51_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.DPChanged($event));\n    })(\"input\", function NewClaimComponent_tr_864_Template_input_input_51_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.DPChanged($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(52, \"td\")(53, \"div\", 9)(54, \"div\", 46)(55, \"input\", 316);\n    i0.ɵɵlistener(\"keyup\", function NewClaimComponent_tr_864_Template_input_keyup_55_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.calculateCharges(null, i_r2));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(56, \"td\")(57, \"div\", 9)(58, \"div\", 46);\n    i0.ɵɵelement(59, \"input\", 317);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"td\")(61, \"div\", 9)(62, \"div\", 46)(63, \"select\", 318);\n    i0.ɵɵelement(64, \"option\", 319);\n    i0.ɵɵelementStart(65, \"option\", 320);\n    i0.ɵɵtext(66, \"Yes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"option\", 321);\n    i0.ɵɵtext(68, \"No\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(69, \"td\")(70, \"div\", 322)(71, \"div\", 323);\n    i0.ɵɵelement(72, \"input\", 324);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 28)(74, \"mat-icon\", 325);\n    i0.ɵɵtext(75, \"close\");\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const i_r2 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroupName\", i_r2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"fieldError\", ctx_r0.isSubmitted && ctx_r0.isError(\"DateOfServiceFrom\", i_r2));\n    i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(8, 36, ctx_r0.TodaysDate, \"yyyy-MM-dd\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"fieldError\", ctx_r0.isSubmitted && ctx_r0.isError(\"DateOfServiceTo\", i_r2));\n    i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(13, 39, ctx_r0.TodaysDate, \"yyyy-MM-dd\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"fieldError\", ctx_r0.isSubmitted && ctx_r0.isError(\"PlaceOfService3\", i_r2));\n    i0.ɵɵattribute(\"disabled\", ctx_r0.isPlaceOfService3Enabled ? \"\" : null);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.locationOfService6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngValue\", undefined);\n    i0.ɵɵadvance(9);\n    i0.ɵɵclassProp(\"fieldError\", ctx_r0.isSubmitted && ctx_r0.isError(\"JRenderingProviderId\", i_r2));\n    i0.ɵɵpropertyInterpolate1(\"id\", \"option\", i_r2, \"\");\n    i0.ɵɵproperty(\"matTooltip\", \"Please Enter CPT\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier1\", i_r2, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier2\", i_r2, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier3\", i_r2, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier4\", i_r2, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fieldError\", ctx_r0.isSubmitted && ctx_r0.isError(\"DiagnosisPointer1\", i_r2));\n    i0.ɵɵpropertyInterpolate(\"id\", i_r2 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"\", i_r2 + 1, \"\", 1, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"\", i_r2 + 1, \"\", 2, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"\", i_r2 + 1, \"\", 3, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"id\", i_r2 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"\", i_r2 + 1, \"\", 1, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"\", i_r2 + 1, \"\", 2, \"\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵclassProp(\"fieldError\", ctx_r0.isSubmitted && ctx_r0.isError(\"JRenderingProviderId\", i_r2));\n  }\n}\n\nexport let NewClaimComponent = /*#__PURE__*/(() => {\n  class NewClaimComponent {\n    //#endregion\n    constructor(el, billingProviderService, referringProviderService, userDataService, toastr, providerManagementService, changeDetect, getClaimInfoByIdService, spinner, userAuthenticationService, claimReportService, claimService, memberService, navigationService, notificationService, getAllCPTCodeService, getAllICDCodeService, qualifierDataByTypeService, allStatesBySearchstringService, allResubmissionCodeService, allPlaceOfServicesService, masterDataService, datePipe, dialog, formBuilder) {\n      this.el = el;\n      this.billingProviderService = billingProviderService;\n      this.referringProviderService = referringProviderService;\n      this.userDataService = userDataService;\n      this.toastr = toastr;\n      this.providerManagementService = providerManagementService;\n      this.changeDetect = changeDetect;\n      this.getClaimInfoByIdService = getClaimInfoByIdService;\n      this.spinner = spinner;\n      this.userAuthenticationService = userAuthenticationService;\n      this.claimReportService = claimReportService;\n      this.claimService = claimService;\n      this.memberService = memberService;\n      this.navigationService = navigationService;\n      this.notificationService = notificationService;\n      this.getAllCPTCodeService = getAllCPTCodeService;\n      this.getAllICDCodeService = getAllICDCodeService;\n      this.qualifierDataByTypeService = qualifierDataByTypeService;\n      this.allStatesBySearchstringService = allStatesBySearchstringService;\n      this.allResubmissionCodeService = allResubmissionCodeService;\n      this.allPlaceOfServicesService = allPlaceOfServicesService;\n      this.masterDataService = masterDataService;\n      this.datePipe = datePipe;\n      this.dialog = dialog;\n      this.formBuilder = formBuilder;\n      this.userPermissionEnum = UserPermissionEnum; //#region Properties\n\n      this.createClaimForm = {};\n      this.claimArray = [];\n      this.memberProviderDetail = {};\n      this.autoAccident = 'N';\n      this.i = \"0\";\n      this.filteredICDCode = [];\n      this.filteredPlaceOfServiceOptions = new Observable();\n      this.allPlaceOfService = [];\n      this.allPlaceOfService1 = [];\n      this.allPlaceOfService2 = [];\n      this.allPlaceOfService3 = [];\n      this.allPlaceOfService4 = [];\n      this.allResubmissionCode = [];\n      this.ResubmissionCode = '1';\n      this.qualifierDataByType = [];\n      this.Qual = \"431 - Onset of Current Symptoms or Illness\";\n      this.dynamicICDCode = [];\n      this.allICDCode = [{\n        text: \"abcdef\",\n        value: \"abcdef\",\n        addDate: '',\n        termDate: ''\n      }];\n      this.allICDCode1 = [];\n      this.allICDCode2 = [];\n      this.allICDCode3 = [];\n      this.allICDCode4 = [];\n      this.allICDCode5 = [];\n      this.allICDCode6 = [];\n      this.allICDCode7 = [];\n      this.allICDCode8 = [];\n      this.allICDCode9 = [];\n      this.allICDCode10 = [];\n      this.allICDCode11 = [];\n      this.allICDCode12 = [];\n      this.allCPTCodes = [];\n      this.filteredAllCPTCodes = new Observable();\n      this.allCPTCode = [];\n      this.allStatesBySearchstring = [];\n      this.allStatesList = [];\n      this.specialitySearchResult = [];\n      this.filteredspeciality = new Observable();\n      this.specialityTouched = false;\n      this.taxonomySearchResult = [];\n      this.filteredTaxonomy = new Observable();\n      this.taxonomyTouched = false;\n      this.typeOfProvider = [];\n      this.isPlaceOfService1Enabled = false;\n      this.isPlaceOfService2Enabled = false;\n      this.isPlaceOfService3Enabled = false;\n      this.isPlaceOfService4Enabled = false;\n      this.isResubmissionEnabled = false;\n      this.isICDInput1Enabled = false;\n      this.isICDInput2Enabled = false;\n      this.isICDInput3Enabled = false;\n      this.isICDInput4Enabled = false;\n      this.isICDInput5Enabled = false;\n      this.isICDInput6Enabled = false;\n      this.isICDInput7Enabled = false;\n      this.isICDInput8Enabled = false;\n      this.isICDInput9Enabled = false;\n      this.isICDInput10Enabled = false;\n      this.isICDInput11Enabled = false;\n      this.isICDInput12Enabled = false;\n      this.isInsuredEnabled = false;\n      this.isBillingProviderEnabled = false;\n      this.isPatientEnabled = false;\n      this.isSticky = false; //for making header sticky\n\n      this.submitted = false;\n      this.searchMember = {};\n      this.memberResult = [];\n      this.selectedMember = [];\n      this.selectedBillingProvider = [];\n      this.selectedReferringProvider = [];\n      this.selectedRenderingProvider = [];\n      this.PlaceOfService3 = \"11\";\n      this.InputLoader = false;\n      this.ICDCheckbox = \"ICD10\";\n      this.SelectArr = [];\n      this.patientState = [];\n      this.copyInsured = false;\n      this.claimForm837P = {};\n      this.referringProviderDetail = {};\n      this.referringProviderResult = [];\n      this.renderingProviderDetail = {};\n      this.renderingProviderResult = [];\n      this.billingProviderSearch = {};\n      this.billingProviderResult = [];\n      this.Status = '';\n      this.FormCreatedDate = '';\n      this.FormCreatedBy = '';\n      this.ClaimFormType = '';\n      this.SubscriberIdCode = '';\n      this.currentDate = new Date();\n      this.facilityNPI = false;\n      this.ICDDebounceMethod = new Subject();\n      this.CPTDebounceMethod = new Subject();\n      this.ICDInputIndexVal = '';\n      this.CPTInputIndexVal = ''; //#region Private Valdiation Properties\n\n      this.tempICD = Array();\n      this.tempDP = Array();\n      this.notes = [];\n      this.showNotes = false;\n      this.tempDP = [];\n      this.tempICD = [];\n      this.searchMember.iPACode = this.userDataService.getUserIPA();\n      this.searchMember.searchParameter = {};\n      this.searchMember.searchParameter.ipaCode = this.searchMember.iPACode;\n      this.searchMember.searchParameter.dOSTo = null;\n      this.searchMember.searchParameter.dOSFrom = null;\n      this.searchMember.searchParameter.cPTDOSFrom = null;\n      this.searchMember.p_DOSTo = null;\n      this.searchMember.searchParameter.dateOfBirth = null;\n      this.searchMember.p_DOSFrom = null;\n      this.searchMember.childSearchParameter = null;\n      this.searchMember.selectedIPACodes = this.searchMember.iPACode;\n      this.searchMember.userId = JSON.parse(localStorage.getItem(\"uuid\"));\n      this.searchMember.userName = JSON.parse(localStorage.getItem(\"email\"));\n      this.searchMember.paginationOption = {\n        pageNumber: 1,\n        pageOffset: 50\n      };\n      this.searchMember.defaultSortOptions = [{\n        columnName: \"FIRSTNAME\",\n        sortingOrder: 0\n      }];\n      this.searchMember.sortOptions = [];\n      this.searchMember.providerList = [];\n      this.searchMember.p_DOSFrom = \"2020-05-16T00:00:00\";\n      this.searchMember.p_DOSTo = \"2021-05-16T00:00:00\";\n      this.referringProviderDetail.ipaCode = this.userDataService.getUserIPA();\n      this.referringProviderDetail.sortBy = \"ProviderNPI\";\n      this.referringProviderDetail.sortOrder = \"ASC\";\n      this.referringProviderDetail.uuid = JSON.parse(localStorage.getItem(\"uuid\"));\n      this.referringProviderDetail.username = JSON.parse(localStorage.getItem(\"email\"));\n      this.referringProviderDetail.index = 0;\n      this.referringProviderDetail.limit = 100;\n      this.renderingProviderDetail.ipaCode = this.userDataService.getUserIPA();\n      this.renderingProviderDetail.sortBy = \"ProviderNPI\";\n      this.renderingProviderDetail.sortOrder = \"ASC\";\n      this.renderingProviderDetail.uuid = JSON.parse(localStorage.getItem(\"uuid\"));\n      this.renderingProviderDetail.username = JSON.parse(localStorage.getItem(\"email\"));\n      this.renderingProviderDetail.index = 0;\n      this.renderingProviderDetail.limit = 100;\n      this.billingProviderSearch.IPACode = this.userDataService.getUserIPA();\n      this.billingProviderSearch.sortBy = \"ProviderNPI\";\n      this.billingProviderSearch.sortOrder = \"ASC\";\n      this.billingProviderSearch.index = 0;\n      this.billingProviderSearch.limit = 50; //this.notes = JSON.parse(localStorage.getItem('notes')) || [{ id: 0, content: '' }];\n\n      const {\n        webkitSpeechRecognition\n      } = window;\n      this.recognition = new webkitSpeechRecognition();\n\n      this.recognition.onresult = event => {\n        //console.log(this.el.nativeElement.querySelectorAll(\".content\")[0]);\n        this.el.nativeElement.querySelectorAll(\".content\")[0].innerText = event.results[0][0].transcript;\n      };\n    }\n\n    selectEvent($event) {\n      throw new Error('Method not implemented.');\n    }\n\n    onFocused($event) {\n      throw new Error('Method not implemented.');\n    }\n\n    onChangeSearch($event) {\n      throw new Error('Method not implemented.');\n    }\n\n    checkScroll() {\n      this.isSticky = window.pageYOffset >= 100;\n    } // private _filterPlaceOfServiceArray(filterValue: string): AllPlaceOfServices[] {\n    //   return this.allPlaceOfService1.filter(option => option.text.toUpperCase().includes(filterValue.toUpperCase()));\n    // }\n\n\n    ShowBox() {\n      const dialogRef = this.dialog.open(SaveModalComponent, {\n        data: \"123456789\"\n      });\n    }\n\n    ngOnInit() {\n      this.ICDDebounceMethod.pipe(debounceTime(400)).subscribe(value => {\n        this.fetchchAllICDCode(value);\n      });\n      this.CPTDebounceMethod.pipe(debounceTime(400)).subscribe(value => {\n        this.fetchchAllCPTCode(value);\n      });\n      this.fetchchAllICDCode();\n      this.fetchAllStatesBySearchstring();\n      this.TodaysDate = new Date();\n      this.InitiateClaimForm();\n\n      if (this.getClaimInfoByIdService.ClaimFormType == 'edit') {\n        this.ClaimFormType = 'edit';\n        this.fetchchGetClaimInfoByIdService();\n      } else {\n        this.ClaimFormType = 'add';\n      }\n\n      this.ClaimForm.controls.PatientsOrAuthorizedPersonsSignature.setValue(this.datePipe.transform(this.currentDate, 'yyyy-MM-dd'));\n      this.fetchchQualifierDataByType();\n      this.fetchchAllResubmissionCode();\n      this.fetchClaimDataFromMaster();\n      this.fetchchAllPlaceOfServices(); // this.filteredPlaceOfServiceOptions1 =this.ClaimForm.get(\"PlaceOfService1\").valueChanges.pipe(\n      //   startWith<string | AllPlaceOfServices[\"text\"]>(\"\"),\n      //   map(value => (typeof value === \"string\" ? value : value)),\n      //   map(name => (name ? this._filter(name) : this.allPlaceOfService1.slice()))\n      // );\n\n      this.createClaimForm.allICDCodes = [];\n      this.createClaimForm.allCPTCodes = [];\n      this.createClaimForm.allPlacesOfServices = []; // this.AddNewAutoComplete();\n      // this.manageNameControl(0); // TBD\n\n      this.manageDateOfServiceControl(0); //this.notes.push({ id: 789, content: 'abc' });\n      //this.notes.push({ id: 1011, content: 'xyz' });\n    }\n\n    InitiateClaimForm() {\n      this.ClaimForm = this.formBuilder.group({\n        insuranceType: ['MB', [Validators.required]],\n        PatientFirstName: [''],\n        PatientLastName: [''],\n        PatientMiddleInitial: ['', []],\n        PatientStreetNumber: [''],\n        PatientAddressLine2: ['', []],\n        PatientCity: [''],\n        PatientState: [''],\n        PatientZip: [''],\n        PatientTelephone: ['', []],\n        COPYFROMINSUREDPERSON: [false, []],\n        OtherInsuredFirstName: ['', []],\n        OtherInsuredLastName: ['', []],\n        OtherInsuredMiddleInitial: ['', []],\n        OtherInsuredPolicy: ['', []],\n        ReservedForNUCCUse: ['', []],\n        InsurancePlanNameOrProgramName: [''],\n        PersonsSignature: ['Y'],\n        Qual: [''],\n        QualDate: [''],\n        NameOfReferringProvider: [''],\n        ReferringProviderLastName: [''],\n        ReferringProviderMiddleName: ['', []],\n        ReferringProviderFirstName: [''],\n        AdditionalClaimInformation: ['', []],\n        NDC: ['HIDENDC', []],\n        FederalTaxNumber: [''],\n        NDC1: ['EIN'],\n        DateOfInitialTreatment: ['', []],\n        LatestVisit: ['', []],\n        SupervisingPhysicianLastName: ['', []],\n        SupervisingPhysicianFIRSTNAME: [''],\n        SupervisingPhysicianMIDDLENAME: [''],\n        SupervisingPhysicianNPI: ['', []],\n        SupervisingPhysicianID: ['', []],\n        SupervisingPhysicianIDLastName: ['', []],\n        SupervisingPhysicianIDFIRSTNAME: ['', []],\n        SupervisingPhysicianIDMIDDLENAME: ['', []],\n        NPI: ['', []],\n        OrderingPhysicianID: ['', []],\n        CLIA: ['', []],\n        AccidentDate: ['', []],\n        MammographyCertificate: ['', []],\n        PatientDOB: [''],\n        PatientGender: [''],\n        PatientRelationshipToInsured: ['18'],\n        NUCC1: ['', []],\n        NUCC2: ['', []],\n        Employment: ['N'],\n        AutoAccident: ['N'],\n        AutoAccidentState: [''],\n        OtherAccidents: ['N'],\n        ClaimsCodes: ['', []],\n        OtherDateFrom: ['', []],\n        OtherDateTo: ['', []],\n        PatientAccountNumber: [''],\n        AcceptanceAssignment: ['Yes', []],\n        FacilityName: [''],\n        FacilityLocationAddress: [''],\n        FacilityLocationADDRESSLINE2A: ['', []],\n        FacilityLocationADDRESSLINE2B: ['', []],\n        FacilityLocationCity: [''],\n        FacilityLocationState: [''],\n        FacilityLocationZip: [''],\n        FacilityLocationNPI: ['', []],\n        FacilityId: ['', []],\n        PayerName: [''],\n        PayerID: ['', [Validators.required]],\n        PayerAddress1: [''],\n        PayerAddress2: ['', []],\n        PayerCity: [''],\n        PayerState: [''],\n        PayerZip: [''],\n        InsuredSIDNumber: ['', [Validators.required]],\n        InsuredFirstName: [''],\n        InsuredLastName: [''],\n        InsuredMiddleInitial: ['', []],\n        InsuredStreetNumber: [''],\n        InsuredAddressLine2: ['', []],\n        InsuredCity: [''],\n        InsuredState: [''],\n        InsuredZip: [''],\n        InsuredTelephone: ['', []],\n        FECANumber: [''],\n        InsuredDateOfBirth: ['', [Validators.required]],\n        SexOfInsured: [''],\n        OtherClaimID: ['', []],\n        InsurancePlanName: ['', []],\n        AnotherHealthBenefitPlan: ['No'],\n        AuthorizedPersonsSignature: ['Y'],\n        PatientUnableToWorkInCurrentOccupationFrom: ['', []],\n        PatientUnableToWorkInCurrentOccupationTo: ['', []],\n        HospitalizationDatesRelatedToCurrentServicesFrom: ['', []],\n        HospitalizationDatesRelatedToCurrentServicesTo: ['', []],\n        OutsideLab: ['No', []],\n        ResubmissionCode: [''],\n        ReferenceNumber: ['', []],\n        PriorAuthorisationNumber: ['', []],\n        TotalCharge: ['0.01'],\n        AmountPaid: [''],\n        ReservedForNUCC: ['', []],\n        BillingProvider: [''],\n        BillingProviderFIRSTNAME: ['', []],\n        BillingProviderMIDDLENAME: ['', []],\n        BillingProviderAddress: [''],\n        BillingProviderADDRESSLINE2: ['', []],\n        BillingProviderCity: [''],\n        BillingProviderState: [''],\n        BillingProviderZip: [''],\n        BillingProviderTelephone: [''],\n        Taxonomy: [''],\n        RenderingProvider: ['', [Validators.required]],\n        RenderingProviderName: [''],\n        RenderingProviderMiddleName: [''],\n        RenderingProviderPin: [''],\n        ProviderSpecialty: [''],\n        ProviderNPI: [''],\n        ProviderPIN: [''],\n        GroupNPI: [''],\n        ProviderId: [''],\n        IdQual: [''],\n        ICD: [''],\n        ClaimType: ['CAP', [Validators.required]],\n        ReferringProviderNPI: [''],\n        TypeOfProvider: [''],\n        PlaceOfService1: [''],\n        PlaceOfService2: ['', []],\n        //PlaceOfService3: ['', []],\n        PlaceOfService4: ['', []],\n        //DateOfServiceFrom: ['', Validators.required],\n        //DateOfServiceTo: ['', Validators.required],\n        ICDInput1: ['', [Validators.required]],\n        ICDInput2: ['', []],\n        ICDInput3: ['', []],\n        ICDInput4: ['', []],\n        ICDInput5: ['', []],\n        ICDInput6: ['', []],\n        ICDInput7: ['', []],\n        ICDInput8: ['', []],\n        ICDInput9: ['', []],\n        ICDInput10: ['', []],\n        ICDInput11: ['', []],\n        ICDInput12: ['', []],\n        //locationOfService6: [''],\n        CPTInputs: this.formBuilder.array([]),\n        //CPTInputs2: this.initCpts2(),\n        CPTCodeInput2: ['', []],\n        CPTCodeInput3: ['', []],\n        CPTCodeInput4: ['', []],\n        OtherDateQual: [''],\n        Qual2: ['', []],\n        DateOfCurrentIllness: ['', []],\n        PatientsOrAuthorizedPersonsSignature: [this.datePipe.transform(this.currentDate, 'yyyy-MM-dd'), Validators.required],\n        abcdef: [''],\n        FacilityNPIorBilling: ['']\n      });\n      this.addCPTInputsItem();\n    }\n\n    get CPTInputs() {\n      return this.ClaimForm.get('CPTInputs');\n    }\n\n    addCPTInputsItem() {\n      const newItem = this.formBuilder.group({\n        option: '',\n        Modifier1: '',\n        Modifier2: '',\n        Modifier3: '',\n        Modifier4: '',\n        DiagnosisPointer1: '',\n        DiagnosisPointer2: '',\n        DiagnosisPointer3: '',\n        DiagnosisPointer4: '',\n        UnitCharges: '',\n        DaysAndUnitCharges: '',\n        Charges: '',\n        EPSDT: '',\n        JRenderingProviderId: '',\n        locationOfService6: '',\n        DateOfServiceFrom: '',\n        DateOfServiceTo: '',\n        PlaceOfService3: '' // TBD new\n\n      });\n      this.CPTInputs.push(newItem);\n    }\n\n    EnableAllFields() {\n      this.isPlaceOfService1Enabled = false;\n      this.isPlaceOfService2Enabled = false;\n      this.isPlaceOfService3Enabled = false;\n      this.isPlaceOfService4Enabled = false;\n      this.isResubmissionEnabled = false;\n      this.isICDInput1Enabled = false;\n      this.isICDInput2Enabled = false;\n      this.isICDInput3Enabled = false;\n      this.isICDInput4Enabled = false;\n      this.isICDInput5Enabled = false;\n      this.isICDInput6Enabled = false;\n      this.isICDInput7Enabled = false;\n      this.isICDInput8Enabled = false;\n      this.isICDInput9Enabled = false;\n      this.isICDInput10Enabled = false;\n      this.isICDInput11Enabled = false;\n      this.isICDInput12Enabled = false;\n      this.isInsuredEnabled = false;\n      this.isBillingProviderEnabled = false;\n      this.isPatientEnabled = false;\n    }\n\n    onButtonSave() {\n      this.MapClaimData();\n      console.log('formData', this.ClaimForm);\n    }\n\n    OnSpecialtyChange(filterValue, type) {\n      if (filterValue == null) return;\n      this.InputLoader = true;\n      this.fetchSpecialtyResult(filterValue, type);\n    }\n\n    fetchSpecialtyResult(filterValue, type) {\n      this.specialityTouched = true;\n      this.taxonomyTouched = true;\n      filterValue = filterValue.trim();\n\n      if (filterValue == null ? true : filterValue.length < 1) {\n        this.specialitySearchResult = null;\n        this.filteredspeciality = null;\n        this.specialityTouched = false;\n        this.taxonomyTouched = false;\n        this.InputLoader = false;\n      } else {\n        this.providerManagementService.fetchSpecialityData(filterValue).pipe(first()).subscribe(response => {\n          // if (response.statusCode == 200) {\n          if (type == \"speciality\") {\n            //this.specialitySearchResult = response.content;\n            this.filteredspeciality = of(this.specialitySearchResult);\n          } else {\n            //this.taxonomySearchResult = response.content;\n            this.filteredTaxonomy = of(this.taxonomySearchResult);\n          }\n\n          this.InputLoader = false; // this.changeDetection.detectChanges();\n          // }\n          //},\n          // error => {\n          //   this.InputLoader = false;\n        });\n      }\n    }\n\n    fetchchGetClaimInfoByIdService() {\n      this.getClaimInfoByIdService.fetchchGetClaimInfoByIdService().pipe(first()).subscribe(response => {\n        //next() callback\n        this.claimForm837P = response;\n        this.notes = [];\n\n        for (let i = 0; i < this.claimForm837P.notes.length; i++) {\n          this.notes.push({\n            id: this.claimForm837P.notes[i].notesPkId,\n            content: this.claimForm837P.notes[i].description\n          });\n        }\n\n        this.MapInputFields(); // this.spinnerService.hide();\n      });\n    }\n\n    _filter(value) {\n      const filterValue = value.toLowerCase();\n      return this.allCPTCodes.filter(option => option.description.toLowerCase().includes(filterValue));\n    }\n\n    _PlaceServiceFilter(value) {\n      const filterValue = value.toLowerCase();\n      return this.allPlaceOfService.filter(option => option.text.toLowerCase().includes(filterValue));\n    }\n\n    AddNewAutoComplete() {\n      this.AddCpts();\n      this.calculateCharges(null, null);\n    }\n\n    RemoveAddedControl(index) {\n      const control1 = this.ClaimForm.get('CPTInputs');\n      control1.removeAt(index); //const control3 = this.ClaimForm.get('CPTInputs2') as FormArray;\n      //control3.removeAt(index);\n      // const control2 = this.ClaimForm.get('DatesOfService') as FormArray;\n      // control2.removeAt(index);\n\n      this.calculateCharges(null, null);\n    }\n\n    AddCpts() {\n      const control = this.ClaimForm.get('CPTInputs');\n      var renderingProviderID = ''; //check if Rendering Provider NPI is available    \n\n      if (this.ClaimForm.controls.ProviderNPI.value != null && this.ClaimForm.controls.ProviderNPI.value != '') {\n        renderingProviderID = this.ClaimForm.controls.ProviderNPI.value;\n      } else {\n        if (this.memberProviderDetail != null) {\n          if (this.memberProviderDetail.renderingProvider != null) {\n            if (this.memberProviderDetail.renderingProvider.providerNPI != null) {\n              if (this.memberProviderDetail.renderingProvider.providerNPI.length > 5) {\n                renderingProviderID = this.memberProviderDetail.renderingProvider.providerNPI;\n              }\n            }\n          }\n        }\n      }\n\n      control.push(this.formBuilder.group({\n        option: ['', Validators.required],\n        Modifier1: [''],\n        Modifier2: [''],\n        Modifier3: [''],\n        Modifier4: [''],\n        DiagnosisPointer1: ['', [Validators.required]],\n        DiagnosisPointer2: [''],\n        DiagnosisPointer3: [''],\n        DiagnosisPointer4: [''],\n        UnitCharges: ['0.01'],\n        DaysAndUnitCharges: ['1'],\n        Charges: ['0.01'],\n        EPSDT: [' '],\n        JRenderingProviderId: [renderingProviderID]\n      }));\n      this.manageNameControl(control.length - 1);\n    }\n\n    AddDos() {\n      const dateControls = this.ClaimForm.get('DatesOfService');\n      dateControls.push(this.formBuilder.group({\n        option: ['', Validators.required]\n      }));\n      this.manageDateOfServiceControl(dateControls.length - 1);\n    }\n\n    initDos() {\n      var formArray = this.formBuilder.array([]);\n\n      for (let i = 0; i < 1; i++) {\n        formArray.push(this.formBuilder.control(''));\n      }\n\n      return formArray;\n    }\n\n    initCpts() {\n      var formArray = this.formBuilder.array([]);\n\n      for (let i = 0; i < 1; i++) {\n        formArray.push(this.formBuilder.control('option', [Validators.required]));\n        formArray.push(this.formBuilder.control('Modifier1', [Validators.min(2)]));\n        formArray.push(this.formBuilder.control('Modifier2', [Validators.min(2)]));\n        formArray.push(this.formBuilder.control('Modifier3', [Validators.min(2)]));\n        formArray.push(this.formBuilder.control('Modifier4', [Validators.min(2)]));\n        formArray.push(this.formBuilder.control('DiagnosisPointer1', [Validators.required]));\n        formArray.push(this.formBuilder.control('DiagnosisPointer2'));\n        formArray.push(this.formBuilder.control('DiagnosisPointer3'));\n        formArray.push(this.formBuilder.control('DiagnosisPointer4'));\n        formArray.push(this.formBuilder.control('UnitCharges'));\n        formArray.push(this.formBuilder.control('DaysAndUnitCharges'));\n        formArray.push(this.formBuilder.control('Charges'));\n        formArray.push(this.formBuilder.control('EPSDT'));\n        formArray.push(this.formBuilder.control('JRenderingProviderId'));\n      }\n\n      return formArray;\n    }\n\n    initCpts2() {\n      var formArray = this.formBuilder.array([]);\n\n      for (let i = 0; i < 1; i++) {\n        formArray.push(this.formBuilder.control('1'));\n        formArray.push(this.formBuilder.control({\n          value: '0.01',\n          disabled: true\n        }));\n        formArray.push(this.formBuilder.control(' '));\n        formArray.push(this.formBuilder.control(''));\n      }\n\n      return formArray;\n    }\n\n    DPChanged(event) {\n      if (event.target.value > 0) {\n        //Adding & updating Inserted DP Value in Array for Validation\n        var dp = this.tempDP.filter(({\n          index\n        }) => index == event.target.id);\n\n        if (dp.length > 0) {\n          var dpIndex = this.tempDP.findIndex(({\n            index\n          }) => index == event.target.id);\n          this.tempDP[dpIndex].value = Number(event.target.value);\n        } else {\n          this.tempDP.push({\n            index: Number(event.target.id),\n            value: Number(event.target.value)\n          });\n        }\n      } else if (event.target.value === \"\") //Removing Inserted DP Value in Array for Validation\n        {\n          var dp = this.tempDP.filter(({\n            index\n          }) => index == event.target.id);\n\n          if (dp.length > 0) {\n            var dpIndex = this.tempDP.findIndex(({\n              index\n            }) => index == event.target.id);\n            this.tempDP.splice(dpIndex, 1);\n          }\n        }\n    }\n\n    AddInTempICD(value, indexValue) {\n      if (value != null) {\n        if (value.length > 0) {\n          //Adding & updating Inserted DP Value in Array for Validation\n          var icd = this.tempICD.filter(({\n            index\n          }) => index == indexValue);\n\n          if (icd.length > 0) {\n            var icdIndex = this.tempICD.findIndex(({\n              index\n            }) => index == indexValue);\n            this.tempICD[icdIndex].value = value;\n          } else {\n            this.tempICD.push({\n              index: indexValue,\n              value: value\n            });\n          }\n        } else //Removing Inserted DP Value in Array for Validation\n          {\n            var icd = this.tempICD.filter(({\n              index\n            }) => index == indexValue);\n\n            if (icd != null) {\n              var icdIndex = this.tempICD.findIndex(({\n                index\n              }) => index == indexValue);\n              this.tempICD.splice(icdIndex, 1);\n            }\n          }\n      }\n    }\n\n    calculateCharges(value, index) {\n      var totalCharges = 0;\n      var arrayControl = this.ClaimForm.get('CPTInputs'); //var arrayControl2 = this.ClaimForm.get('CPTInputs2') as FormArray;\n\n      for (let i = 0; i < arrayControl.length; i++) {\n        var arrayGroup = arrayControl.at(i); //var arrayGroup2 = arrayControl2.at(i) as FormGroup;\n\n        this.ClaimForm.get('CPTInputs').at(i).controls['Charges'].setValue(arrayGroup.get('DaysAndUnitCharges').value * arrayGroup.get('UnitCharges').value);\n        totalCharges = totalCharges + arrayGroup.get('DaysAndUnitCharges').value * arrayGroup.get('UnitCharges').value;\n      }\n\n      this.ClaimForm.get('TotalCharge').setValue(totalCharges);\n      this.changeDetect.detectChanges();\n    }\n\n    validateICD() {\n      if (this.ClaimForm.controls.ProviderNPI.value != '' && this.ClaimForm.controls.ProviderNPI.value != null) {\n        if (this.ClaimForm.controls.ProviderNPI.value.length != 10) {\n          this.notificationService.showWarning('', 'Please check length of NPI.', 4000);\n          return false;\n        }\n      }\n\n      if (this.ClaimForm.controls.ReferringProviderNPI.value != '' && this.ClaimForm.controls.ReferringProviderNPI.value != null) if (this.ClaimForm.controls.ReferringProviderNPI.value.length != 10) {\n        this.notificationService.showWarning('', 'Please check length of NPI.', 4000);\n        return false;\n      }\n\n      if (!!this.ClaimForm.controls.Qual2.value && !Date.parse(this.ClaimForm.controls.OtherDateQual.value)) {\n        this.notificationService.showWarning('', 'Please check Qual 2 Date.', 4000);\n        return false;\n      }\n\n      let icdValidated = new Array(this.tempICD.length);\n      let dpValidated = new Array(this.tempDP.length);\n      icdValidated = [];\n      dpValidated = [];\n\n      for (let i = 0; i < icdValidated.length; i++) {\n        icdValidated.push(false);\n      }\n\n      for (let i = 0; i < dpValidated.length; i++) {\n        dpValidated.push(false);\n      }\n\n      for (let i = 0; i < this.tempICD.length; i++) {\n        for (let j = 0; j < this.tempDP.length; j++) {\n          var target = this.tempICD.some(obj => obj.index === this.tempDP[j].value);\n\n          if (target == false) {\n            this.notificationService.showWarning('', 'Please assign ICD to corresponding CPT(s).', 4000);\n            return false;\n          }\n\n          if (this.tempDP[j].value == this.tempICD[i].index) {\n            icdValidated[i] = true;\n          } else {}\n        }\n      }\n\n      let pass = icdValidated.filter(value => value).length;\n\n      if (pass == this.tempICD.length) {\n        return true;\n      } else {\n        this.notificationService.showWarning('', 'Please assign ICD to corresponding CPT(s).', 4000);\n        return false;\n      }\n    }\n\n    manageNameControl(index) {\n      var arrayControl = this.ClaimForm.get('CPTInputs');\n      this.filteredAllCPTCodes[index] = arrayControl.at(index).get('option')?.valueChanges.pipe(startWith(''), map(value => this._filter(value)));\n    }\n    /*manageNameControl2(index: number) {\r\n      var arrayControl = this.ClaimForm.get('CPTInputs2') as FormArray;\r\n      this.filteredAllCPTCodes[index] = arrayControl.at(index).get('option')?.valueChanges\r\n        .pipe(\r\n          startWith(''),\r\n          map(value => this._filter(value))\r\n        );\r\n    }*/\n\n\n    manageDateOfServiceControl(index) {\n      var arrayControl = this.ClaimForm.get('DatesOfService');\n\n      if (arrayControl != null) {\n        this.filteredPlaceOfServiceOptions[index] = arrayControl.at(index).get('option')?.valueChanges.pipe(startWith(''), map(value => this._PlaceServiceFilter(value)));\n      }\n    }\n\n    get f() {\n      return this.ClaimForm.controls;\n    }\n\n    onSubmit() {\n      this.isSubmitted = true;\n      this.CreateClaim();\n      /*if (this.ClaimForm.invalid) {\r\n        this.ClaimForm.markAllAsTouched();\r\n        //this.ClaimForm.setErrors({ ...this.ClaimForm.errors, 'yourErrorName': true });\r\n        this.notificationService.showError('Error', 'Please fill highlighted fields.', 4000);\r\n        return;\r\n      }\r\n      else {\r\n        this.CreateClaim();\r\n      }*/\n    }\n\n    EditClaim() {}\n\n    OpenTab(name, claimForm837) {\n      var claimId = claimForm837.parentClaimForm837Pid;\n      var formControlName = claimForm837.parentPatientCtrlNo;\n      this.navigationService.claimid = formControlName;\n      this.getClaimInfoByIdService.SetClaimIdForPreview(claimId, this.claimReportService.getClaimListText());\n\n      if (this.getClaimInfoByIdService.fetchchGetClaimInfoByIdService() == null) {\n        const dialogConfig = new MatDialogConfig();\n        dialogConfig.data = {\n          MessageToDisplay: 'Un-execptional error, please try again',\n          MessageToDisplayOnButton: 'Back',\n          RedirectTo: ''\n        };\n        this.dialog.open(ErrorMessagePopUpComponent, dialogConfig);\n      } else {\n        if (name == '42') {\n          this.getClaimInfoByIdService.ClaimFormType = 'edit';\n        }\n\n        this.navigationService.toggle(name);\n      }\n    }\n\n    isParentClaimIdNotNull(ClaimForm) {\n      if (ClaimForm.parentPatientCtrlNo != null && ClaimForm.parentPatientCtrlNo != undefined) {\n        return true;\n      }\n\n      return false;\n    }\n\n    isError(objName, pos) {\n      return this.ClaimForm.get('CPTInputs').at(pos).controls[objName].errors != null; //.controls[pos].controls.DateOfServiceFrom.errors);\n    }\n\n    CreateClaim() {\n      var valid = this.validateICD();\n\n      if (valid == true) {\n        if (this.ClaimForm.invalid) {\n          for (const name in this.ClaimForm.controls) {\n            if (this.ClaimForm.controls[name].invalid) {}\n          }\n\n          this.ClaimForm.markAllAsTouched();\n          this.notificationService.showError('', 'Please fill highlighted fields.', 4000);\n          return;\n        }\n\n        this.spinner.show();\n        this.MapClaimData();\n        this.claimArray.push(this.createClaimForm.claimForm837);\n\n        if (this.claimArray.length > 1) {\n          this.claimArray.pop();\n        }\n\n        if (this.ClaimFormType == 'edit') {\n          this.claimArray[0].claimsProfessional837.claimskey = this.claimForm837P.claimsProfessional837id;\n          this.claimArray[0].claimsProfessional837.subscriberkey = this.claimForm837P.claimsProfessional837.subscriberkey;\n          this.claimArray[0].claimsProfessional837.subscriberkeyNavigation.subscriberkey = this.claimForm837P.claimsProfessional837.subscriberkey;\n          this.claimArray[0].claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.infosourcekey = this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.infosourcekey;\n          this.claimArray[0].patientCtrlNo = this.claimForm837P.patientCtrlNo;\n          this.claimArray[0].isActive = this.claimForm837P.isActive;\n          this.claimArray[0].claimForm837Pid = this.claimForm837P.claimForm837Pid;\n          this.claimArray[0].claimsProfessional837id = this.claimForm837P.claimsProfessional837id;\n          this.claimArray[0].fileName = this.claimForm837P.fileName;\n          this.claimArray[0].uploadExcelWarningMessage = this.claimForm837P.uploadExcelWarningMessage;\n          this.claimArray[0].status = this.claimForm837P.status;\n\n          for (var counterClaimFormNotes = 0; counterClaimFormNotes < this.claimForm837P.notes.length; counterClaimFormNotes++) {\n            let claimNoteid = this.claimForm837P.notes[counterClaimFormNotes].notesPkId;\n            let noteItem = this.notes.find(f => f.id == claimNoteid);\n\n            if (noteItem != null) {\n              const index = this.notes.indexOf(noteItem);\n\n              if (index > -1) {\n                this.claimForm837P.notes[counterClaimFormNotes].description = noteItem.content;\n                this.claimForm837P.notes[counterClaimFormNotes].lastModifiedBy = JSON.parse(localStorage.getItem(\"email\"));\n                this.claimForm837P.notes[counterClaimFormNotes].lastModifiedDate = this.claimForm837P.lastModifiedDate.toString();\n                this.notes.splice(index, 1);\n              }\n            } else {\n              this.claimForm837P.notes[counterClaimFormNotes].isRemoved = true;\n            }\n          }\n\n          for (var counterNotes = 0; counterNotes < this.notes.length; counterNotes++) {\n            this.claimForm837P.notes.push({\n              notesPkId: 0,\n              description: this.notes[counterNotes].content,\n              claimForm837Pid: this.claimForm837P.claimForm837Pid,\n              isRemoved: false,\n              createdBy: JSON.parse(localStorage.getItem(\"email\")),\n              createdDate: formatDate(new Date(), 'yyyy-MM-dd', 'en'),\n              lastModifiedDate: formatDate(new Date(), 'yyyy-MM-dd', 'en'),\n              lastModifiedBy: JSON.parse(localStorage.getItem(\"email\")),\n              title: ''\n            });\n          }\n\n          this.notes = [];\n          this.claimArray[0].notes = this.claimForm837P.notes;\n        }\n\n        this.claimService.createClaim(this.claimArray).pipe(first()).subscribe(data => {\n          this.spinner.hide();\n\n          if (this.ClaimFormType == 'edit') {\n            //const dialogRef = this.dialog.open(UpdateclaimModalComponent, { data: data });\n            this.InitiateClaimForm();\n            this.claimArray = [];\n            this.fetchchGetClaimInfoByIdService();\n            this.notificationService.showSuccess('Claim ID: ' + data, 'Claim updated successfully.', 4000);\n          } else {\n            const dialogRef = this.dialog.open(SaveModalComponent, {\n              data: data\n            });\n            dialogRef.afterClosed().subscribe(result => {\n              this.createClaimForm = {};\n              let memberRenderingProvider = this.memberProviderDetail;\n              this.claimArray = [];\n              this.memberProviderDetail = {};\n              this.ClaimForm.reset();\n              this.InitiateClaimForm();\n              this.EnableAllFields();\n              this.tempDP = [];\n              this.tempICD = [];\n              var arrayControl2CPTInputs2 = this.ClaimForm.get('CPTInputs');\n\n              if (arrayControl2CPTInputs2 != null && arrayControl2CPTInputs2.length > 0) {\n                for (let i = 0; i < arrayControl2CPTInputs2.length; i++) {\n                  this.ClaimForm.get('CPTInputs').at(i).controls['JRenderingProviderId'].setValue(\"\");\n                  this.ClaimForm.get('CPTInputs').at(i).controls['PlaceOfService3'].setValue(\"11\");\n                }\n              }\n\n              var arrayControlDatesOfService = this.ClaimForm.get('DatesOfService');\n\n              if (arrayControlDatesOfService != null && arrayControlDatesOfService.length > 0) {\n                for (let i = arrayControlDatesOfService.length - 1; i >= 0; i--) {\n                  arrayControlDatesOfService.removeAt(i);\n                }\n\n                arrayControlDatesOfService.clear();\n              }\n\n              this.ClaimForm.controls.Qual.setValue('431 - Onset of Current Symptoms or Illness');\n              this.ClaimForm.controls.ResubmissionCode.setValue('1'); //this.ClaimForm.controls.PlaceOfService3.setValue('11');\n\n              this.ClaimForm.controls.ICD.setValue('ICD10');\n              this.PlaceOfService3 = \"11\";\n              this.ICDCheckbox = \"ICD10\";\n\n              if (result == 'New blank claim') {\n                this.memberProviderDetail = null;\n                this.selectedRenderingProvider = null;\n              } else if (result == 'New claim for same Member') {\n                this.MappingInsuredInfo();\n              } else if (result == 'New claim for same Provider') {\n                if (this.selectedRenderingProvider[0] != null) {\n                  this.MapRenderingProvider();\n                } else {\n                  this.MapRenderingProviderWithMemberProvider(memberRenderingProvider);\n                  memberRenderingProvider = {};\n                }\n              } else {}\n            }); //debugger;\n            //this.notificationService.showSuccess('Claim ID: ' + data, 'Claim created successfully.', 4000);\n          }\n        }); // }\n      }\n    }\n\n    OnPlaceOfServiceChange(filterValue, InputIndex) {\n      if (filterValue == null ? true : filterValue.length < 2) {\n        switch (InputIndex) {\n          case '0':\n            this.filteredPlaceOfServiceOptions = of(this.allPlaceOfService.filter(option => option.text.includes(\"123456\")));\n            break;\n\n          case '1':\n            this.filteredPlaceOfServiceOptions1 = of(this.allPlaceOfService1.filter(option => option.text.includes(\"123456\")));\n            break;\n\n          case '2':\n            this.filteredPlaceOfServiceOptions2 = of(this.allPlaceOfService2.filter(option => option.text.includes(\"123456\")));\n            break;\n\n          case '3':\n            this.filteredPlaceOfServiceOptions3 = of(this.allPlaceOfService3.filter(option => option.text.includes(\"123456\")));\n            break;\n\n          case '4':\n            this.filteredPlaceOfServiceOptions4 = of(this.allPlaceOfService4.filter(option => option.text.includes(\"123456\")));\n            break;\n        }\n\n        return;\n      }\n\n      switch (InputIndex) {\n        case '0':\n          this.filteredPlaceOfServiceOptions = of(this.allPlaceOfService.filter(option => option.text.toUpperCase().includes(filterValue.toUpperCase())));\n          break;\n\n        case '1':\n          this.filteredPlaceOfServiceOptions1 = of(this.allPlaceOfService1.filter(option => option.text.toUpperCase().includes(filterValue.toUpperCase())));\n          break;\n\n        case '2':\n          this.filteredPlaceOfServiceOptions2 = of(this.allPlaceOfService2.filter(option => option.text.toUpperCase().includes(filterValue.toUpperCase())));\n          break;\n\n        case '3':\n          this.filteredPlaceOfServiceOptions3 = of(this.allPlaceOfService3.filter(option => option.text.toUpperCase().includes(filterValue.toUpperCase())));\n          break;\n\n        case '4':\n          this.filteredPlaceOfServiceOptions4 = of(this.allPlaceOfService4.filter(option => option.text.toUpperCase().includes(filterValue.toUpperCase())));\n          break;\n      }\n    } // to open save modal on click of save button\n\n\n    OnSave() {}\n\n    MapClaimData() {\n      var arrayControl = this.ClaimForm.get('DatesOfService');\n      this.createClaimForm.claimForm837 = {};\n\n      let _cptControls = this.ClaimForm.get('CPTInputs');\n\n      this.createClaimForm.claimForm837.fromDos = _cptControls.at(0).get('DateOfServiceFrom').value; // this.ClaimForm.controls.DateOfServiceFrom.value;\n\n      this.createClaimForm.claimForm837.claimDosfrom = _cptControls.at(0).get('DateOfServiceFrom').value; //this.ClaimForm.controls.DateOfServiceFrom.value;\n\n      this.createClaimForm.claimForm837.claimDosto = _cptControls.at(0).get('DateOfServiceTo').value; //this.ClaimForm.controls.DateOfServiceTo.value;\n\n      this.createClaimForm.claimForm837.claimsProfessional837 = {};\n      this.createClaimForm.claimForm837.claimsProfessional837.prv03ProviderTaxonomyCode = this.ClaimForm.controls.ProviderSpecialty.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional = {};\n      this.createClaimForm.claimForm837.claimsProfessional837.clm0501PlaceOfServiceCode = _cptControls.at(0).get('PlaceOfService3').value; // this.ClaimForm.controls.PlaceOfService3.value;\n      //#region Facility Info\n\n      this.createClaimForm.claimForm837.claimsProfessional837.n301LabFacilityAddress1 = this.ClaimForm.controls.FacilityLocationADDRESSLINE2A.value == \"\" ? null : this.ClaimForm.controls.FacilityLocationADDRESSLINE2A.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.n302LabFacilityAddress2 = this.ClaimForm.controls.FacilityLocationADDRESSLINE2B.value == \"\" ? null : this.ClaimForm.controls.FacilityLocationADDRESSLINE2B.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.n401LabFacilityCity = this.ClaimForm.controls.FacilityLocationCity.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.n402LabFacilityState = this.ClaimForm.controls.FacilityLocationState.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.n403LabFacilityZip = this.ClaimForm.controls.FacilityLocationZip.value;\n\n      if (this.facilityNPI == true) {\n        this.createClaimForm.claimForm837.claimsProfessional837.nm103LabFacilityName = this.ClaimForm.controls.FacilityName.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.nm109LabFacilityIdentifier = this.ClaimForm.controls.FacilityId.value;\n      } else {\n        this.createClaimForm.claimForm837.claimsProfessional837.nm103LabFacilityName = this.ClaimForm.controls.BillingProvider.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.nm109LabFacilityIdentifier = this.ClaimForm.controls.GroupNPI.value;\n      } //#endregion\n      //#region Rendering Provider Info\n\n\n      this.createClaimForm.claimForm837.claimsProfessional837.nm103RenderingProviderLastOrOrganizationName = this.ClaimForm.controls.RenderingProvider.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.nm105RenderingProviderMiddle = this.ClaimForm.controls.RenderingProviderMiddleName.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.nm104RenderingProviderFirst = this.ClaimForm.controls.RenderingProviderName.value; // this.createClaimForm.claimForm837.claimsProfessional837.nm102RenderingProviderTypeQualifier = this.ClaimForm.controls.IdQual.value;\n\n      this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.billingGroupIdQlfr = this.ClaimForm.controls.IdQual.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.outsideLab = this.ClaimForm.controls.OutsideLab.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientOrAuthorizedSignatureDate = _cptControls.at(0).get('DateOfServiceFrom').value; // this.ClaimForm.controls.DateOfServiceFrom.value;\n\n      this.createClaimForm.claimForm837.claimsProfessional837.nm109RenderingProviderIdentifier = this.ClaimForm.controls.RenderingProvider.value; //#endregion\n      //#region Subscriber Professional 837 Info\n\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation = {};\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation = {};\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.nm109SubscriberIdCode = this.ClaimForm.controls.InsuredSIDNumber.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.sbr09ClaimFilingIndicatorCode = this.ClaimForm.controls.insuranceType.value; //this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.sbr05InsuranceTypeCode = this.ClaimForm.controls.insuranceType.value;\n\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.nm104SubscriberFirst = this.ClaimForm.controls.InsuredFirstName.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.nm105SubscriberMiddle = this.ClaimForm.controls.InsuredMiddleInitial.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.nm103SubscriberLastOrOrganizationName = this.ClaimForm.controls.InsuredLastName.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.dmg02SubscriberBirthDate = this.ClaimForm.controls.InsuredDateOfBirth.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.n301SubscriberAddr1 = this.ClaimForm.controls.InsuredStreetNumber.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.n302SubscriberAddr2 = this.ClaimForm.controls.InsuredAddressLine2.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.n401SubscriberCity = this.ClaimForm.controls.InsuredCity.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.n402SubscriberState = this.ClaimForm.controls.InsuredState.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.n403SubscriberZip = this.ClaimForm.controls.InsuredZip.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.dmg03SubscriberGenderCode = this.ClaimForm.controls.SexOfInsured.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.per04SubscriberPhoneNo = this.ClaimForm.controls.InsuredTelephone.value; //#endregion\n      // this.selectedBillingProvider[0].payToDetails[0].payToStreet;\n      //#region Biller Information\n\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.nm103BillingProviderLastOrOrganizationName = this.ClaimForm.controls.BillingProvider.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.nm104BillingProviderFirst = this.ClaimForm.controls.BillingProviderFIRSTNAME.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.nm105BillingProviderMiddle = this.ClaimForm.controls.BillingProviderMIDDLENAME.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.n301BillingProviderAddr1 = this.ClaimForm.controls.BillingProviderAddress.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.n302BillingProviderAddr2 = this.ClaimForm.controls.BillingProviderADDRESSLINE2.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.n401BillingProviderCity = this.ClaimForm.controls.BillingProviderCity.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.n402BillingProviderState = this.ClaimForm.controls.BillingProviderState.value;\n\n      if (this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.perAdministrativeCommunicationContactProfessional837s == null) {\n        this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.perAdministrativeCommunicationContactProfessional837s = [];\n      }\n\n      if (this.selectedBillingProvider[0].contactPeople !== null) {\n        if (this.selectedBillingProvider[0].contactPeople.length > 0) {\n          this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.perAdministrativeCommunicationContactProfessional837s.push({\n            per0xPhoneNo: this.selectedBillingProvider[0].contactPeople[0].contactPersonPhone,\n            per02ContactName: this.selectedBillingProvider[0].contactPeople[0].contactPersonName,\n            per0xEmail: this.selectedBillingProvider[0].contactPeople[0].contactPersonEmail,\n            per0xFaxNo: this.selectedBillingProvider[0].contactPeople[0].contactPersonFax\n          });\n        }\n      }\n\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.n403BillingProviderZip = this.ClaimForm.controls.BillingProviderZip.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.prv03BillingProviderIdCode = this.ClaimForm.controls.Taxonomy.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.nm109BillingProviderIdCode = this.ClaimForm.controls.GroupNPI.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.renderingProviderPin = this.ClaimForm.controls.RenderingProviderPin.value; //#endregion\n      //#region Dependent Professional\n\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s = [];\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s = [{\n        dmg02PatientBirthDate: this.ClaimForm.controls.PatientDOB.value,\n        dmg03PatientGenderCode: this.ClaimForm.controls.PatientGender.value,\n        n301PatientAddr1: this.ClaimForm.controls.PatientStreetNumber.value,\n        n302PatientAddr2: this.ClaimForm.controls.PatientAddressLine2.value,\n        n401PatientCity: this.ClaimForm.controls.PatientCity.value,\n        n402PatientState: this.ClaimForm.controls.PatientState.value,\n        n403PatientZip: this.ClaimForm.controls.PatientZip.value,\n        nm103PatientLastOrOrganizationName: this.ClaimForm.controls.PatientLastName.value,\n        nm104PatientFirst: this.ClaimForm.controls.PatientFirstName.value,\n        nm105PatientMiddle: this.ClaimForm.controls.PatientMiddleInitial.value,\n        pat01IndividualRelationshipCode: this.ClaimForm.controls.PatientRelationshipToInsured.value,\n        per04PatientPhoneNo: this.ClaimForm.controls.PatientTelephone.value\n      }];\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s.push; //#endregion\n      //#region Payer Detail\n\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.nm109PayerIdCode = this.ClaimForm.controls.PayerID.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.nm103PayerLastOrOrganizatioName = this.ClaimForm.controls.PayerName.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.n301PayerAddr1 = this.ClaimForm.controls.PayerAddress1.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.n302PayerAddr2 = this.ClaimForm.controls.PayerAddress2.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.n401PayerCity = this.ClaimForm.controls.PayerCity.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.n403PayerZip = this.ClaimForm.controls.PayerZip.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.n402PayerState = this.ClaimForm.controls.PayerState.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.billingGroupNumber = this.ClaimForm.controls.ProviderId.value; //#endregion\n      //#region Service Professional Array\n\n      this.createClaimForm.claimForm837.claimsProfessional837.serviceLineProfessional837s = [];\n      const cptControls = this.ClaimForm.get('CPTInputs'); //const cptControls2 = this.ClaimForm.get('CPTInputs2') as FormArray;\n\n      var TotalCharge = 0;\n      var charges = 0;\n\n      for (let i = 0; i < cptControls.length; i++) {\n        charges = cptControls.at(i).get('DaysAndUnitCharges').value * cptControls.at(i).get('UnitCharges').value;\n        TotalCharge = TotalCharge + charges;\n        this.createClaimForm.claimForm837.claimsProfessional837.serviceLineProfessional837s.push({\n          n301ServiceFacilityAddress1: this.ClaimForm.controls.FacilityLocationADDRESSLINE2A.value == \"\" ? null : this.ClaimForm.controls.FacilityLocationADDRESSLINE2A.value,\n          n302ServiceFacilityAddress2: this.ClaimForm.controls.FacilityLocationADDRESSLINE2B.value == \"\" ? null : this.ClaimForm.controls.FacilityLocationADDRESSLINE2B.value,\n          n401ServiceFacilityCity: this.ClaimForm.controls.FacilityLocationCity.value,\n          n402ServiceFacilityState: this.ClaimForm.controls.FacilityLocationState.value,\n          n403ServiceFacilityZip: this.ClaimForm.controls.FacilityLocationZip.value,\n          nm103RenderingProviderNameLastOrg: this.ClaimForm.controls.RenderingProviderName.value,\n          nm109RenderingProviderId: this.ClaimForm.controls.ProviderNPI.value,\n          nm109ServiceFacilityId: this.ClaimForm.controls.FacilityId.value,\n          sv10102ProcedureCode: cptControls.at(i).get('option').value,\n          sv105PlaceOfServiceCode: cptControls.at(i).get('PlaceOfService3').value,\n          sv10103ProcedureModifier1: cptControls.at(i).get('Modifier1').value,\n          sv10104ProcedureModifier2: cptControls.at(i).get('Modifier2').value,\n          sv10105ProcedureModifier3: cptControls.at(i).get('Modifier3').value,\n          sv10106ProcedureModifier4: cptControls.at(i).get('Modifier4').value,\n          sv10701DiagnosisCodePointer1: cptControls.at(i).get('DiagnosisPointer1').value,\n          sv10702DiagnosisCodePointer2: cptControls.at(i).get('DiagnosisPointer2').value,\n          sv10703DiagnosisCodePointer3: cptControls.at(i).get('DiagnosisPointer3').value,\n          sv10704DiagnosisCodePointer4: cptControls.at(i).get('DiagnosisPointer4').value,\n          sv102LineItemChargeAmount: cptControls.at(i).get('UnitCharges').value,\n          sv104ServiceUnitCount: cptControls.at(i).get('DaysAndUnitCharges').value,\n          DOSFrom: cptControls.at(i).get('DateOfServiceFrom').value,\n          DOSTo: cptControls.at(i).get('DateOfServiceTo').value,\n          sv111EpsdtIndicator: cptControls.at(i).get('EPSDT').value,\n          svEmg: cptControls.at(i).get('locationOfService6').value //this.ClaimForm.controls.locationOfService6.value,\n\n        });\n        this.createClaimForm.claimForm837.fromDos = cptControls.at(i).get('DateOfServiceFrom').value;\n        this.createClaimForm.claimForm837.claimDosfrom = cptControls.at(i).get('DateOfServiceFrom').value; //#endregion\n        //#region ICD Information Array\n\n        this.createClaimForm.claimForm837.claimsProfessional837.claimListDetails = [];\n        this.createClaimForm.claimForm837.claimsProfessional837.claimListDetails = [];\n        this.createClaimForm.claimForm837.claimsProfessional837.claimListDetails.push({\n          iCDCodes: [{\n            iCD: this.ClaimForm.controls.ICDInput1.value == undefined ? null : this.ClaimForm.controls.ICDInput1.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput2.value == undefined ? null : this.ClaimForm.controls.ICDInput2.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput3.value == undefined ? null : this.ClaimForm.controls.ICDInput3.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput4.value == undefined ? null : this.ClaimForm.controls.ICDInput4.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput5.value == undefined ? null : this.ClaimForm.controls.ICDInput5.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput6.value == undefined ? null : this.ClaimForm.controls.ICDInput6.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput7.value == undefined ? null : this.ClaimForm.controls.ICDInput7.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput8.value == undefined ? null : this.ClaimForm.controls.ICDInput8.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput9.value == undefined ? null : this.ClaimForm.controls.ICDInput9.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput10.value == undefined ? null : this.ClaimForm.controls.ICDInput10.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput11.value == undefined ? null : this.ClaimForm.controls.ICDInput11.value\n          }, {\n            iCD: this.ClaimForm.controls.ICDInput12.value == undefined ? null : this.ClaimForm.controls.ICDInput12.value\n          }]\n        });\n\n        for (let i = 0; i < this.createClaimForm.claimForm837.claimsProfessional837.claimListDetails.length; i++) {\n          if (this.createClaimForm.claimForm837.claimsProfessional837.claimListDetails[i].iCDCodes != null) {\n            this.createClaimForm.claimForm837.claimsProfessional837.claimListDetails[i].serviceLineDetails = [];\n            this.createClaimForm.claimForm837.claimsProfessional837.claimListDetails[i].serviceLineDetails = this.createClaimForm.claimForm837.claimsProfessional837.serviceLineProfessional837s;\n          }\n        } //#endregion\n        //#region Other Details\n\n\n        this.createClaimForm.claimForm837.claimsProfessional837.clm08BenefitsAssignmentCertIndicator = this.ClaimForm.controls.AcceptanceAssignment.value;\n        this.createClaimForm.claimForm837.paidAmount = this.ClaimForm.controls.AmountPaid.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.nm109RenderingProviderIdentifier = this.ClaimForm.controls.ProviderNPI.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.insurancePlanName = this.ClaimForm.controls.InsurancePlanName.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.insuredOtherHealthBenefitPlan = this.ClaimForm.controls.AnotherHealthBenefitPlan.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.clm06SupplierSignatureIndicator = this.ClaimForm.controls.PersonsSignature.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.clm08BenefitsAssignmentCertIndicator = this.ClaimForm.controls.AuthorizedPersonsSignature.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientOrAuthorizedSignature = this.ClaimForm.controls.PersonsSignature.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.clm0503ClaimFrequencyCode = this.ClaimForm.controls.ResubmissionCode.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.insuredSignature = this.ClaimForm.controls.AuthorizedPersonsSignature.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientConditionRelatedToOtherAccident = this.ClaimForm.controls.OtherAccidents.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientConditionRelatedToAutoAccident = this.ClaimForm.controls.AutoAccident.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientConditionRelatedToAutoAccidentState = this.ClaimForm.controls.AutoAccidentState.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.clm1104AutoAccidentStateCode = this.ClaimForm.controls.AutoAccidentState.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.clm1101RelatedCausesCode = this.ClaimForm.controls.Employment.value == 'Y' ? 'EM' : null;\n        this.createClaimForm.claimForm837.claimsProfessional837.clm1102RelatedCausesCode = this.ClaimForm.controls.AutoAccident.value == 'Y' ? 'AA' : null;\n        this.createClaimForm.claimForm837.claimsProfessional837.clm1103RelatedCausesCode = this.ClaimForm.controls.OtherAccidents.value == 'Y' ? 'OA' : null;\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientConditionRelatedToEmp = this.ClaimForm.controls.Employment.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.ref02BillingProviderEmployerId = this.ClaimForm.controls.FederalTaxNumber.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.dateOfCurrentIllnessQlfr = this.ClaimForm.controls.Qual.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.dtp03OnsetofCurrentIllnessInjuryDate = this.ClaimForm.controls.QualDate.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.nm109SubscriberIdCode = this.SubscriberIdCode.toString();\n        this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.per04SubscriberPhoneNo = this.ClaimForm.controls.InsuredTelephone.value;\n        this.createClaimForm.claimForm837.displayMemberFirstnameName = this.ClaimForm.controls.InsuredFirstName.value;\n        this.createClaimForm.claimForm837.displayMemberLastName = this.ClaimForm.controls.InsuredLastName.value;\n        this.createClaimForm.claimForm837.facilityId = this.ClaimForm.controls.FacilityId.value;\n        this.createClaimForm.claimForm837.formCreatedDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.icdidentifier = this.ClaimForm.controls.ICD.value;\n\n        if (this.selectedMember.length > 0) {\n          this.createClaimForm.claimForm837.ipacode = this.selectedMember[0].ipaCode;\n        } else {\n          this.createClaimForm.claimForm837.ipacode = this.selectedIpa;\n        }\n\n        this.createClaimForm.claimForm837.insuranceCompany = this.ClaimForm.controls.PayerName.value;\n        this.createClaimForm.claimForm837.lastModifiedBy = JSON.parse(localStorage.getItem('email'));\n        this.createClaimForm.claimForm837.lastModifiedByFirstName = localStorage.getItem('userName');\n        this.createClaimForm.claimForm837.lastModifiedDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');\n        this.createClaimForm.claimForm837.userName = localStorage.getItem('userName');\n        this.createClaimForm.claimForm837.claimFormStatusCode = \"ON\";\n        this.createClaimForm.claimForm837.uuid = JSON.parse(localStorage.getItem('uuid'));\n        this.createClaimForm.claimForm837.claimType = this.ClaimForm.controls.ClaimType.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.sbr02IndividualRelationshipCode = this.ClaimForm.controls.PatientRelationshipToInsured.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.hcp14PolicyComplianceCode = this.ClaimForm.controls.FECANumber.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientOtherDateQlfr = this.ClaimForm.controls.Qual2.value; //#endregion\n        //#region Referring Provider\n\n        this.createClaimForm.claimForm837.claimsProfessional837.nm109ReferringProviderIdentifier = this.ClaimForm.controls.ReferringProviderNPI.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.nm103ReferringProviderLastName = this.ClaimForm.controls.ReferringProviderLastName.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.nm104ReferringProviderLastFirst = this.ClaimForm.controls.ReferringProviderFirstName.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.nm105ReferringProviderLastMiddle = this.ClaimForm.controls.ReferringProviderMiddleName.value;\n        this.createClaimForm.claimForm837.claimsProfessional837.nm107ReferringProviderLastSuffix = this.ClaimForm.controls.NameOfReferringProvider.value; //#endregion\n      }\n\n      this.createClaimForm.claimForm837.totalCharges = TotalCharge.toString();\n      this.createClaimForm.claimForm837.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientOtherDate = this.ClaimForm.controls.OtherDateQual.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.dtp03LastWorkedDate = this.ClaimForm.controls.PatientUnableToWorkInCurrentOccupationFrom.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.dtp03WorkReturnDate = this.ClaimForm.controls.PatientUnableToWorkInCurrentOccupationTo.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.dtp03HospitalizationAdmissionDate = this.ClaimForm.controls.HospitalizationDatesRelatedToCurrentServicesFrom.value;\n      this.createClaimForm.claimForm837.claimsProfessional837.dtp03HospitalizationDischargeDate = this.ClaimForm.controls.HospitalizationDatesRelatedToCurrentServicesTo.value;\n    }\n\n    fetchClaimDataFromMaster() {\n      this.masterDataService.fetchchMasterData(\"Providertype\").pipe(first()).subscribe(response => {\n        this.typeOfProvider = response;\n      });\n    }\n\n    ChangeFacilityNPI(event) {\n      if (event.currentTarget.checked) {\n        this.ClaimForm.controls.FacilityName.setValue(this.memberProviderDetail.facility.facilityName);\n        this.ClaimForm.controls.FacilityLocationNPI.setValue(this.memberProviderDetail.facility.facilityOrganizationNPI);\n        this.facilityNPI = true;\n      } else {\n        this.ClaimForm.controls.FacilityName.setValue(this.ClaimForm.controls.BillingProvider.value);\n        this.ClaimForm.controls.FacilityLocationNPI.setValue(this.ClaimForm.controls.GroupNPI.value);\n        this.facilityNPI = false;\n      }\n    }\n\n    SetBillingProviderThroughName() {\n      if (this.ClaimForm.controls.BillingProvider.value != null && this.ClaimForm.controls.BillingProvider.value != '') {\n        this.spinner.show();\n        this.billingProviderSearch.paytoName = this.ClaimForm.controls.BillingProvider.value.trim();\n        this.fetchBillingProviderResult();\n      } else {\n        this.ResetBillingProviderModalData();\n        this.changeDetect.detectChanges();\n        this.notificationService.showError('Please enter Billing Provider', '', 4000);\n      }\n    }\n\n    fetchBillingProviderResult() {\n      this.billingProviderService.fetchBillingProviderResult(this.billingProviderSearch).pipe(first()).subscribe(data => {\n        this.billingProviderResult = data;\n        this.selectedBillingProvider = this.billingProviderResult;\n        this.spinner.hide();\n        this.ResetBillingProviderModalData();\n        this.changeDetect.detectChanges();\n\n        if (this.billingProviderResult != null ? this.billingProviderResult.length == 1 : false) {\n          this.MapBillingProvider();\n        } else if (this.billingProviderResult != null ? this.billingProviderResult.length > 1 : false) {\n          this.OnBillingProvider();\n        } else {\n          this.ClaimForm.controls.BillingProvider.setValue(this.billingProviderSearch.paytoName);\n          this.billingProviderSearch.paytoName = '';\n          this.notificationService.showError('No result found for Billing Provider', '', 4000);\n          this.changeDetect.detectChanges();\n        }\n      }, error => {});\n    }\n\n    OnBillingProvider() {\n      const dialogConfig = new MatDialogConfig();\n      dialogConfig.height = '70%';\n      dialogConfig.width = '80%';\n      dialogConfig.panelClass = 'custom-dialog-containers';\n      dialogConfig.data = {\n        name: \"Insured's I.D. Number\",\n        title: \"Are you sure you want to logout?\",\n        NameToSearch: this.billingProviderSearch.paytoName,\n        TableContent: [{\n          heading: 'Last Name',\n          type: 'string',\n          inputName: 'lastName',\n          inputType: 'text'\n        }, {\n          heading: 'First Name',\n          type: 'string',\n          inputName: 'firstName',\n          inputType: 'text'\n        }, {\n          heading: 'DOB',\n          type: 'string',\n          inputName: 'DOB',\n          inputType: 'date'\n        }, {\n          heading: 'Gender',\n          type: 'string',\n          inputName: 'Gender',\n          inputType: 'select'\n        }],\n        headings: ['InsuredId', 'LastName', 'FirstName', 'DOB', 'Gender'],\n        description: \"Pretend this is a convincing argument on why you shouldn't logout :)\",\n        actionButtonText: \"Logout\"\n      };\n      const dialogRef = this.dialog.open(BillingProviderPopupComponent, dialogConfig);\n      dialogRef.afterClosed().subscribe(result => {\n        //this.selectedBillingProvider[0].payToDetails[0].iPADetails=[];\n        if (result != true) {\n          this.selectedBillingProvider = result;\n          this.MapBillingProvider();\n          this.SetBillingProviderThroughName();\n        }\n      });\n    }\n\n    MapBillingProvider() {\n      this.ClaimForm.controls.BillingProvider.setValue(this.selectedBillingProvider[0].payToDetails[0].paytoName);\n      this.isBillingProviderEnabled = true;\n      this.ClaimForm.controls.BillingProviderAddress.setValue(this.selectedBillingProvider[0].payToDetails[0].payToStreet);\n      this.ClaimForm.controls.BillingProviderADDRESSLINE2.setValue(this.selectedBillingProvider[0].payToDetails[0].payToSuite);\n      this.ClaimForm.controls.BillingProviderState.setValue(this.selectedBillingProvider[0].payToDetails[0].payToState);\n      this.ClaimForm.controls.BillingProviderCity.setValue(this.selectedBillingProvider[0].payToDetails[0].payToCity);\n      this.ClaimForm.controls.FederalTaxNumber.setValue(this.selectedBillingProvider[0].taxIdOrSSN);\n      this.ClaimForm.controls.BillingProviderZip.setValue(this.selectedBillingProvider[0].payToDetails[0].payToZip);\n\n      if (this.selectedBillingProvider[0].contactPeople !== null && this.selectedBillingProvider[0].contactPeople.length > 0) {\n        if (this.selectedBillingProvider[0].contactPeople[0].contactPersonPhone != null) {\n          this.ClaimForm.controls.BillingProviderTelephone.setValue(this.selectedBillingProvider[0].contactPeople[0].contactPersonPhone);\n        }\n      }\n\n      this.ClaimForm.controls.GroupNPI.setValue(this.selectedBillingProvider[0].payToDetails[0].payToNPI);\n\n      if (this.facilityNPI) {\n        this.ClaimForm.controls.FacilityName.setValue(this.memberProviderDetail.facility.facilityName);\n        this.ClaimForm.controls.FacilityLocationNPI.setValue(this.memberProviderDetail.facility.facilityOrganizationNPI);\n      } else {\n        this.ClaimForm.controls.FacilityName.setValue(this.selectedBillingProvider[0].payToDetails[0].paytoName);\n        this.ClaimForm.controls.FacilityLocationNPI.setValue(this.selectedBillingProvider[0].payToDetails[0].payToNPI);\n      }\n\n      this.changeDetect.detectChanges();\n    }\n\n    ResetBillingProviderModalData() {\n      this.ClaimForm.controls.BillingProvider.setValue(\"\");\n      this.isBillingProviderEnabled = true;\n      this.ClaimForm.controls.BillingProviderAddress.setValue(\"\");\n      this.ClaimForm.controls.BillingProviderADDRESSLINE2.setValue(\"\");\n      this.ClaimForm.controls.BillingProviderState.setValue(\"\");\n      this.ClaimForm.controls.BillingProviderCity.setValue(\"\");\n      this.ClaimForm.controls.FederalTaxNumber.setValue(\"\");\n      this.ClaimForm.controls.BillingProviderZip.setValue(\"\");\n\n      if (this.selectedBillingProvider[0].contactPeople !== null && this.selectedBillingProvider[0].contactPeople.length > 0) {\n        if (this.selectedBillingProvider[0].contactPeople[0].contactPersonPhone != null) {\n          this.ClaimForm.controls.BillingProviderTelephone.setValue(\"\");\n        }\n      }\n\n      this.ClaimForm.controls.GroupNPI.setValue(\"\");\n\n      if (this.facilityNPI) {\n        this.ClaimForm.controls.FacilityName.setValue(\"\");\n        this.ClaimForm.controls.FacilityLocationNPI.setValue(\"\");\n      } else {\n        this.ClaimForm.controls.FacilityName.setValue(\"\");\n        this.ClaimForm.controls.FacilityLocationNPI.setValue(\"\");\n      }\n\n      this.changeDetect.detectChanges();\n    }\n\n    fetchMemberResult() {\n      //this.searchMember.isActive = true;\n      this.memberService.fetchMemberResult(this.searchMember).pipe(first()).subscribe(data => {\n        this.memberResult = data;\n        this.ResetBillingProvider();\n        this.ResetPayer();\n        this.ResetMemberAndPatient();\n        this.changeDetect.detectChanges();\n        this.spinner.hide(); // on member change the rendering provider get changed so we should remove if any rendering provider is selected before member change\n\n        this.selectedRenderingProvider = [];\n\n        if (this.memberResult != null && this.memberResult != undefined ? this.memberResult.length == 1 : false) {\n          var memberProfile = this.memberService.fetchMemberProfile(this.memberResult[0].uniqueMemberID, this.memberResult[0].planCode).subscribe(data => {\n            if (data != null) {\n              if (data.member != null) {\n                if (data.member.middleName != null) {\n                  this.memberResult[0].middleName = data.member.middleName;\n                }\n              }\n            }\n\n            this.selectedMember = this.memberResult;\n            this.MappingInsuredInfo();\n          });\n        } else if (this.memberResult != null && this.memberResult != undefined ? this.memberResult.length > 1 : false) {\n          this.OnInsuredIdClick();\n        } else {\n          this.ClaimForm.controls.InsuredSIDNumber.setValue(this.searchMember.searchParameter.subscriberID);\n          this.searchMember.searchParameter.subscriberID = '';\n          this.notificationService.showError('No result found for Provided Member ID', '', 4000);\n        }\n      }, error => {\n        this.spinner.hide();\n      });\n    }\n\n    SetInsuredThroughId() {\n      if (this.ClaimForm.controls.InsuredSIDNumber.value != null && this.ClaimForm.controls.InsuredSIDNumber.value != '') {\n        this.spinner.show();\n        this.searchMember.searchParameter.subscriberID = this.ClaimForm.controls.InsuredSIDNumber.value;\n        this.fetchMemberResult();\n      } else {\n        this.ResetBillingProvider();\n        this.ResetPayer();\n        this.ResetMemberAndPatient();\n        this.notificationService.showError('Please enter Insured ID', '', 4000);\n      }\n    } // to open generic method insured id with data sent in MatDialogConfig.data for insured id\n\n\n    OnInsuredIdClick() {\n      const dialogConfig = new MatDialogConfig();\n      dialogConfig.height = '70%';\n      dialogConfig.width = '80%';\n      dialogConfig.panelClass = 'custom-dialog-containers';\n      dialogConfig.data = {\n        name: \"Insured's I.D. Number\",\n        title: \"Are you sure you want to logout?\",\n        InsuredIdValueToSearch: this.searchMember.searchParameter.subscriberID,\n        TableContent: [{\n          heading: 'Last Name',\n          type: 'string',\n          inputName: 'lastName',\n          inputType: 'text'\n        }, {\n          heading: 'First Name',\n          type: 'string',\n          inputName: 'firstName',\n          inputType: 'text'\n        }, {\n          heading: 'DOB',\n          type: 'string',\n          inputName: 'DOB',\n          inputType: 'date'\n        }, {\n          heading: 'Gender',\n          type: 'string',\n          inputName: 'Gender',\n          inputType: 'select'\n        }],\n        headings: ['InsuredId', 'LastName', 'FirstName', 'DOB', 'Gender'],\n        description: \"Pretend this is a convincing argument on why you shouldn't logout :)\",\n        actionButtonText: \"Logout\"\n      };\n      const dialogRef = this.dialog.open(SearchInsuredPopupComponent, dialogConfig);\n      dialogRef.afterClosed().subscribe(result => {\n        console.log(result);\n        this.selectedMember = result;\n        this.ResetBillingProvider();\n        this.ResetPayer();\n        this.ResetMemberAndPatient();\n        this.MappingInsuredInfo();\n      });\n    }\n\n    ResetBillingProvider() {\n      this.selectedBillingProvider = null;\n      this.ClaimForm.controls.BillingProvider.setValue(\"\");\n      this.isBillingProviderEnabled = false;\n      this.ClaimForm.controls.BillingProviderAddress.setValue(\"\");\n      this.ClaimForm.controls.BillingProviderState.setValue(\"\");\n      this.ClaimForm.controls.BillingProviderCity.setValue(\"\");\n      this.ClaimForm.controls.FederalTaxNumber.setValue(\"\");\n      this.ClaimForm.controls.BillingProviderZip.setValue(\"\");\n      this.ClaimForm.controls.GroupNPI.setValue(\"\");\n      this.ClaimForm.controls.FacilityName.setValue(\"\");\n      this.ClaimForm.controls.FacilityLocationNPI.setValue(\"\");\n      this.ClaimForm.controls.FacilityName.setValue(\"\");\n      this.ClaimForm.controls.FacilityLocationNPI.setValue(\"\");\n      this.ClaimForm.controls.NameOfReferringProvider.setValue(\"\");\n      this.ClaimForm.controls.ReferringProviderNPI.setValue(\"\");\n      this.ClaimForm.controls.ReferringProviderFirstName.setValue(\"\");\n      this.ClaimForm.controls.ReferringProviderLastName.setValue(\"\");\n      this.ClaimForm.controls.ReferringProviderMiddleName.setValue(\"\");\n      this.ClaimForm.controls.Taxonomy.setValue(\"\"); // Reseting Insured Rendering PRovider\n\n      this.ClaimForm.controls.RenderingProviderName.setValue(\"\");\n      this.ClaimForm.controls.RenderingProvider.setValue(\"\");\n      this.ClaimForm.controls.RenderingProviderMiddleName.setValue(\"\");\n      this.ClaimForm.controls.ProviderNPI.setValue(\"\");\n      this.ClaimForm.controls.ProviderPIN.setValue(\"\");\n      this.ClaimForm.controls.ProviderSpecialty.setValue(\"\");\n      var arrayControl2 = this.ClaimForm.get('CPTInputs');\n\n      for (let i = 0; i < arrayControl2.length; i++) {\n        this.ClaimForm.get('CPTInputs').at(i).controls['JRenderingProviderId'].setValue(\"\");\n      }\n\n      this.changeDetect.detectChanges();\n    }\n\n    ResetPayer() {\n      this.ClaimForm.controls.PayerID.setValue(\"\");\n      this.ClaimForm.controls.PayerAddress1.setValue(\"\");\n      this.ClaimForm.controls.PayerAddress2.setValue(\"\");\n      this.ClaimForm.controls.PayerName.setValue(\"\");\n      this.ClaimForm.controls.PayerState.setValue(\"\");\n      this.ClaimForm.controls.PayerZip.setValue(\"\");\n      this.ClaimForm.controls.PayerCity.setValue(\"\");\n    }\n\n    ResetMemberAndPatient() {\n      this.ClaimForm.controls.InsuredFirstName.setValue(\"\");\n      this.isInsuredEnabled = false;\n      this.ClaimForm.controls.InsuredLastName.setValue(\"\");\n      this.ClaimForm.controls.InsuredMiddleInitial.setValue(\"\");\n      this.ClaimForm.controls.InsuredStreetNumber.setValue(\"\");\n      this.ClaimForm.controls.InsuredAddressLine2.setValue(\"\");\n      this.ClaimForm.controls.InsuredZip.setValue(\"\");\n      this.ClaimForm.controls.InsuredCity.setValue(\"\");\n      this.ClaimForm.controls.InsuredTelephone.setValue(\"\");\n      this.ClaimForm.controls.SexOfInsured.setValue(\"\");\n      this.ClaimForm.controls.AnotherHealthBenefitPlan.setValue(\"\");\n      this.ClaimForm.controls.InsuredDateOfBirth.setValue(\"\");\n      this.ClaimForm.controls.InsuredSIDNumber.setValue(\"\");\n      this.SelectedInsuredState = \"\";\n      this.ClaimForm.controls.COPYFROMINSUREDPERSON.setValue(false);\n      this.copyInsured = true;\n      this.ClaimForm.controls.PatientRelationshipToInsured.setValue(\"18\");\n      this.copyInsuredInfo();\n    }\n\n    MappingInsuredInfo() {\n      if (this.selectedMember.length > 0) {\n        this.memberService.fetchPayerDetail(this.selectedMember[0].planCode).pipe(first()).subscribe(payer => {\n          this.ClaimForm.controls.PayerID.setValue(payer.payerId);\n          this.ClaimForm.controls.PayerAddress1.setValue(payer.payerFirstAddress);\n          this.ClaimForm.controls.PayerAddress2.setValue(payer.payerSecondAddress);\n          this.ClaimForm.controls.PayerName.setValue(payer.payerName);\n          this.ClaimForm.controls.PayerState.setValue(payer.payerState);\n          this.ClaimForm.controls.PayerZip.setValue(payer.payerZip);\n          this.ClaimForm.controls.PayerCity.setValue(payer.payerCity);\n        });\n        this.memberService.fetchMemberProvider(this.selectedMember[0]).pipe(first()).subscribe(data => {\n          this.memberProviderDetail = data;\n\n          if (this.memberProviderDetail.renderingProvider != null) {\n            this.ClaimForm.controls.RenderingProviderName.setValue(this.memberProviderDetail.renderingProvider.providerFirstName);\n            this.ClaimForm.controls.RenderingProvider.setValue(this.memberProviderDetail.renderingProvider.providerLastName);\n            this.ClaimForm.controls.RenderingProviderMiddleName.setValue(this.memberProviderDetail.renderingProvider.providerMiddleName); // this.ClaimForm.controls.RenderingProvider.setValue(this.memberProviderDetail.renderingProvider.providerLastName);\n\n            this.ClaimForm.controls.ProviderNPI.setValue(this.memberProviderDetail.renderingProvider.providerNPI);\n            this.ClaimForm.controls.ProviderPIN.setValue(this.memberProviderDetail.renderingProvider.providerZip); // this.ClaimForm.controls.RenderingProviderMiddleName.setValue(this.memberProviderDetail.renderingProvider.providerMiddleName);\n            //this.ClaimForm.controls.ProviderId.setValue(this.memberProviderDetail.renderingProvider.providerUniqueId);\n\n            this.ClaimForm.controls.ProviderSpecialty.setValue(this.memberProviderDetail.renderingProvider.taxonomy); //this.ClaimForm.controls.ProviderId.setValue(this.memberProviderDetail.renderingProvider.providerUniqueId);\n\n            var arrayControl2 = this.ClaimForm.get('CPTInputs');\n\n            for (let i = 0; i < arrayControl2.length; i++) {\n              this.ClaimForm.get('CPTInputs').at(i).controls['JRenderingProviderId'].setValue(this.memberProviderDetail.renderingProvider.providerNPI);\n            }\n          }\n\n          if (this.memberProviderDetail.facility != null) {\n            //this.ClaimForm.controls.FacilityId.setValue(this.memberProviderDetail.facility.facilityId);\n            this.ClaimForm.controls.FacilityName.setValue(this.memberProviderDetail.facility.facilityName);\n            this.ClaimForm.controls.FacilityLocationADDRESSLINE2A.setValue(this.memberProviderDetail.facility.facilityAddress1);\n            this.ClaimForm.controls.FacilityLocationADDRESSLINE2B.setValue(this.memberProviderDetail.facility.facilityAddress2);\n            this.ClaimForm.controls.FacilityLocationCity.setValue(this.memberProviderDetail.facility.facilityCity);\n            this.ClaimForm.controls.FacilityLocationState.setValue(this.memberProviderDetail.facility.facilityState);\n            this.ClaimForm.controls.FacilityLocationZip.setValue(this.memberProviderDetail.facility.facilityZip);\n            this.ClaimForm.controls.FacilityLocationNPI.setValue(this.memberProviderDetail.facility.facilityOrganizationNPI);\n          }\n        });\n        this.SubscriberIdCode = this.selectedMember[0].subscriberID;\n      }\n\n      this.ClaimForm.controls.InsuredFirstName.setValue(this.selectedMember[0].firstName);\n      this.isInsuredEnabled = true;\n      this.ClaimForm.controls.InsuredLastName.setValue(this.selectedMember[0].lastName);\n      this.ClaimForm.controls.InsuredMiddleInitial.setValue(this.selectedMember[0].middleName);\n      const InsuredAddress = this.selectedMember[0].addressLine1.split(',');\n      if (InsuredAddress != null ? InsuredAddress.length > 0 : false) this.ClaimForm.controls.InsuredStreetNumber.setValue(InsuredAddress[0]);\n      if (InsuredAddress != null ? InsuredAddress.length > 1 : false) this.ClaimForm.controls.InsuredAddressLine2.setValue(InsuredAddress[1]);\n      this.ClaimForm.controls.InsuredZip.setValue(this.selectedMember[0].zipCode);\n      this.ClaimForm.controls.InsuredCity.setValue(this.selectedMember[0].city);\n      this.ClaimForm.controls.InsuredTelephone.setValue(this.selectedMember[0].phoneNumber);\n      this.ClaimForm.controls.InsuredState.setValue(this.selectedMember[0].state); //These two field was empty in Existing Application So it is commented\n      //this.ClaimForm.controls.FECANumber.setValue(this.selectedMember[0].planCode);\n      //this.ClaimForm.controls.InsurancePlanName.setValue(this.selectedMember[0].planName);\n\n      const InsuredGender = this.selectedMember[0].gender != null ? this.selectedMember[0].gender.toLowerCase() : this.selectedMember[0].gender;\n\n      if (InsuredGender != null) {\n        this.ClaimForm.controls.SexOfInsured.setValue(InsuredGender.includes('f') ? 'F' : InsuredGender.includes('o') ? 'O' : 'M');\n      } else {\n        this.ClaimForm.controls.SexOfInsured.setValue('');\n      }\n\n      if (this.selectedMember[0].isSecondaryPlanExist) {\n        this.ClaimForm.controls.AnotherHealthBenefitPlan.setValue('Yes');\n      } else {\n        this.ClaimForm.controls.AnotherHealthBenefitPlan.setValue('No');\n      }\n\n      this.ClaimForm.controls.InsuredDateOfBirth.setValue(formatDate(this.selectedMember[0].dateOfBirth, 'yyyy-MM-dd', 'en'));\n      this.ClaimForm.controls.InsuredSIDNumber.setValue(this.selectedMember[0].subscriberID);\n      this.SelectedInsuredState = this.allStatesBySearchstring.filter(option => option.stateCode === this.selectedMember[0].state)[0].stateName;\n      this.ClaimForm.controls.COPYFROMINSUREDPERSON.setValue(true);\n      this.copyInsured = false;\n      this.ClaimForm.controls.PatientRelationshipToInsured.setValue('18');\n      this.copyInsuredInfo();\n    }\n\n    SetRenderingProviderThroughId() {\n      if (this.ClaimForm.controls.RenderingProvider.value != null && this.ClaimForm.controls.RenderingProvider.value != '') {\n        this.spinner.show();\n        this.renderingProviderDetail.firstName = this.ClaimForm.controls.RenderingProvider.value.trim();\n        this.fetchRenderingProviderResult();\n      } else {\n        this.ResetRenderingProvider();\n        this.changeDetect.detectChanges();\n        this.notificationService.showError('Please enter Rendering Provider', '', 4000);\n      }\n    }\n\n    fetchRenderingProviderResult() {\n      this.referringProviderService.fetchProviderResult(this.renderingProviderDetail).pipe(first()).subscribe(data => {\n        this.renderingProviderResult = data;\n        this.selectedRenderingProvider = this.renderingProviderResult;\n        this.spinner.hide();\n        this.ResetRenderingProvider();\n        this.changeDetect.detectChanges();\n\n        if (this.renderingProviderResult != null ? this.renderingProviderResult.length == 1 : false) {\n          this.MapRenderingProvider();\n        } else if (this.renderingProviderResult.length > 1) {\n          this.OnRenderingProviderClick();\n        } else {\n          this.ClaimForm.controls.RenderingProvider.setValue(this.renderingProviderDetail.firstName);\n          this.notificationService.showError('No result found for Provider Name', '', 4000);\n          this.changeDetect.detectChanges();\n        }\n      }, error => {});\n    } // to open generic method insured id with data sent in MatDialogConfig.data for Rendering provider Id\n\n\n    OnRenderingProviderClick() {\n      const dialogConfig = new MatDialogConfig();\n      dialogConfig.data = {\n        name: \"Rendering Provider NPI\",\n        title: \"Are you sure you want to logout?\",\n        RenderingProviderName: this.renderingProviderDetail.firstName,\n        TableContent: [{\n          heading: 'Name',\n          inputName: \"name\",\n          type: 'string',\n          inputType: 'text'\n        }, {\n          heading: 'NPI',\n          type: 'string',\n          inputName: \"providerNPI\",\n          inputType: 'text'\n        }, {\n          heading: 'Address',\n          type: 'string',\n          inputName: \"providerFirstAddress\",\n          inputType: 'text'\n        }, {\n          heading: 'Tax ID',\n          type: 'string',\n          inputName: \"taxId\",\n          inputType: 'number'\n        }, {\n          heading: 'Plan Name',\n          type: 'string',\n          inputName: \"planName\",\n          inputType: 'text'\n        }, {\n          heading: 'IPA Name',\n          type: 'string',\n          inputName: \"ipaName\",\n          inputType: 'text'\n        }],\n        headings: ['PlanName', 'Name', 'TaxId', 'Address', 'IPAName'],\n        description: \"Pretend this is a convincing argument on why you shouldn't logout :)\",\n        actionButtonText: \"Logout\"\n      };\n      dialogConfig.height = '70%';\n      dialogConfig.width = '900px';\n      const dialogRef = this.dialog.open(RenderingProviderPopupComponent, dialogConfig);\n      dialogRef.afterClosed().subscribe(result => {\n        this.selectedRenderingProvider = result;\n        this.MapRenderingProvider();\n      });\n    }\n\n    MapRenderingProvider() {\n      if (this.selectedRenderingProvider == null || this.selectedRenderingProvider == undefined) return;\n\n      if (this.selectedRenderingProvider[0] != null) {\n        this.ClaimForm.controls.RenderingProviderName.setValue(this.selectedRenderingProvider[0].providerFirstName);\n        this.ClaimForm.controls.RenderingProvider.setValue(this.selectedRenderingProvider[0].providerLastName);\n        this.ClaimForm.controls.RenderingProviderMiddleName.setValue(this.selectedRenderingProvider[0].providerMiddleName);\n        this.ClaimForm.controls.ProviderId.setValue(this.selectedRenderingProvider[0].providerUniqueId);\n        this.ClaimForm.controls.ProviderNPI.setValue(this.selectedRenderingProvider[0].providerNPI);\n        this.ClaimForm.controls.RenderingProviderPin.setValue(this.selectedRenderingProvider[0].ProviderZip);\n        this.ClaimForm.controls.ProviderSpecialty.setValue(this.selectedRenderingProvider[0].ProviderTaxonomy);\n      }\n\n      var arrayControl2 = this.ClaimForm.get('CPTInputs');\n\n      for (let i = 0; i < arrayControl2.length; i++) {\n        this.ClaimForm.get('CPTInputs').at(i).controls['JRenderingProviderId'].setValue(this.selectedRenderingProvider[0].providerNPI);\n      }\n    }\n\n    MapRenderingProviderWithMemberProvider(providerData) {\n      if (providerData == null || providerData == undefined) return;\n\n      if (providerData.renderingProvider != null) {\n        this.ClaimForm.controls.RenderingProviderName.setValue(providerData.renderingProvider.providerFirstName);\n        this.ClaimForm.controls.RenderingProvider.setValue(providerData.renderingProvider.providerLastName);\n        this.ClaimForm.controls.RenderingProviderMiddleName.setValue(providerData.renderingProvider.providerMiddleName);\n        this.ClaimForm.controls.ProviderNPI.setValue(providerData.renderingProvider.providerNPI);\n        this.ClaimForm.controls.ProviderPIN.setValue(providerData.renderingProvider.providerZip);\n        this.ClaimForm.controls.ProviderSpecialty.setValue(providerData.renderingProvider.taxonomy);\n      }\n\n      var arrayControl2 = this.ClaimForm.get('CPTInputs');\n\n      for (let i = 0; i < arrayControl2.length; i++) {\n        this.ClaimForm.get('CPTInputs').at(i).controls['JRenderingProviderId'].setValue(providerData.renderingProvider.providerNPI);\n      }\n    }\n\n    ResetRenderingProvider() {\n      this.ClaimForm.controls.RenderingProviderName.setValue(\"\");\n      this.ClaimForm.controls.RenderingProvider.setValue(\"\");\n      this.ClaimForm.controls.RenderingProviderMiddleName.setValue(\"\"); //this.ClaimForm.controls.ProviderId.setValue(\"\");\n\n      this.ClaimForm.controls.ProviderNPI.setValue(\"\");\n      this.ClaimForm.controls.ProviderPIN.setValue(\"\");\n      this.ClaimForm.controls.ProviderSpecialty.setValue(\"\");\n    }\n\n    SetReferringProviderThroughId() {\n      if (this.ClaimForm.controls.ReferringProviderNPI.value != null && this.ClaimForm.controls.ReferringProviderNPI.value != undefined && this.ClaimForm.controls.ReferringProviderNPI.value != '') {\n        this.spinner.show();\n        this.referringProviderDetail.providerNPI = this.ClaimForm.controls.ReferringProviderNPI.value;\n        this.fetchReferringProviderResult();\n      } else {\n        this.ResetRefferingProvider();\n        this.changeDetect.detectChanges();\n        this.notificationService.showError('Please enter Referring Provider ID', '', 4000);\n      }\n    }\n\n    fetchReferringProviderResult() {\n      this.referringProviderService.fetchProviderResult(this.referringProviderDetail).pipe(first()).subscribe(data => {\n        this.referringProviderResult = data;\n        this.selectedReferringProvider = this.referringProviderResult;\n        this.spinner.hide();\n        this.ResetRefferingProvider();\n        this.changeDetect.detectChanges();\n\n        if (this.referringProviderResult != null && this.referringProviderResult != undefined ? this.referringProviderResult.length == 1 : false) {\n          this.MapRefferingProvider();\n        } else if (this.referringProviderResult != null && this.referringProviderResult != undefined ? this.selectedReferringProvider.length > 1 : false) {\n          this.OnReferringProviderClick();\n        } else {\n          this.ClaimForm.controls.ReferringProviderNPI.setValue(this.referringProviderDetail.providerNPI);\n          this.referringProviderDetail.providerNPI = '';\n          this.notificationService.showError('No result found for Provider ID', '', 4000);\n          this.changeDetect.detectChanges();\n        }\n      }, error => {});\n    } // to open generic method insured id with data sent in MatDialogConfig.data for Reffering Provider id\n\n\n    OnReferringProviderClick() {\n      const dialogConfig = new MatDialogConfig();\n      dialogConfig.data = {\n        name: \"Referring Provider NPI\",\n        title: \"Are you sure you want to logout?\",\n        ReferringProviderId: this.referringProviderDetail.providerNPI,\n        TableContent: [{\n          heading: 'Name',\n          inputName: \"name\",\n          type: 'string',\n          inputType: 'text'\n        }, {\n          heading: 'NPI',\n          type: 'string',\n          inputName: \"providerNPI\",\n          inputType: 'text'\n        }, {\n          heading: 'Address',\n          type: 'string',\n          inputName: \"providerFirstAddress\",\n          inputType: 'text'\n        }, {\n          heading: 'Tax ID',\n          type: 'string',\n          inputName: \"taxId\",\n          inputType: 'number'\n        }, {\n          heading: 'Plan Name',\n          type: 'string',\n          inputName: \"planName\",\n          inputType: 'text'\n        }, {\n          heading: 'IPA Name',\n          type: 'string',\n          inputName: \"ipaName\",\n          inputType: 'text'\n        }],\n        headings: ['planName', 'Name', 'TaxId', 'Address', 'iPAName'],\n        description: \"Pretend this is a convincing argument on why you shouldn't logout :)\",\n        actionButtonText: \"Logout\"\n      };\n      const dialogRef = this.dialog.open(ReferingProviderModalComponent, dialogConfig);\n      dialogRef.afterClosed().subscribe(result => {\n        this.selectedReferringProvider = result;\n        this.MapRefferingProvider();\n      });\n    }\n\n    MapRefferingProvider() {\n      this.ClaimForm.controls.NameOfReferringProvider.setValue(this.selectedReferringProvider[0].providerUniqueId);\n      this.ClaimForm.controls.ReferringProviderNPI.setValue(this.selectedReferringProvider[0].providerNPI);\n      this.ClaimForm.controls.ReferringProviderFirstName.setValue(this.selectedReferringProvider[0].providerFirstName);\n      this.ClaimForm.controls.ReferringProviderLastName.setValue(this.selectedReferringProvider[0].providerLastName);\n      this.ClaimForm.controls.ReferringProviderMiddleName.setValue(this.selectedReferringProvider[0].providerMiddleName);\n      this.ClaimForm.controls.Taxonomy.setValue(this.selectedReferringProvider[0].ProviderTaxonomy);\n      this.ClaimForm.controls.TypeOfProvider.setValue('DN'); // this.ClaimForm.controls.ProviderNPI.setValue(this.selectedReferringProvider[0].providerNPI);\n    }\n\n    ResetRefferingProvider() {\n      this.ClaimForm.controls.NameOfReferringProvider.setValue('');\n      this.ClaimForm.controls.ReferringProviderNPI.setValue('');\n      this.ClaimForm.controls.ReferringProviderFirstName.setValue('');\n      this.ClaimForm.controls.ReferringProviderLastName.setValue('');\n      this.ClaimForm.controls.ReferringProviderMiddleName.setValue('');\n      this.ClaimForm.controls.Taxonomy.setValue('');\n      this.ClaimForm.controls.TypeOfProvider.setValue(''); // this.ClaimForm.controls.ProviderNPI.setValue('');\n    } // to open generic method insured id with data sent in MatDialogConfig.data for Federal Tax Number\n\n\n    OnFederalTaxNumberClick() {\n      const dialogConfig = new MatDialogConfig();\n      dialogConfig.data = {\n        name: \"Federal Tax Number\",\n        title: \"Are you sure you want to logout?\",\n        TableContent: [{\n          heading: 'Name',\n          type: 'string',\n          inputType: 'text'\n        }, {\n          heading: 'Address',\n          type: 'string',\n          inputType: 'text'\n        }, {\n          heading: 'Tax ID',\n          type: 'string',\n          inputType: 'number'\n        }, {\n          heading: 'Plan Name',\n          type: 'string',\n          inputType: 'text'\n        }, {\n          heading: 'IPA Name',\n          type: 'string',\n          inputType: 'text'\n        }],\n        headings: ['PlanName', 'Name', 'TaxId', 'Address', 'IPAName'],\n        description: \"Pretend this is a convincing argument on why you shouldn't logout :)\",\n        actionButtonText: \"Logout\"\n      };\n      this.dialog.open(InsuredIdModalComponent, dialogConfig);\n    }\n\n    handlePatientRelationshipChanged(event) {\n      var target = event.target;\n\n      if (target.value !== '18') {\n        this.copyInsured = true;\n        this.copyInsuredInfo();\n      } else {\n        this.copyInsured = false;\n        this.copyInsuredInfo();\n      }\n    }\n\n    copyInsuredInfo() {\n      if (this.copyInsured) {\n        this.ClaimForm.controls.COPYFROMINSUREDPERSON.setValue(false);\n        this.ClaimForm.controls.PatientStreetNumber.setValue('');\n        this.ClaimForm.controls.PatientAddressLine2.setValue('');\n        this.ClaimForm.controls.PatientCity.setValue('');\n        this.ClaimForm.controls.PatientState.setValue('');\n        this.ClaimForm.controls.PatientTelephone.setValue('');\n        this.ClaimForm.controls.PatientZip.setValue('');\n        this.ClaimForm.controls.PatientFirstName.setValue('');\n        this.ClaimForm.controls.PatientLastName.setValue('');\n        this.ClaimForm.controls.PatientMiddleInitial.setValue('');\n        this.ClaimForm.controls.PatientDOB.setValue('');\n        this.ClaimForm.controls.PatientGender.setValue('');\n        this.patientState.splice(0);\n        this.copyInsured = false;\n        this.isPatientEnabled = false;\n        return;\n      } else {\n        this.copyInsured = true;\n        this.isPatientEnabled = true;\n        this.ClaimForm.controls.COPYFROMINSUREDPERSON.setValue(true);\n        this.ClaimForm.controls.PatientFirstName.setValue(this.selectedMember[0].firstName);\n        this.ClaimForm.controls.PatientLastName.setValue(this.selectedMember[0].lastName);\n        this.ClaimForm.controls.PatientMiddleInitial.setValue(this.selectedMember[0].middleName); // this.ClaimForm.controls.PatientGender.setValue(this.selectedMember[0].gender);\n\n        const PatientGender = this.selectedMember[0].gender != null ? this.selectedMember[0].gender.toLowerCase() : this.selectedMember[0].gender;\n\n        if (PatientGender != null) {\n          this.ClaimForm.controls.PatientGender.setValue(PatientGender.includes('f') ? 'F' : PatientGender.includes('o') ? 'O' : 'M');\n        } else {\n          this.ClaimForm.controls.PatientGender.setValue('');\n        }\n\n        this.ClaimForm.controls.PatientDOB.setValue(formatDate(this.selectedMember[0].dateOfBirth, 'yyyy-MM-dd', 'en'));\n        const PatientAddress = this.selectedMember[0].addressLine1.split(',');\n        if (PatientAddress != null ? PatientAddress.length > 0 : false) this.ClaimForm.controls.PatientStreetNumber.setValue(PatientAddress[0]);\n        if (PatientAddress != null ? PatientAddress.length > 1 : false) this.ClaimForm.controls.PatientAddressLine2.setValue(PatientAddress[1]);\n        this.ClaimForm.controls.PatientCity.setValue(this.selectedMember[0].city);\n        this.ClaimForm.controls.PatientTelephone.setValue(this.selectedMember[0].phoneNumber);\n        this.ClaimForm.controls.PatientZip.setValue(this.selectedMember[0].zipCode);\n        this.ClaimForm.controls.PatientState.setValue(this.selectedMember[0].state);\n        this.SelectedPatientState = this.SelectedInsuredState;\n        return;\n      }\n    }\n\n    fetchchAllPlaceOfServices() {\n      this.allPlaceOfServicesService.fetchchAllPlaceOfServices().pipe(first()).subscribe(response => {\n        this.allPlaceOfService = response;\n        this.allPlaceOfService1 = response;\n        this.allPlaceOfService2 = response;\n        this.allPlaceOfService3 = response;\n        this.allPlaceOfService4 = response;\n      });\n    }\n\n    fetchchAllResubmissionCode() {\n      this.allResubmissionCodeService.fetchchAllResubmissionCode().pipe(first()).subscribe(response => {\n        this.allResubmissionCode = response; // this.filteredResubmissionCode=of(this.allResubmissionCode);\n      });\n    }\n\n    OnResubmissionCodeChange(filterValue) {\n      if (filterValue == null ? true : filterValue.length < 1) {\n        this.filteredResubmissionCode = null;\n        return;\n      }\n\n      this.filteredResubmissionCode = of(this.allResubmissionCode.filter(option => option.text.toUpperCase().includes(filterValue.toUpperCase())));\n    }\n\n    fetchchQualifierDataByType() {\n      this.qualifierDataByTypeService.fetchchQualifierDataByType(\"\").pipe(first()).subscribe(response => {\n        this.qualifierDataByType = response; // this.filteredQualifierDataByType=of(this.qualifierDataByType);\n      });\n    }\n\n    OnQualifierDataByTypeChange(filterValue, InputNumber) {\n      if (filterValue == null || filterValue == undefined) {\n        filterValue = '';\n      }\n\n      switch (InputNumber) {\n        case '1':\n          this.filteredQualifierDataByType = of(this.qualifierDataByType.filter(option => option.qualifier.toUpperCase().includes(filterValue.toUpperCase())));\n          break;\n\n        case '2':\n          this.filteredQualifierDataByType2 = of(this.qualifierDataByType.filter(option => option.qualifier.toUpperCase().includes(filterValue.toUpperCase())));\n      }\n    }\n\n    fetchAllStatesBySearchstring() {\n      this.allStatesBySearchstringService.fetchchAllStatesBySearchstring().pipe(first()).subscribe(response => {\n        this.allStatesBySearchstring = response;\n      });\n      return this.allStatesBySearchstring;\n    }\n\n    OnStateChange(filterValue, InputIndex) {\n      this.InputLoader = true;\n      this.allStatesList = this.fetchAllStatesBySearchstring();\n      this.allStatesList = this.allStatesList.filter(option => option.stateName.toUpperCase().includes(filterValue != null ? filterValue.trim().toUpperCase() : '')); // this.filteredAllStatesList=of(this.allStatesList);\n\n      switch (InputIndex) {\n        case 'InsuredState':\n          // this.allStatesListForInsured= this.fetchAllStatesBySearchstring();\n          // this.allStatesListForInsured=this.allStatesListForInsured.filter(option => option.stateName.toUpperCase().includes(filterValue.toUpperCase()));\n          this.filteredAllStatesListForInsured = of(this.allStatesList);\n          break;\n\n        case 'PatientState':\n          this.filteredAllStatesListForPatient = of(this.allStatesList);\n          break;\n\n        case 'PayerState':\n          this.filteredAllStatesListForPayer = of(this.allStatesList);\n          break;\n\n        case 'FacilityLocationState':\n          this.filteredAllStatesListForFacilityLocation = of(this.allStatesList);\n          break;\n\n        case 'BillingProviderState':\n          this.filteredAllStatesListForBillingProvider = of(this.allStatesList);\n          break;\n\n        case 'AutoAccidentState':\n          this.filteredAllStatesListForAutoAccident = of(this.allStatesList);\n          break;\n      }\n\n      this.InputLoader = false;\n    }\n\n    fetchchAllICDCode(filterValue) {\n      var InputIndex = this.ICDInputIndexVal; //console.log(filterValue);\n\n      if (filterValue == null ? true : filterValue.length < 3) {\n        this.allICDCode = null;\n        this.filteredAllICDCode1 = null;\n        this.filteredAllICDCode2 = null;\n        this.filteredAllICDCode3 = null;\n        this.filteredAllICDCode4 = null;\n        this.filteredAllICDCode5 = null;\n        this.filteredAllICDCode6 = null;\n        this.filteredAllICDCode7 = null;\n        this.filteredAllICDCode8 = null;\n        this.filteredAllICDCode9 = null;\n        this.filteredAllICDCode10 = null;\n        this.filteredAllICDCode11 = null;\n        this.filteredAllICDCode12 = null;\n        return null;\n      } else {\n        this.InputLoader = true;\n        this.getAllICDCodeService.fetchICDBySearch(filterValue).pipe(first()).subscribe(response => {\n          this.allICDCode = response; //console.log('#allICDCode', response);\n          //console.log('Filtered value', filterValue);\n\n          switch (InputIndex) {\n            case '0':\n              this.filteredAllICDCode = of(this.allICDCode);\n              break;\n\n            case '1':\n              this.filteredAllICDCode1 = of(this.allICDCode);\n              break;\n\n            case '2':\n              this.filteredAllICDCode2 = of(this.allICDCode);\n              break;\n\n            case '3':\n              this.filteredAllICDCode3 = of(this.allICDCode);\n              break;\n\n            case '4':\n              this.filteredAllICDCode4 = of(this.allICDCode);\n              break;\n\n            case '5':\n              this.filteredAllICDCode5 = of(this.allICDCode);\n              break;\n\n            case '6':\n              this.filteredAllICDCode6 = of(this.allICDCode);\n              break;\n\n            case '7':\n              this.filteredAllICDCode7 = of(this.allICDCode);\n              break;\n\n            case '8':\n              this.filteredAllICDCode8 = of(this.allICDCode);\n              break;\n\n            case '9':\n              this.filteredAllICDCode9 = of(this.allICDCode);\n              break;\n\n            case '10':\n              this.filteredAllICDCode10 = of(this.allICDCode);\n              break;\n\n            case '11':\n              this.filteredAllICDCode11 = of(this.allICDCode);\n              break;\n\n            case '12':\n              this.filteredAllICDCode12 = of(this.allICDCode);\n              break;\n          }\n\n          this.changeDetect.detectChanges();\n        });\n        this.InputLoader = false;\n        return this.allICDCode;\n      }\n    }\n\n    OnICDCodeChange(filterValue, InputIndex) {\n      this.AddInTempICD(filterValue, Number(InputIndex)); // this.InputLoader = true;\n\n      if (filterValue != '' && filterValue != undefined) {\n        this.ICDInputIndexVal = InputIndex;\n        this.ICDDebounceMethod.next(filterValue);\n      } // this.fetchchAllICDCode(filterValue, InputIndex);\n\n\n      this.InputLoader = false;\n    }\n\n    fetchchAllCPTCode(filterValue) {\n      var InputIndex = this.CPTInputIndexVal;\n\n      if (filterValue == null ? true : filterValue.length < 3) {\n        this.allCPTCode = null;\n\n        switch (InputIndex) {\n          case '0':\n            this.filteredAllCPTCodes = null;\n            break;\n\n          case '1':\n            this.filteredAllCPTCode1 = null;\n            break;\n\n          case '2':\n            this.filteredAllCPTCode2 = null;\n            break;\n\n          case '3':\n            this.filteredAllCPTCode3 = null;\n            break;\n\n          case '4':\n            this.filteredAllCPTCode4 = null;\n            break;\n\n          default:\n            break;\n        }\n\n        return null;\n      } else {\n        this.InputLoader = true;\n        this.getAllCPTCodeService.fetchchAllCPTCode(filterValue, \"\", this.PlaceOfService3.toString()).pipe(first()).subscribe(response => {\n          this.allCPTCode = null;\n          this.allCPTCode = response;\n\n          switch (InputIndex) {\n            case '0':\n              this.filteredAllCPTCodes = of(this.allCPTCode);\n              break;\n\n            case '1':\n              this.filteredAllCPTCode1 = of(this.allCPTCode);\n              break;\n\n            case '2':\n              this.filteredAllCPTCode2 = of(this.allCPTCode);\n              break;\n\n            case '3':\n              this.filteredAllCPTCode3 = of(this.allCPTCode);\n              break;\n\n            case '4':\n              this.filteredAllCPTCode4 = of(this.allCPTCode);\n              break;\n          }\n\n          this.changeDetect.detectChanges();\n          this.InputLoader = false;\n          return this.allCPTCode;\n        });\n      }\n    }\n\n    OnCPTCodeChange(filterValue, InputIndex, PlaceOfService) {\n      // this.InputLoader = true;\n      console.log(\"OnCPTCodeChange\");\n\n      if (filterValue != '' && filterValue != undefined) {\n        this.CPTInputIndexVal = InputIndex;\n        this.CPTDebounceMethod.next(filterValue);\n      }\n\n      this.InputLoader = false;\n    }\n\n    CloseTab() {\n      this.navigationService.closeTabEvent('');\n    }\n\n    AcceptSelected(status) {\n      var valid = this.validateICD();\n\n      if (valid == true) {\n        if (this.ClaimForm.invalid) {\n          for (const name in this.ClaimForm.controls) {\n            if (this.ClaimForm.controls[name].invalid) {}\n          }\n\n          this.ClaimForm.markAllAsTouched();\n          this.notificationService.showError('Error', 'Please fill highlighted fields.', 4000);\n          return;\n        }\n\n        if (this.getClaimInfoByIdService.claimId == '') {\n          this.notificationService.showError('Claim not found', '', 4000);\n          return;\n        }\n\n        this.spinner.show();\n\n        if (this.getClaimInfoByIdService.claimId != null) {\n          this.claimReportService.changeClaimStatus(this.getClaimInfoByIdService.claimId, status).pipe(first()).subscribe(response => {\n            //next() callback\n            if (response.statusCode == 200) {\n              this.notificationService.showSuccess('', response.message, 4000);\n              this.spinner.hide();\n              this.CloseTab();\n            } else {\n              this.notificationService.showError('', response.message, 4000);\n              this.spinner.hide();\n            }\n          });\n        }\n      }\n    }\n\n    MapInputFields() {\n      this.createClaimForm.claimForm837 = this.claimForm837P;\n      this.createClaimForm.claimForm837.notes = this.claimForm837P.notes;\n      this.createClaimForm.claimForm837.fileName = this.claimForm837P.fileName;\n      this.createClaimForm.claimForm837.uploadExcelWarningMessage = this.claimForm837P.uploadExcelWarningMessage;\n      this.createClaimForm.claimForm837.status = this.claimForm837P.status;\n      this.createClaimForm.claimForm837.claimsProfessional837.claimskey = this.claimForm837P.claimsProfessional837id;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkey = this.claimForm837P.claimsProfessional837.subscriberkey;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.subscriberkey = this.claimForm837P.claimsProfessional837.subscriberkey;\n      this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.infosourcekey = this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.infosourcekey;\n      this.createClaimForm.claimForm837.fileName = this.claimForm837P.fileName;\n\n      switch (this.claimForm837P.claimFormStatusCode) {\n        case 'OH':\n          this.Status = 'ON HOLD';\n          break;\n\n        case 'ON':\n          this.Status = 'OPEN';\n          break;\n\n        case \"AC\":\n          this.Status = 'ACCEPTED';\n          break;\n\n        case \"NTR\":\n          this.Status = 'RESUBMISSION';\n          break;\n\n        case \"DP\":\n          this.Status = 'DISPATCHED';\n          break;\n\n        case \"UACH\":\n          this.Status = 'UNACKNOWLEDGED BY CLEARING HOUSE';\n          break;\n\n        case \"RCH\":\n          this.Status = 'REJECTED BY CLEARING HOUSE';\n          break;\n\n        case \"ABCH\":\n          this.Status = 'ACCEPTED By CH';\n          break;\n\n        case \"ANCH\":\n          this.Status = 'ACKNOWLEDGED By CH';\n          break;\n\n        case \"ABP\":\n          this.Status = 'ACKNOWLEDGED BY PAYER';\n          break;\n\n        case \"UAP\":\n          this.Status = 'UNACKNOWLEDGED BY PAYER';\n          break;\n\n        case \"AP\":\n          this.Status = 'ACCEPTED By PAYER';\n          break;\n\n        case \"RP\":\n          this.Status = 'REJECTED BY PAYER';\n          break;\n\n        case \"Pend\":\n          this.Status = 'PENDING';\n          break;\n\n        case \"EOB\":\n          this.Status = 'EOB RECIEVED';\n          break;\n\n        case \"DBP\":\n          this.Status = 'DENIED BY PAYER';\n          break;\n      }\n\n      this.ClaimForm.controls.insuranceType.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.sbr09ClaimFilingIndicatorCode); // if(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.sbr05InsuranceTypeCode!==null)\n      // {\n      //   switch(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.sbr05InsuranceTypeCode.toLowerCase())\n      //   {\n      //     case 'medicare':\n      //     case 'group health plan':\n      //     case 'feca blk lung':\n      //     case 'medicaid':\n      //     case 'tricare':\n      //     case 'champva':\n      //       this.ClaimForm.controls.insuranceType.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.sbr05InsuranceTypeCode);\n      //       break;\n      //     default:\n      //       this.ClaimForm.controls.insuranceType.setValue('Others');\n      //       break;\n      //   }\n      // }\n      // this.CptArray=this.claimForm837P.cptcodes.split(',');\n      // for(var i=0;i<this.CptArray.length;i++)\n      // {\n      //   this.CPTCodeInput[i]=this.CptArray[i];\n      // }\n      // this.serviceLineProfessional837s=this.claimForm837P.claimsProfessional837.serviceLineProfessional837s;\n      // for(var i=0;i<this.serviceLineProfessional837s.length;i++)\n      // {\n      // }\n\n      const _cptControls = this.ClaimForm.get('CPTInputs');\n\n      this.ClaimForm.get('CPTInputs').at(0).controls['PlaceOfService3'].setValue(this.claimForm837P.claimsProfessional837.clm0501PlaceOfServiceCode); //this.ClaimForm.controls.PlaceOfService3.setValue(this.claimForm837P.claimsProfessional837.clm0501PlaceOfServiceCode);\n      // this.PlaceOfService1=this.claimForm837P.claimsProfessional837.clm0501PlaceOfServiceCode;\n\n      var DOSFROM = new Date(this.claimForm837P.claimDosfrom);\n      this.ClaimForm.get('CPTInputs').at(0).controls['DateOfServiceFrom'].setValue(this.datePipe.transform(DOSFROM, 'yyyy-MM-dd')); //this.ClaimForm.controls.DateOfServiceFrom.setValue(this.datePipe.transform(DOSFROM, 'yyyy-MM-dd'));\n\n      var DOSTO = new Date(this.claimForm837P.claimDosto);\n      this.ClaimForm.get('CPTInputs').at(0).controls['DateOfServiceTo'].setValue(this.datePipe.transform(DOSTO, 'yyyy-MM-dd')); //this.ClaimForm.controls.DateOfServiceTo.setValue(this.datePipe.transform(DOSTO, 'yyyy-MM-dd'));\n\n      this.ClaimForm.controls.OutsideLab.setValue(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.outsideLab != 'true' ? 'false' : 'true');\n      this.ClaimForm.controls.PatientFirstName.setValue(this.claimForm837P.patientFirstName);\n      this.ClaimForm.controls.PatientLastName.setValue(this.claimForm837P.patientLastName);\n      this.ClaimForm.controls.PatientMiddleInitial.setValue(this.claimForm837P.patientMiddleName);\n      this.ClaimForm.controls.PatientDOB.setValue(this.datePipe.transform(this.claimForm837P.patientDob, 'yyyy-MM-dd'));\n\n      if (this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s.length > 0) {\n        this.ClaimForm.controls.PatientStreetNumber.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].n301PatientAddr1);\n        this.ClaimForm.controls.PatientAddressLine2.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].n302PatientAddr2);\n        this.ClaimForm.controls.PatientState.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].n402PatientState);\n        this.ClaimForm.controls.PatientCity.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].n401PatientCity);\n        this.ClaimForm.controls.PatientTelephone.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].per04PatientPhoneNo);\n        this.ClaimForm.controls.PatientZip.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].n403PatientZip);\n        this.ClaimForm.controls.PatientFirstName.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].nm104PatientFirst);\n        this.ClaimForm.controls.PatientLastName.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].nm103PatientLastOrOrganizationName); //this.ClaimForm.controls.PatientMiddleInitial.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].nm105PatientMiddle);\n        // this.ClaimForm.controls.PatientGender.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].dmg03PatientGenderCode);\n\n        if (this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].dmg03PatientGenderCode != null) {\n          const PatientGender = this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].dmg03PatientGenderCode.toLowerCase();\n          this.ClaimForm.controls.PatientGender.setValue(PatientGender.includes('f') ? 'F' : PatientGender.includes('o') ? 'O' : 'M');\n        }\n      }\n\n      var OnsetOfCurrentIllness = this.claimForm837P.claimsProfessional837.dtp03OnsetofCurrentIllnessInjuryDate;\n      this.ClaimForm.controls.DateOfCurrentIllness.setValue(this.datePipe.transform(OnsetOfCurrentIllness, 'yyyy-MM-dd'));\n      this.ClaimForm.controls.InsurancePlanName.setValue(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.insurancePlanName);\n      this.ClaimForm.controls.AnotherHealthBenefitPlan.setValue(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.insuredOtherHealthBenefitPlan == 'Yes' ? 'Yes' : 'No');\n      this.ClaimForm.controls.Qual.setValue(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.dateOfCurrentIllnessQlfr);\n      var QualDate = this.claimForm837P.claimsProfessional837.dtp03OnsetofCurrentIllnessInjuryDate;\n\n      if (QualDate != null && QualDate != '' ? !QualDate.includes('T') : false) {\n        QualDate = QualDate.slice(0, 4) + '-' + QualDate.slice(4, 6) + '-' + QualDate.slice(6, 8);\n        this.ClaimForm.controls.QualDate.setValue(this.datePipe.transform(QualDate, 'yyyy-MM-dd'));\n      }\n\n      var OtherDateQual = this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientOtherDate;\n\n      if (OtherDateQual != null && OtherDateQual != '' ? !OtherDateQual.includes('T') : false) {\n        OtherDateQual = OtherDateQual.slice(0, 4) + '-' + OtherDateQual.slice(4, 6) + '-' + OtherDateQual.slice(6, 8);\n        this.ClaimForm.controls.OtherDateQual.setValue(this.datePipe.transform(OtherDateQual, 'yyyy-MM-dd'));\n      }\n\n      this.selectedContactPeople = {};\n\n      if (this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.perAdministrativeCommunicationContactProfessional837s != null) {\n        this.selectedContactPeople.contactPersonPhone = this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.perAdministrativeCommunicationContactProfessional837s[0].per0xPhoneNo;\n        this.selectedContactPeople.contactPersonName = this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.perAdministrativeCommunicationContactProfessional837s[0].per02ContactName;\n        this.selectedContactPeople.contactPersonEmail = this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.perAdministrativeCommunicationContactProfessional837s[0].per0xEmail;\n        this.selectedContactPeople.contactPersonFax = this.createClaimForm.claimForm837.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.perAdministrativeCommunicationContactProfessional837s[0].per0xFaxNo;\n        this.selectedBillingProvider = [];\n        this.selectedBillingProvider.push({\n          contactPeople: []\n        });\n        this.selectedBillingProvider[0].contactPeople.push(this.selectedContactPeople);\n      } // this.ClaimForm.controls.OtherDateQual.setValue(this.datePipe.transform(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientOtherDate,'yyyy-MM-dd'));\n      // var otherDate=this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientOtherDate;\n      // this.ClaimForm.controls.OtherDate.setValue(this.datepipe.transform(otherDate,'yyyy-MM-dd'));\n\n\n      var patientUnableToWorkInCurrentOccupationFrom = this.claimForm837P.claimsProfessional837.dtp03LastWorkedDate;\n\n      if (patientUnableToWorkInCurrentOccupationFrom != null && patientUnableToWorkInCurrentOccupationFrom != '' ? !patientUnableToWorkInCurrentOccupationFrom.includes('T') : false) {\n        patientUnableToWorkInCurrentOccupationFrom = patientUnableToWorkInCurrentOccupationFrom.slice(0, 4) + '-' + patientUnableToWorkInCurrentOccupationFrom.slice(4, 6) + '-' + patientUnableToWorkInCurrentOccupationFrom.slice(6, 8);\n        this.ClaimForm.controls.PatientUnableToWorkInCurrentOccupationFrom.setValue(this.datePipe.transform(patientUnableToWorkInCurrentOccupationFrom, 'yyyy-MM-dd'));\n      }\n\n      var patientUnableToWorkInCurrentOccupationTo = this.claimForm837P.claimsProfessional837.dtp03WorkReturnDate;\n\n      if (patientUnableToWorkInCurrentOccupationTo != null && patientUnableToWorkInCurrentOccupationTo != '' ? !patientUnableToWorkInCurrentOccupationTo.includes('T') : false) {\n        patientUnableToWorkInCurrentOccupationTo = patientUnableToWorkInCurrentOccupationTo.slice(0, 4) + '-' + patientUnableToWorkInCurrentOccupationTo.slice(4, 6) + '-' + patientUnableToWorkInCurrentOccupationTo.slice(6, 8);\n        this.ClaimForm.controls.PatientUnableToWorkInCurrentOccupationTo.setValue(this.datePipe.transform(patientUnableToWorkInCurrentOccupationTo, 'yyyy-MM-dd'));\n      }\n\n      var hospitalizationDatesRelatedToCurrentServicesFrom = this.claimForm837P.claimsProfessional837.dtp03HospitalizationAdmissionDate;\n\n      if (hospitalizationDatesRelatedToCurrentServicesFrom != null && hospitalizationDatesRelatedToCurrentServicesFrom != '' ? !hospitalizationDatesRelatedToCurrentServicesFrom.includes('T') : false) {\n        hospitalizationDatesRelatedToCurrentServicesFrom = hospitalizationDatesRelatedToCurrentServicesFrom.slice(0, 4) + '-' + hospitalizationDatesRelatedToCurrentServicesFrom.slice(4, 6) + '-' + hospitalizationDatesRelatedToCurrentServicesFrom.slice(6, 8);\n        this.ClaimForm.controls.HospitalizationDatesRelatedToCurrentServicesFrom.setValue(this.datePipe.transform(hospitalizationDatesRelatedToCurrentServicesFrom, 'yyyy-MM-dd'));\n      }\n\n      var hospitalizationDatesRelatedToCurrentServicesTo = this.claimForm837P.claimsProfessional837.dtp03HospitalizationDischargeDate;\n\n      if (hospitalizationDatesRelatedToCurrentServicesTo != null && hospitalizationDatesRelatedToCurrentServicesTo != '' ? !hospitalizationDatesRelatedToCurrentServicesTo.includes('T') : false) {\n        hospitalizationDatesRelatedToCurrentServicesTo = hospitalizationDatesRelatedToCurrentServicesTo.slice(0, 4) + '-' + hospitalizationDatesRelatedToCurrentServicesTo.slice(4, 6) + '-' + hospitalizationDatesRelatedToCurrentServicesTo.slice(6, 8);\n        this.ClaimForm.controls.HospitalizationDatesRelatedToCurrentServicesTo.setValue(this.datePipe.transform(hospitalizationDatesRelatedToCurrentServicesTo, 'yyyy-MM-dd'));\n      } // if(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s.length>2){\n      //   var DateOfCurrentIllnessQlfr = this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dependentProfessional837s[0].dmg02PatientBirthDate;\n      //   DateOfCurrentIllnessQlfr  =  DateOfCurrentIllnessQlfr.substring(4,6)+'/'+ DateOfCurrentIllnessQlfr.substring(6,8)+'/'+DateOfCurrentIllnessQlfr.substring(0,4) ;\n      //   this.ClaimForm.controls.DateOfCurrentIllness.setValue(DateOfCurrentIllnessQlfr);\n      // }\n\n\n      var patientsOrAuthorizedPersonsSignature = this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientOrAuthorizedSignatureDate;\n\n      if (patientsOrAuthorizedPersonsSignature != null && patientsOrAuthorizedPersonsSignature != '' ? !patientsOrAuthorizedPersonsSignature.includes('T') : false) {\n        patientsOrAuthorizedPersonsSignature = patientsOrAuthorizedPersonsSignature.slice(0, 4) + '-' + patientsOrAuthorizedPersonsSignature.slice(4, 6) + '-' + patientsOrAuthorizedPersonsSignature.slice(6, 8);\n        this.ClaimForm.controls.PatientsOrAuthorizedPersonsSignature.setValue(this.datePipe.transform(patientsOrAuthorizedPersonsSignature, 'yyyy-MM-dd'));\n      }\n\n      this.ClaimForm.controls.PersonsSignature.setValue(this.claimForm837P.claimsProfessional837.clm06SupplierSignatureIndicator != 'Y' ? 'N' : 'Y');\n      this.ClaimForm.controls.AuthorizedPersonsSignature.setValue(this.claimForm837P.claimsProfessional837.clm08BenefitsAssignmentCertIndicator != 'Y' ? 'N' : 'Y');\n      this.ClaimForm.controls.OtherAccidents.setValue(this.claimForm837P.claimsProfessional837.clm1103RelatedCausesCode == 'OA' ? 'Y' : 'N');\n      this.ClaimForm.controls.AutoAccident.setValue(this.claimForm837P.claimsProfessional837.clm1102RelatedCausesCode == 'AA' ? 'Y' : 'N');\n      this.autoAccident = this.claimForm837P.claimsProfessional837.clm1102RelatedCausesCode == 'AA' ? 'Y' : 'N'; // this.autoAccident=this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientConditionRelatedToAutoAccident!='Y'?'N':'Y';\n      // if(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientConditionRelatedToAutoAccident==\"No\"){\n      //  this.ClaimForm.controls.AutoAccidentState.setValue(this.claimForm837P.claimsProfessional837.clm1104AutoAccidentStateCode);\n      // } \n\n      this.ClaimForm.controls.AutoAccidentState.setValue(this.claimForm837P.claimsProfessional837.clm1104AutoAccidentStateCode);\n      this.SelectedAutoAccidentState = this.claimForm837P.claimsProfessional837.clm1104AutoAccidentStateCode; // if(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientConditionRelatedToEmp==null)\n      // {\n      //   this.ClaimForm.controls.Employment.setValue('Yes');\n      // }else{\n      //   this.ClaimForm.controls.Employment.setValue(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientConditionRelatedToEmp);\n      // }\n\n      this.ClaimForm.controls.Employment.setValue(this.claimForm837P.claimsProfessional837.clm1101RelatedCausesCode == \"EM\" ? 'Y' : 'N');\n      this.ClaimForm.controls.ResubmissionCode.setValue(this.claimForm837P.claimsProfessional837.clm0503ClaimFrequencyCode);\n      this.ClaimForm.controls.ReferenceNumber.setValue(this.claimForm837P.claimsProfessional837.ref02PayerClaimControlNumber);\n      this.ClaimForm.controls.PriorAuthorisationNumber.setValue(this.claimForm837P.claimsProfessional837.ref02PriorAuthorizationNumber);\n      this.ClaimForm.controls.PatientAccountNumber.setValue(this.claimForm837P.patientCtrlNo);\n\n      if (this.claimForm837P.claimsProfessional837.clm08BenefitsAssignmentCertIndicator == 'No') {\n        this.ClaimForm.controls.AcceptanceAssignment.setValue(this.claimForm837P.claimsProfessional837.clm08BenefitsAssignmentCertIndicator);\n      }\n\n      if (this.claimForm837P.claimsProfessional837.serviceLineProfessional837s.length > 0) {\n        this.ClaimForm.controls.CLIA.setValue(this.claimForm837P.claimsProfessional837.serviceLineProfessional837s[0].ref02ReferringCliaNumber);\n        this.ClaimForm.controls.SupervisingPhysicianID.setValue(this.claimForm837P.claimsProfessional837.serviceLineProfessional837s[0].nm109SupervisingProviderId);\n        this.ClaimForm.controls.SupervisingPhysicianMIDDLENAME.setValue(this.claimForm837P.claimsProfessional837.serviceLineProfessional837s[0].nm105SupervisingProviderMiddle);\n        this.ClaimForm.controls.SupervisingPhysicianLastName.setValue(this.claimForm837P.claimsProfessional837.serviceLineProfessional837s[0].nm103SupervisingProviderLastName);\n        this.ClaimForm.controls.SupervisingPhysicianFIRSTNAME.setValue(this.claimForm837P.claimsProfessional837.serviceLineProfessional837s[0].nm104SupervisingProviderFirst);\n        this.ClaimForm.controls.SupervisingPhysicianIDMIDDLENAME.setValue(this.claimForm837P.claimsProfessional837.serviceLineProfessional837s[0].nm105OrderingProviderMiddle);\n        this.ClaimForm.controls.SupervisingPhysicianIDLastName.setValue(this.claimForm837P.claimsProfessional837.serviceLineProfessional837s[0].nm103OrderingProviderLastName);\n        this.ClaimForm.controls.SupervisingPhysicianIDFIRSTNAME.setValue(this.claimForm837P.claimsProfessional837.serviceLineProfessional837s[0].nm104OrderingProviderFirst);\n        this.ClaimForm.controls.OrderingPhysicianID.setValue(this.claimForm837P.claimsProfessional837.serviceLineProfessional837s[0].nm109OrderingProviderId);\n        this.ClaimForm.controls.MammographyCertificate.setValue(this.claimForm837P.claimsProfessional837.ref02MammographyCertificationNumber);\n        this.ClaimForm.controls.AmountPaid.setValue(\"\");\n        this.ClaimForm.controls.ProviderSpecialty.setValue(this.claimForm837P.claimsProfessional837.prv03ProviderTaxonomyCode);\n      }\n\n      var AccidentDate = this.claimForm837P.claimsProfessional837.dtp03AccidentDate;\n\n      if (AccidentDate != null && AccidentDate != '' ? !AccidentDate.includes('T') : false) {\n        AccidentDate = AccidentDate.slice(0, 4) + '-' + AccidentDate.slice(4, 6) + '-' + AccidentDate.slice(6, 8);\n      }\n\n      this.ClaimForm.controls.AccidentDate.setValue(this.datePipe.transform(AccidentDate, 'yyyy-MM-dd'));\n      var dateOfInitialTreatment = this.claimForm837P.claimsProfessional837.dtp03InitialTreatmentDate;\n\n      if (dateOfInitialTreatment != null && dateOfInitialTreatment != '' ? !dateOfInitialTreatment.includes('T') : false) {\n        dateOfInitialTreatment = dateOfInitialTreatment.slice(0, 4) + '-' + dateOfInitialTreatment.slice(4, 6) + '-' + dateOfInitialTreatment.slice(6, 8);\n      }\n\n      this.ClaimForm.controls.DateOfInitialTreatment.setValue(this.datePipe.transform(dateOfInitialTreatment, 'yyyy-MM-dd'));\n      this.ClaimForm.controls.ReferringProviderFirstName.setValue(this.claimForm837P.claimsProfessional837.nm104ReferringProviderLastFirst);\n      this.ClaimForm.controls.ReferringProviderMiddleName.setValue(this.claimForm837P.claimsProfessional837.nm105ReferringProviderLastMiddle);\n      this.ClaimForm.controls.ReferringProviderLastName.setValue(this.claimForm837P.claimsProfessional837.nm103ReferringProviderLastName);\n      this.ClaimForm.controls.RenderingProvider.setValue(this.claimForm837P.claimsProfessional837.nm103RenderingProviderLastOrOrganizationName);\n      this.ClaimForm.controls.RenderingProviderMiddleName.setValue(this.claimForm837P.claimsProfessional837.nm105RenderingProviderMiddle);\n      this.ClaimForm.controls.RenderingProviderName.setValue(this.claimForm837P.claimsProfessional837.nm104RenderingProviderFirst);\n      this.ClaimForm.controls.IdQual.setValue(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.billingGroupIdQlfr); // this.ClaimForm.controls.Taxonomy.setValue(this.claimForm837P.claimsProfessional837.prv03ProviderTaxonomyCode);\n\n      this.ClaimForm.controls.ProviderNPI.setValue(this.createClaimForm.claimForm837.claimsProfessional837.nm109RenderingProviderIdentifier);\n      this.ClaimForm.controls.ClaimType.setValue(this.claimForm837P.claimType); //this.ClaimForm.controls.Taxonomy.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.prv03BillingProviderIdCode);\n\n      this.ClaimForm.controls.Taxonomy.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.prv03BillingProviderIdCode);\n      this.ClaimForm.controls.BillingProvider.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.nm103BillingProviderLastOrOrganizationName);\n      this.ClaimForm.controls.BillingProviderFIRSTNAME.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.nm104BillingProviderFirst);\n      this.ClaimForm.controls.BillingProviderMIDDLENAME.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.nm105BillingProviderMiddle);\n      this.ClaimForm.controls.BillingProviderAddress.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.n301BillingProviderAddr1);\n      this.ClaimForm.controls.BillingProviderADDRESSLINE2.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.n302BillingProviderAddr2);\n      this.ClaimForm.controls.BillingProviderCity.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.n401BillingProviderCity);\n      this.ClaimForm.controls.BillingProviderState.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.n402BillingProviderState);\n      this.ClaimForm.controls.BillingProviderZip.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.n403BillingProviderZip);\n\n      if (this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.perAdministrativeCommunicationContactProfessional837s != null) {\n        this.ClaimForm.controls.BillingProviderTelephone.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.perAdministrativeCommunicationContactProfessional837s[0].per0xPhoneNo);\n      }\n\n      if (this.claimForm837P.claimsProfessional837.otherSubscriberInfoProfessional837s.length > 0) {\n        this.ClaimForm.controls.OtherInsuredFirstName.setValue(this.claimForm837P.claimsProfessional837.otherSubscriberInfoProfessional837s[0].nm104OtherInsuredFirst);\n        this.ClaimForm.controls.OtherInsuredLastName.setValue(this.claimForm837P.claimsProfessional837.otherSubscriberInfoProfessional837s[0].nm103OtherInsuredLastName);\n        this.ClaimForm.controls.OtherInsuredMiddleInitial.setValue(this.claimForm837P.claimsProfessional837.otherSubscriberInfoProfessional837s[0].nm105OtherInsuredMiddle);\n      }\n\n      this.ClaimForm.controls.FederalTaxNumber.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.ref02BillingProviderEmployerId);\n      this.ClaimForm.controls.InsuredSIDNumber.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.nm109SubscriberIdCode);\n      this.ClaimForm.controls.InsuredStreetNumber.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.n301SubscriberAddr1);\n      this.ClaimForm.controls.InsuredAddressLine2.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.n302SubscriberAddr2);\n      this.ClaimForm.controls.InsuredCity.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.n401SubscriberCity);\n      this.ClaimForm.controls.InsuredState.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.n402SubscriberState);\n      this.ClaimForm.controls.InsuredZip.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.n403SubscriberZip);\n      this.ClaimForm.controls.InsuredTelephone.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.per04SubscriberPhoneNo);\n      const SubscriberGender = this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dmg03SubscriberGenderCode.toLowerCase();\n      this.ClaimForm.controls.SexOfInsured.setValue(SubscriberGender.includes('f') ? 'F' : SubscriberGender.includes('o') ? 'O' : 'M');\n      this.ClaimForm.controls.InsuredLastName.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.nm103SubscriberLastOrOrganizationName);\n      this.ClaimForm.controls.InsuredFirstName.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.nm104SubscriberFirst);\n      this.ClaimForm.controls.InsuredMiddleInitial.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.nm105SubscriberMiddle);\n      var newInsuredDOBDate = this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.dmg02SubscriberBirthDate;\n\n      if (!newInsuredDOBDate.includes('T')) {\n        newInsuredDOBDate = newInsuredDOBDate.slice(0, 4) + '-' + newInsuredDOBDate.slice(4, 6) + '-' + newInsuredDOBDate.slice(6, 8);\n      }\n\n      this.ClaimForm.controls.InsuredDateOfBirth.setValue(this.datePipe.transform(newInsuredDOBDate, 'yyyy-MM-dd'));\n      this.ClaimForm.controls.InsuredFirstName.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.nm104SubscriberFirst);\n      this.ClaimForm.controls.InsuredLastName.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.nm103SubscriberLastOrOrganizationName);\n      this.ClaimForm.controls.InsuredMiddleInitial.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.nm105SubscriberMiddle);\n      this.ClaimForm.controls.FECANumber.setValue(this.claimForm837P.claimsProfessional837.hcp14PolicyComplianceCode);\n      this.ClaimForm.controls.OtherClaimID.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.ref02PropertyCasualtyClaimNo);\n      this.ClaimForm.controls.InsurancePlanName.setValue(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.insurancePlanName);\n      this.ClaimForm.controls.PayerID.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.nm109PayerIdCode);\n      this.ClaimForm.controls.PayerName.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.nm103PayerLastOrOrganizatioName);\n      this.ClaimForm.controls.PayerAddress1.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.n301PayerAddr1);\n      this.ClaimForm.controls.PayerAddress2.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.n302PayerAddr2);\n      this.ClaimForm.controls.PayerCity.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.n401PayerCity);\n      this.ClaimForm.controls.PayerState.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.n402PayerState);\n      this.ClaimForm.controls.PayerZip.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.n403PayerZip);\n      this.ClaimForm.controls.Qual2.setValue(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.patientOtherDateQlfr);\n      this.selectedIpa = this.claimForm837P.ipacode;\n      this.ICDCheckbox = this.claimForm837P.icdversion;\n      var separatedIcdCode = this.claimForm837P.icdcodes.split(',');\n      this.ClaimForm.controls.ICDInput1.setValue(separatedIcdCode[0]);\n      this.ClaimForm.controls.ICDInput2.setValue(separatedIcdCode[1]);\n      this.ClaimForm.controls.ICDInput3.setValue(separatedIcdCode[2]);\n      this.ClaimForm.controls.ICDInput4.setValue(separatedIcdCode[3]);\n      this.ClaimForm.controls.ICDInput5.setValue(separatedIcdCode[4]);\n      this.ClaimForm.controls.ICDInput6.setValue(separatedIcdCode[5]);\n      this.ClaimForm.controls.ICDInput7.setValue(separatedIcdCode[6]);\n      this.ClaimForm.controls.ICDInput8.setValue(separatedIcdCode[7]);\n      this.ClaimForm.controls.ICDInput9.setValue(separatedIcdCode[8]);\n      this.ClaimForm.controls.ICDInput10.setValue(separatedIcdCode[9]);\n      this.ClaimForm.controls.ICDInput11.setValue(separatedIcdCode[10]);\n      this.ClaimForm.controls.ICDInput12.setValue(separatedIcdCode[11]);\n      separatedIcdCode[0] != null ? this.AddInTempICD(separatedIcdCode[0], 1) : null;\n      separatedIcdCode[1] != null ? this.AddInTempICD(separatedIcdCode[1], 2) : null;\n      separatedIcdCode[2] != null ? this.AddInTempICD(separatedIcdCode[2], 3) : null;\n      separatedIcdCode[3] != null ? this.AddInTempICD(separatedIcdCode[3], 4) : null;\n      separatedIcdCode[4] != null ? this.AddInTempICD(separatedIcdCode[4], 5) : null;\n      separatedIcdCode[5] != null ? this.AddInTempICD(separatedIcdCode[5], 6) : null;\n      separatedIcdCode[6] != null ? this.AddInTempICD(separatedIcdCode[6], 7) : null;\n      separatedIcdCode[7] != null ? this.AddInTempICD(separatedIcdCode[7], 8) : null;\n      separatedIcdCode[8] != null ? this.AddInTempICD(separatedIcdCode[8], 9) : null;\n      separatedIcdCode[9] != null ? this.AddInTempICD(separatedIcdCode[9], 10) : null;\n      separatedIcdCode[10] != null ? this.AddInTempICD(separatedIcdCode[10], 11) : null;\n      separatedIcdCode[11] != null ? this.AddInTempICD(separatedIcdCode[11], 12) : null;\n      this.ClaimForm.controls.GroupNPI.setValue(this.claimForm837P.billingProviderId);\n      this.ClaimForm.controls.NameOfReferringProvider.setValue(this.createClaimForm.claimForm837.claimsProfessional837.nm107ReferringProviderLastSuffix);\n      this.ClaimForm.controls.FacilityLocationNPI.setValue(this.claimForm837P.facilityId);\n      this.ClaimForm.controls.FacilityName.setValue(this.claimForm837P.serviceLocationLastName);\n      this.ClaimForm.controls.FacilityLocationADDRESSLINE2A.setValue(this.claimForm837P.claimsProfessional837.n301LabFacilityAddress1);\n      this.ClaimForm.controls.FacilityLocationADDRESSLINE2B.setValue(this.claimForm837P.claimsProfessional837.n302LabFacilityAddress2); // this.ClaimForm.controls.FacilityLocationADDRESSLINE2B.setValue(this.claimForm837P.claimsProfessional837.serviceLineProfessional837s[0].n301ServiceFacilityAddress1);\n\n      this.ClaimForm.controls.FacilityLocationCity.setValue(this.claimForm837P.claimsProfessional837.n401LabFacilityCity);\n      this.ClaimForm.controls.FacilityLocationZip.setValue(this.claimForm837P.claimsProfessional837.n403LabFacilityZip);\n      this.ClaimForm.controls.FacilityLocationState.setValue(this.claimForm837P.claimsProfessional837.n402LabFacilityState);\n      this.SubscriberIdCode = this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.nm109SubscriberIdCode;\n      this.ClaimForm.controls.TotalCharge.setValue(Number(this.claimForm837P.totalCharges).toFixed(2));\n      this.FormCreatedDate = this.claimForm837P.formCreatedDate.toString();\n      this.ClaimControlNumber = this.claimForm837P.patientCtrlNo;\n      this.ClaimCreatedBy = this.claimForm837P.lastModifiedByFirstName;\n      this.ReasonForOnHold = this.claimForm837P.onHoldCategory;\n\n      if (this.claimForm837P.claimFormStatusCode == 'OH') {\n        this.LastModifiedBy = this.claimForm837P.lastModifiedByLastName + ' ' + this.claimForm837P.lastModifiedByFirstName;\n        this.LastModifiedDate = this.claimForm837P.lastModifiedDate.toString();\n      }\n\n      this.ClaimForm.controls.GroupNPI.setValue(this.claimForm837P.claimsProfessional837.subscriberkeyNavigation.infoSourcekeyNavigation.nm109BillingProviderIdCode);\n      this.ClaimForm.controls.RenderingProviderPin.setValue(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.renderingProviderPin);\n      this.ClaimForm.controls.PlaceOfService2.setValue(this.claimForm837P.claimsProfessional837.clm0501PlaceOfServiceCode);\n      this.ClaimForm.controls.ReferringProviderNPI.setValue(this.claimForm837P.claimsProfessional837.nm109ReferringProviderIdentifier);\n      this.ClaimForm.controls.PatientRelationshipToInsured.setValue('18');\n      this.ClaimForm.controls.TypeOfProvider.setValue('DN - ReferringProvider');\n      this.ClaimForm.controls.NDC.setValue('HIDENDC');\n      this.ClaimForm.controls.NDC1.setValue('EIN');\n      this.ClaimForm.controls.AuthorizedPersonsSignature.setValue(this.claimForm837P.claimsProfessional837.clm08BenefitsAssignmentCertIndicator != \"Y\" ? \"N\" : \"Y\");\n      this.setServiceLine();\n      this.changeDetect.detectChanges();\n    }\n\n    setServiceLine() {\n      var ServiceLine = this.claimForm837P.claimsProfessional837.serviceLineProfessional837s;\n\n      for (var lengthOfServiLine = 1; lengthOfServiLine < ServiceLine.length; lengthOfServiLine++) {\n        this.AddNewAutoComplete();\n      }\n\n      var totalCharges = 0;\n      var arrayControl = this.ClaimForm.get('CPTInputs');\n\n      for (let i = 0; i < arrayControl.length; i++) {\n        var arrayGroup = arrayControl.at(i); //this.ClaimForm.controls.locationOfService6.setValue(ServiceLine[0].svEmg);\n\n        this.ClaimForm.get('CPTInputs').at(i).controls['locationOfService6'].setValue(ServiceLine[i].svEmg);\n        this.ClaimForm.get('CPTInputs').at(i).controls['option'].setValue(ServiceLine[i].sv10102ProcedureCode);\n        this.ClaimForm.get('CPTInputs').at(i).controls['Modifier1'].setValue(ServiceLine[i].sv10103ProcedureModifier1);\n        this.ClaimForm.get('CPTInputs').at(i).controls['Modifier2'].setValue(ServiceLine[i].sv10104ProcedureModifier2);\n        this.ClaimForm.get('CPTInputs').at(i).controls['Modifier3'].setValue(ServiceLine[i].sv10105ProcedureModifier3);\n        this.ClaimForm.get('CPTInputs').at(i).controls['Modifier4'].setValue(ServiceLine[i].sv10106ProcedureModifier4);\n        this.ClaimForm.get('CPTInputs').at(i).controls['DiagnosisPointer1'].setValue(ServiceLine[i].sv10701DiagnosisCodePointer1);\n        this.ClaimForm.get('CPTInputs').at(i).controls['DiagnosisPointer2'].setValue(ServiceLine[i].sv10702DiagnosisCodePointer2);\n        this.ClaimForm.get('CPTInputs').at(i).controls['DiagnosisPointer3'].setValue(ServiceLine[i].sv10703DiagnosisCodePointer3);\n        this.ClaimForm.get('CPTInputs').at(i).controls['DiagnosisPointer4'].setValue(ServiceLine[i].sv10704DiagnosisCodePointer4);\n        this.ClaimForm.get('CPTInputs').at(i).controls['UnitCharges'].setValue(ServiceLine[i].sv102LineItemChargeAmount);\n\n        if (!!ServiceLine[i].sv10701DiagnosisCodePointer1) {\n          this.tempDP.push({\n            index: i + 1,\n            value: Number(ServiceLine[i].sv10701DiagnosisCodePointer1)\n          });\n        }\n\n        if (!!ServiceLine[i].sv10702DiagnosisCodePointer2) {\n          this.tempDP.push({\n            index: Number(i + 1 + \"\" + 1),\n            value: Number(ServiceLine[i].sv10702DiagnosisCodePointer2)\n          });\n        }\n\n        if (!!ServiceLine[i].sv10703DiagnosisCodePointer3) {\n          this.tempDP.push({\n            index: Number(i + 2 + \"\" + 2),\n            value: Number(ServiceLine[i].sv10703DiagnosisCodePointer3)\n          });\n        }\n\n        if (!!ServiceLine[i].sv10704DiagnosisCodePointer4) {\n          this.tempDP.push({\n            index: Number(i + 3 + \"\" + 3),\n            value: Number(ServiceLine[i].sv10704DiagnosisCodePointer4)\n          });\n        }\n\n        this.ClaimForm.get('CPTInputs').at(i).controls['DaysAndUnitCharges'].setValue(ServiceLine[i].sv104ServiceUnitCount);\n        this.ClaimForm.get('CPTInputs').at(i).controls['EPSDT'].setValue(ServiceLine[i].sv111EpsdtIndicator);\n        this.ClaimForm.get('CPTInputs').at(i).controls['JRenderingProviderId'].setValue(ServiceLine[i].nm109RenderingProviderId);\n        this.ClaimForm.get('CPTInputs').at(i).controls['Charges'].setValue(Number(ServiceLine[i].sv102LineItemChargeAmount) * parseFloat(ServiceLine[i].sv104ServiceUnitCount));\n      }\n\n      this.ClaimForm.controls.ProviderId.setValue(this.claimForm837P.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional.billingGroupNumber);\n      this.ClaimForm.get('TotalCharge').setValue(Number(this.claimForm837P.claimsProfessional837.clm02TotalClaimChargeAmount).toFixed(2));\n    }\n\n    changeQualDate($event) {\n      this.ClaimForm.controls.QualDate.setValue(this.datePipe.transform($event.target.value, 'yyyy-MM-dd'));\n      this.ClaimForm.controls.PatientsOrAuthorizedPersonsSignature.setValue(this.datePipe.transform($event.target.value, 'yyyy-MM-dd'));\n    }\n\n    checkPermission(permissionCode) {\n      return this.userAuthenticationService.userPermissionList.some(x => x.roleCode === permissionCode);\n    }\n\n    AddressMismatchModalOpen() {\n      const dialogConfig = new MatDialogConfig();\n      dialogConfig.data = {\n        UserInfo: null,\n        IpaList: [],\n        UserSelction: 'Single'\n      };\n      const dialogRef = this.dialog.open(AddressMismatchModalComponent, dialogConfig);\n      dialogRef.afterClosed().subscribe(result => {});\n    }\n\n    ChangeSSNEIN(event) {\n      if (event.target.value == 'SSN') {\n        this.ClaimForm.controls.FederalTaxNumber.setValue('');\n      } else {\n        this.ClaimForm.controls.FederalTaxNumber.setValue(this.selectedBillingProvider[0].taxIdOrSSN);\n      }\n    }\n\n    IndexValue(value, index, multiple) {\n      return value + index * multiple;\n    } //Notes\n\n\n    updateAllNotes() {\n      //console.log(document.querySelectorAll('app-note'));\n      let notes = document.querySelectorAll('app-note');\n      notes.forEach((note, index) => {\n        // console.log(note.querySelector('.content').innerHTML)\n        this.notes[note.id].content = note.querySelector('.content').innerHTML;\n      }); //localStorage.setItem('notes', JSON.stringify(this.notes));\n    }\n\n    addNote() {\n      this.notes.push({\n        id: this.notes.length + 1,\n        content: ''\n      }); // sort the array\n\n      this.notes = this.notes.sort((a, b) => {\n        return b.id - a.id;\n      }); //localStorage.setItem('notes', JSON.stringify(this.notes));\n\n      this.showNotes = true;\n    }\n\n    saveNote(event) {\n      const id = event.srcElement.parentElement.parentElement.getAttribute('id');\n      const content = event.target.innerText;\n      event.target.innerText = content;\n      const json = {\n        'id': id,\n        'content': content\n      };\n      this.updateNote(json); //localStorage.setItem('notes', JSON.stringify(this.notes));\n      //console.log(\"********* updating note *********\")\n    }\n\n    updateNote(newValue) {\n      this.notes.forEach((note, index) => {\n        if (note.id == newValue.id) {\n          this.notes[index].content = newValue.content;\n        }\n      });\n    }\n\n    deleteNote(event) {\n      const id = event.srcElement.parentElement.parentElement.parentElement.getAttribute('id'); //console.log('before deleting', this.notes);\n\n      this.notes.forEach((note, index) => {\n        if (note.id == id) {\n          this.notes.splice(index, 1); //localStorage.setItem('notes', JSON.stringify(this.notes));\n          //console.log(\"********* deleting note *********\")\n          // console.log('after deleting', this.notes);\n\n          return;\n        }\n      });\n    }\n\n    displayNotes() {\n      if (this.showNotes) {\n        this.showNotes = false;\n      } else {\n        this.showNotes = true;\n      }\n    }\n\n    record(event) {\n      this.recognition.start();\n      this.addNote();\n    }\n\n  }\n\n  NewClaimComponent.ɵfac = function NewClaimComponent_Factory(t) {\n    return new (t || NewClaimComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.BillingProviderService), i0.ɵɵdirectiveInject(i2.ReferringProviderService), i0.ɵɵdirectiveInject(i3.UserDataService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i5.ProviderManagementService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i6.GetClaimInfoByIdService), i0.ɵɵdirectiveInject(i7.NgxSpinnerService), i0.ɵɵdirectiveInject(i8.UserAuthenticationService), i0.ɵɵdirectiveInject(i9.ClaimReportServiceService), i0.ɵɵdirectiveInject(i10.ClaimService), i0.ɵɵdirectiveInject(i11.MemberService), i0.ɵɵdirectiveInject(i12.NavigationServiceService), i0.ɵɵdirectiveInject(i13.NotificationService), i0.ɵɵdirectiveInject(i14.GetAllCPTCodeService), i0.ɵɵdirectiveInject(i15.GetAllICDCodeService), i0.ɵɵdirectiveInject(i16.QualifierDataByTypeService), i0.ɵɵdirectiveInject(i17.AllStatesBySearchstringService), i0.ɵɵdirectiveInject(i18.AllResubmissionCodeService), i0.ɵɵdirectiveInject(i19.AllPlaceOfServicesService), i0.ɵɵdirectiveInject(i20.MasterdataService), i0.ɵɵdirectiveInject(i21.DatePipe), i0.ɵɵdirectiveInject(i22.MatDialog), i0.ɵɵdirectiveInject(i23.FormBuilder));\n  };\n\n  NewClaimComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NewClaimComponent,\n    selectors: [[\"app-new-claim\"]],\n    hostBindings: function NewClaimComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"scroll\", function NewClaimComponent_scroll_HostBindingHandler($event) {\n          return ctx.checkScroll($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 1235,\n    vars: 126,\n    consts: [[1, \"container-fluid\"], [1, \"mat-card\", \"mt-3\", \"create-claim-form-styles\"], [1, \"create-claim\"], [1, \"create-claim-title\"], [1, \"create-claim-action\"], [\"type\", \"submit\", \"form\", \"Claimform\", 1, \"btn\", \"btn-primary\", \"sbt-btn\"], [\"id\", \"Claimform\", 3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md\", \"form-border\"], [1, \"row\", \"mt-2\"], [1, \"col-md\", \"claim-title\"], [1, \"col-md\"], [\"for\", \"flexCheckDefault\", 1, \"\"], [\"for\", \"PayerName\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"PayerName\", \"name\", \"PayerName\", \"id\", \"PayerName\", \"placeholder\", \"Payer Name\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"PayerID\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"required\", \"\", \"formControlName\", \"PayerID\", \"name\", \"PayerID\", \"id\", \"PayerID\", \"placeholder\", \"Payer ID\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"PayerAddress1\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"PayerAddress1\", \"id\", \"PayerAddress1\", \"name\", \"PayerAddress1\", \"placeholder\", \"Address Line - 1\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"PayerAddress2\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"PayerAddress2\", \"name\", \"PayerAddress2\", \"id\", \"PayerAddress2\", \"placeholder\", \"Address Line - 2\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"PayerCity\", 1, \"create-claims-labels\"], [\"for\", \"PayerState\", 1, \"create-claims-labels\"], [\"for\", \"PayerZip\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"PayerCity\", \"name\", \"PayerCity\", \"id\", \"PayerCity\", \"placeholder\", \"City\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"State\", \"required\", \"\", \"formControlName\", \"PayerState\", \"id\", \"PayerState\", \"name\", \"PayerState\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Payer Zip\", \"formControlName\", \"PayerZip\", \"name\", \"PayerZip\", \"id\", \"PayerZip\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-md-8\", \"form-border\"], [1, \"col-md-3\"], [1, \"form-title\"], [1, \"form-check\", \"form-check-inline\", \"radio-flex\"], [\"type\", \"radio\", \"formControlName\", \"insuranceType\", \"name\", \"insuranceType\", \"value\", \"MB\", \"checked\", \"\", \"id\", \"Medicare\", 1, \"form-check-input\"], [\"for\", \"Medicare\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"insuranceType\", \"name\", \"insuranceType\", \"value\", \"MC\", \"id\", \"Medicaid\", 1, \"form-check-input\"], [\"for\", \"Medicaid\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"insuranceType\", \"name\", \"insuranceType\", \"value\", \"TR\", \"id\", \"Tricare\", 1, \"form-check-input\"], [\"for\", \"Tricare\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"insuranceType\", \"name\", \"insuranceType\", \"value\", \"CH\", \"id\", \"CHAMPVA\", 1, \"form-check-input\"], [\"for\", \"CHAMPVA\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"insuranceType\", \"name\", \"insuranceType\", \"value\", \"GR\", \"id\", \"GroupHealthPlan\", 1, \"form-check-input\"], [\"for\", \"GroupHealthPlan\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"insuranceType\", \"name\", \"insuranceType\", \"value\", \"FE\", \"id\", \"FECABLKLUNG\", 1, \"form-check-input\"], [\"for\", \"FECABLKLUNG\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"insuranceType\", \"name\", \"insuranceType\", \"value\", \"CI\", \"id\", \"Others\", 1, \"form-check-input\"], [\"for\", \"Others\", 1, \"create-claim-radio-labels\"], [1, \"col-md-4\", \"form-border\"], [1, \"col\"], [1, \"search-icon-alignment\"], [\"tabindex\", \"8\", \"type\", \"text\", \"required\", \"\", \"formControlName\", \"InsuredSIDNumber\", \"placeholder\", \"Click on the Search Icon to Select the Member\", \"id\", \"InsuredSIDNumber\", \"name\", \"InsuredSIDNumber\", 1, \"form-control\", \"height27\", \"w-100\"], [1, \"search-icons\", 2, \"margin-top\", \"-2px\", 3, \"click\"], [1, \"2\", \"bd\"], [\"for\", \"PatientLastName\", 1, \"create-claims-labels\"], [\"for\", \"PatientFirstName\", 1, \"create-claims-labels\"], [\"for\", \"PatientMiddleInitial\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"PatientLastName\", \"id\", \"PatientLastName\", \"placeholder\", \"Last Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"PatientFirstName\", \"id\", \"PatientFirstName\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"PatientMiddleInitial\", \"id\", \"PatientMiddleInitial\", \"placeholder\", \"Middle Name\", 1, \"form-control\", \"form-control-sm\"], [1, \"3\", \"bd\"], [\"for\", \"PatientDOB\", 1, \"create-claims-labels\"], [\"for\", \"PatientMale\", 1, \"create-claims-labels\"], [\"type\", \"date\", \"formControlName\", \"PatientDOB\", \"id\", \"PatientDOB\", 1, \"form-control\", \"form-control-sm\", 3, \"max\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"radio\", \"formControlName\", \"PatientGender\", \"name\", \"PatientGender\", \"value\", \"M\", \"checked\", \"\", \"id\", \"PatientMale\", 1, \"form-check-input\"], [\"for\", \"PatientMale\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"PatientGender\", \"name\", \"PatientGender\", \"value\", \"F\", \"id\", \"PatientFemale\", 1, \"form-check-input\"], [\"for\", \"PatientFemale\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"PatientGender\", \"name\", \"PatientGender\", \"value\", \"O\", \"id\", \"PatientOthers\", 1, \"form-check-input\"], [\"for\", \"PatientOthers\", 1, \"create-claim-radio-labels\"], [\"for\", \"InsuredLastName\", 1, \"create-claims-labels\"], [\"for\", \"InsuredFirstName\", 1, \"create-claims-labels\"], [\"for\", \"InsuredMiddleInitial\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"InsuredLastName\", \"id\", \"InsuredLastName\", \"name\", \"InsuredLastName\", \"placeholder\", \"Last Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"InsuredFirstName\", \"name\", \"InsuredFirstName\", \"id\", \"InsuredFirstName\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"InsuredMiddleInitial\", \"id\", \"InsuredMiddleInitial\", \"name\", \"InsuredMiddleInitial\", \"placeholder\", \"Middle Name\", 1, \"form-control\", \"form-control-sm\"], [1, \"5\", \"bd\"], [\"for\", \"PatientStreetNumber\", 1, \"create-claims-labels\"], [\"for\", \"PatientAddressLine2\", 1, \"create-claims-labels\"], [\"for\", \"PatientCity\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"PatientStreetNumber\", \"name\", \"Patient street number\", \"id\", \"PatientStreetNumber\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"PatientAddressLine2\", \"id\", \"PatientAddressLine2\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"PatientCity\", \"id\", \"PatientCity\", \"name\", \"Patient city\", \"placeholder\", \"Address\", 1, \"form-control\", \"Value-Inside-Input\"], [\"for\", \"PatientState\", 1, \"create-claims-labels\"], [\"for\", \"PatientZip\", 1, \"create-claims-labels\"], [\"for\", \"PatientTelephone\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"PatientState\", \"id\", \"PatientState\", \"name\", \"PatientState\", \"placeholder\", \"State\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"PatientZip\", \"id\", \"PatientZip\", \"placeholder\", \"Zip\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"PatientTelephone\", \"id\", \"PatientTelephone\", \"placeholder\", \"Telephone\", 1, \"form-control\", \"form-control-sm\"], [1, \"6\", \"bd\"], [1, \"col\", \"radio-flex\"], [\"type\", \"radio\", \"formControlName\", \"PatientRelationshipToInsured\", \"name\", \"PatientRelationshipToInsured\", \"id\", \"PatientRelationshipSelf\", \"value\", \"18\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"PatientRelationshipSelf\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"PatientRelationshipToInsured\", \"name\", \"PatientRelationshipToInsured\", \"id\", \"PatientRelationshipSpouse\", \"value\", \"01\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"PatientRelationshipSpouse\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"PatientRelationshipToInsured\", \"name\", \"PatientRelationshipToInsured\", \"id\", \"PatientRelationshipChild\", \"value\", \"19\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"PatientRelationshipChild\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"PatientRelationshipToInsured\", \"name\", \"PatientRelationshipToInsured\", \"id\", \"PatientRelationshipOther\", \"value\", \"21\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"PatientRelationshipOther\", 1, \"create-claim-radio-labels\"], [1, \"8\", \"bd\"], [\"type\", \"text\", \"formControlName\", \"NUCC1\", \"name\", \"NUCC1\", \"id\", \"NUCC1\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"NUCC2\", \"name\", \"NUCC2\", \"id\", \"NUCC2\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"InsuredStreetNumber\", 1, \"create-claims-labels\"], [\"for\", \"InsuredAddressLine2\", 1, \"create-claims-labels\"], [\"for\", \"InsuredCity\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"InsuredStreetNumber\", \"name\", \"InsuredStreetNumber\", \"id\", \"InsuredStreetNumber\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"InsuredAddressLine2\", \"id\", \"InsuredAddressLine2\", \"name\", \"InsuredAddressLine2\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"InsuredCity\", \"name\", \"InsuredCity\", \"id\", \"InsuredCity\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"InsuredState\", 1, \"create-claims-labels\"], [\"for\", \"InsuredZip\", 1, \"create-claims-labels\"], [\"for\", \"InsuredTelephone\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"InsuredState\", \"id\", \"InsuredState\", \"name\", \"InsuredState\", \"required\", \"\", \"placeholder\", \"Stae\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"InsuredZip\", \"name\", \"InsuredZip\", \"id\", \"InsuredZip\", \"placeholder\", \"Zip\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"InsuredTelephone\", \"id\", \"InsuredTelephone\", \"name\", \"InsuredTelephone\", \"placeholder\", \"Telephone\", 1, \"form-control\", \"form-control-sm\"], [1, \"9\", \"bd\"], [\"for\", \"OtherInsuredLastName\", 1, \"create-claims-labels\"], [\"for\", \"OtherInsuredFirstName\", 1, \"create-claims-labels\"], [\"for\", \"OtherInsuredMiddleInitial\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"OtherInsuredLastName\", \"id\", \"OtherInsuredLastName\", \"name\", \"OtherInsuredLastName\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"OtherInsuredFirstName\", \"id\", \"OtherInsuredFirstName\", \"name\", \"OtherInsuredFirstName\", \"placeholder\", \"Last Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"OtherInsuredMiddleInitial\", \"name\", \"OtherInsuredMiddleInitial\", \"id\", \"OtherInsuredMiddleInitial\", \"placeholder\", \"Middle Name\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"OtherInsuredPolicy\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"OtherInsuredPolicy\", \"name\", \"OtherInsuredPolicy\", \"id\", \"OtherInsuredPolicy\", \"placeholder\", \"Other Insured's Policy Or Group Number\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-4\"], [\"for\", \"ReservedForNUCCUse\", 1, \"create-claims-labels\"], [1, \"col-8\"], [\"for\", \"InsurancePlanNameOrProgramName\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"ReservedForNUCCUse\", \"name\", \"ReservedForNUCCUse\", \"id\", \"ReservedForNUCCUse\", \"placeholder\", \"Reserved For NUCC Use\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"InsurancePlanNameOrProgramName\", \"name\", \"InsurancePlanNameOrProgramName\", \"id\", \"InsurancePlanNameOrProgramName\", \"placeholder\", \"Reserved For NUCC Use\", 1, \"form-control\", \"form-control-sm\"], [1, \"10\", \"bd\"], [\"for\", \"flexCheckDefault\", 1, \"create-claims-labels\"], [\"type\", \"radio\", \"formControlName\", \"Employment\", \"name\", \"Employment\", \"id\", \"EmploymentYes\", \"value\", \"Y\", \"checked\", \"\", 1, \"form-check-input\"], [\"for\", \"EmploymentYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"Employment\", \"name\", \"Employment\", \"id\", \"EmploymentNo\", \"value\", \"N\", 1, \"form-check-input\"], [\"for\", \"EmploymentNo\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"AutoAccident\", \"name\", \"AutoAccident\", \"id\", \"AutoAccidentYes\", \"value\", \"Y\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"AutoAccidentYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"AutoAccident\", \"name\", \"AutoAccident\", \"id\", \"AutoAccidentNo\", \"value\", \"N\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"AutoAccidentNo\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"OtherAccidents\", \"name\", \"OtherAccidents\", \"id\", \"OtherAccidentsYes\", \"value\", \"Y\", \"checked\", \"\", 1, \"form-check-input\"], [\"for\", \"OtherAccidentsYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"OtherAccidents\", \"name\", \"OtherAccidents\", \"id\", \"OtherAccidentsNo\", \"value\", \"N\", 1, \"form-check-input\"], [\"for\", \"OtherAccidentsNo\", 1, \"create-claim-radio-labels\"], [\"for\", \"ClaimsCodes\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"ClaimsCodes\", \"name\", \"ClaimsCodes\", \"id\", \"ClaimsCodes\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"FECANumber\", \"name\", \"FECANumber\", \"id\", \"FECANumber\", \"placeholder\", \"Please Enter Insured\\u2019s Policy Group Or FECA Number\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"InsuredDateOfBirth\", 1, \"create-claims-labels\"], [\"type\", \"date\", \"type\", \"date\", \"id\", \"InsuredDateOfBirth\", \"formControlName\", \"InsuredDateOfBirth\", 1, \"form-control\", \"form-control-sm\", 3, \"max\"], [\"type\", \"radio\", \"formControlName\", \"SexOfInsured\", \"name\", \"SexOfInsured\", \"value\", \"M\", \"id\", \"SexOfInsuredMale\", \"checked\", \"\", 1, \"form-check-input\"], [\"for\", \"SexOfInsuredMale\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"SexOfInsured\", \"name\", \"SexOfInsured\", \"id\", \"SexOfInsuredFemale\", \"value\", \"F\", 1, \"form-control\"], [\"for\", \"SexOfInsuredFemale\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"SexOfInsured\", \"name\", \"SexOfInsured\", \"value\", \"U\", \"id\", \"SexOfInsuredOthers\", 1, \"form-control\"], [\"for\", \"SexOfInsuredOthers\", 1, \"create-claim-radio-labels\"], [\"for\", \"OtherClaimID\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"type\", \"text\", \"formControlName\", \"OtherClaimID\", \"name\", \"OtherClaimID\", \"id\", \"OtherClaimID\", \"value\", \"Reserved For NUCC Use\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"InsurancePlanName\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"type\", \"text\", \"formControlName\", \"InsurancePlanName\", \"name\", \"InsurancePlanName\", \"id\", \"InsurancePlanName\", \"value\", \"Insurance Program or Plan Name\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"AnotherHealthBenefitPlanYes\", 1, \"create-claims-labels\"], [1, \"col-md\", \"radio-flex\"], [\"type\", \"radio\", \"formControlName\", \"AnotherHealthBenefitPlan\", \"name\", \"AnotherHealthBenefitPlan\", \"value\", \"Yes\", \"id\", \"AnotherHealthBenefitPlanYes\", 1, \"form-control\"], [\"for\", \"AnotherHealthBenefitPlanYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"AnotherHealthBenefitPlan\", \"name\", \"AnotherHealthBenefitPlan\", \"value\", \"No\", \"id\", \"AnotherHealthBenefitPlanNo\", 1, \"form-control\"], [\"for\", \"AnotherHealthBenefitPlanNo\", 1, \"create-claim-radio-labels\"], [1, \"\"], [\"type\", \"radio\", \"formControlName\", \"PersonsSignature\", \"name\", \"PersonsSignature\", \"value\", \"Y\", \"id\", \"PersonsSignatureYes\", \"checked\", \"\", 1, \"form-check-input\"], [\"for\", \"PersonsSignatureYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"PersonsSignature\", \"name\", \"PersonsSignature\", \"value\", \"N\", \"id\", \"PersonsSignatureNo\", 1, \"form-check-input\"], [\"for\", \"PersonsSignatureNo\", 1, \"create-claim-radio-labels\"], [\"for\", \"PatientsOrAuthorizedPersonsSignature\", 1, \"create-claims-labels\"], [\"type\", \"date\", \"value\", \"currentDate\", \"id\", \"PatientsOrAuthorizedPersonsSignature\", \"formControlName\", \"PatientsOrAuthorizedPersonsSignature\", 1, \"form-control\", \"form-control-sm\", 3, \"max\"], [\"type\", \"radio\", \"formControlName\", \"AuthorizedPersonsSignature\", \"name\", \"AuthorizedPersonsSignature\", \"id\", \"AuthorizedPersonsSignatureYes\", \"value\", \"Y\", \"checked\", \"\", 1, \"form-check-input\"], [\"for\", \"AuthorizedPersonsSignatureYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"AuthorizedPersonsSignature\", \"name\", \"AuthorizedPersonsSignature\", \"id\", \"AuthorizedPersonsSignatureNo\", \"value\", \"N\", 1, \"form-check-input\"], [\"for\", \"AuthorizedPersonsSignatureNo\", 1, \"create-claim-radio-labels\"], [1, \"14\", \"bd\"], [\"for\", \"DateOfCurrentIllness\", 1, \"create-claims-labels\"], [\"for\", \"Qual\", 1, \"create-claims-labels\"], [\"type\", \"date\", \"id\", \"DateOfCurrentIllness\", \"value\", \"\", \"formControlName\", \"QualDate\", 1, \"w-50\", 3, \"max\"], [\"type\", \"text\", \"formControlName\", \"Qual\", \"id\", \"Qual\", \"name\", \"Qual\", \"required\", \"\", \"placeholder\", \"Select\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [1, \"15\", \"bd\"], [\"for\", \"OtherDateQual\", 1, \"create-claim-radio-labels\"], [\"type\", \"date\", \"formControlName\", \"OtherDateQual\", \"id\", \"OtherDateQual\", \"name\", \"OtherDateQual\", 1, \"form-control\", \"form-control-sm\", 3, \"max\"], [\"for\", \"inlineRadio2\", 1, \"create-claim-radio-labels\"], [\"type\", \"text\", \"formControlName\", \"Qual2\", \"id\", \"Qual2\", \"name\", \"Qual2\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"PatientUnableToWorkInCurrentOccupationFrom\", 1, \"create-claims-labels\"], [\"type\", \"date\", \"formControlName\", \"PatientUnableToWorkInCurrentOccupationFrom\", \"name\", \"PatientUnableToWorkInCurrentOccupationFrom\", \"id\", \"PatientUnableToWorkInCurrentOccupationFrom\", 1, \"form-control\", \"form-control-sm\", 3, \"max\"], [\"for\", \"PatientUnableToWorkInCurrentOccupationTo\", 1, \"create-claims-labels\"], [\"type\", \"date\", \"formControlName\", \"PatientUnableToWorkInCurrentOccupationTo\", \"name\", \"PatientUnableToWorkInCurrentOccupationTo\", \"id\", \"PatientUnableToWorkInCurrentOccupationTo\", 1, \"form-control\", 3, \"max\"], [1, \"17\", \"bd\"], [\"for\", \"ReferringProviderLastName\", 1, \"create-claims-labels\"], [\"for\", \"ReferringProviderFirstName\", 1, \"create-claims-labels\"], [\"for\", \"ReferringProviderMiddleName\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"formControlName\", \"ReferringProviderLastName\", \"name\", \"ReferringProviderLastName\", \"id\", \"ReferringProviderLastName\", \"placeholder\", \"Last Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"ReferringProviderFirstName\", \"placeholder\", \"Enter First Name\", \"id\", \"ReferringProviderFirstName\", \"name\", \"ReferringProviderFirstName\", \"placeholder\", \"Middle Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"ReferringProviderMiddleName\", \"placeholder\", \"Enter Middle Name\", \"id\", \"ReferringProviderMiddleName\", \"name\", \"ReferringProviderMiddleName\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [1, \"bd\"], [1, \"col-md-2\"], [1, \"col-md-4\"], [\"aria-label\", \"Default select example\", 1, \"form-select\", \"form-select-sm\"], [\"selected\", \"\"], [\"value\", \"1\"], [\"type\", \"date\", \"name\", \"birthday\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"HospitalizationDatesRelatedToCurrentServicesFrom\", 1, \"create-claims-labels\"], [\"type\", \"date\", \"formControlName\", \"HospitalizationDatesRelatedToCurrentServicesFrom\", \"name\", \"HospitalizationDatesRelatedToCurrentServicesFrom\", \"id\", \"HospitalizationDatesRelatedToCurrentServicesFrom\", 1, \"form-control\", \"form-control-sm\", 3, \"max\"], [\"for\", \"HospitalizationDatesRelatedToCurrentServicesTo\", 1, \"create-claims-labels\"], [\"type\", \"date\", \"formControlName\", \"HospitalizationDatesRelatedToCurrentServicesTo\", \"name\", \"HospitalizationDatesRelatedToCurrentServicesTo\", \"id\", \"HospitalizationDatesRelatedToCurrentServicesTo\", 1, \"form-control\", 3, \"max\"], [1, \"19\", \"bd\"], [\"type\", \"text\", \"formControlName\", \"AdditionalClaimInformation\", \"name\", \"AdditionalClaimInformation\", \"id\", \"AdditionalClaimInformation\", \"placeholder\", \"Additional Claim Information\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"radio\", \"formControlName\", \"OutsideLab\", \"name\", \"OutsideLab\", \"value\", \"Yes\", \"id\", \"OutsideLabYes\", \"checked\", \"\", 1, \"form-check-input\"], [\"for\", \"OutsideLabYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"OutsideLab\", \"name\", \"OutsideLab\", \"value\", \"No\", \"id\", \"OutsideLabNo\", 1, \"form-check-input\"], [\"for\", \"OutsideLabNo\", 1, \"create-claim-radio-labels\"], [1, \"21\", \"bd\"], [\"type\", \"text\", \"formControlName\", \"ICDInput1\", \"id\", \"ICDInput1\", \"name\", \"ICDInput1\", \"required\", \"\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput2\", \"id\", \"ICDInput2\", \"name\", \"ICDInput2\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput3\", \"id\", \"ICDInput3\", \"name\", \"ICDInput3\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput4\", \"id\", \"ICDInput4\", \"name\", \"ICDInput4\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput5\", \"id\", \"ICDInput5\", \"name\", \"ICDInput5\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput6\", \"id\", \"ICDInput6\", \"name\", \"ICDInput6\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput7\", \"id\", \"ICDInput7\", \"name\", \"ICDInput7\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput8\", \"id\", \"ICDInput8\", \"name\", \"ICDInput8\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput9\", \"id\", \"ICDInput9\", \"name\", \"ICDInput9\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput10\", \"id\", \"ICDInput10\", \"name\", \"ICDInput10\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput11\", \"id\", \"ICDInput11\", \"name\", \"ICDInput11\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"formControlName\", \"ICDInput12\", \"id\", \"ICDInput12\", \"name\", \"ICDInput12\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [1, \"form-control\", \"form-control-sm\", 2, \"font-size\", \"12px\"], [\"required\", \"\", \"formControlName\", \"ResubmissionCode\", \"type\", \"text\", \"placeholder\", \"Resubmission Code\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\"], [\"formControlName\", \"ReferenceNumber\", \"name\", \"ReferenceNumber\", \"type\", \"text\", \"placeholder\", \"Reference Number\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"PriorAuthorisationNumber\", \"name\", \"PriorAuthorisationNumber\", \"id\", \"PriorAuthorisationNumber\", \"placeholder\", \"Prior Authorization Number\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup Address 1\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup Address 2\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup City\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup State\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Zip Code\", 1, \"form-control\", \"form-control-sm\"], [1, \"24\"], [1, \"table-responsive\"], [1, \"table\", \"table-bordered\"], [\"colspan\", \"2\", 2, \"font-size\", \"12px\"], [\"scope\", \"col\", 2, \"font-size\", \"12px\"], [\"scope\", \"col\"], [\"formArrayName\", \"CPTInputs\"], [3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [1, \"action-flex\"], [\"disabled\", \"\", 1, \"btn-common-danger\"], [1, \"btn-primary\", 3, \"click\"], [\"type\", \"radio\", \"name\", \"inlineRadioOptions\", \"id\", \"inlineRadio1\", \"value\", \"option1\", 1, \"form-check-input\"], [\"for\", \"inlineRadio1\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"name\", \"inlineRadioOptions\", \"id\", \"inlineRadio2\", \"value\", \"option2\", 1, \"form-check-input\"], [\"type\", \"text\", \"required\", \"\", \"formControlName\", \"FederalTaxNumber\", \"id\", \"FederalTaxNumber\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Patient Account Number\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"TotalCharge\", \"placeholder\", \"Total Charge\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"AmountPaid\", \"placeholder\", \"Amount Paid\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"ReservedForNUCC\", \"placeholder\", \"Reserved For NUCC\", 1, \"form-control\", \"form-control-sm\"], [1, \"31\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Last Name\", 1, \"form-control\", \"form-control-sm\"], [1, \"row\", \"mt-2\", \"mt-3\"], [\"type\", \"text\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Middle Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"NPI\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Provider ID\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"CLIA\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Mammography Certificate\", 1, \"form-control\", \"form-control-sm\"], [1, \"32\", \"bd\"], [\"type\", \"text\", \"formControlName\", \"FacilityName\", \"name\", \"FacilityName\", \"id\", \"FacilityName\", \"placeholder\", \"Facility Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"FacilityLocationADDRESSLINE2A\", \"name\", \"FacilityLocationADDRESSLINE2A\", \"id\", \"FacilityLocationADDRESSLINE2A\", \"placeholder\", \"Address Line 1\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"FacilityLocationADDRESSLINE2B\", \"name\", \"FacilityLocationADDRESSLINE2B\", \"id\", \"FacilityLocationADDRESSLINE2B\", \"placeholder\", \"Address Line 2\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"FacilityLocationCity\", \"name\", \"FacilityLocationCity\", \"id\", \"FacilityLocationCity\", \"placeholder\", \"City\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"FacilityLocationState\", \"id\", \"FacilityLocationState\", \"placeholder\", \"State\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"FacilityLocationZip\", \"name\", \"FacilityLocationZip\", \"id\", \"FacilityLocationZip\", \"placeholder\", \"Zip Code\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"FacilityLocationNPI\", \"name\", \"FacilityLocationNPI\", \"id\", \"FacilityLocationNPI\", \"placeholder\", \"NPI\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"FacilityId\", \"name\", \"FacilityId\", \"id\", \"FacilityId\", \"placeholder\", \"Facility ID\", 1, \"form-control\", \"form-control-sm\"], [1, \"33\"], [1, \"col\", \"search-icon-alignment\"], [\"type\", \"text\", \"required\", \"\", \"formControlName\", \"BillingProvider\", \"placeholder\", \"Billing provider\", 1, \"form-control\", \"form-control-sm\", 3, \"blur\"], [1, \"search-icons\", 2, \"right\", \"6%\", \"top\", \"10%\", 3, \"click\"], [\"formControlName\", \"BillingProviderFIRSTNAME\", \"type\", \"text\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"BillingProviderMIDDLENAME\", \"type\", \"text\", \"placeholder\", \"Middle Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"BillingProviderAddress\", \"placeholder\", \"Address Line 1\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"BillingProviderADDRESSLINE2\", \"placeholder\", \"Address Line 2\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"BillingProviderCity\", \"placeholder\", \"City\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"BillingProviderState\", \"type\", \"text\", \"placeholder\", \"State\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"BillingProviderZip\", \"placeholder\", \"ZIP Code\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"BillingProviderTelephone\", \"placeholder\", \"Telephone\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Taxonomy Code\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"RenderingProvider\", \"placeholder\", \"Rendering Provider\", 1, \"form-control\", \"form-control-sm\"], [1, \"search-icons\", 2, \"margin-right\", \"12px\", \"margin-top\", \"-6px\", 3, \"click\"], [\"formControlName\", \"RenderingProviderName\", \"type\", \"text\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"RenderingProviderMiddleName\", \"type\", \"text\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"ProviderSpecialty\", \"type\", \"text\", \"placeholder\", \"Provider Specialty\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"ProviderNPI\", \"name\", \"ProviderNPI\", \"id\", \"ProviderNPI\", \"placeholder\", \"Provider NPI\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"RenderingProviderPin\", \"name\", \"RenderingProviderPin\", \"id\", \"RenderingProviderPin\", \"placeholder\", \"PIN\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"GroupNPI\", \"name\", \"GroupNPI\", \"id\", \"GroupNPI\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"ProviderId\", \"name\", \"ProviderId\", \"id\", \"ProviderId\", \"placeholder\", \"Provider ID\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"IdQual\", \"name\", \"IdQual\", \"id\", \"IdQual\", \"placeholder\", \"Qual\", 1, \"form-control\", \"form-control-sm\"], [3, \"formGroupName\"], [1, \"row\", \"mt-2\", 2, \"width\", \"150px\"], [1, \"col-2\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"value\", \"\", \"id\", \"flexCheckDefault\", 1, \"form-check-input\"], [1, \"col-10\"], [\"id\", \"DateOfServiceFrom\", \"formControlName\", \"DateOfServiceFrom\", \"type\", \"date\", \"id\", \"dateOfService5\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"max\", \"change\"], [\"formControlName\", \"DateOfServiceTo\", \"id\", \"dateOfService6\", \"required\", \"\", \"type\", \"date\", 1, \"form-control\", \"form-control-sm\", 3, \"max\"], [\"type\", \"text\", \"value\", \"11\", \"required\", \"\", \"formControlName\", \"PlaceOfService3\", \"id\", \"PlaceOfService3\", \"placeholder\", \"POS\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\"], [\"id\", \"locationOfService6\", \"name\", \"locationOfService6\", \"placeholder\", \"d.ff\", \"formControlName\", \"locationOfService6\", 1, \"form-select\", \"form-select-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"hidden\", \"\", \"selected\", \"\", 3, \"ngValue\"], [\"type\", \"text\", \"required\", \"\", \"formControlName\", \"option\", \"matInput\", \"\", \"placeholder\", \"CPT\", 1, \"form-control\", \"form-control-sm\", 3, \"matTooltip\", \"id\", \"ngModelChange\"], [1, \"table-flex\", \"mt-2\"], [\"formControlName\", \"Modifier1\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"M1\", 1, \"form-control\", \"form-control-sm\", 3, \"id\"], [\"formControlName\", \"Modifier2\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"M2\", 1, \"form-control\", \"form-control-sm\", 3, \"id\"], [\"formControlName\", \"Modifier3\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"M3\", 1, \"form-control\", \"form-control-sm\", 3, \"id\"], [\"formControlName\", \"Modifier4\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"M4\", 1, \"form-control\", \"form-control-sm\", 3, \"id\"], [\"formControlName\", \"DiagnosisPointer1\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"D1\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"id\", \"keypress\", \"input\"], [\"formControlName\", \"DiagnosisPointer2\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"D2\", 1, \"form-control\", \"form-control-sm\", 3, \"id\", \"keypress\", \"input\"], [\"formControlName\", \"DiagnosisPointer3\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"D3\", 1, \"form-control\", \"form-control-sm\", 3, \"id\", \"keypress\", \"input\"], [\"formControlName\", \"DiagnosisPointer4\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"D4\", 1, \"form-control\", \"form-control-sm\", 3, \"id\", \"keypress\", \"input\"], [\"type\", \"text\", \"placeholder\", \"\", \"value\", \"0.01\", \"formControlName\", \"DaysAndUnitCharges\", 1, \"form-control\", \"form-control-sm\", 3, \"id\", \"keyup\"], [\"type\", \"text\", \"placeholder\", \"\", \"value\", \"0.01\", \"formControlName\", \"UnitCharges\", 1, \"form-control\", \"form-control-sm\", 3, \"id\"], [\"formControlName\", \"EPSDT\", \"aria-label\", \"Default select example\", 1, \"form-select\", \"form-select-sm\", 3, \"id\"], [\"selected\", \"\", \"value\", \" \"], [\"value\", \"Y\"], [\"value\", \"N\"], [1, \"d-flex\", \"mt-2\"], [1, \"col-md-9\"], [\"type\", \"text\", \"placeholder\", \"\", \"required\", \"\", \"formControlName\", \"JRenderingProviderId\", 1, \"form-control\", \"form-control-sm\"], [2, \"color\", \"red\"]],\n    template: function NewClaimComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵtext(4, \" Create Claim (CMS 1500) \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 4)(6, \"button\", 5);\n        i0.ɵɵtext(7, \"Save\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"form\", 6);\n        i0.ɵɵlistener(\"ngSubmit\", function NewClaimComponent_Template_form_ngSubmit_8_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"uppercase\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"div\", 9)(16, \"div\", 10);\n        i0.ɵɵtext(17);\n        i0.ɵɵpipe(18, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 10);\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"uppercase\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 9)(24, \"div\", 11);\n        i0.ɵɵelement(25, \"label\", 12);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(26, \"div\", 8)(27, \"div\", 9)(28, \"div\", 10);\n        i0.ɵɵtext(29, \" Held On \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"div\", 9)(31, \"div\", 10);\n        i0.ɵɵtext(32, \" Held By \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"div\", 9)(34, \"div\", 10);\n        i0.ɵɵtext(35, \" Reason for holding \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 10);\n        i0.ɵɵtext(38, \" Description \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(39, \"div\", 8)(40, \"div\", 9)(41, \"div\", 11)(42, \"label\", 13);\n        i0.ɵɵtext(43, \"Payer Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(44, \"input\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"div\", 11)(46, \"label\", 15);\n        i0.ɵɵtext(47, \"Payer ID\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(48, \"input\", 16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(49, \"div\", 9)(50, \"div\", 11)(51, \"label\", 17);\n        i0.ɵɵtext(52, \"1st Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(53, \"input\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 11)(56, \"label\", 19);\n        i0.ɵɵtext(57, \"2nd Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(58, \"input\", 20);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(59, \"div\", 9)(60, \"div\", 11)(61, \"label\", 21);\n        i0.ɵɵtext(62, \"City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(63, \"div\", 11)(64, \"label\", 22);\n        i0.ɵɵtext(65, \"State\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(66, \"div\", 11)(67, \"label\", 23);\n        i0.ɵɵtext(68, \"Payer Zip\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(69, \"div\", 7)(70, \"div\", 11);\n        i0.ɵɵelement(71, \"input\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"div\", 11);\n        i0.ɵɵelement(73, \"input\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(74, \"div\", 11);\n        i0.ɵɵelement(75, \"input\", 26);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(76, \"div\", 7)(77, \"div\", 27)(78, \"div\", 7)(79, \"div\", 28)(80, \"p\", 29);\n        i0.ɵɵtext(81, \"1. Medicare\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(82, \"div\", 30);\n        i0.ɵɵelement(83, \"input\", 31);\n        i0.ɵɵelementStart(84, \"label\", 32);\n        i0.ɵɵtext(85, \"Medicare\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(86, \"div\", 28)(87, \"p\", 29);\n        i0.ɵɵtext(88, \"Medicaid\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(89, \"div\", 30);\n        i0.ɵɵelement(90, \"input\", 33);\n        i0.ɵɵelementStart(91, \"label\", 34);\n        i0.ɵɵtext(92, \"Medicaid\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(93, \"div\", 28)(94, \"p\", 29);\n        i0.ɵɵtext(95, \"Tricare\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(96, \"div\", 30);\n        i0.ɵɵelement(97, \"input\", 35);\n        i0.ɵɵelementStart(98, \"label\", 36);\n        i0.ɵɵtext(99, \"Tricare\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(100, \"div\", 28)(101, \"p\", 29);\n        i0.ɵɵtext(102, \"CHAMPVA\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"div\", 30);\n        i0.ɵɵelement(104, \"input\", 37);\n        i0.ɵɵelementStart(105, \"label\", 38);\n        i0.ɵɵtext(106, \"CHAMPVA\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(107, \"div\", 28)(108, \"p\", 29);\n        i0.ɵɵtext(109, \"Group Health Plan\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(110, \"div\", 30);\n        i0.ɵɵelement(111, \"input\", 39);\n        i0.ɵɵelementStart(112, \"label\", 40);\n        i0.ɵɵtext(113, \"Group Health Plan\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(114, \"div\", 28)(115, \"p\", 29);\n        i0.ɵɵtext(116, \"FECA BLK LUNG\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(117, \"div\", 30);\n        i0.ɵɵelement(118, \"input\", 41);\n        i0.ɵɵelementStart(119, \"label\", 42);\n        i0.ɵɵtext(120, \"FECA BLK LUNG\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(121, \"div\", 28)(122, \"p\", 29);\n        i0.ɵɵtext(123, \"Others\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(124, \"div\", 30);\n        i0.ɵɵelement(125, \"input\", 43);\n        i0.ɵɵelementStart(126, \"label\", 44);\n        i0.ɵɵtext(127, \"Others\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(128, \"div\", 45)(129, \"div\", 46)(130, \"p\", 29);\n        i0.ɵɵtext(131, \"1A. Insured\\u2019s I.D. Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(132, \"div\", 47);\n        i0.ɵɵelement(133, \"input\", 48);\n        i0.ɵɵelementStart(134, \"mat-icon\", 49);\n        i0.ɵɵlistener(\"click\", function NewClaimComponent_Template_mat_icon_click_134_listener() {\n          return ctx.OnInsuredIdClick();\n        });\n        i0.ɵɵtext(135, \"search\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(136, \"div\", 7)(137, \"div\", 8)(138, \"section\", 50)(139, \"div\", 9)(140, \"div\", 46)(141, \"p\", 29);\n        i0.ɵɵtext(142, \"2. Patient Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(143, \"div\", 9)(144, \"div\", 46)(145, \"label\", 51);\n        i0.ɵɵtext(146, \"Last Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(147, \"div\", 46)(148, \"label\", 52);\n        i0.ɵɵtext(149, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(150, \"div\", 46)(151, \"label\", 53);\n        i0.ɵɵtext(152, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(153, \"div\", 7)(154, \"div\", 46);\n        i0.ɵɵelement(155, \"input\", 54);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(156, \"div\", 46);\n        i0.ɵɵelement(157, \"input\", 55);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(158, \"div\", 46);\n        i0.ɵɵelement(159, \"input\", 56);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(160, \"div\", 8)(161, \"section\", 57)(162, \"div\", 9)(163, \"div\", 46)(164, \"p\", 29);\n        i0.ɵɵtext(165, \"3. Patient Birth Date\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(166, \"div\", 9)(167, \"div\", 46)(168, \"label\", 58);\n        i0.ɵɵtext(169, \"Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(170, \"div\", 46)(171, \"label\", 59);\n        i0.ɵɵtext(172, \"Sex\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(173, \"div\", 7)(174, \"div\", 46);\n        i0.ɵɵelement(175, \"input\", 60);\n        i0.ɵɵpipe(176, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(177, \"div\", 46)(178, \"div\", 61);\n        i0.ɵɵelement(179, \"input\", 62);\n        i0.ɵɵelementStart(180, \"label\", 63);\n        i0.ɵɵtext(181, \"Male\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(182, \"div\", 61);\n        i0.ɵɵelement(183, \"input\", 64);\n        i0.ɵɵelementStart(184, \"label\", 65);\n        i0.ɵɵtext(185, \"Female\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(186, \"div\", 61);\n        i0.ɵɵelement(187, \"input\", 66);\n        i0.ɵɵelementStart(188, \"label\", 67);\n        i0.ɵɵtext(189, \"Other\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(190, \"div\", 8)(191, \"div\", 9)(192, \"p\", 29);\n        i0.ɵɵtext(193, \"4. Insured\\u2019s Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(194, \"div\", 9)(195, \"div\", 46)(196, \"label\", 68);\n        i0.ɵɵtext(197, \"Last Name \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(198, \"div\", 46)(199, \"label\", 69);\n        i0.ɵɵtext(200, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(201, \"div\", 46)(202, \"label\", 70);\n        i0.ɵɵtext(203, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(204, \"div\", 7)(205, \"div\", 46);\n        i0.ɵɵelement(206, \"input\", 71);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(207, \"div\", 46);\n        i0.ɵɵelement(208, \"input\", 72);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(209, \"div\", 46);\n        i0.ɵɵelement(210, \"input\", 73);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(211, \"div\", 7)(212, \"div\", 8)(213, \"section\", 74)(214, \"div\", 9)(215, \"p\", 29);\n        i0.ɵɵtext(216, \"5.Patient Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(217, \"div\", 9)(218, \"div\", 46)(219, \"label\", 75);\n        i0.ɵɵtext(220, \"Address Line - 1 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(221, \"div\", 46)(222, \"label\", 76);\n        i0.ɵɵtext(223, \"Address Line - 2\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(224, \"div\", 46)(225, \"label\", 77);\n        i0.ɵɵtext(226, \"City\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(227, \"div\", 7)(228, \"div\", 46);\n        i0.ɵɵelement(229, \"input\", 78);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(230, \"div\", 46);\n        i0.ɵɵelement(231, \"input\", 79);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(232, \"div\", 46);\n        i0.ɵɵelement(233, \"input\", 80);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(234, \"div\", 9)(235, \"div\", 46)(236, \"label\", 81);\n        i0.ɵɵtext(237, \"State \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(238, \"div\", 46)(239, \"label\", 82);\n        i0.ɵɵtext(240, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(241, \"div\", 46)(242, \"label\", 83);\n        i0.ɵɵtext(243, \"Telephone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(244, \"div\", 7)(245, \"div\", 46)(246, \"input\", 84);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_246_listener($event) {\n          return ctx.SelectedPatientState = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(247, \"div\", 46);\n        i0.ɵɵelement(248, \"input\", 85);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(249, \"div\", 46);\n        i0.ɵɵelement(250, \"input\", 86);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(251, \"div\", 8)(252, \"section\", 87)(253, \"div\", 9)(254, \"div\", 46)(255, \"p\", 29);\n        i0.ɵɵtext(256, \"6. Patient Relationship To Insured\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(257, \"div\", 9)(258, \"div\", 88)(259, \"div\", 30)(260, \"input\", 89);\n        i0.ɵɵlistener(\"change\", function NewClaimComponent_Template_input_change_260_listener($event) {\n          return ctx.handlePatientRelationshipChanged($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(261, \"label\", 90);\n        i0.ɵɵtext(262, \"Self\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(263, \"div\", 30)(264, \"input\", 91);\n        i0.ɵɵlistener(\"change\", function NewClaimComponent_Template_input_change_264_listener($event) {\n          return ctx.handlePatientRelationshipChanged($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(265, \"label\", 92);\n        i0.ɵɵtext(266, \"Spouse\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(267, \"div\", 30)(268, \"input\", 93);\n        i0.ɵɵlistener(\"change\", function NewClaimComponent_Template_input_change_268_listener($event) {\n          return ctx.handlePatientRelationshipChanged($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(269, \"label\", 94);\n        i0.ɵɵtext(270, \"Child\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(271, \"div\", 30)(272, \"input\", 95);\n        i0.ɵɵlistener(\"change\", function NewClaimComponent_Template_input_change_272_listener($event) {\n          return ctx.handlePatientRelationshipChanged($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(273, \"label\", 96);\n        i0.ɵɵtext(274, \"Other\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(275, \"section\", 97)(276, \"div\", 9)(277, \"div\", 46)(278, \"p\", 29);\n        i0.ɵɵtext(279, \"8. Reserved For NUCC Use\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(280, \"div\", 9)(281, \"div\", 46);\n        i0.ɵɵelement(282, \"input\", 98);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(283, \"div\", 9)(284, \"div\", 46);\n        i0.ɵɵelement(285, \"input\", 99);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(286, \"div\", 8)(287, \"div\", 9)(288, \"p\", 29);\n        i0.ɵɵtext(289, \"7.Insured's Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(290, \"div\", 9)(291, \"div\", 46)(292, \"label\", 100);\n        i0.ɵɵtext(293, \"Address Line - 1 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(294, \"div\", 46)(295, \"label\", 101);\n        i0.ɵɵtext(296, \"Address Line - 2\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(297, \"div\", 46)(298, \"label\", 102);\n        i0.ɵɵtext(299, \"City\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(300, \"div\", 7)(301, \"div\", 46);\n        i0.ɵɵelement(302, \"input\", 103);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(303, \"div\", 46);\n        i0.ɵɵelement(304, \"input\", 104);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(305, \"div\", 46);\n        i0.ɵɵelement(306, \"input\", 105);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(307, \"div\", 9)(308, \"div\", 46)(309, \"label\", 106);\n        i0.ɵɵtext(310, \"State \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(311, \"div\", 46)(312, \"label\", 107);\n        i0.ɵɵtext(313, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(314, \"div\", 46)(315, \"label\", 108);\n        i0.ɵɵtext(316, \"Telephone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(317, \"div\", 7)(318, \"div\", 46)(319, \"input\", 109);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_319_listener($event) {\n          return ctx.SelectedInsuredState = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(320, \"div\", 46);\n        i0.ɵɵelement(321, \"input\", 110);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(322, \"div\", 46);\n        i0.ɵɵelement(323, \"input\", 111);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(324, \"div\", 7)(325, \"div\", 8)(326, \"section\", 112)(327, \"div\", 9)(328, \"div\", 46)(329, \"p\", 29);\n        i0.ɵɵtext(330, \"9. Other Insured's Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(331, \"div\", 9)(332, \"div\", 46)(333, \"label\", 113);\n        i0.ɵɵtext(334, \"Last Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(335, \"div\", 46)(336, \"label\", 114);\n        i0.ɵɵtext(337, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(338, \"div\", 46)(339, \"label\", 115);\n        i0.ɵɵtext(340, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(341, \"div\", 7)(342, \"div\", 46);\n        i0.ɵɵelement(343, \"input\", 116);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(344, \"div\", 46);\n        i0.ɵɵelement(345, \"input\", 117);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(346, \"div\", 46);\n        i0.ɵɵelement(347, \"input\", 118);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(348, \"div\", 9)(349, \"div\", 46)(350, \"label\", 119);\n        i0.ɵɵtext(351, \"A. Other Insured's Policy Or Group Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(352, \"input\", 120);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(353, \"div\", 9)(354, \"div\", 121)(355, \"label\", 122);\n        i0.ɵɵtext(356, \"B. Reserved For NUCC Use\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(357, \"div\", 123)(358, \"label\", 124);\n        i0.ɵɵtext(359, \"C. Reserved For NUCC Use\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(360, \"div\", 7)(361, \"div\", 121);\n        i0.ɵɵelement(362, \"input\", 125);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(363, \"div\", 123);\n        i0.ɵɵelement(364, \"input\", 126);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(365, \"div\", 8)(366, \"section\", 127)(367, \"div\", 9)(368, \"div\", 46)(369, \"p\", 29);\n        i0.ɵɵtext(370, \"10. Is Patient\\u2019s Condition Related To\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(371, \"div\", 9)(372, \"div\", 46)(373, \"label\", 128);\n        i0.ɵɵtext(374, \"10.A. Employment? (Current Or Previous)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(375, \"div\", 7)(376, \"div\", 88)(377, \"div\", 30);\n        i0.ɵɵelement(378, \"input\", 129);\n        i0.ɵɵelementStart(379, \"label\", 130);\n        i0.ɵɵtext(380, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(381, \"div\", 30);\n        i0.ɵɵelement(382, \"input\", 131);\n        i0.ɵɵelementStart(383, \"label\", 132);\n        i0.ɵɵtext(384, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(385, \"div\", 9)(386, \"div\", 46)(387, \"label\", 128);\n        i0.ɵɵtext(388, \"10.B. Auto Accident?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(389, \"div\", 7)(390, \"div\", 88)(391, \"div\", 30)(392, \"input\", 133);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_392_listener($event) {\n          return ctx.autoAccident = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(393, \"label\", 134);\n        i0.ɵɵtext(394, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(395, \"div\", 30)(396, \"input\", 135);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_396_listener($event) {\n          return ctx.autoAccident = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(397, \"label\", 136);\n        i0.ɵɵtext(398, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(399, \"div\", 9)(400, \"div\", 46)(401, \"label\", 128);\n        i0.ɵɵtext(402, \"10.C. Other Accidents?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(403, \"div\", 7)(404, \"div\", 88)(405, \"div\", 30);\n        i0.ɵɵelement(406, \"input\", 137);\n        i0.ɵɵelementStart(407, \"label\", 138);\n        i0.ɵɵtext(408, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(409, \"div\", 30);\n        i0.ɵɵelement(410, \"input\", 139);\n        i0.ɵɵelementStart(411, \"label\", 140);\n        i0.ɵɵtext(412, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(413, \"div\", 9)(414, \"div\", 46)(415, \"label\", 141);\n        i0.ɵɵtext(416, \"10.D Claims Codes(Designed By NUCC)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(417, \"div\", 9)(418, \"div\", 88);\n        i0.ɵɵelement(419, \"input\", 142);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(420, \"div\", 8)(421, \"div\", 9)(422, \"div\", 11)(423, \"p\", 29);\n        i0.ɵɵtext(424, \"11. Insured\\u2019s Policy Group Or FECA Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(425, \"input\", 143);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(426, \"div\", 9)(427, \"div\", 11)(428, \"label\", 144);\n        i0.ɵɵtext(429, \" A. Insured's Date Of Birth\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(430, \"div\", 11)(431, \"label\", 128);\n        i0.ɵɵtext(432, \" Sex\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(433, \"div\", 7)(434, \"div\", 11);\n        i0.ɵɵelement(435, \"input\", 145);\n        i0.ɵɵpipe(436, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(437, \"div\", 11)(438, \"div\", 61);\n        i0.ɵɵelement(439, \"input\", 146);\n        i0.ɵɵelementStart(440, \"label\", 147);\n        i0.ɵɵtext(441, \"Male\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(442, \"div\", 61);\n        i0.ɵɵelement(443, \"input\", 148);\n        i0.ɵɵelementStart(444, \"label\", 149);\n        i0.ɵɵtext(445, \"Female\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(446, \"div\", 61);\n        i0.ɵɵelement(447, \"input\", 150);\n        i0.ɵɵelementStart(448, \"label\", 151);\n        i0.ɵɵtext(449, \"Others\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(450, \"div\", 9)(451, \"div\", 11)(452, \"label\", 152);\n        i0.ɵɵtext(453, \" B.Other Claim ID (Designated By NUCC)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(454, \"input\", 153);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(455, \"div\", 9)(456, \"div\", 11)(457, \"label\", 154);\n        i0.ɵɵtext(458, \" C. Insurance Plan Name Or Program Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(459, \"input\", 155);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(460, \"div\", 9)(461, \"div\", 11)(462, \"label\", 128);\n        i0.ɵɵtext(463, \" D. Is There Another Health Benefit Plan\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(464, \"div\", 11);\n        i0.ɵɵelement(465, \"label\", 156);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(466, \"div\", 9)(467, \"div\", 157)(468, \"div\", 30);\n        i0.ɵɵelement(469, \"input\", 158);\n        i0.ɵɵelementStart(470, \"label\", 159);\n        i0.ɵɵtext(471, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(472, \"div\", 30);\n        i0.ɵɵelement(473, \"input\", 160);\n        i0.ɵɵelementStart(474, \"label\", 161);\n        i0.ɵɵtext(475, \"No\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(476, \"div\", 11);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(477, \"div\", 7)(478, \"div\", 8)(479, \"section\", 162)(480, \"div\", 9)(481, \"p\", 29);\n        i0.ɵɵtext(482, \"12. Patients Or Authorized Persons Signature\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(483, \"div\", 7)(484, \"div\", 88)(485, \"div\", 30);\n        i0.ɵɵelement(486, \"input\", 163);\n        i0.ɵɵelementStart(487, \"label\", 164);\n        i0.ɵɵtext(488, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(489, \"div\", 30);\n        i0.ɵɵelement(490, \"input\", 165);\n        i0.ɵɵelementStart(491, \"label\", 166);\n        i0.ɵɵtext(492, \"No\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(493, \"div\", 46)(494, \"label\", 167);\n        i0.ɵɵtext(495, \" Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(496, \"input\", 168);\n        i0.ɵɵpipe(497, \"date\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(498, \"div\", 8);\n        i0.ɵɵelementStart(499, \"div\", 8)(500, \"div\", 9)(501, \"p\", 29);\n        i0.ɵɵtext(502, \"13. Insured Or Authorized Persons Signature\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(503, \"div\", 7)(504, \"div\", 88)(505, \"div\", 30);\n        i0.ɵɵelement(506, \"input\", 169);\n        i0.ɵɵelementStart(507, \"label\", 170);\n        i0.ɵɵtext(508, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(509, \"div\", 30);\n        i0.ɵɵelement(510, \"input\", 171);\n        i0.ɵɵelementStart(511, \"label\", 172);\n        i0.ɵɵtext(512, \"No\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(513, \"div\", 7)(514, \"div\", 8)(515, \"section\", 173)(516, \"div\", 9)(517, \"p\", 29);\n        i0.ɵɵtext(518, \"14. Date Of Current Illness,Injury Or Pregnancy (LMP)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(519, \"div\", 7)(520, \"div\", 46)(521, \"label\", 174);\n        i0.ɵɵtext(522, \" Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(523, \"div\", 46)(524, \"label\", 175);\n        i0.ɵɵtext(525, \" Qualifier\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(526, \"div\", 7)(527, \"div\", 46);\n        i0.ɵɵelement(528, \"input\", 176);\n        i0.ɵɵpipe(529, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(530, \"div\", 46)(531, \"input\", 177);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_531_listener($event) {\n          return ctx.OnQualifierDataByTypeChange($event, \"1\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_531_listener($event) {\n          return ctx.Qual = $event;\n        });\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(532, \"div\", 8)(533, \"section\", 178)(534, \"div\", 9)(535, \"div\", 46)(536, \"p\", 29);\n        i0.ɵɵtext(537, \"15. Other Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(538, \"div\", 7)(539, \"div\", 46)(540, \"label\", 179);\n        i0.ɵɵtext(541, \"Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(542, \"input\", 180);\n        i0.ɵɵpipe(543, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(544, \"div\", 46)(545, \"label\", 181);\n        i0.ɵɵtext(546, \"Qualifier\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(547, \"input\", 182);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_547_listener($event) {\n          return ctx.OnQualifierDataByTypeChange($event, \"2\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_547_listener($event) {\n          return ctx.Qual2 = $event;\n        });\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(548, \"div\", 8)(549, \"div\", 9)(550, \"p\", 29);\n        i0.ɵɵtext(551, \"16. Patient Unable To Work In Current Occupation\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(552, \"div\", 7)(553, \"div\", 46)(554, \"label\", 183);\n        i0.ɵɵtext(555, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(556, \"input\", 184);\n        i0.ɵɵpipe(557, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(558, \"div\", 46)(559, \"label\", 185);\n        i0.ɵɵtext(560, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(561, \"input\", 186);\n        i0.ɵɵpipe(562, \"date\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(563, \"div\", 7)(564, \"div\", 8)(565, \"section\", 187)(566, \"div\", 9)(567, \"div\", 46)(568, \"p\", 29);\n        i0.ɵɵtext(569, \"17. Name Of The Referring Provider Or Other Source\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(570, \"div\", 9)(571, \"div\", 46)(572, \"label\", 188);\n        i0.ɵɵtext(573, \"Last Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(574, \"div\", 46)(575, \"label\", 189);\n        i0.ɵɵtext(576, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(577, \"div\", 46)(578, \"label\", 190);\n        i0.ɵɵtext(579, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(580, \"div\", 7)(581, \"div\", 46);\n        i0.ɵɵelement(582, \"input\", 191);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(583, \"div\", 46);\n        i0.ɵɵelement(584, \"input\", 192);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(585, \"div\", 46);\n        i0.ɵɵelement(586, \"input\", 193);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(587, \"div\", 8)(588, \"section\", 194)(589, \"div\", 9)(590, \"div\", 195)(591, \"p\", 29);\n        i0.ɵɵtext(592, \"17A.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(593, \"div\", 196)(594, \"label\", 128);\n        i0.ɵɵtext(595, \"Payer Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(596, \"div\", 196)(597, \"label\", 128);\n        i0.ɵɵtext(598, \"Payer Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(599, \"div\", 7);\n        i0.ɵɵelement(600, \"div\", 195);\n        i0.ɵɵelementStart(601, \"div\", 196)(602, \"select\", 197)(603, \"option\", 198);\n        i0.ɵɵtext(604, \"Select\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(605, \"option\", 199);\n        i0.ɵɵtext(606, \"431-onset\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(607, \"div\", 196);\n        i0.ɵɵelement(608, \"input\", 200);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(609, \"div\", 9)(610, \"div\", 195)(611, \"p\", 29);\n        i0.ɵɵtext(612, \"17B.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(613, \"div\", 196)(614, \"label\", 128);\n        i0.ɵɵtext(615, \"Payer Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(616, \"div\", 196)(617, \"label\", 128);\n        i0.ɵɵtext(618, \"Payer Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(619, \"div\", 7);\n        i0.ɵɵelement(620, \"div\", 195);\n        i0.ɵɵelementStart(621, \"div\", 196);\n        i0.ɵɵelement(622, \"input\", 201);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(623, \"div\", 196);\n        i0.ɵɵelement(624, \"input\", 201);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(625, \"div\", 8)(626, \"div\", 9)(627, \"p\", 29);\n        i0.ɵɵtext(628, \"18. Hospitalization Dates Related To Current Services\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(629, \"div\", 7)(630, \"div\", 46)(631, \"label\", 202);\n        i0.ɵɵtext(632, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(633, \"input\", 203);\n        i0.ɵɵpipe(634, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(635, \"div\", 46)(636, \"label\", 204);\n        i0.ɵɵtext(637, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(638, \"input\", 205);\n        i0.ɵɵpipe(639, \"date\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(640, \"div\", 7)(641, \"div\", 27)(642, \"section\", 206)(643, \"div\", 9)(644, \"div\", 46)(645, \"p\", 29);\n        i0.ɵɵtext(646, \"19. Additional Claim Information (Designated By NUCC)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(647, \"div\", 7)(648, \"div\", 46);\n        i0.ɵɵelement(649, \"input\", 207);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(650, \"div\", 45)(651, \"div\", 9)(652, \"p\", 29);\n        i0.ɵɵtext(653, \"20. Outside Lab\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(654, \"div\", 7)(655, \"div\", 88)(656, \"div\", 30);\n        i0.ɵɵelement(657, \"input\", 208);\n        i0.ɵɵelementStart(658, \"label\", 209);\n        i0.ɵɵtext(659, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(660, \"div\", 30);\n        i0.ɵɵelement(661, \"input\", 210);\n        i0.ɵɵelementStart(662, \"label\", 211);\n        i0.ɵɵtext(663, \"No\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(664, \"div\", 7)(665, \"div\", 27)(666, \"section\", 212)(667, \"div\", 9)(668, \"div\", 46)(669, \"p\", 29);\n        i0.ɵɵtext(670, \"21. Diagnosis Or Nature Of Illness Or Injury. (Relate A-L To Service Line Below (24E)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(671, \"div\", 7)(672, \"div\", 46)(673, \"label\", 128);\n        i0.ɵɵtext(674, \"A(1)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(675, \"div\", 46)(676, \"label\", 128);\n        i0.ɵɵtext(677, \"B(2)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(678, \"div\", 46)(679, \"label\", 128);\n        i0.ɵɵtext(680, \"C(3)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(681, \"div\", 46)(682, \"label\", 128);\n        i0.ɵɵtext(683, \"D(4)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(684, \"div\", 7)(685, \"div\", 46)(686, \"input\", 213);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_686_listener($event) {\n          return ctx.OnICDCodeChange($event, \"1\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_686_listener($event) {\n          return ctx.ICDInput1 = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(687, \"div\", 46)(688, \"input\", 214);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_688_listener($event) {\n          return ctx.OnICDCodeChange($event, \"2\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_688_listener($event) {\n          return ctx.ICDInput2 = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(689, \"div\", 46)(690, \"input\", 215);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_690_listener($event) {\n          return ctx.OnICDCodeChange($event, \"3\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_690_listener($event) {\n          return ctx.ICDInput3 = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(691, \"div\", 46)(692, \"input\", 216);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_692_listener($event) {\n          return ctx.OnICDCodeChange($event, \"4\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_692_listener($event) {\n          return ctx.ICDInput4 = $event;\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(693, \"div\", 9)(694, \"div\", 46)(695, \"label\", 128);\n        i0.ɵɵtext(696, \"E(5)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(697, \"div\", 46)(698, \"label\", 128);\n        i0.ɵɵtext(699, \"F(6)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(700, \"div\", 46)(701, \"label\", 128);\n        i0.ɵɵtext(702, \"G(7)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(703, \"div\", 46)(704, \"label\", 128);\n        i0.ɵɵtext(705, \"H(8)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(706, \"div\", 7)(707, \"div\", 46)(708, \"input\", 217);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_708_listener($event) {\n          return ctx.OnICDCodeChange($event, \"5\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_708_listener($event) {\n          return ctx.ICDInput5 = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(709, \"div\", 46)(710, \"input\", 218);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_710_listener($event) {\n          return ctx.OnICDCodeChange($event, \"6\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_710_listener($event) {\n          return ctx.ICDInput6 = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(711, \"div\", 46)(712, \"input\", 219);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_712_listener($event) {\n          return ctx.OnICDCodeChange($event, \"7\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_712_listener($event) {\n          return ctx.ICDInput7 = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(713, \"div\", 46)(714, \"input\", 220);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_714_listener($event) {\n          return ctx.OnICDCodeChange($event, \"8\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_714_listener($event) {\n          return ctx.ICDInput8 = $event;\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(715, \"div\", 9)(716, \"div\", 46)(717, \"label\", 128);\n        i0.ɵɵtext(718, \"I(9)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(719, \"div\", 46)(720, \"label\", 128);\n        i0.ɵɵtext(721, \"J(10)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(722, \"div\", 46)(723, \"label\", 128);\n        i0.ɵɵtext(724, \"K(11)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(725, \"div\", 46)(726, \"label\", 128);\n        i0.ɵɵtext(727, \"L(12)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(728, \"div\", 7)(729, \"div\", 46)(730, \"input\", 221);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_730_listener($event) {\n          return ctx.OnICDCodeChange($event, \"9\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_730_listener($event) {\n          return ctx.ICDInput9 = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(731, \"div\", 46)(732, \"input\", 222);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_732_listener($event) {\n          return ctx.OnICDCodeChange($event, \"10\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_732_listener($event) {\n          return ctx.ICDInput10 = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(733, \"div\", 46)(734, \"input\", 223);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_734_listener($event) {\n          return ctx.OnICDCodeChange($event, \"11\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_734_listener($event) {\n          return ctx.ICDInput11 = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(735, \"div\", 46)(736, \"input\", 224);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_736_listener($event) {\n          return ctx.OnICDCodeChange($event, \"12\");\n        })(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_736_listener($event) {\n          return ctx.ICDInput12 = $event;\n        });\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(737, \"section\", 212)(738, \"div\", 9)(739, \"div\", 11)(740, \"label\", 128);\n        i0.ɵɵtext(741, \"Place of service 2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(742, \"select\", 225)(743, \"option\", 198);\n        i0.ɵɵtext(744, \"Select\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(745, \"div\", 9)(746, \"div\", 11)(747, \"label\", 128);\n        i0.ɵɵtext(748, \"EMG 2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(749, \"select\", 225)(750, \"option\", 198);\n        i0.ɵɵtext(751, \"Select\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(752, \"div\", 9)(753, \"div\", 11)(754, \"label\", 128);\n        i0.ɵɵtext(755, \"EPSDT 2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(756, \"select\", 225)(757, \"option\", 198);\n        i0.ɵɵtext(758, \"Select\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(759, \"div\", 45)(760, \"div\", 9)(761, \"p\", 29);\n        i0.ɵɵtext(762, \"22. Resubmission Code\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(763, \"div\", 7)(764, \"div\", 11)(765, \"label\", 128);\n        i0.ɵɵtext(766, \"Resubmission Code\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(767, \"div\", 11)(768, \"label\", 128);\n        i0.ɵɵtext(769, \"Reference Number\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(770, \"div\", 9)(771, \"div\", 11)(772, \"input\", 226);\n        i0.ɵɵlistener(\"ngModelChange\", function NewClaimComponent_Template_input_ngModelChange_772_listener($event) {\n          return ctx.OnResubmissionCodeChange($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(773, \"div\", 11);\n        i0.ɵɵelement(774, \"input\", 227);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(775, \"div\", 9)(776, \"p\", 29);\n        i0.ɵɵtext(777, \"23. Prior Authorization Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(778, \"div\", 9)(779, \"div\", 11)(780, \"label\", 128);\n        i0.ɵɵtext(781, \"Prior Authorization Number\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(782, \"div\", 7)(783, \"div\", 11);\n        i0.ɵɵelement(784, \"input\", 228);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(785, \"div\", 9)(786, \"div\", 11)(787, \"label\", 128);\n        i0.ɵɵtext(788, \"Ambulance Pickup Address 1\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(789, \"div\", 11)(790, \"label\", 128);\n        i0.ɵɵtext(791, \"Ambulance Pickup Address 2\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(792, \"div\", 7)(793, \"div\", 11);\n        i0.ɵɵelement(794, \"input\", 229);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(795, \"div\", 11);\n        i0.ɵɵelement(796, \"input\", 230);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(797, \"div\", 9)(798, \"div\", 11)(799, \"label\", 128);\n        i0.ɵɵtext(800, \"Ambulance Pickup City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(801, \"div\", 11)(802, \"label\", 128);\n        i0.ɵɵtext(803, \"Ambulance Pickup State\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(804, \"div\", 7)(805, \"div\", 11);\n        i0.ɵɵelement(806, \"input\", 231);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(807, \"div\", 11);\n        i0.ɵɵelement(808, \"input\", 232);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(809, \"div\", 9)(810, \"div\", 11)(811, \"label\", 128);\n        i0.ɵɵtext(812, \"Ambulance Pickup Zip Code\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(813, \"div\", 7)(814, \"div\", 11);\n        i0.ɵɵelement(815, \"input\", 233);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(816, \"div\", 162)(817, \"div\", 7)(818, \"div\", 8)(819, \"div\", 7)(820, \"p\", 29);\n        i0.ɵɵtext(821, \"24. Service Line\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(822, \"div\", 9)(823, \"div\", 11)(824, \"section\", 234)(825, \"div\", 235)(826, \"table\", 236)(827, \"thead\")(828, \"tr\")(829, \"th\", 237);\n        i0.ɵɵtext(830, \"D.O.S\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(831, \"th\", 238);\n        i0.ɵɵtext(832, \"P.O.S\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(833, \"th\", 238);\n        i0.ɵɵtext(834, \"EMG\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(835, \"th\", 237);\n        i0.ɵɵtext(836, \"Procedures\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(837, \"th\", 238);\n        i0.ɵɵtext(838, \"Diagnosis Pointer\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(839, \"th\", 238);\n        i0.ɵɵtext(840, \"Unit Charge\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(841, \"th\", 238);\n        i0.ɵɵtext(842, \"Unit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(843, \"th\", 238);\n        i0.ɵɵtext(844, \"EPSDT\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(845, \"th\", 238);\n        i0.ɵɵtext(846, \"Rendering Provider ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(847, \"tr\")(848, \"th\", 238);\n        i0.ɵɵtext(849, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(850, \"th\", 238);\n        i0.ɵɵtext(851, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(852, \"th\", 239)(853, \"th\", 239);\n        i0.ɵɵelementStart(854, \"th\", 238);\n        i0.ɵɵtext(855, \"CPT/HCPCS\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(856, \"th\", 238);\n        i0.ɵɵtext(857, \"Modifier\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(858, \"th\", 239)(859, \"th\", 239)(860, \"th\", 239)(861, \"th\", 239)(862, \"th\", 239);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(863, \"tbody\", 240);\n        i0.ɵɵtemplate(864, NewClaimComponent_tr_864_Template, 76, 42, \"tr\", 241);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(865, \"div\", 242)(866, \"div\")(867, \"button\", 243);\n        i0.ɵɵtext(868, \"Delete\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(869, \"div\")(870, \"button\", 244);\n        i0.ɵɵlistener(\"click\", function NewClaimComponent_Template_button_click_870_listener() {\n          return ctx.addCPTInputsItem();\n        });\n        i0.ɵɵtext(871, \"Add\");\n        i0.ɵɵelementEnd()()()()()()()()()();\n        i0.ɵɵelementStart(872, \"div\", 7)(873, \"div\", 8)(874, \"div\", 9)(875, \"div\", 11)(876, \"p\", 29);\n        i0.ɵɵtext(877, \"25. Federal Tax Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(878, \"div\", 157)(879, \"div\", 30);\n        i0.ɵɵelement(880, \"input\", 245);\n        i0.ɵɵelementStart(881, \"label\", 246);\n        i0.ɵɵtext(882, \"SSN\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(883, \"div\", 30);\n        i0.ɵɵelement(884, \"input\", 247);\n        i0.ɵɵelementStart(885, \"label\", 181);\n        i0.ɵɵtext(886, \"EIN\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(887, \"div\", 7)(888, \"div\", 11);\n        i0.ɵɵelement(889, \"input\", 248);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(890, \"div\", 8)(891, \"div\", 9)(892, \"div\", 11)(893, \"p\", 29);\n        i0.ɵɵtext(894, \"26. Patient Account Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(895, \"div\", 11)(896, \"p\", 29);\n        i0.ɵɵtext(897, \"27. Acceptance Assignment?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(898, \"div\", 7)(899, \"div\", 11);\n        i0.ɵɵelement(900, \"input\", 249);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(901, \"div\", 11)(902, \"div\", 30);\n        i0.ɵɵelement(903, \"input\", 245);\n        i0.ɵɵelementStart(904, \"label\", 246);\n        i0.ɵɵtext(905, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(906, \"div\", 30);\n        i0.ɵɵelement(907, \"input\", 247);\n        i0.ɵɵelementStart(908, \"label\", 181);\n        i0.ɵɵtext(909, \"No\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(910, \"div\", 8)(911, \"div\", 9)(912, \"div\", 11)(913, \"p\", 29);\n        i0.ɵɵtext(914, \"28. Total Charge($)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(915, \"div\", 11)(916, \"p\", 29);\n        i0.ɵɵtext(917, \"29. Amount Paid\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(918, \"div\", 11)(919, \"p\", 29);\n        i0.ɵɵtext(920, \"30. Reserved For NUCC\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(921, \"div\", 7)(922, \"div\", 11);\n        i0.ɵɵelement(923, \"input\", 250);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(924, \"div\", 11);\n        i0.ɵɵelement(925, \"input\", 251);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(926, \"div\", 11);\n        i0.ɵɵelement(927, \"input\", 252);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(928, \"div\", 7)(929, \"div\", 8)(930, \"section\", 253)(931, \"div\", 9)(932, \"div\", 46);\n        i0.ɵɵelement(933, \"p\", 29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(934, \"div\", 88)(935, \"div\", 30);\n        i0.ɵɵelement(936, \"input\", 245);\n        i0.ɵɵelementStart(937, \"label\", 246);\n        i0.ɵɵtext(938, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(939, \"div\", 30);\n        i0.ɵɵelement(940, \"input\", 245);\n        i0.ɵɵelementStart(941, \"label\", 246);\n        i0.ɵɵtext(942, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(943, \"div\", 9)(944, \"div\", 46)(945, \"label\", 128);\n        i0.ɵɵtext(946, \"Provider Signature Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(947, \"div\", 46);\n        i0.ɵɵelement(948, \"input\", 254);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(949, \"div\", 9)(950, \"div\", 46)(951, \"label\", 128);\n        i0.ɵɵtext(952, \"Date Of Initial Treatment\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(953, \"div\", 46);\n        i0.ɵɵelement(954, \"input\", 254);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(955, \"div\", 9)(956, \"div\", 46)(957, \"label\", 128);\n        i0.ɵɵtext(958, \"Latest Visit OrConsultation Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(959, \"div\", 46);\n        i0.ɵɵelement(960, \"input\", 254);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(961, \"div\", 9)(962, \"div\", 46)(963, \"label\", 128);\n        i0.ɵɵtext(964, \"Supervising Physician\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(965, \"div\", 46);\n        i0.ɵɵelement(966, \"input\", 255);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(967, \"div\", 256)(968, \"div\", 46);\n        i0.ɵɵelement(969, \"label\", 128);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(970, \"div\", 46);\n        i0.ɵɵelement(971, \"input\", 257);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(972, \"div\", 256)(973, \"div\", 46);\n        i0.ɵɵelement(974, \"label\", 128);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(975, \"div\", 46);\n        i0.ɵɵelement(976, \"input\", 258);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(977, \"div\", 256)(978, \"div\", 46)(979, \"label\", 128);\n        i0.ɵɵtext(980, \"Supervising Physician NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(981, \"div\", 46);\n        i0.ɵɵelement(982, \"input\", 259);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(983, \"div\", 256)(984, \"div\", 46)(985, \"label\", 128);\n        i0.ɵɵtext(986, \"Ordering Physician\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(987, \"div\", 46);\n        i0.ɵɵelement(988, \"input\", 255);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(989, \"div\", 256)(990, \"div\", 46);\n        i0.ɵɵelement(991, \"label\", 128);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(992, \"div\", 46);\n        i0.ɵɵelement(993, \"input\", 257);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(994, \"div\", 256)(995, \"div\", 46);\n        i0.ɵɵelement(996, \"label\", 128);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(997, \"div\", 46);\n        i0.ɵɵelement(998, \"input\", 258);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(999, \"div\", 256)(1000, \"div\", 46)(1001, \"label\", 128);\n        i0.ɵɵtext(1002, \"Ordering Physician NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1003, \"div\", 46);\n        i0.ɵɵelement(1004, \"input\", 258);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1005, \"div\", 256)(1006, \"div\", 46)(1007, \"label\", 128);\n        i0.ɵɵtext(1008, \"Ordering Physician ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1009, \"div\", 46);\n        i0.ɵɵelement(1010, \"input\", 259);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1011, \"div\", 256)(1012, \"div\", 46)(1013, \"label\", 128);\n        i0.ɵɵtext(1014, \"Accident Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1015, \"div\", 46);\n        i0.ɵɵelement(1016, \"input\", 260);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1017, \"div\", 256)(1018, \"div\", 46)(1019, \"label\", 128);\n        i0.ɵɵtext(1020, \"CLIA\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1021, \"div\", 46);\n        i0.ɵɵelement(1022, \"input\", 261);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1023, \"div\", 256)(1024, \"div\", 46)(1025, \"label\", 128);\n        i0.ɵɵtext(1026, \"Mammography Certificate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1027, \"div\", 46);\n        i0.ɵɵelement(1028, \"input\", 262);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1029, \"div\", 8)(1030, \"section\", 263)(1031, \"div\", 9)(1032, \"div\", 46)(1033, \"p\", 29);\n        i0.ɵɵtext(1034, \" 32. Service Facility Location And Information\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1035, \"div\", 256)(1036, \"div\", 46)(1037, \"label\", 128);\n        i0.ɵɵtext(1038, \"Facility Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1039, \"div\", 46);\n        i0.ɵɵelement(1040, \"input\", 264);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1041, \"div\", 256)(1042, \"div\", 46)(1043, \"label\", 128);\n        i0.ɵɵtext(1044, \"Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1045, \"div\", 46);\n        i0.ɵɵelement(1046, \"input\", 265);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1047, \"div\", 256)(1048, \"div\", 46);\n        i0.ɵɵelement(1049, \"label\", 128);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1050, \"div\", 46);\n        i0.ɵɵelement(1051, \"input\", 266);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1052, \"div\", 256)(1053, \"div\", 46)(1054, \"label\", 128);\n        i0.ɵɵtext(1055, \"City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1056, \"div\", 46);\n        i0.ɵɵelement(1057, \"input\", 267);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1058, \"div\", 256)(1059, \"div\", 46)(1060, \"label\", 128);\n        i0.ɵɵtext(1061, \"State\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1062, \"div\", 46);\n        i0.ɵɵelement(1063, \"input\", 268);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1064, \"div\", 256)(1065, \"div\", 46)(1066, \"label\", 128);\n        i0.ɵɵtext(1067, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1068, \"div\", 46);\n        i0.ɵɵelement(1069, \"input\", 269);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1070, \"div\", 256)(1071, \"div\", 46)(1072, \"label\", 128);\n        i0.ɵɵtext(1073, \"a. NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1074, \"div\", 46);\n        i0.ɵɵelement(1075, \"input\", 270);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1076, \"div\", 256)(1077, \"div\", 46)(1078, \"label\", 128);\n        i0.ɵɵtext(1079, \"b. Facility ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1080, \"div\", 46);\n        i0.ɵɵelement(1081, \"input\", 271);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1082, \"div\", 256)(1083, \"div\", 46)(1084, \"label\", 128);\n        i0.ɵɵtext(1085, \"Ambulance Drop off Address 1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(1086, \"input\", 257);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1087, \"div\", 256)(1088, \"div\", 46)(1089, \"label\", 128);\n        i0.ɵɵtext(1090, \" Ambulance Drop off Address 1\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1091, \"div\", 46)(1092, \"label\", 128);\n        i0.ɵɵtext(1093, \"Ambulance Drop off Address 2\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1094, \"div\", 256)(1095, \"div\", 46);\n        i0.ɵɵelement(1096, \"input\", 201);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1097, \"div\", 46);\n        i0.ɵɵelement(1098, \"input\", 201);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1099, \"div\", 256)(1100, \"div\", 46)(1101, \"label\", 128);\n        i0.ɵɵtext(1102, \"Ambulance Drop off City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1103, \"div\", 46)(1104, \"label\", 128);\n        i0.ɵɵtext(1105, \"Ambulance Drop off State\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1106, \"div\", 256)(1107, \"div\", 46);\n        i0.ɵɵelement(1108, \"input\", 201);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1109, \"div\", 46);\n        i0.ɵɵelement(1110, \"input\", 201);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1111, \"div\", 256)(1112, \"div\", 46)(1113, \"label\", 128);\n        i0.ɵɵtext(1114, \"Ambulance Drop off Zip Code\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(1115, \"input\", 201);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1116, \"div\", 8)(1117, \"section\", 272)(1118, \"div\", 9)(1119, \"div\", 46)(1120, \"p\", 29);\n        i0.ɵɵtext(1121, \" 33. Billing Provider Info And Phone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1122, \"div\", 256)(1123, \"div\", 46)(1124, \"label\", 128);\n        i0.ɵɵtext(1125, \" Billing Provider\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1126, \"div\", 273)(1127, \"input\", 274);\n        i0.ɵɵlistener(\"blur\", function NewClaimComponent_Template_input_blur_1127_listener() {\n          return ctx.SetBillingProviderThroughName();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1128, \"mat-icon\", 275);\n        i0.ɵɵlistener(\"click\", function NewClaimComponent_Template_mat_icon_click_1128_listener() {\n          return ctx.OnBillingProvider();\n        });\n        i0.ɵɵtext(1129, \"search\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1130, \"div\", 256)(1131, \"div\", 46);\n        i0.ɵɵelement(1132, \"label\", 128);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1133, \"div\", 46);\n        i0.ɵɵelement(1134, \"input\", 276);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1135, \"div\", 256)(1136, \"div\", 46);\n        i0.ɵɵelement(1137, \"label\", 128);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1138, \"div\", 46);\n        i0.ɵɵelement(1139, \"input\", 277);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1140, \"div\", 256)(1141, \"div\", 46)(1142, \"label\", 128);\n        i0.ɵɵtext(1143, \"Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1144, \"div\", 46);\n        i0.ɵɵelement(1145, \"input\", 278);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1146, \"div\", 256)(1147, \"div\", 46);\n        i0.ɵɵelement(1148, \"label\", 128);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1149, \"div\", 46);\n        i0.ɵɵelement(1150, \"input\", 279);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1151, \"div\", 256)(1152, \"div\", 46)(1153, \"label\", 128);\n        i0.ɵɵtext(1154, \"City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1155, \"div\", 46);\n        i0.ɵɵelement(1156, \"input\", 280);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1157, \"div\", 256)(1158, \"div\", 46)(1159, \"label\", 128);\n        i0.ɵɵtext(1160, \" State\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1161, \"div\", 46);\n        i0.ɵɵelement(1162, \"input\", 281);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1163, \"div\", 256)(1164, \"div\", 46)(1165, \"label\", 128);\n        i0.ɵɵtext(1166, \"Zip Code\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1167, \"div\", 46);\n        i0.ɵɵelement(1168, \"input\", 282);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1169, \"div\", 256)(1170, \"div\", 46)(1171, \"label\", 128);\n        i0.ɵɵtext(1172, \"Telephone\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1173, \"div\", 46);\n        i0.ɵɵelement(1174, \"input\", 283);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1175, \"div\", 256)(1176, \"div\", 46)(1177, \"label\", 128);\n        i0.ɵɵtext(1178, \"Specialty/Taxonomy\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1179, \"div\", 46);\n        i0.ɵɵelement(1180, \"input\", 284);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1181, \"div\", 256)(1182, \"div\", 46)(1183, \"label\", 128);\n        i0.ɵɵtext(1184, \" Rendering Provider (Last,First,MI)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1185, \"div\", 273);\n        i0.ɵɵelement(1186, \"input\", 285);\n        i0.ɵɵelementStart(1187, \"mat-icon\", 286);\n        i0.ɵɵlistener(\"click\", function NewClaimComponent_Template_mat_icon_click_1187_listener() {\n          return ctx.OnRenderingProviderClick();\n        });\n        i0.ɵɵtext(1188, \"search\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1189, \"div\", 256)(1190, \"div\", 46);\n        i0.ɵɵelement(1191, \"label\", 128);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1192, \"div\", 46);\n        i0.ɵɵelement(1193, \"input\", 287);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1194, \"div\", 256)(1195, \"div\", 46);\n        i0.ɵɵelement(1196, \"label\", 128);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1197, \"div\", 46);\n        i0.ɵɵelement(1198, \"input\", 288);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1199, \"div\", 256)(1200, \"div\", 46)(1201, \"label\", 128);\n        i0.ɵɵtext(1202, \"Provider Specialty\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1203, \"div\", 46);\n        i0.ɵɵelement(1204, \"input\", 289);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1205, \"div\", 256)(1206, \"div\", 46)(1207, \"label\", 128);\n        i0.ɵɵtext(1208, \" Provider NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1209, \"div\", 46);\n        i0.ɵɵelement(1210, \"input\", 290);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1211, \"div\", 256)(1212, \"div\", 46)(1213, \"label\", 128);\n        i0.ɵɵtext(1214, \" Provider PIN\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1215, \"div\", 46);\n        i0.ɵɵelement(1216, \"input\", 291);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1217, \"div\", 256)(1218, \"div\", 46)(1219, \"label\", 128);\n        i0.ɵɵtext(1220, \" A. Billing/Group NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1221, \"div\", 46);\n        i0.ɵɵelement(1222, \"input\", 292);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1223, \"div\", 256)(1224, \"div\", 46)(1225, \"label\", 128);\n        i0.ɵɵtext(1226, \" Provider ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1227, \"div\", 46);\n        i0.ɵɵelement(1228, \"input\", 293);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1229, \"div\", 256)(1230, \"div\", 46)(1231, \"label\", 128);\n        i0.ɵɵtext(1232, \" Id Qual\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1233, \"div\", 46);\n        i0.ɵɵelement(1234, \"input\", 294);\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"formGroup\", ctx.ClaimForm);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" Current Claim Status: \", i0.ɵɵpipeBind1(14, 92, ctx.Status), \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" Created on: \", i0.ɵɵpipeBind2(18, 94, ctx.FormCreatedDate, \"MM/dd/yyyy HH:mm:ss\"), \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" Created By: \", i0.ɵɵpipeBind1(22, 97, ctx.ClaimCreatedBy), \" \");\n        i0.ɵɵadvance(27);\n        i0.ɵɵclassProp(\"fieldError\", ctx.isSubmitted && ctx.f.PayerID.errors);\n        i0.ɵɵadvance(25);\n        i0.ɵɵclassProp(\"fieldError\", ctx.isSubmitted && ctx.f.PayerState.errors);\n        i0.ɵɵadvance(60);\n        i0.ɵɵclassProp(\"fieldError\", ctx.isSubmitted && ctx.f.InsuredSIDNumber.errors);\n        i0.ɵɵadvance(22);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(16);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(176, 99, ctx.TodaysDate, \"yyyy-MM-dd\"));\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(19);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(19);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngModel\", ctx.SelectedPatientState);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isPatientEnabled ? \"\" : null);\n        i0.ɵɵadvance(52);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(13);\n        i0.ɵɵclassProp(\"fieldError\", ctx.isSubmitted && ctx.f.InsuredState.errors);\n        i0.ɵɵproperty(\"ngModel\", ctx.SelectedInsuredState);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(69);\n        i0.ɵɵproperty(\"ngModel\", ctx.autoAccident);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.autoAccident);\n        i0.ɵɵadvance(29);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(10);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(436, 102, ctx.TodaysDate, \"yyyy-MM-dd\"));\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(7);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(5);\n        i0.ɵɵattribute(\"disabled\", ctx.isInsuredEnabled ? \"\" : null);\n        i0.ɵɵadvance(37);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(497, 105, ctx.TodaysDate, \"yyyy-MM-dd\"));\n        i0.ɵɵadvance(32);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(529, 108, ctx.TodaysDate, \"yyyy-MM-dd\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"fieldError\", ctx.isSubmitted && ctx.f.Qual.errors);\n        i0.ɵɵproperty(\"ngModel\", ctx.Qual);\n        i0.ɵɵadvance(11);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(543, 111, ctx.TodaysDate, \"yyyy-MM-dd\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.Qual2);\n        i0.ɵɵadvance(9);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(557, 114, ctx.TodaysDate, \"yyyy-MM-dd\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(562, 117, ctx.TodaysDate, \"yyyy-MM-dd\"));\n        i0.ɵɵadvance(72);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(634, 120, ctx.TodaysDate, \"yyyy-MM-dd\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(639, 123, ctx.TodaysDate, \"yyyy-MM-dd\"));\n        i0.ɵɵadvance(48);\n        i0.ɵɵclassProp(\"fieldError\", ctx.isSubmitted && ctx.f.ICDInput1.errors);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput1);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput1Enabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput2);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput2Enabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput3);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput3Enabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput4);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput4Enabled ? \"\" : null);\n        i0.ɵɵadvance(16);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput5);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput5Enabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput6);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput6Enabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput7);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput7Enabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput8);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput8Enabled ? \"\" : null);\n        i0.ɵɵadvance(16);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput9);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput9Enabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput10);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput10Enabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput11);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput11Enabled ? \"\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.ICDInput12);\n        i0.ɵɵattribute(\"disabled\", ctx.isICDInput12Enabled ? \"\" : null);\n        i0.ɵɵadvance(36);\n        i0.ɵɵclassProp(\"fieldError\", ctx.isSubmitted && ctx.f.ResubmissionCode.errors);\n        i0.ɵɵattribute(\"disabled\", ctx.isResubmissionEnabled ? \"\" : null);\n        i0.ɵɵadvance(92);\n        i0.ɵɵproperty(\"ngForOf\", ctx.CPTInputs.controls);\n        i0.ɵɵadvance(25);\n        i0.ɵɵclassProp(\"fieldError\", ctx.isSubmitted && ctx.f.FederalTaxNumber.errors);\n        i0.ɵɵadvance(238);\n        i0.ɵɵclassProp(\"fieldError\", ctx.isSubmitted && ctx.f.BillingProvider.errors);\n      }\n    },\n    dependencies: [i21.NgForOf, i23.ɵNgNoValidate, i23.NgSelectOption, i23.ɵNgSelectMultipleOption, i23.DefaultValueAccessor, i23.SelectControlValueAccessor, i23.RadioControlValueAccessor, i23.NgControlStatus, i23.NgControlStatusGroup, i23.RequiredValidator, i23.FormGroupDirective, i23.FormControlName, i23.FormGroupName, i23.FormArrayName, i24.MatIcon, i25.MatInput, i26.MatTooltip, i21.UpperCasePipe, i21.DatePipe],\n    styles: [\".create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.create-claim-form-styles[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.create-claim-form-styles[_ngcontent-%COMP%]   .form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.create-claim-form-styles[_ngcontent-%COMP%]   .search-icon-alignment[_ngcontent-%COMP%]{position:relative}.create-claim-form-styles[_ngcontent-%COMP%]   .search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.create-claim-form-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.create-claim-form-styles[_ngcontent-%COMP%]   .table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.create-claim-form-styles[_ngcontent-%COMP%]   .radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.create-claim-form-styles[_ngcontent-%COMP%]   .form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}\"],\n    changeDetection: 0\n  });\n  return NewClaimComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}