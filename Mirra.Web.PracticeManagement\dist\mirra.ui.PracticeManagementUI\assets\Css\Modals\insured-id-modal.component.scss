.Insured-Modal-Heading {
    font-weight: bold;
    font-size: 22px;
    margin-left: -10px;
}

.Grey-Color {
    color: #646464;
}

input {
    outline: none;
    border: 1px solid #D9DADE;
    font-family: 'Poppins';
    font-size: 12px;
    width: 132px;
    border-radius: 8px;
    height: 34px;
}

select {
    padding: 0 10px;
    font-size: 12px;
    font-family: 'Poppins';
    height: 34px;
    outline: none;
    border: 1px solid #D9DADE;
    width: 132px;
    border-radius: 8px;
}

tr.mat-header-row {
    border-radius: 8px 8px 0px 0px;
    top: 0px;
    position: sticky;
    z-index: 100;
}

th {
    color: #272D3B!important;
    font-family: 'Poppins-Bold';
    background-color: #EDEDED!important;
    opacity: 1;
    border: 1px solid #EDEDEE!important;
}

td {
    font-family: 'Poppins';
    color: #272D3B;
    font-size: 12px;
    letter-spacing: 0px;
    opacity: 1;
    text-overflow: ellipsis;
    line-height: 33px;
    padding: 11px 0 0 13px!important;
}

::ng-deep .custom-frame {
    & .mat-checkbox-background,
    .mat-checkbox-frame {
        border-radius: 70% !important;
    }
}

th.mat-sort-header-sorted {
    color: black;
}

.mat-row:hover .mat-cell {
    background-color: #EFF9FF;
}

// .mat-checkbox{
//   display: none;
// }
.mat-checkbox-checked {
    display: inline;
}

.Table-Width-Styling {
    width: fit-content;
}

// th.mat-header-cell:first-of-type{
//   padding-left: 24px;
// }
// td.mat-cell:first-of-type{
//   padding-left: 30px!important;
// }
mat-table {
    overflow-y: auto;
    height: 48vh;
    width: 98%!important;
    box-shadow: none;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #DEDFE1;
    border-radius: 8px;
    opacity: 1;
}

.Input-Label {
    color: #646464;
    font-family: 'Poppins';
}

.btn {
    width: 80px;
    height: 34px;
    font-family: 'Poppins-SemiBold';
    box-shadow: 0px 0px 8px #0000001A;
    border-radius: 4px;
    opacity: 1;
}

.btn-outline-custom {
    color: #0074BC;
    // background-color: #0074BC;
    border-color: #0074BC;
}

.NoDataBox {
    text-align: center;
    height: 17vw;
    width: 98% !important;
    box-shadow: none;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #DEDFE1;
    border-radius: 8px;
    opacity: 1;
    background-color: #f7f7f7
}

.FirstHeadingNoData {
    color: #646464;
    font-size: 32px;
    margin-top: 7%;
}

.SecondHeadingNoData {
    color: #646464;
    margin-top: 2%;
}

.btn-outline-custom:active {
    color: #fff;
    background-color: #0074BC;
    border-color: #0074BC;
}

.Right-Align {
    text-align: end;
    justify-content: flex-end;
    margin-right: 10px;
}

.SpaceBtwnBtns {
    background-color: #0074BC;
    color: white;
    margin-right: 15px;
}

.Close-Icon {
    margin-top: -10px;
    margin-left: 10px;
    padding-right: 0px;
    text-align: right;
}

.col-2 {
    flex: 0 0 auto;
    width: 146px;
}

.Total-Records {
    padding-top: 17px;
    text-align: end;
    color: #0074BC;
    font-family: 'Poppins';
    font-size: 15px;
    opacity: 1;
}

th {
    padding-left: 10px!important;
    padding-top: 11px!important;
}


.Modal-Close-Image {
    cursor: pointer;
    width: 26px;
}

.CheckboxHeader {
    margin-left: 10px;
}

::ng-deep .mat-sort-header-container:not(.mat-sort-header-sorted) .mat-sort-header-arrow {
    opacity: 0.54 !important;
    transform: translateY(0px) !important;
}

.OverflowYInitial {
    overflow-y: initial!important;
}

.WidthOfCheckBox {
    width: 8%;
}

.DOCDateRangePicker {
    width: 150px;
    height: 28px;
}

.DOCDateRangePicker .e-input-group {
    margin-top: -10px;
}

.DOCInputBox {
    width: 150px;
    height: 28px;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #D5D7E3;
    border-radius: 8px;
    opacity: 1;
}

:ng-deep .e-calendar,
.e-bigger.e-small .e-calendar {
    overflow: hidden!important;
}

:ng-deep .e-calendar {
    overflow: hidden!important;
}

.DOCDateRangePicker1 .e-input-group {
    margin-top: 0;
}

.DOCInputBox1 {
    width: 135px;
    height: 33px;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #D5D7E3;
    border-radius: 8px;
    opacity: 1;
}

input[type="radio"] {
    color: white !important;
    border-radius: 50%;
    appearance: none;
    border: 1px solid #d3d3d3;
    width: 22px;
    height: 22px;
    content: none;
    outline: none;
    margin: 0;
    padding: 0px!important;
    margin-right: 2px;
    box-shadow: none;
}

input[type="radio"]:checked {
    width: 22px;
    height: 22px;
    appearance: none;
    outline: none;
    padding: 0;
    content: none;
    border: none;
    box-shadow: none;
}


/* some more property of checked radio button */

input[type="radio"]:checked::before {
    // position: absolute;
    background-color: #0074BC!important;
    color: white !important;
    content: "\00A0\2713\00A0" !important;
    border: 1px solid #d3d3d3;
    font-weight: bold;
    font-size: 16px;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    box-shadow: none;
    border: none;
    padding: 0px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}


:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-down:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-up-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}

::ng-deep .p-datatable-scrollable-body
{
    min-height: 200px;
}

.Width10{
    width:10%;
}
.Width15{
    width:15%;
}
.Width25{
    width:25%;
}
.Width17{
    width:17%;
}
.Width23{
    width:23%;
}

.Width2{
    width: 2%;
}

.Width5{
    width: 5%;
}

/* styling for daterangepicker */

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day {
    background-color: #0074BC!important;
    color: white!important;
}

::ng-deep .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected.e-focused-date span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected.e-focused-date span.e-day {
    background-color: #0074BC!important;
    color: white!important;
}

::ng-deep .e-calendar .e-content td.e-today.e-selected span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-today.e-selected span.e-day {
    background-color: #0074BC!important;
    border: 1px solid #0074BC!important;
    box-shadow: inset 0 0 0 2px #fff!important;
    color: #fff!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-today span.e-day {
    background-color: #0074BC!important;
    color: white!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-today:hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-focused-date.e-today span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-start-date.e-selected.e-today span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-end-date.e-selected.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-today:hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-focused-date.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-start-date.e-selected.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-end-date.e-selected.e-today span.e-day {
    border: 1px solid #0074BC!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-content.e-month .e-today.e-range-hover span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content.e-month .e-today.e-range-hover span {
    background-color: #eee!important;
    border: 1px solid #0074BC!important;
    color: #0074BC!important;
}

::ng-deep .e-calendar .e-content td.e-today span.e-day,
.e-calendar[_ngcontent-kbk-c96] .e-content[_ngcontent-kbk-c96] td.e-focused-date.e-today[_ngcontent-kbk-c96] span.e-day[_ngcontent-kbk-c96],
.e-bigger.e-small[_ngcontent-kbk-c96] .e-calendar[_ngcontent-kbk-c96] .e-content[_ngcontent-kbk-c96] td.e-today[_ngcontent-kbk-c96] span.e-day[_ngcontent-kbk-c96],
.e-bigger.e-small[_ngcontent-kbk-c96] .e-calendar[_ngcontent-kbk-c96] .e-content[_ngcontent-kbk-c96] td.e-focused-date.e-today[_ngcontent-kbk-c96] span.e-day[_ngcontent-kbk-c96] {
    background: none;
    border: 1px solid #0074BC!important;
    border-radius: 50%;
    color: #0074BC!important;
}

::ng-deep .e-calendar .e-content td.e-today span.e-day,
.e-calendar .e-content td.e-focused-date.e-today span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-today span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-focused-date.e-today span.e-day {
    background: none;
    border: 1px solid #0074BC!important;
    border-radius: 50%;
    color: #0074BC!important;
}

::ng-deep .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected.e-focused-date span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected.e-focused-date span.e-day {
    background-color: #0074BC!important;
    color: #fff!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day {
    background-color: #0074BC!important;
    color: #fff!important;
}

::ng-deep .e-daterangepicker.e-popup .e-presets .e-list-item.e-active,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-list-item.e-active {
    color: #0074BC!important;
}

::ng-deep .e-date-range-wrapper .e-input-group-icon.e-icons.e-active {
    color: #0074BC!important;
}

::ng-deep .e-input-group:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after {
    background-color: #0074BC!important;
}

::ng-deep .e-btn.e-flat.e-primary,
.e-css.e-btn.e-flat.e-primary {
    color: white!important;
    border-color: transparent;
    background-color: #0074BC!important;
}


/* ::ng-deep .e-btn.e-flat.e-primary:focus,
.e-css.e-btn.e-flat.e-primary:focus {
    background-color: rgba(0, 116, 188, 0.12)!important;
    border-color: transparent;
    color: #0074BC!important
} */

::ng-deep .e-btn.e-flat.e-primary:active,
.e-css.e-btn.e-flat.e-primary:active {
    background-color: rgba(0, 116, 188, 0.12)!important;
    border-color: transparent;
    color: #0074BC!important
}


/* only for start date */

::ng-deep .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected:hover span.e-day,
.e-calendar .e-content td.e-selected.e-focused-date span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-today.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected:hover span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected.e-focused-date span.e-day {
    background-color: #0074BC!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day {
    background-color: #0074BC!important;
}

::ng-deep .e-calendar .e-content td.e-selected span.e-day,
.e-bigger.e-small .e-calendar .e-content td.e-selected span.e-day {
    background-color: #0074BC!important;
}

:ng-deep .e-daterangepicker.e-popup .e-calendar-container .e-left-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container .e-left-container .e-left-calendar {
    overflow: hidden!important;
}

:ng-deep .e-calendar,
.e-bigger.e-small .e-calendar {
    overflow: hidden!important;
}

::ng-deep .e-daterangepicker.e-popup .e-calendar-container .e-left-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container .e-left-container .e-calendar .e-left-calendar .e-lib .e-keyboard {
    overflow: hidden!important;
}

:ng-deep .e-daterangepicker.e-popup,
.e-bigger.e-small .e-daterangepicker.e-popup {
    box-shadow: 0px 0px 8px #0000001A!important;
    border-radius: 8px!important;
}

::ng-deep .e-daterangepicker.e-popup.e-preset-wrapper,
.e-bigger.e-small .e-daterangepicker.e-popup.e-preset-wrapper {
    box-shadow: 0px 0px 8px #0000001A!important;
    border-radius: 8px!important;
}

::ng-deep .e-daterangepicker.e-popup .e-footer,
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer {
    border-radius: 8px 0;
}