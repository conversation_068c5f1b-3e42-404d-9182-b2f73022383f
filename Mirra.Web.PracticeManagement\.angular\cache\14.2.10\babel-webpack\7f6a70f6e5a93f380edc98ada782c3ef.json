{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/Practice Management/Web/Mirra.Web.PracticeManagement/Mirra.Web.PracticeManagement/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ChangeDetectorRef, EventEmitter } from '@angular/core';\nimport { FormControl, Validators } from '@angular/forms';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport { AllCPTCode } from 'src/app/classmodels/ResponseModel/ClaimForm/AllCPTCode';\nimport { distinctUntilChanged, first } from 'rxjs';\nimport * as moment from 'moment';\nimport Swal from 'sweetalert2';\nimport * as JSLZString from 'lz-string';\nimport { CptViewHistoryComponent } from '../../../../../modals/cpt-view-history/cpt-view-history.component';\nimport { numberWith3DecimalValidator, modifierValidator } from 'src/app/common/form.validators';\nimport { LocalStorageKey, PriceCost } from 'src/app/shared/constant/constatnt';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/ClaimForm/all-place-of-services.service\";\nimport * as i3 from \"src/app/shared/services/subject.service\";\nimport * as i4 from \"src/app/services/ClaimForm/get-all-cptcode.service\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"src/app/services/cache-service/cache.service\";\nimport * as i7 from \"ngx-spinner\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"../../../../../shared/directives/numbers-only.directive\";\nimport * as i12 from \"../../../../../shared/directives/number-only-withdecimal\";\nimport * as i13 from \"../../../../../shared/directives/number-only-twodecimal\";\nimport * as i14 from \"@angular/material/datepicker\";\n\nfunction ServiceLineClaimComponent_th_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\", 17);\n  }\n}\n\nfunction ServiceLineClaimComponent_a_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function ServiceLineClaimComponent_a_52_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.cptViewHistory());\n    });\n    i0.ɵɵelement(1, \"span\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 79);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r82 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r82.ndcValidation(i_r7).controls[\"proceduceC\"].value && ctx_r82.ndcValidation(i_r7).controls[\"proceduceC\"].value.length > 0 ? ctx_r82.ndcValidation(i_r7).controls[\"proceduceC\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 81);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r84 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r84.ndcValidation(i_r7).controls[\"proceduceCount\"].value && ctx_r84.ndcValidation(i_r7).controls[\"proceduceCount\"].value.length > 0 ? ctx_r84.ndcValidation(i_r7).controls[\"proceduceCount\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 82);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r86 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r86.ndcValidation(i_r7).controls[\"lineNote\"].value && ctx_r86.ndcValidation(i_r7).controls[\"lineNote\"].value.length > 0 ? ctx_r86.ndcValidation(i_r7).controls[\"lineNote\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 83);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r88 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r88.ndcValidation(i_r7).controls[\"anesStart\"].value && ctx_r88.ndcValidation(i_r7).controls[\"anesStart\"].value.length > 0 ? ctx_r88.ndcValidation(i_r7).controls[\"anesStart\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 84);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r90 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r90.ndcValidation(i_r7).controls[\"anesStop1\"].value && ctx_r90.ndcValidation(i_r7).controls[\"anesStop1\"].value.length > 0 ? ctx_r90.ndcValidation(i_r7).controls[\"anesStop1\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_ng_select_27_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r114 = ctx.item;\n    i0.ɵɵtextInterpolate2(\" \", item_r114.qualifier, \"-\", item_r114.description, \" \");\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction ServiceLineClaimComponent_span_56_tr_1_ng_select_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r117 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 85, 86);\n    i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_span_56_tr_1_ng_select_27_Template_ng_select_change_0_listener() {\n      i0.ɵɵrestoreView(_r117);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r115 = i0.ɵɵnextContext();\n      ctx_r115.changeValuesToMarkValidation(i_r7);\n      return i0.ɵɵresetView(ctx_r115.ndcQualChange(i_r7));\n    });\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_1_ng_select_27_ng_template_2_Template, 1, 2, \"ng-template\", 87);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r91 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"items\", ctx_r91.nDcQualifier)(\"searchFn\", ctx_r91.onSearchQualifer)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r91.ndcValidation(i_r7).controls[\"ndcQual\"].invalid && ctx_r91.ndcValidation(i_r7).controls[\"ndcQual\"].errors));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" NDC Qual is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_tr_1_div_28_div_1_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r92 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r92.ndcValidation(i_r7).controls[\"ndcQual\"] == null ? null : ctx_r92.ndcValidation(i_r7).controls[\"ndcQual\"].errors[\"required\"]);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r93 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r93.ndcValidation(i_r7).controls[\"ndcQual\"].value && ctx_r93.ndcValidation(i_r7).controls[\"ndcQual\"].value.length > 0 ? ctx_r93.ndcValidation(i_r7).controls[\"ndcQual\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r124 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 89);\n    i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_span_56_tr_1_input_33_Template_input_change_0_listener() {\n      i0.ɵɵrestoreView(_r124);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r122 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r122.changeValuesToMarkValidation(i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r94 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r94.ndcValidation(i_r7).controls[\"ndcCode\"].invalid && ctx_r94.ndcValidation(i_r7).controls[\"ndcCode\"].errors));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" NDC Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_34_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Invalid NDC code, it should be a 11 digit code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_tr_1_div_34_div_1_Template, 2, 0, \"div\", 29);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_1_div_34_div_2_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r95 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"] == null ? null : ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"] == null ? null : ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"].errors[\"required\"]) && (ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"] == null ? null : ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"].errors[\"isInvalid\"]));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r96 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r96.ndcValidation(i_r7).controls[\"ndcCode\"].value && ctx_r96.ndcValidation(i_r7).controls[\"ndcCode\"].value.length > 0 ? ctx_r96.ndcValidation(i_r7).controls[\"ndcCode\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r132 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 90);\n    i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_span_56_tr_1_input_40_Template_input_change_0_listener() {\n      i0.ɵɵrestoreView(_r132);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r130 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r130.changeValuesToMarkValidation(i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r97 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r97.ndcValidation(i_r7).controls[\"ndcQty\"].invalid && ctx_r97.ndcValidation(i_r7).controls[\"ndcQty\"].errors));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_41_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" NDC Quantity is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_41_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter valid NDC Quantity. Only numbers and three decimals accepted. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_41_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" The field NDC Quantity must be between 0 and 9999999.999. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_tr_1_div_41_div_1_Template, 2, 0, \"div\", 29);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_1_div_41_div_2_Template, 2, 0, \"div\", 29);\n    i0.ɵɵtemplate(3, ServiceLineClaimComponent_span_56_tr_1_div_41_div_3_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r137 = i0.ɵɵnextContext(2);\n    const i_r7 = ctx_r137.index;\n    const items_r6 = ctx_r137.$implicit;\n    const ctx_r98 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r98.ndcValidation(i_r7).controls[\"ndcQty\"] == null ? null : ctx_r98.ndcValidation(i_r7).controls[\"ndcQty\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = items_r6.get(\"ndcQty\")) == null ? null : tmp_1_0.invalid) && items_r6.get(\"ndcQty\").errors.isInvalid && !items_r6.get(\"ndcQty\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = items_r6.get(\"ndcQty\")) == null ? null : tmp_2_0.invalid) && !items_r6.get(\"ndcQty\").errors.isInvalid && !items_r6.get(\"ndcQty\").errors.required && (items_r6.get(\"ndcQty\").errors.max || items_r6.get(\"ndcQty\").errors.min));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r99 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r99.ndcValidation(i_r7).controls[\"ndcQty\"].value && ctx_r99.ndcValidation(i_r7).controls[\"ndcQty\"].value.length > 0 ? ctx_r99.ndcValidation(i_r7).controls[\"ndcQty\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_ng_select_46_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r141 = ctx.item;\n    i0.ɵɵtextInterpolate2(\" \", item_r141.qualifier, \"-\", item_r141.description, \" \");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_ng_select_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r144 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 91, 86);\n    i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_span_56_tr_1_ng_select_46_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r144);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r142 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r142.changeQual($event, i_r7));\n    })(\"clear\", function ServiceLineClaimComponent_span_56_tr_1_ng_select_46_Template_ng_select_clear_0_listener() {\n      i0.ɵɵrestoreView(_r144);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r145 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r145.clear(i_r7));\n    });\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_1_ng_select_46_ng_template_2_Template, 1, 2, \"ng-template\", 87);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r100 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"items\", ctx_r100.nDcQualifierQty)(\"searchFn\", ctx_r100.onSearchQualifer)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r100.ndcValidation(i_r7).controls[\"ndcQtyQual\"].invalid && ctx_r100.ndcValidation(i_r7).controls[\"ndcQtyQual\"].errors));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_47_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" NDC Qty Qual is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_tr_1_div_47_div_1_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r101 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r101.ndcValidation(i_r7).controls[\"ndcQtyQual\"] == null ? null : ctx_r101.ndcValidation(i_r7).controls[\"ndcQtyQual\"].errors[\"required\"]);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r102 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r102.ndcValidation(i_r7).controls[\"ndcQtyQual\"].value && ctx_r102.ndcValidation(i_r7).controls[\"ndcQtyQual\"].value.length > 0 ? ctx_r102.ndcValidation(i_r7).controls[\"ndcQtyQual\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 92);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r104 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r104.ndcValidation(i_r7).controls[\"anesStop2\"].value && ctx_r104.ndcValidation(i_r7).controls[\"anesStop2\"].value.length > 0 ? ctx_r104.ndcValidation(i_r7).controls[\"anesStop2\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 93);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r106 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r106.ndcValidation(i_r7).controls[\"anesStop3\"].value && ctx_r106.ndcValidation(i_r7).controls[\"anesStop3\"].value.length > 0 ? ctx_r106.ndcValidation(i_r7).controls[\"anesStop3\"].value : \"-\");\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"service-line-view-label\": a0\n  };\n};\n\nfunction ServiceLineClaimComponent_span_56_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 61);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_1_input_2_Template, 1, 0, \"input\", 62);\n    i0.ɵɵtemplate(3, ServiceLineClaimComponent_span_56_tr_1_span_3_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 61);\n    i0.ɵɵtemplate(5, ServiceLineClaimComponent_span_56_tr_1_input_5_Template, 1, 0, \"input\", 63);\n    i0.ɵɵtemplate(6, ServiceLineClaimComponent_span_56_tr_1_span_6_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 64)(8, \"div\", 65)(9, \"div\", 66);\n    i0.ɵɵtext(10, \" Line Note: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 67);\n    i0.ɵɵtemplate(12, ServiceLineClaimComponent_span_56_tr_1_input_12_Template, 1, 0, \"input\", 68);\n    i0.ɵɵtemplate(13, ServiceLineClaimComponent_span_56_tr_1_span_13_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 66);\n    i0.ɵɵtext(15, \" Anes Start: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 67);\n    i0.ɵɵtemplate(17, ServiceLineClaimComponent_span_56_tr_1_input_17_Template, 1, 0, \"input\", 69);\n    i0.ɵɵtemplate(18, ServiceLineClaimComponent_span_56_tr_1_span_18_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 66);\n    i0.ɵɵtext(20, \" Anes Stop: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 67);\n    i0.ɵɵtemplate(22, ServiceLineClaimComponent_span_56_tr_1_input_22_Template, 1, 0, \"input\", 70);\n    i0.ɵɵtemplate(23, ServiceLineClaimComponent_span_56_tr_1_span_23_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 66);\n    i0.ɵɵtext(25, \" NDC Qual: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 71);\n    i0.ɵɵtemplate(27, ServiceLineClaimComponent_span_56_tr_1_ng_select_27_Template, 3, 5, \"ng-select\", 72);\n    i0.ɵɵtemplate(28, ServiceLineClaimComponent_span_56_tr_1_div_28_Template, 2, 1, \"div\", 33);\n    i0.ɵɵtemplate(29, ServiceLineClaimComponent_span_56_tr_1_span_29_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 66);\n    i0.ɵɵtext(31, \" NDC Code: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 73);\n    i0.ɵɵtemplate(33, ServiceLineClaimComponent_span_56_tr_1_input_33_Template, 1, 3, \"input\", 74);\n    i0.ɵɵtemplate(34, ServiceLineClaimComponent_span_56_tr_1_div_34_Template, 3, 2, \"div\", 33);\n    i0.ɵɵtemplate(35, ServiceLineClaimComponent_span_56_tr_1_span_35_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 65)(37, \"div\", 66);\n    i0.ɵɵtext(38, \" NDC Qty: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 67);\n    i0.ɵɵtemplate(40, ServiceLineClaimComponent_span_56_tr_1_input_40_Template, 1, 3, \"input\", 75);\n    i0.ɵɵtemplate(41, ServiceLineClaimComponent_span_56_tr_1_div_41_Template, 4, 3, \"div\", 33);\n    i0.ɵɵtemplate(42, ServiceLineClaimComponent_span_56_tr_1_span_42_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 66);\n    i0.ɵɵtext(44, \" NDC Qty Qual: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 67);\n    i0.ɵɵtemplate(46, ServiceLineClaimComponent_span_56_tr_1_ng_select_46_Template, 3, 5, \"ng-select\", 76);\n    i0.ɵɵtemplate(47, ServiceLineClaimComponent_span_56_tr_1_div_47_Template, 2, 1, \"div\", 33);\n    i0.ɵɵtemplate(48, ServiceLineClaimComponent_span_56_tr_1_span_48_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 73);\n    i0.ɵɵtemplate(50, ServiceLineClaimComponent_span_56_tr_1_input_50_Template, 1, 0, \"input\", 77);\n    i0.ɵɵtemplate(51, ServiceLineClaimComponent_span_56_tr_1_span_51_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 67);\n    i0.ɵɵtemplate(53, ServiceLineClaimComponent_span_56_tr_1_input_53_Template, 1, 0, \"input\", 78);\n    i0.ɵɵtemplate(54, ServiceLineClaimComponent_span_56_tr_1_span_54_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(55, \"div\", 73)(56, \"div\", 73)(57, \"div\", 73)(58, \"div\", 73);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext().index;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(33, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(35, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(37, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(39, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.ndcValidation(i_r7).controls[\"ndcQual\"].errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(41, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.ndcValidation(i_r7).controls[\"ndcCode\"].errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(43, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.ndcValidation(i_r7).controls[\"ndcQty\"].errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(45, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.ndcValidation(i_r7).controls[\"ndcQtyQual\"].errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_2_input_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 97);\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext(2).$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"ndcUnitPrice\")) == null ? null : tmp_0_0.invalid) && (items_r6.get(\"ndcUnitPrice\").dirty || items_r6.get(\"ndcUnitPrice\").touched) && ((tmp_0_0 = items_r6.get(\"ndcUnitPrice\")) == null ? null : tmp_0_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter valid NDC Unit Price. Only numbers and three decimals accepted. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" The field NDC Unit Price must be between 0 and 9999999.999. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_2_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r157 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r157.ndcValidation(i_r7).controls[\"ndcUnitPrice\"].value && ctx_r157.ndcValidation(i_r7).controls[\"ndcUnitPrice\"].value.length > 0 ? ctx_r157.ndcValidation(i_r7).controls[\"ndcUnitPrice\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 94);\n    i0.ɵɵtext(2, \"NDC Unit Price:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 95);\n    i0.ɵɵtemplate(4, ServiceLineClaimComponent_span_56_tr_2_input_4_Template, 1, 3, \"input\", 96);\n    i0.ɵɵtemplate(5, ServiceLineClaimComponent_span_56_tr_2_div_5_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(6, ServiceLineClaimComponent_span_56_tr_2_div_6_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(7, ServiceLineClaimComponent_span_56_tr_2_span_7_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_3_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, ctx_r9.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = items_r6.get(\"ndcUnitPrice\")) == null ? null : tmp_2_0.invalid) && items_r6.get(\"ndcUnitPrice\").errors.isInvalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = items_r6.get(\"ndcUnitPrice\")) == null ? null : tmp_3_0.invalid) && !items_r6.get(\"ndcUnitPrice\").errors.isInvalid && (items_r6.get(\"ndcUnitPrice\").errors.max || items_r6.get(\"ndcUnitPrice\").errors.min));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.claimFormData.isViewClaim);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r162 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 98);\n    i0.ɵɵlistener(\"click\", function ServiceLineClaimComponent_span_56_input_7_Template_input_click_0_listener() {\n      i0.ɵɵrestoreView(_r162);\n      i0.ɵɵnextContext();\n\n      const _r11 = i0.ɵɵreference(9);\n\n      return i0.ɵɵresetView(_r11.open());\n    })(\"blur\", function ServiceLineClaimComponent_span_56_input_7_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r162);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r163 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r163.dosFromChangedEvent(i_r7, \"dateServiceFrom\"));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n\n    const _r11 = i0.ɵɵreference(9);\n\n    const ctx_r10 = i0.ɵɵnextContext();\n    let tmp_3_0;\n    i0.ɵɵproperty(\"min\", ctx_r10.minDate)(\"max\", ctx_r10.todayDate)(\"matDatepicker\", _r11)(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ((tmp_3_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_3_0.invalid) && (items_r6.get(\"dateServiceFrom\").dirty || items_r6.get(\"dateServiceFrom\").touched) && ((tmp_3_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_3_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please select date of service from. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" From date Should be less than To Date. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" DOS From should be greater than or equal to Date of Current Illness. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Select a date greater than or equal to 10-01-2015. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", !!items_r6.get(\"dateServiceFrom\").value && items_r6.get(\"dateServiceFrom\").value.length > 0 ? i0.ɵɵpipeBind2(2, 1, items_r6.get(\"dateServiceFrom\").value, \"MM/dd/yyyy\") : \"-\", \"\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r168 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 99);\n    i0.ɵɵlistener(\"click\", function ServiceLineClaimComponent_span_56_input_18_Template_input_click_0_listener() {\n      i0.ɵɵrestoreView(_r168);\n      i0.ɵɵnextContext();\n\n      const _r18 = i0.ɵɵreference(20);\n\n      return i0.ɵɵresetView(_r18.open());\n    })(\"blur\", function ServiceLineClaimComponent_span_56_input_18_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r168);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r169 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r169.checkDateValidations(i_r7, \"dateServiceTo\"));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n\n    const _r18 = i0.ɵɵreference(20);\n\n    const ctx_r17 = i0.ɵɵnextContext();\n    let tmp_3_0;\n    i0.ɵɵproperty(\"min\", ctx_r17.minDate)(\"max\", ctx_r17.todayDate)(\"matDatepicker\", _r18)(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ((tmp_3_0 = items_r6.get(\"dateServiceTo\")) == null ? null : tmp_3_0.invalid) && (items_r6.get(\"dateServiceTo\").dirty || items_r6.get(\"dateServiceTo\").touched) && ((tmp_3_0 = items_r6.get(\"dateServiceTo\")) == null ? null : tmp_3_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please select date of service to. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" To date Should be greater than From Date. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Select a date greater than or equal to 10-01-2015. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", !!items_r6.get(\"dateServiceTo\").value && items_r6.get(\"dateServiceTo\").value.length > 0 ? i0.ɵɵpipeBind2(2, 1, items_r6.get(\"dateServiceTo\").value, \"MM/dd/yyyy\") : \"-\", \"\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_28_ng_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r174 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r174);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, item_r174.text), \" \");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r176 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 100);\n    i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_span_56_ng_select_28_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r176);\n      const ctx_r175 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r175.placeOfServiceChanges($event));\n    });\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_ng_select_28_ng_option_1_Template, 3, 4, \"ng-option\", 101);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_0_0 = items_r6.get(\"locationOfService\")) == null ? null : tmp_0_0.invalid) && (items_r6.get(\"locationOfService\").dirty || items_r6.get(\"locationOfService\").touched) && ((tmp_0_0 = items_r6.get(\"locationOfService\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.required)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r23.allPlaceOfServices);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Place of Service is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"locationOfService\").value && items_r6.get(\"locationOfService\").value.length > 0 ? items_r6.get(\"locationOfService\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_34_ng_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r180 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r180);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, item_r180), \" \");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-select\", 103);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_ng_select_34_ng_option_1_Template, 3, 4, \"ng-option\", 101);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.cmg);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"emg\").value && items_r6.get(\"emg\").value.length > 0 ? items_r6.get(\"emg\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_39_ng_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r183 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r183);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", item_r183.cpt, \" - \", item_r183.shortDescription, \" \");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r186 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 104);\n    i0.ɵɵlistener(\"ngModelChange\", function ServiceLineClaimComponent_span_56_ng_select_39_Template_ng_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r186);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r184 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r184.OnCPTCodeChange($event, i_r7));\n    });\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_ng_select_39_ng_option_1_Template, 2, 3, \"ng-option\", 101);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_1_0 = items_r6.get(\"cpt\")) == null ? null : tmp_1_0.invalid) && (items_r6.get(\"cpt\").dirty || items_r6.get(\"cpt\").touched) && (((tmp_1_0 = items_r6.get(\"cpt\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.required) || ((tmp_1_0 = items_r6.get(\"cpt\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.invalidCptlength) || ((tmp_1_0 = items_r6.get(\"cpt\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.invalidCpt))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r28.cPTCodes);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Invalid CPT code entered. Please provide a valid CPT code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please Give Valid CPT Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"cpt\").value && items_r6.get(\"cpt\").value.length > 0 ? items_r6.get(\"cpt\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 105);\n  }\n\n  if (rf & 2) {\n    const ctx_r189 = i0.ɵɵnextContext();\n    const i_r7 = ctx_r189.index;\n    const items_r6 = ctx_r189.$implicit;\n    const ctx_r33 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier1\", i_r7, \"\");\n    i0.ɵɵproperty(\"disabled\", ctx_r33.isControl)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_2_0 = items_r6.get(\"m1\")) == null ? null : tmp_2_0.invalid) && (items_r6.get(\"m1\").dirty || items_r6.get(\"m1\").touched) && ((tmp_2_0 = items_r6.get(\"m1\")) == null ? null : tmp_2_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Modifier1 must be of 2 characters in length \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"m1\").value && items_r6.get(\"m1\").value.length > 0 ? items_r6.get(\"m1\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 106);\n  }\n\n  if (rf & 2) {\n    const ctx_r191 = i0.ɵɵnextContext();\n    const i_r7 = ctx_r191.index;\n    const items_r6 = ctx_r191.$implicit;\n    const ctx_r36 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier2\", i_r7, \"\");\n    i0.ɵɵproperty(\"disabled\", ctx_r36.isControl)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_2_0 = items_r6.get(\"m2\")) == null ? null : tmp_2_0.invalid) && (items_r6.get(\"m2\").dirty || items_r6.get(\"m2\").touched) && ((tmp_2_0 = items_r6.get(\"m2\")) == null ? null : tmp_2_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Modifier2 must be of 2 characters in length \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"m2\").value && items_r6.get(\"m2\").value.length > 0 ? items_r6.get(\"m2\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 107);\n  }\n\n  if (rf & 2) {\n    const ctx_r193 = i0.ɵɵnextContext();\n    const i_r7 = ctx_r193.index;\n    const items_r6 = ctx_r193.$implicit;\n    const ctx_r39 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier3\", i_r7, \"\");\n    i0.ɵɵproperty(\"disabled\", ctx_r39.isControl)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_2_0 = items_r6.get(\"m3\")) == null ? null : tmp_2_0.invalid) && (items_r6.get(\"m3\").dirty || items_r6.get(\"m3\").touched) && ((tmp_2_0 = items_r6.get(\"m3\")) == null ? null : tmp_2_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Modifier3 must be of 2 characters in length \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"m3\").value && items_r6.get(\"m3\").value.length > 0 ? items_r6.get(\"m3\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 108);\n  }\n\n  if (rf & 2) {\n    const ctx_r195 = i0.ɵɵnextContext();\n    const i_r7 = ctx_r195.index;\n    const items_r6 = ctx_r195.$implicit;\n    const ctx_r42 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier4\", i_r7, \"\");\n    i0.ɵɵproperty(\"disabled\", ctx_r42.isControl)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_2_0 = items_r6.get(\"m4\")) == null ? null : tmp_2_0.invalid) && (items_r6.get(\"m4\").dirty || items_r6.get(\"m4\").touched) && ((tmp_2_0 = items_r6.get(\"m4\")) == null ? null : tmp_2_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Modifier4 must be of 2 characters in length \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"m4\").value && items_r6.get(\"m4\").value.length > 0 ? items_r6.get(\"m4\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 109);\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r45 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵproperty(\"disabled\", ctx_r45.isControl)(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_1_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_1_0.invalid) && (((tmp_1_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_1_0.dirty) || ((tmp_1_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_1_0.touched))));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please Maintain Continuity while entering Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"diagnosispointer1\").value && items_r6.get(\"diagnosispointer1\").value.length > 0 ? items_r6.get(\"diagnosispointer1\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 110);\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_0_0.touched))));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please Maintain Continuity while entering Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"diagnosispointer2\").value && items_r6.get(\"diagnosispointer2\").value.length > 0 ? items_r6.get(\"diagnosispointer2\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 111);\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_0_0.touched))));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please Maintain Continuity while entering Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"diagnosispointer3\").value && items_r6.get(\"diagnosispointer3\").value.length > 0 ? items_r6.get(\"diagnosispointer3\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 112);\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_0_0.touched))));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please Maintain Continuity while entering Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"diagnosispointer4\").value && items_r6.get(\"diagnosispointer4\").value.length > 0 ? items_r6.get(\"diagnosispointer4\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_92_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r207 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 113);\n    i0.ɵɵlistener(\"blur\", function ServiceLineClaimComponent_span_56_input_92_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r207);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r205 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r205.calculateTotalAmount($event, i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"unitCharges\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = items_r6.get(\"unitCharges\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = items_r6.get(\"unitCharges\")) == null ? null : tmp_0_0.touched)) && ((tmp_0_0 = items_r6.get(\"unitCharges\")) == null ? null : tmp_0_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" The unit charges field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"unitCharges\").value && items_r6.get(\"unitCharges\").value.length > 0 ? items_r6.get(\"unitCharges\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_98_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r212 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 114);\n    i0.ɵɵlistener(\"blur\", function ServiceLineClaimComponent_span_56_input_98_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r212);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r210 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r210.calculateTotalAmount($event, i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_0_0.touched)) && ((tmp_0_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_0_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" The days and units field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" The field days and units must be between 1 and 99999999. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"dayUnitChanges\").value && items_r6.get(\"dayUnitChanges\").value.length > 0 ? items_r6.get(\"dayUnitChanges\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 115);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"total\").value && items_r6.get(\"total\").value.length > 0 ? items_r6.get(\"total\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_110_ng_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r217 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r217);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, item_r217), \" \");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-select\", 116);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_ng_select_110_ng_option_1_Template, 3, 4, \"ng-option\", 101);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r75.epsdt);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"ePSDT\").value && items_r6.get(\"ePSDT\").value.length > 0 ? items_r6.get(\"ePSDT\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_117_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r220 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 117);\n    i0.ɵɵlistener(\"input\", function ServiceLineClaimComponent_span_56_input_117_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r220);\n      const ctx_r219 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r219.renderingProvider($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_0_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_118_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter Rendering Provider NPI \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_118_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Rendering Provider NPI must be of 10 characters in length. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_div_118_div_1_Template, 2, 0, \"div\", 29);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_div_118_div_2_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_0_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_1_0.hasError(\"maxlength\")) || ((tmp_1_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_1_0.hasError(\"minlength\")));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"jRenderingProviderId\").value && items_r6.get(\"jRenderingProviderId\").value.length > 0 ? items_r6.get(\"jRenderingProviderId\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_td_120_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r229 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function ServiceLineClaimComponent_span_56_td_120_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r229);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r227 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r227.removeService(i_r7));\n    });\n    i0.ɵɵtext(1, \"X\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_td_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"div\", 118);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_td_120_button_2_Template, 2, 0, \"button\", 119);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r80 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r80.serviceLine.length > 1);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_tr_1_Template, 59, 47, \"tr\", 29);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_2_Template, 8, 7, \"tr\", 29);\n    i0.ɵɵelementStart(3, \"tr\")(4, \"td\")(5, \"div\", 11)(6, \"div\", 30);\n    i0.ɵɵtemplate(7, ServiceLineClaimComponent_span_56_input_7_Template, 1, 6, \"input\", 31);\n    i0.ɵɵelement(8, \"mat-datepicker\", null, 32);\n    i0.ɵɵtemplate(10, ServiceLineClaimComponent_span_56_div_10_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(11, ServiceLineClaimComponent_span_56_div_11_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(12, ServiceLineClaimComponent_span_56_div_12_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(13, ServiceLineClaimComponent_span_56_div_13_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(14, ServiceLineClaimComponent_span_56_span_14_Template, 3, 4, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"td\")(16, \"div\", 11)(17, \"div\", 30);\n    i0.ɵɵtemplate(18, ServiceLineClaimComponent_span_56_input_18_Template, 1, 6, \"input\", 35);\n    i0.ɵɵelement(19, \"mat-datepicker\", null, 36);\n    i0.ɵɵtemplate(21, ServiceLineClaimComponent_span_56_div_21_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(22, ServiceLineClaimComponent_span_56_div_22_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(23, ServiceLineClaimComponent_span_56_div_23_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(24, ServiceLineClaimComponent_span_56_span_24_Template, 3, 4, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"td\", 37)(26, \"div\", 11)(27, \"div\", 30);\n    i0.ɵɵtemplate(28, ServiceLineClaimComponent_span_56_ng_select_28_Template, 2, 4, \"ng-select\", 38);\n    i0.ɵɵtemplate(29, ServiceLineClaimComponent_span_56_div_29_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(30, ServiceLineClaimComponent_span_56_span_30_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"td\")(32, \"div\", 11)(33, \"div\", 30);\n    i0.ɵɵtemplate(34, ServiceLineClaimComponent_span_56_ng_select_34_Template, 2, 1, \"ng-select\", 39);\n    i0.ɵɵtemplate(35, ServiceLineClaimComponent_span_56_span_35_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"td\", 37)(37, \"div\", 11)(38, \"div\", 30);\n    i0.ɵɵtemplate(39, ServiceLineClaimComponent_span_56_ng_select_39_Template, 2, 5, \"ng-select\", 40);\n    i0.ɵɵtemplate(40, ServiceLineClaimComponent_span_56_div_40_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(41, ServiceLineClaimComponent_span_56_div_41_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(42, ServiceLineClaimComponent_span_56_div_42_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(43, ServiceLineClaimComponent_span_56_span_43_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"td\", 41)(45, \"div\", 42)(46, \"div\", 30);\n    i0.ɵɵtemplate(47, ServiceLineClaimComponent_span_56_input_47_Template, 1, 5, \"input\", 43);\n    i0.ɵɵtemplate(48, ServiceLineClaimComponent_span_56_div_48_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(49, ServiceLineClaimComponent_span_56_span_49_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 30);\n    i0.ɵɵtemplate(51, ServiceLineClaimComponent_span_56_input_51_Template, 1, 5, \"input\", 44);\n    i0.ɵɵtemplate(52, ServiceLineClaimComponent_span_56_div_52_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(53, ServiceLineClaimComponent_span_56_span_53_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 30);\n    i0.ɵɵtemplate(55, ServiceLineClaimComponent_span_56_input_55_Template, 1, 5, \"input\", 45);\n    i0.ɵɵtemplate(56, ServiceLineClaimComponent_span_56_div_56_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(57, ServiceLineClaimComponent_span_56_span_57_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 30);\n    i0.ɵɵtemplate(59, ServiceLineClaimComponent_span_56_input_59_Template, 1, 5, \"input\", 46);\n    i0.ɵɵtemplate(60, ServiceLineClaimComponent_span_56_div_60_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(61, ServiceLineClaimComponent_span_56_span_61_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(62, \"td\", 41)(63, \"div\", 47)(64, \"div\", 30);\n    i0.ɵɵtemplate(65, ServiceLineClaimComponent_span_56_input_65_Template, 1, 4, \"input\", 48);\n    i0.ɵɵtemplate(66, ServiceLineClaimComponent_span_56_div_66_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(67, ServiceLineClaimComponent_span_56_div_67_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(68, ServiceLineClaimComponent_span_56_div_68_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(69, ServiceLineClaimComponent_span_56_div_69_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(70, ServiceLineClaimComponent_span_56_span_70_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"div\", 30);\n    i0.ɵɵtemplate(72, ServiceLineClaimComponent_span_56_input_72_Template, 1, 3, \"input\", 49);\n    i0.ɵɵtemplate(73, ServiceLineClaimComponent_span_56_div_73_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(74, ServiceLineClaimComponent_span_56_div_74_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(75, ServiceLineClaimComponent_span_56_div_75_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(76, ServiceLineClaimComponent_span_56_span_76_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 30);\n    i0.ɵɵtemplate(78, ServiceLineClaimComponent_span_56_input_78_Template, 1, 3, \"input\", 50);\n    i0.ɵɵtemplate(79, ServiceLineClaimComponent_span_56_div_79_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(80, ServiceLineClaimComponent_span_56_div_80_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(81, ServiceLineClaimComponent_span_56_div_81_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(82, ServiceLineClaimComponent_span_56_span_82_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"div\", 30);\n    i0.ɵɵtemplate(84, ServiceLineClaimComponent_span_56_input_84_Template, 1, 3, \"input\", 51);\n    i0.ɵɵtemplate(85, ServiceLineClaimComponent_span_56_div_85_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(86, ServiceLineClaimComponent_span_56_div_86_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(87, ServiceLineClaimComponent_span_56_div_87_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(88, ServiceLineClaimComponent_span_56_span_88_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(89, \"td\", 52)(90, \"div\", 11)(91, \"div\", 30);\n    i0.ɵɵtemplate(92, ServiceLineClaimComponent_span_56_input_92_Template, 1, 3, \"input\", 53);\n    i0.ɵɵtemplate(93, ServiceLineClaimComponent_span_56_div_93_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(94, ServiceLineClaimComponent_span_56_span_94_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(95, \"td\", 52)(96, \"div\", 11)(97, \"div\", 30);\n    i0.ɵɵtemplate(98, ServiceLineClaimComponent_span_56_input_98_Template, 1, 3, \"input\", 54);\n    i0.ɵɵtemplate(99, ServiceLineClaimComponent_span_56_div_99_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(100, ServiceLineClaimComponent_span_56_div_100_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(101, ServiceLineClaimComponent_span_56_span_101_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(102, \"td\", 52)(103, \"div\", 11)(104, \"div\", 30);\n    i0.ɵɵtemplate(105, ServiceLineClaimComponent_span_56_input_105_Template, 1, 0, \"input\", 55);\n    i0.ɵɵtemplate(106, ServiceLineClaimComponent_span_56_span_106_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(107, \"td\")(108, \"div\", 11)(109, \"div\", 30);\n    i0.ɵɵtemplate(110, ServiceLineClaimComponent_span_56_ng_select_110_Template, 2, 1, \"ng-select\", 56);\n    i0.ɵɵtemplate(111, ServiceLineClaimComponent_span_56_span_111_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(112, \"td\")(113, \"div\", 57);\n    i0.ɵɵtext(114, \" NPI \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(115, \"td\", 58)(116, \"div\", 59);\n    i0.ɵɵtemplate(117, ServiceLineClaimComponent_span_56_input_117_Template, 1, 3, \"input\", 60);\n    i0.ɵɵtemplate(118, ServiceLineClaimComponent_span_56_div_118_Template, 3, 2, \"div\", 33);\n    i0.ɵɵtemplate(119, ServiceLineClaimComponent_span_56_span_119_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(120, ServiceLineClaimComponent_span_56_td_120_Template, 3, 1, \"td\", 29);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const items_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    let tmp_15_0;\n    let tmp_20_0;\n    let tmp_21_0;\n    let tmp_22_0;\n    let tmp_25_0;\n    let tmp_28_0;\n    let tmp_31_0;\n    let tmp_34_0;\n    let tmp_37_0;\n    let tmp_38_0;\n    let tmp_39_0;\n    let tmp_40_0;\n    let tmp_43_0;\n    let tmp_44_0;\n    let tmp_45_0;\n    let tmp_48_0;\n    let tmp_49_0;\n    let tmp_50_0;\n    let tmp_53_0;\n    let tmp_54_0;\n    let tmp_55_0;\n    let tmp_58_0;\n    let tmp_61_0;\n    let tmp_62_0;\n    let tmp_70_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r7);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f.showNDC.value === \"Yes\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f.showNDC.value === \"Yes\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_4_0.invalid) && items_r6.get(\"dateServiceFrom\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_5_0.invalid) && items_r6.get(\"dateServiceFrom\").errors.invalidDate && !items_r6.get(\"dateServiceFrom\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_6_0.invalid) && items_r6.get(\"dateServiceFrom\").errors.currentIllnessError && !items_r6.get(\"dateServiceFrom\").errors.invalidDate && !items_r6.get(\"dateServiceFrom\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_7_0.invalid) && !items_r6.get(\"dateServiceFrom\").errors.currentIllnessError && !items_r6.get(\"dateServiceFrom\").errors.invalidDate && !items_r6.get(\"dateServiceFrom\").errors.required && items_r6.get(\"dateServiceFrom\").errors.invalidFromDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = items_r6.get(\"dateServiceTo\")) == null ? null : tmp_10_0.invalid) && items_r6.get(\"dateServiceTo\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = items_r6.get(\"dateServiceTo\")) == null ? null : tmp_11_0.invalid) && items_r6.get(\"dateServiceTo\").errors.invalidDate && !items_r6.get(\"dateServiceTo\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = items_r6.get(\"dateServiceTo\")) == null ? null : tmp_12_0.invalid) && !items_r6.get(\"dateServiceTo\").errors.invalidDate && !items_r6.get(\"dateServiceTo\").errors.required && items_r6.get(\"dateServiceTo\").errors.invalidFromDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isEditClaim || ctx_r2.claimFormData.isAddClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = items_r6.get(\"locationOfService\")) == null ? null : tmp_15_0.invalid) && items_r6.get(\"locationOfService\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isEditClaim || ctx_r2.claimFormData.isAddClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = items_r6.get(\"cpt\")) == null ? null : tmp_20_0.invalid) && items_r6.get(\"cpt\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_21_0 = items_r6.get(\"cpt\")) == null ? null : tmp_21_0.invalid) && items_r6.get(\"cpt\").errors.invalidCptlength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_22_0 = items_r6.get(\"cpt\")) == null ? null : tmp_22_0.invalid) && items_r6.get(\"cpt\").errors.invalidCpt);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_25_0 = items_r6.get(\"m1\")) == null ? null : tmp_25_0.invalid) && items_r6.get(\"m1\").errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_28_0 = items_r6.get(\"m2\")) == null ? null : tmp_28_0.invalid) && items_r6.get(\"m2\").errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_31_0 = items_r6.get(\"m3\")) == null ? null : tmp_31_0.invalid) && items_r6.get(\"m3\").errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_34_0 = items_r6.get(\"m4\")) == null ? null : tmp_34_0.invalid) && items_r6.get(\"m4\").errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_37_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_37_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_38_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_38_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_39_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_39_0.hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_40_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_40_0.hasError(\"continue\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_43_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_43_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_44_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_44_0.hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_45_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_45_0.hasError(\"continue\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_48_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_48_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_49_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_49_0.hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_50_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_50_0.hasError(\"continue\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_53_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_53_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_54_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_54_0.hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_55_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_55_0.hasError(\"continue\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_58_0 = items_r6.get(\"unitCharges\")) == null ? null : tmp_58_0.invalid) && items_r6.get(\"unitCharges\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_61_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_61_0.invalid) && items_r6.get(\"dayUnitChanges\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_62_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_62_0.invalid) && (items_r6.get(\"dayUnitChanges\").errors.max || items_r6.get(\"dayUnitChanges\").errors.min));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(73, _c1, ctx_r2.claimFormData.isViewClaim));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_70_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_70_0.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isEditClaim || ctx_r2.claimFormData.isAddClaim);\n  }\n}\n\nfunction ServiceLineClaimComponent_button_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r231 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function ServiceLineClaimComponent_button_57_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r231);\n      const ctx_r230 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r230.addnewService());\n    });\n    i0.ɵɵelementStart(1, \"i\", 122);\n    i0.ɵɵtext(2, \"playlist_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Add Service\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c2 = function (a0) {\n  return {\n    \"width\": a0\n  };\n};\n\nexport let ServiceLineClaimComponent = /*#__PURE__*/(() => {\n  class ServiceLineClaimComponent {\n    constructor(serviceLineform, placeOfService, subjectService, cptservice, dialog, cacheService, cdRef, spinner) {\n      this.serviceLineform = serviceLineform;\n      this.placeOfService = placeOfService;\n      this.subjectService = subjectService;\n      this.cptservice = cptservice;\n      this.dialog = dialog;\n      this.cacheService = cacheService;\n      this.cdRef = cdRef;\n      this.spinner = spinner;\n      this.todayDate = new Date();\n      this.minDate = new Date('2015-10-01');\n      this.diagnosisPointerBlur = new EventEmitter();\n      this.dosFromChanged = new EventEmitter();\n      this.calculateTotal = new EventEmitter();\n      this.allPlaceOfServices = [];\n      this.allICDCode = [{\n        text: \"abcdef\",\n        value: \"abcdef\",\n        addDate: '',\n        termDate: ''\n      }];\n      this.isControl = true;\n      this.nDcQualifierQty = [];\n      this.nDcQualifier = [];\n      this.allCPTCode = [];\n      this.allCPTCharges = [];\n      this.viewHistoryCPTList = [];\n      this.cmg = [\"Yes\", \"No\"];\n      this.epsdt = [\"Yes\", \"No\"];\n      this.isSubmited = false;\n      this.invalidCptCode = [];\n      this.minimumcharacterTohitAPI = 3;\n      this.cPTCodes = [];\n      this.addServiceShow = false;\n      this.dateServiceFrom = [];\n      this.dateServiceTo = [];\n      this.oldServiceDateFrom = '';\n      this.oldServiceDateTo = '';\n      this.subjectService.getBillingProviderNPIChange().subscribe(res => {\n        if (res || res == '') {\n          this.changeRenderProviderNo(res);\n          this.subjectService.resetServicelineProviderNPIChange();\n        }\n      });\n    }\n\n    ngOnInit() {\n      this.spinner.show('child');\n      this.cdRef.detectChanges();\n      setTimeout(() => {\n        this.patchValue(); // Heavy synchronous form patch\n\n        this.nDcQualifier = this.claimFormData.ndcQualifierDataByType;\n        this.nDcQualifierQty = this.claimFormData.ndcQtyQualifierDataByType;\n\n        if (this.claimFormData.isAddClaim) {\n          this.serviceLine.controls.forEach(control => {\n            control.get('proceduceC')?.setValue('HC', {\n              emitEvent: false\n            });\n          });\n        }\n\n        this.spinner.hide('child');\n      }, 500);\n    }\n\n    createForm() {\n      this.serviceLineInfo = this.serviceLineform.group({\n        showNDC: new FormControl('No'),\n        serviceLines: this.serviceLineform.array([])\n      });\n      return this.serviceLineInfo;\n    }\n\n    get serviceLine() {\n      return this.serviceLineInfo.get('serviceLines');\n    }\n\n    get f() {\n      return this.serviceLineInfo.controls;\n    } // allPlaceOfServices: AllPlaceOfServices[] = [];\n\n\n    placeOfServiceData() {\n      if (!!this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices)) {\n        this.allPlaceOfServices = this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices);\n      } else {\n        this.placeOfService.fetchchAllPlaceOfServices().subscribe(res => {\n          this.allPlaceOfServices = res;\n        });\n      }\n    }\n\n    patchValue() {\n      if (this.claimFormData.isAddClaim) {\n        const renderingNPI = this.claimFormData?.provider?.renderingProvider?.providerNPI;\n        const posCode = this.claimFormData?.placeOfServiceCode;\n        const dOSFrom = new Date(this.memberProfile?.dOSFrom);\n        const dOSTo = this.memberProfile?.dOSTo;\n        const groups = this.serviceLineInfos?.serviceLines.map(ele => {\n          const charges = Number(ele.charges) || 0;\n          const units = Number(ele.daysunit) || 0;\n          const total = (units * charges).toFixed(2);\n          return this.serviceLineform.group({\n            dateServiceFrom: [{\n              value: dOSFrom,\n              disabled: true\n            }],\n            dateServiceTo: [{\n              value: dOSTo,\n              disabled: true\n            }],\n            locationOfService: [{\n              value: posCode,\n              disabled: false\n            }],\n            emg: [{\n              value: ele.eMG,\n              disabled: false\n            }],\n            cpt: [{\n              value: ele.cptCode,\n              disabled: false\n            }, Validators.required],\n            desc: [{\n              value: ele.desc\n            }],\n            m1: [{\n              value: ele.m1,\n              disabled: false\n            }],\n            m2: [{\n              value: ele.m2,\n              disabled: false\n            }],\n            m3: [{\n              value: ele.m3,\n              disabled: false\n            }],\n            m4: [{\n              value: ele.m4,\n              disabled: false\n            }],\n            diagnosispointer1: [{\n              value: Number(ele.d1).toString(),\n              disabled: false\n            }],\n            diagnosispointer2: [{\n              value: ele.d2 ? Number(ele.d2).toString() : '',\n              disabled: false\n            }],\n            diagnosispointer3: [{\n              value: ele.d3 ? Number(ele.d3).toString() : '',\n              disabled: false\n            }],\n            diagnosispointer4: [{\n              value: ele.d4 ? Number(ele.d4).toString() : '',\n              disabled: false\n            }],\n            unitCharges: [{\n              value: ele.charges,\n              disabled: false\n            }, [Validators.required]],\n            dayUnitChanges: [{\n              value: ele.daysunit,\n              disabled: false\n            }, [Validators.required, Validators.max(99999999), Validators.min(1)]],\n            total: [{\n              value: total,\n              disabled: true\n            }],\n            ePSDT: [{\n              value: ele.ePSDT,\n              disabled: false\n            }],\n            jRenderingProviderId: [{\n              value: renderingNPI,\n              disabled: true\n            }],\n            proceduceC: '',\n            proceduceCount: '',\n            ndcUnitPrice: '',\n            lineNote: '',\n            ndcQtyQual: null,\n            anesStart: '',\n            ndcQty: '',\n            anesStop1: '',\n            anesStop2: '',\n            anesStop3: '',\n            ndcQual: null,\n            ndcCode: ''\n          });\n        });\n        groups.forEach(group => this.serviceLine.push(group));\n      }\n\n      if (this.claimFormData.isEditClaim || this.claimFormData.isAddClaim) {\n        this.fetchCPTData();\n        this.fetchchAllCPTCharges();\n        this.placeOfServiceData();\n      } // View Claim Show NDC radio button enabled \n\n\n      if (this.claimFormData.isViewClaim) {\n        this.patchServiceLineData();\n        this.serviceLineInfo.disable();\n        this.serviceLineInfo.controls[\"showNDC\"].enable();\n      }\n\n      this.dateValidators();\n    }\n\n    showNDC() {\n      if (this.claimFormData.isAddClaim) {\n        for (let index = 0; index < this.serviceLineInfos?.serviceLines.length; index++) {\n          this.serviceLine.at(index).get('proceduceC').patchValue('HC');\n          this.serviceLine.at(index).get('proceduceCount').patchValue(index + 1);\n        }\n      }\n    }\n\n    hideNDC() {}\n\n    setValidator() {\n      this.isSubmited;\n\n      if (this.serviceLineInfo.invalid) {\n        submitValidateAllFields.validateAllFields(this.serviceLineInfo);\n        return false;\n      }\n\n      return true;\n    }\n\n    OnCPTCodeChange(codeValue, index) {\n      let dayUnitChanges = this.serviceLine.at(index).get('dayUnitChanges').value ? this.serviceLine.at(index).get('dayUnitChanges').value : '1';\n      let unitCharges = this.serviceLine.at(index).get('unitCharges').value;\n\n      if (codeValue) {\n        let cptCharge = this.allCPTCharges.filter(item => item.code == codeValue)[0]?.charge;\n\n        if (!!cptCharge) {\n          unitCharges = cptCharge;\n        } else {\n          unitCharges = this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n        }\n\n        let cptDescription = this.cptCodes.find(item => item.cpt == codeValue).description;\n        this.serviceLine.at(index).patchValue({\n          dayUnitChanges: dayUnitChanges,\n          total: (dayUnitChanges * unitCharges).toFixed(2),\n          desc: {\n            value: cptDescription\n          },\n          proceduceC: 'HC',\n          unitCharges: Number.parseFloat(unitCharges).toFixed(2)\n        });\n        this.invalidCptCode.forEach(element => {\n          if (element === codeValue) {\n            this.serviceLine.at(index).get('cpt').setErrors({\n              invalidCpt: true\n            });\n          }\n        });\n      } else {\n        this.serviceLine.at(index).patchValue({\n          dayUnitChanges: null,\n          total: (0 * unitCharges).toFixed(2)\n        });\n      }\n\n      this.calculateTotal.emit();\n      this.validateCPTCode();\n    }\n\n    fetchchAllCPTCharges() {\n      if (this.cacheService.localStorageGetItem(LocalStorageKey.allCPTCharges)) {\n        this.allCPTCharges = this.cacheService.localStorageGetItem(LocalStorageKey.allCPTCharges);\n        return;\n      }\n\n      this.cptservice.fetchchAllCPTCharges().pipe(first()).subscribe(response => {\n        this.allCPTCharges = response.content;\n      });\n    }\n\n    validateForm() {\n      for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {\n        if (this.serviceLine.at(index).get('jRenderingProviderId').value.length != 10) {\n          this.serviceLine.at(this.serviceLineInfo.controls['serviceLines'].value.length - 1).get('jRenderingProviderId').setErrors({\n            maxlength: true\n          });\n        }\n\n        this.checkDateValidations(index, 'dateServiceFrom');\n        this.checkDateFromValidation(index, 'dateServiceTo');\n        this.ndcQualChange(index);\n        this.changeValuesToMarkValidation(index);\n        this.validateCPTCode();\n      }\n\n      return this.setValidator();\n    }\n\n    changeQual(e, index) {\n      if (e) {\n        if (!this.serviceLine.at(index).get('ndcCode').value) this.serviceLine.at(index).get('ndcCode').setErrors({\n          required: true\n        });\n        if (!this.serviceLine.at(index).get('ndcQty').value) this.serviceLine.at(index).get('ndcQty').setErrors({\n          required: true\n        });\n        if (!this.serviceLine.at(index).get('ndcQual').value) this.serviceLine.at(index).get('ndcQual').setErrors({\n          required: true\n        });\n      } else {\n        this.serviceLine.at(index).get('ndcCode').setErrors(null);\n        this.serviceLine.at(index).get('ndcQty').setErrors(null);\n        this.serviceLine.at(index).get('ndcQual').setErrors(null);\n      }\n\n      this.changeValuesToMarkValidation(index);\n    }\n\n    changeValuesToMarkValidation(index) {\n      let fields = ['ndcCode', 'ndcQty', 'ndcQual', 'ndcQtyQual'];\n      let validatorToBeApplied = false;\n\n      for (const field of fields) {\n        validatorToBeApplied = validatorToBeApplied || !!this.serviceLine.at(index).get(field).value;\n      }\n\n      if (validatorToBeApplied) {\n        for (const field of fields) {\n          if (!!!this.serviceLine.at(index).get(field).value) {\n            this.serviceLine.at(index).get(field).setErrors({\n              required: true\n            });\n            this.serviceLine.at(index).get(field).setValidators(Validators.required);\n          }\n        }\n      } else {\n        for (const field of fields) {\n          this.serviceLine.at(index).get(field).removeValidators(Validators.required);\n\n          if (this.serviceLine.at(index).get(field).hasError('required')) {\n            delete this.serviceLine.at(index).get(field).errors['required'];\n            this.serviceLine.at(index).get(field).updateValueAndValidity();\n          }\n        }\n      } // fields = fields.filter(field => field != fieldName);\n      // if (!!this.serviceLine.at(index).get(fieldName).value) {\n      //   for (const field of fields) {\n      //     if (!(!!this.serviceLine.at(index).get(field).value)) {\n      //       this.serviceLine.at(index).get(field).setErrors({required: true})\n      //     } else {\n      //       if (this.serviceLine.at(index).get(field).hasError('required')) {\n      //         delete this.serviceLine.at(index).get(field).errors['required'];\n      //         this.serviceLine.at(index).get(field).updateValueAndValidity();\n      //       }\n      //     }\n      //   }\n      // }\n\n    }\n\n    clear(index) {\n      this.serviceLine.at(index).get('ndcCode').setErrors(null);\n      this.serviceLine.at(index).get('ndcQty').setErrors(null);\n      this.serviceLine.at(index).get('ndcQual').setErrors(null);\n    }\n\n    ndcValidation(index) {\n      let formArr = this.serviceLineInfo.controls['serviceLines'];\n      const formGroup = formArr.controls[index];\n      return formGroup;\n    }\n\n    calculateTotalAmount(e, i) {\n      let unitCharges = this.serviceLine.at(i).get('unitCharges').value;\n\n      if (unitCharges == null || unitCharges == undefined || unitCharges.length == 0) {\n        unitCharges = this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n        this.serviceLine.at(i).get('unitCharges').setValue(unitCharges);\n      }\n\n      let dayUnitChanges = this.serviceLine.at(i).get('dayUnitChanges').value;\n      this.serviceLine.at(i).patchValue({\n        total: Number.parseFloat((dayUnitChanges * unitCharges).toString()).toFixed(2)\n      });\n      this.calculateTotal.emit(e);\n      return false;\n    }\n\n    addnewService() {\n      const dOSFrom = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSFrom) : new Date(this.claimFormData?.claimViewModel.claimDosfrom);\n      const dOSTo = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSTo) : new Date(this.claimFormData?.claimViewModel.claimDosto);\n      let locationOfService = '11';\n\n      if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\n        locationOfService = this.serviceLine.controls[0].value.locationOfService;\n      }\n\n      const empGroup = this.serviceLineform.group({\n        dateServiceFrom: new FormControl({\n          value: dOSFrom,\n          disabled: true\n        }, [Validators.required]),\n        dateServiceTo: new FormControl({\n          value: dOSTo,\n          disabled: true\n        }, [Validators.required]),\n        locationOfService: new FormControl(locationOfService, Validators.required),\n        emg: new FormControl(null),\n        desc: [{\n          value: null\n        }],\n        cpt: new FormControl(null, [Validators.required]),\n        m1: [{\n          value: '',\n          disabled: this.claimFormData.isViewClaim\n        }, modifierValidator],\n        m2: [{\n          value: '',\n          disabled: this.claimFormData.isViewClaim\n        }, modifierValidator],\n        m3: [{\n          value: \"\",\n          disabled: this.claimFormData.isViewClaim\n        }, modifierValidator],\n        m4: [{\n          value: '',\n          disabled: this.claimFormData.isViewClaim\n        }, modifierValidator],\n        diagnosispointer1: new FormControl('1', [Validators.required]),\n        diagnosispointer2: [{\n          value: '',\n          disabled: this.claimFormData.isViewClaim\n        }],\n        diagnosispointer3: [{\n          value: '',\n          disabled: this.claimFormData.isViewClaim\n        }],\n        diagnosispointer4: [{\n          value: '',\n          disabled: this.claimFormData.isViewClaim\n        }],\n        unitCharges: new FormControl(this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice, Validators.required),\n        dayUnitChanges: new FormControl({\n          value: '1',\n          disabled: this.claimFormData.isViewClaim\n        }, [Validators.required, Validators.max(99999999), Validators.min(1)]),\n        total: [{\n          value: this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice,\n          disabled: true\n        }],\n        ePSDT: [{\n          value: null,\n          disabled: this.claimFormData.isViewClaim\n        }],\n        jRenderingProviderId: [{\n          value: this.serviceLine.at(0)?.get('jRenderingProviderId')?.value,\n          disabled: true\n        }, [Validators.maxLength(10), Validators.minLength(10)]],\n        //new FormControl(this.serviceLine.at(0)?.get('jRenderingProviderId')?.value, [Validators.maxLength(10), Validators.minLength(10)]).disable(),\n        proceduceC: '',\n        proceduceCount: this.serviceLine.length + 1,\n        ndcUnitPrice: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n        lineNote: '',\n        ndcQtyQual: '',\n        anesStart: '',\n        ndcQty: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n        anesStop1: '',\n        anesStop2: '',\n        anesStop3: '',\n        ndcQual: '',\n        ndcCode: ''\n      });\n      this.serviceLine.push(empGroup); // this.dateValidators();\n      // if (this.serviceLine.invalid) {\n      //   submitValidateAllFields.validateAllFields(this.serviceLineInfo);\n      //   return;\n      // }\n    }\n\n    removeService(i) {\n      this.serviceLine.removeAt(i);\n      this.calculateTotal.emit();\n      this.validateForm();\n    }\n\n    renderingProvider(e) {\n      this.changeRenderProviderNo(e.target.value);\n      this.subjectService.setServicelineProviderNPIChange(e.target.value);\n    }\n\n    changeRenderProviderNo(data) {\n      if (!!this.serviceLineInfo.controls) {\n        for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {\n          this.serviceLine.at(index).get('jRenderingProviderId').patchValue(data);\n        }\n      }\n    }\n\n    dateValidators() {\n      if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\n        for (let item of this.serviceLine.controls) {\n          item.get('dateServiceFrom').valueChanges.pipe(distinctUntilChanged()).subscribe(dateServiceFrom => {\n            if (!!dateServiceFrom) {\n              if (!!item.get('dateServiceTo').value) {\n                if (new Date(dateServiceFrom).getTime() > new Date(item.get('dateServiceTo').value).getTime()) {\n                  item.get('dateServiceFrom').setErrors({\n                    invalidDate: true\n                  });\n                } else if (new Date(dateServiceFrom).getTime() < new Date(item.get('dateServiceTo').value).getTime()) {\n                  item.get('dateServiceFrom').updateValueAndValidity();\n                  item.get('dateServiceTo').updateValueAndValidity();\n                } else {\n                  if (item.get('dateServiceFrom').hasError('invalidDate')) {\n                    delete item.get('dateServiceFrom').errors['invalidDate'];\n                    item.get('dateServiceFrom').updateValueAndValidity();\n                  }\n\n                  if (item.get('dateServiceFrom').hasError('required')) {\n                    delete item.get('dateServiceFrom').errors['invalidDate'];\n                    item.get('dateServiceFrom').updateValueAndValidity();\n                  }\n                }\n              } else {\n                if (item.get('dateServiceFrom').hasError('invalidDate')) {\n                  delete item.get('dateServiceFrom').errors['invalidDate'];\n                  item.get('dateServiceFrom').updateValueAndValidity();\n                }\n\n                if (item.get('dateServiceFrom').hasError('required')) {\n                  delete item.get('dateServiceFrom').errors['invalidDate'];\n                  item.get('dateServiceFrom').updateValueAndValidity();\n                }\n              }\n            } else {\n              item.get('dateServiceFrom').setErrors({\n                required: true\n              });\n            }\n          });\n          item.get('dateServiceTo').valueChanges.pipe(distinctUntilChanged()).subscribe(dateServiceTo => {\n            if (!!dateServiceTo) {\n              if (!!item.get('dateServiceFrom').value) {\n                if (new Date(dateServiceTo).getTime() < new Date(item.get('dateServiceFrom').value).getTime()) {\n                  item.get('dateServiceTo').setErrors({\n                    invalidDate: true\n                  });\n                } else {\n                  item.get('dateServiceTo').setErrors(null);\n                  item.get('dateServiceFrom').updateValueAndValidity();\n                }\n              } else {\n                item.get('dateServiceTo').setErrors(null);\n              }\n            } else {\n              item.get('dateServiceTo').setErrors({\n                required: true\n              });\n            }\n          });\n        }\n      }\n    }\n\n    placeOfServiceChanges(e) {\n      if (!!this.serviceLineInfo.controls) {\n        for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {\n          this.serviceLine.at(index).get('locationOfService').patchValue(!!e?.value ? e.value : null);\n        }\n      }\n    }\n\n    fetchCPT() {\n      let result = [];\n      let dosFrom;\n\n      if (this.claimFormData.isAddClaim) {\n        dosFrom = new Date(this.claimFormData?.profileMember?.dOSFrom).getTime();\n      } else if (!!this.claimFormData?.claimViewModel) {\n        dosFrom = new Date(this.claimFormData?.claimViewModel?.claimDosfrom).getTime();\n      }\n\n      for (const item of this.cptCodes) {\n        let add_date = new Date(item.add_date).getTime();\n        let term_date = new Date(item.term_date).getTime();\n\n        if ((add_date < dosFrom || add_date == dosFrom) && (term_date > dosFrom || term_date == dosFrom) && !!!result.find(e => e.mdmCode == item.mdmCode)) {\n          result.push(item);\n        }\n      }\n\n      this.cPTCodes = result;\n\n      if (this.claimFormData.isEditClaim) {\n        this.validateInvalidCPT();\n      }\n    }\n\n    fetchCPTData() {\n      let cpt = localStorage.getItem(LocalStorageKey.allCPT);\n\n      if (!!cpt) {\n        this.cptCodes = JSON.parse(JSLZString.decompress(cpt));\n        this.fetchCPT();\n      } else {\n        this.cptservice.fetchAllCpt().subscribe(res => {\n          if (!!res) {\n            this.cptCodes = res;\n            this.fetchCPT();\n          }\n        });\n      }\n    }\n\n    setYesOrNo(data) {\n      if (data === '' || data === 'N' || data === 'No') {\n        return 'No';\n      }\n\n      return 'Yes';\n    }\n\n    patchServiceLineData() {\n      let count = 1;\n      this.claimFormData?.claimViewModel?.claimsProfessional837?.serviceLineProfessional837s.forEach(ele => {\n        let date = ele.dtp03ServiceDate;\n        let fromDate = date.substring(0, 4) + \"-\" + date.substring(4, 6) + \"-\" + date.substring(6, 8);\n        let toDate = fromDate;\n        if (date.substring(9, 13) && date.substring(13, 15) && date.substring(15, 17)) toDate = date.substring(9, 13) + \"-\" + date.substring(13, 15) + \"-\" + date.substring(15, 17);\n        const empGroup = this.serviceLineform.group({\n          dateServiceFrom: [{\n            value: fromDate,\n            disabled: true\n          }],\n          dateServiceTo: [{\n            value: toDate,\n            disabled: true\n          }],\n          locationOfService: [{\n            value: ele.sv105PlaceOfServiceCode,\n            disabled: this.claimFormData.isViewClaim\n          }, Validators.required],\n          emg: [{\n            value: this.setYesOrNo(ele.sv109EmergencyIndicator),\n            disabled: this.claimFormData.isViewClaim\n          }],\n          desc: [{\n            value: ele.sv10107ServiceDescription\n          }],\n          cpt: new FormControl(ele.sv10102ProcedureCode, [Validators.required]),\n          m1: new FormControl(ele.sv10103ProcedureModifier1, modifierValidator),\n          m2: new FormControl(ele.sv10104ProcedureModifier2, modifierValidator),\n          m3: new FormControl(ele.sv10105ProcedureModifier3, modifierValidator),\n          m4: new FormControl(ele.sv10106ProcedureModifier4, modifierValidator),\n          diagnosispointer1: new FormControl(Number(ele.sv10701DiagnosisCodePointer1).toString(), [Validators.required]),\n          diagnosispointer2: new FormControl(ele.sv10702DiagnosisCodePointer2 ? Number(ele.sv10702DiagnosisCodePointer2).toString() : null),\n          diagnosispointer3: new FormControl(ele.sv10703DiagnosisCodePointer3 ? Number(ele.sv10703DiagnosisCodePointer3).toString() : null),\n          diagnosispointer4: new FormControl(ele.sv10704DiagnosisCodePointer4 ? Number(ele.sv10704DiagnosisCodePointer4).toString() : null),\n          unitCharges: [{\n            value: Number.parseFloat(ele.sv102LineItemChargeAmount).toFixed(2),\n            disabled: this.claimFormData.isViewClaim\n          }, Validators.required],\n          dayUnitChanges: [{\n            value: ele.sv104ServiceUnitCount,\n            disabled: this.claimFormData.isViewClaim\n          }, [Validators.required]],\n          total: [{\n            value: (Number.parseFloat(ele.sv104ServiceUnitCount) * Number.parseFloat(ele.sv102LineItemChargeAmount)).toFixed(2),\n            disabled: true\n          }],\n          ePSDT: [{\n            value: this.setYesOrNo(ele.sv111EpsdtIndicator),\n            disabled: this.claimFormData.isViewClaim\n          }],\n          jRenderingProviderId: [{\n            value: this.claimFormData?.claimViewModel?.claimsProfessional837?.nm109RenderingProviderIdentifier,\n            disabled: true\n          }, Validators.maxLength(10), Validators.minLength(10)],\n          proceduceC: ele.sv10101ProductServiceIdQualifier,\n          proceduceCount: count,\n          ndcUnitPrice: new FormControl(ele.cpt03NationalDrugUnitPrice, [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n          lineNote: ele.nte02LineNoteText,\n          ndcQtyQual: ele.ctp0501UnitMeasurementCode,\n          anesStart: ele.anesStart,\n          ndcQty: new FormControl(ele.ctp04NationalDrugUnitCount, [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n          anesStop1: ele.anesStop,\n          anesStop2: [{\n            value: '',\n            disabled: this.claimFormData.isViewClaim\n          }],\n          anesStop3: [{\n            value: '',\n            disabled: this.claimFormData.isViewClaim\n          }],\n          ndcQual: ele.lin02NationalDrugCodeQlfr,\n          ndcCode: ele.lin03NationalDrugCode\n        });\n        this.serviceLine.push(empGroup);\n        count = count + 1;\n      });\n\n      if (this.claimFormData.isEditClaim || this.claimFormData.isAddClaim) {\n        for (let i = 0; i < this.serviceLine.controls.length; i++) {\n          this.ndcQualChange(i);\n          this.changeValuesToMarkValidation(i);\n        }\n\n        this.cptValidatorData = this.claimData?.cptValidator;\n        this.validateCPTCodeDOS();\n      }\n    }\n\n    dosFromChangedEvent(index, field) {\n      this.dosFromChanged.emit();\n      this.checkDateValidations(index, field);\n      this.twoYearValidationForDate(index);\n    }\n\n    checkDateFromValidation(index, field) {\n      let fromDate = this.serviceLine.at(index).get(field).value;\n\n      if (!!fromDate && !!new Date(fromDate)) {\n        if (new Date(fromDate).getTime() < this.minDate.getTime()) {\n          this.serviceLine.at(index).get(field).setErrors({\n            'invalidFromDate': true\n          });\n        } else {\n          if (this.serviceLine.at(index).get(field).hasError('invalidFromDate')) {\n            delete this.serviceLine.at(index).get(field).errors['invalidFromDate'];\n            this.serviceLine.at(index).get(field).updateValueAndValidity();\n          }\n        }\n      } else {\n        if (this.serviceLine.at(index).get(field).hasError('invalidFromDate')) {\n          delete this.serviceLine.at(index).get(field).errors['invalidFromDate'];\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\n        }\n      }\n    }\n\n    twoYearValidationForDate(index) {\n      var _this = this;\n\n      if (!!this.serviceLine.at(index).get('dateServiceFrom').value) {\n        let fromDate = new Date(this.serviceLine.at(index).get('dateServiceFrom').value);\n        const days = moment(this.todayDate).diff(moment(fromDate), 'days');\n\n        if (days > 730) {\n          Swal.fire({\n            title: 'Alert',\n            text: \"The Date of Service is beyond 730 days (2 years). Do you want to continue?\",\n            icon: 'warning',\n            showCancelButton: false,\n            confirmButtonText: 'Yes',\n            showDenyButton: true,\n            denyButtonText: 'No',\n            reverseButtons: true\n          }).then( /*#__PURE__*/function () {\n            var _ref = _asyncToGenerator(function* (result) {\n              if (result.value == false) {\n                _this.serviceLine.at(index).get('dateServiceFrom').setValue(null);\n              }\n            });\n\n            return function (_x) {\n              return _ref.apply(this, arguments);\n            };\n          }());\n        }\n      }\n    }\n\n    checkDateValidations(index, field) {\n      let fromDate = this.serviceLine.at(index).get('dateServiceFrom').value;\n      let toDate = this.serviceLine.at(index).get('dateServiceTo').value;\n\n      if (!!fromDate && !!toDate && !!new Date(fromDate) && !!new Date(toDate)) {\n        if (new Date(fromDate).getTime() > new Date(toDate).getTime()) {\n          this.serviceLine.at(index).get(field).setErrors({\n            'invalidDate': true\n          });\n        } else {\n          if (this.serviceLine.at(index).get('dateServiceFrom').hasError('invalidDate')) {\n            delete this.serviceLine.at(index).get(field).errors['invalidDate'];\n            this.serviceLine.at(index).get(field).updateValueAndValidity();\n          }\n\n          if (this.serviceLine.at(index).get('dateServiceTo').hasError('invalidDate')) {\n            delete this.serviceLine.at(index).get(field).errors['invalidDate'];\n            this.serviceLine.at(index).get(field).updateValueAndValidity();\n          }\n\n          if (this.serviceLine.at(index).get(field).hasError('required')) {\n            delete this.serviceLine.at(index).get(field).errors['required'];\n            this.serviceLine.at(index).get(field).updateValueAndValidity();\n          }\n        }\n      } else {\n        if (this.serviceLine.at(index).get(field).hasError('currentIllnessError')) {\n          if (this.serviceLine.at(index).get(field).hasError('invalidDate')) {\n            delete this.serviceLine.at(index).get(field).errors['invalidDate'];\n            this.serviceLine.at(index).get(field).updateValueAndValidity();\n          }\n        } else {\n          this.serviceLine.at(index).get(field).setErrors(null);\n        }\n\n        if (!!!fromDate) {\n          this.serviceLine.at(index).get('dateServiceFrom').setErrors({\n            'required': true\n          });\n\n          if (this.serviceLine.at(index).get('dateServiceTo').hasError('invalidDate')) {\n            delete this.serviceLine.at(index).get('dateServiceTo').errors['invalidDate'];\n            this.serviceLine.at(index).get('dateServiceTo').updateValueAndValidity();\n          }\n        }\n\n        if (!!!toDate) {\n          this.serviceLine.at(index).get('dateServiceTo').setErrors({\n            'required': true\n          });\n\n          if (this.serviceLine.at(index).get('dateServiceFrom').hasError('invalidDate')) {\n            delete this.serviceLine.at(index).get('dateServiceFrom').errors['invalidDate'];\n            this.serviceLine.at(index).get('dateServiceFrom').updateValueAndValidity();\n          }\n        }\n      }\n\n      this.checkDateFromValidation(index, field);\n      this.dateShow();\n    }\n\n    ndcCodeValidator(control) {\n      let isValid = !!control.value && control.value.trim().length == 11 || control.value == null || control.value == '';\n\n      if (!!control.value) {\n        isValid = isValid && String(control.value).match(/[0-9]/g).length == control.value.length;\n      }\n\n      return isValid ? null : {\n        'isInvalid': true\n      };\n    }\n\n    ndcQualChange(index) {\n      if (!!this.ndcValidation(index).controls['ndcQual'].value && this.ndcValidation(index).controls['ndcQual'].value.toLowerCase() == 'n4') {\n        this.ndcValidation(index).controls['ndcCode'].setValidators(this.ndcCodeValidator);\n\n        if (!this.isNdcCodeValid(this.ndcValidation(index).controls['ndcCode'].value)) {\n          this.ndcValidation(index).controls['ndcCode'].setErrors({\n            'isInvalid': true\n          });\n        }\n      } else {\n        this.ndcValidation(index).controls['ndcCode'].removeValidators(this.ndcCodeValidator);\n\n        if (this.serviceLine.at(index).get('ndcCode').hasError('isInvalid')) {\n          delete this.serviceLine.at(index).get('ndcCode').errors['isInvalid'];\n        }\n      }\n\n      this.serviceLine.at(index).get('ndcCode').updateValueAndValidity();\n    }\n\n    isNdcCodeValid(value) {\n      let isValid = !!value && value.trim().length == 11 || value == null || value == '';\n\n      if (!!value) {\n        isValid = isValid && String(value).match(/[0-9]/g).length == value.length;\n      }\n\n      return isValid;\n    }\n\n    servicelineEdit(e) {\n      if (e.target.checked) {\n        this.addServiceShow = true;\n\n        for (let item of this.serviceLine.controls) {\n          this.dateServiceFrom.push(new Date(item.get('dateServiceFrom').value).toString());\n          this.dateServiceTo.push(new Date(item.get('dateServiceTo').value).toString()); // item.get('dateServiceFrom').enable({ onlySelf: true });\n          // item.get('dateServiceTo').enable({ onlySelf: true });\n        } //submitValidateAllFields.validateDisableControl(this.serviceLineInfo, [\"dateServiceFrom\"]);\n\n      } else {\n        this.addServiceShow = false;\n\n        for (let item of this.serviceLine.controls) {// item.get('dateServiceFrom').disable({ onlySelf: true });\n          // item.get('dateServiceTo').disable({ onlySelf: true });\n        }\n      }\n    }\n\n    dateShow() {\n      this.dateServiceFrom = [];\n      this.dateServiceTo = [];\n\n      for (let item of this.serviceLine.controls) {\n        this.dateServiceFrom.push(new Date(item.get('dateServiceFrom').value).toString());\n        this.dateServiceTo.push(new Date(item.get('dateServiceTo').value).toString());\n      }\n\n      let min = this.dateServiceFrom[0];\n      let max = this.dateServiceFrom[0];\n      this.dateServiceFrom.forEach(function (v) {\n        max = new Date(v) > new Date(max) ? v : max;\n        min = new Date(v) < new Date(min) ? v : min;\n      });\n      this.oldServiceDateFrom = new Date(min).toString();\n      this.oldServiceDateTo = new Date(max).toString();\n    }\n\n    onSearchQualifer(term, item) {\n      term = term.toLocaleLowerCase();\n      return item['qualifier'].toLocaleLowerCase().indexOf(term) > -1 || item['description'].toLocaleLowerCase().indexOf(term) > -1;\n    }\n\n    validateCPTCode() {\n      for (let i = 0; i < this.serviceLine.length; i++) {\n        for (let k = 1; k <= 4; k++) {\n          if (this.serviceLine.at(i).get('diagnosispointer' + k).errors?.dublicate) {\n            this.serviceLine.at(i).get('diagnosispointer' + k).setErrors(null);\n          }\n        }\n      }\n\n      for (let i = 0; i < this.serviceLine.length; i++) {\n        for (let h = 1; h <= this.serviceLine.length - 1; h++) {\n          if (this.serviceLine.at(i).get('cpt').value === this.serviceLine.at(h).get('cpt').value) {\n            for (let j = 1; j <= 4; j++) {\n              for (let k = 1; k <= 4; k++) {\n                if (i != h && this.serviceLine.at(i).get('diagnosispointer' + j).value && this.serviceLine.at(h).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('diagnosispointer' + j).value === this.serviceLine.at(h).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('cpt').value === this.serviceLine.at(h).get('cpt').value) {\n                  this.serviceLine.at(i).get('diagnosispointer' + j).setErrors({\n                    dublicate: true\n                  });\n                  this.serviceLine.at(h).get('diagnosispointer' + k).setErrors({\n                    dublicate: true\n                  });\n                  this.serviceLine.at(i).get('diagnosispointer' + j).markAsTouched();\n                  this.serviceLine.at(h).get('diagnosispointer' + k).markAsTouched();\n                }\n              }\n            }\n          }\n        }\n      }\n\n      for (let i = 0; i < this.serviceLine.length; i++) {\n        for (let j = 1; j <= 4; j++) {\n          for (let k = 1; k <= 4; k++) {\n            if (j != k && this.serviceLine.at(i).get('diagnosispointer' + j).value && this.serviceLine.at(i).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('diagnosispointer' + j).value === this.serviceLine.at(i).get('diagnosispointer' + k).value) {\n              if (!this.serviceLine.at(i).get('diagnosispointer' + k).errors) {\n                this.serviceLine.at(i).get('diagnosispointer' + k).setErrors({\n                  dublicate: true\n                });\n                this.serviceLine.at(i).get('diagnosispointer' + i).markAsTouched();\n              }\n            }\n          }\n        }\n      }\n    }\n\n    validateInvalidCPT() {\n      let countMatched = 0;\n\n      for (let index = 0; index < this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length; index++) {\n        if (this.cptCodes.filter(t => t.cpt == this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[index].sv10102ProcedureCode).length > 0) {\n          countMatched = countMatched + 1;\n        } else {\n          this.cptservice.fetchchCPTCodeSeach(this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[index].sv10102ProcedureCode, this.claimFormData.claimViewModel.claimDosfrom, '').subscribe(res => {\n            countMatched = countMatched + 1;\n            res.forEach(element => {\n              this.cPTCodes.push(element);\n            });\n            this.patchValueMatched(countMatched);\n          });\n        }\n      }\n\n      this.patchValueMatched(countMatched);\n    }\n\n    patchValueMatched(countMatched) {\n      let totalCount = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length;\n\n      if (totalCount === countMatched) {\n        this.patchServiceLineData();\n      }\n    }\n\n    validateCPTCodeDOS() {\n      // let cptData: ValidateCPTRequest[] = [];\n      // for (let i = 0; i <= this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length - 1; i++) {\n      //   let cptDosData: ValidateCPTRequest = { cpt: '', dos: '' };\n      //   let date = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[i].dtp03ServiceDate;\n      //   let fromDate = date.substring(6, 8) + \"/\" + date.substring(4, 6) + \"/\" + date.substring(0, 4);\n      //   cptDosData.cpt = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[i].sv10102ProcedureCode;\n      //   cptDosData.dos = fromDate;\n      //   cptData.push(cptDosData);\n      // }\n      let res = this.cptValidatorData; // this.cptservice.CPTValidorNot(cptData).subscribe((res: AllCPTCode[]) => {\n\n      for (let i = 0; i <= this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length - 1; i++) {\n        if (res.filter(t => t.cpt === this.serviceLine.at(i).get('cpt').value).length === 0) {\n          this.serviceLine.at(i).get('cpt').setErrors({\n            invalidCpt: true\n          });\n          res[i].cpt = this.serviceLine.at(i).get('cpt').value;\n          this.cPTCodes.push(res[i]);\n          this.invalidCptCode.push(this.serviceLine.at(i).get('cpt').value);\n        }\n      }\n\n      if (this.serviceLine.invalid) {\n        submitValidateAllFields.validateAllFields(this.serviceLineInfo);\n        return;\n      } //})\n\n    }\n\n    cptViewHistory() {\n      let request;\n\n      if (this.claimFormData.isEditClaim) {\n        if (this.serviceLine.controls.length > 0) {\n          if (!!this.serviceLine.controls[0].value.dateServiceFrom?._d) {\n            request = {\n              subscribeID: this.claimFormData.claimViewModel.subscribeId,\n              dos: this.serviceLine.controls[0].value.dateServiceFrom._d\n            };\n          } else {\n            request = {\n              subscribeID: this.claimFormData.claimViewModel.subscribeId,\n              dos: this.serviceLine.controls[0].value.dateServiceFrom\n            };\n          }\n        }\n      } else if (this.claimFormData.isAddClaim) {\n        request = {\n          subscribeID: this.claimFormData.profileMember.subscriberID,\n          dos: this.claimFormData.profileMember.dOSFrom\n        };\n      }\n\n      this.cptservice.getCPTViewHistory(request).subscribe(resp => {\n        let cptList = [];\n\n        if (resp.statusCode == 200 && (resp.content || []).length > 0) {\n          cptList = resp.content;\n          cptList = cptList.map(cpt => ({ ...cpt,\n            isSelected: false\n          }));\n        }\n\n        const existingServiceLines = this.serviceLine.value;\n        existingServiceLines?.forEach(serviceLine => {\n          cptList.filter(cptItem => cptItem.cpt === serviceLine.cpt).forEach(cpt => {\n            cpt.isSelected = true;\n          });\n        });\n        this.viewHistoryCPTList = cptList;\n        let dialogRef = this.dialog.open(CptViewHistoryComponent, {\n          height: '650px',\n          width: '1100px',\n          autoFocus: false,\n          restoreFocus: false,\n          maxHeight: '90vh',\n          panelClass: 'custom-dialog-containers',\n          data: {\n            cptList: cptList\n          }\n        });\n        dialogRef.afterClosed().subscribe(data => {\n          if (!!data && (data.selectedCPTCodes || []).length > 0) {\n            this.mapServiceLinesSelecteCPCodes(data.selectedCPTCodes);\n          } // if selected cpts is zero then index 0 in existing service line cpt should be empty and remove remaining all.\n          else if (!!data && (data.selectedCPTCodes || []).length == 0) {\n            if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\n              const servicelineMatchedFromViewHistoryCPTCodes = this.serviceLine.value.filter(serviceLineItem => this.viewHistoryCPTList.some(selecteItm => serviceLineItem.cpt === selecteItm.cpt));\n\n              if (!!servicelineMatchedFromViewHistoryCPTCodes && servicelineMatchedFromViewHistoryCPTCodes.length > 0) {\n                servicelineMatchedFromViewHistoryCPTCodes.forEach(item => {\n                  const index = this.serviceLine.controls.findIndex(control => control.value.cpt === item.cpt);\n\n                  if (index == 0) {\n                    let charges = this.getUnitChargesByPayerId();\n                    let cptCharge = this.allCPTCharges.filter(item => item.code == item.cpt)[0]?.charge;\n\n                    if (!!cptCharge) {\n                      charges = cptCharge;\n                    }\n\n                    this.serviceLine.at(index).patchValue({\n                      cpt: null,\n                      unitCharges: Number.parseFloat(charges).toFixed(2)\n                    });\n                  } else {\n                    this.serviceLine.removeAt(index);\n                  }\n                });\n              } // this.serviceLine.controls[0].patchValue({\n              //   cpt: null,\n              //   dayUnitChanges: '0.00'\n              // });\n              // this.isShowDelete = false;\n              // while (this.serviceLine.controls.length > 1) {\n              //   this.serviceLine.removeAt(this.serviceLine.controls.length - 1);  // Remove last control\n              // }\n\n            }\n          }\n        });\n      });\n    }\n\n    mapServiceLinesSelecteCPCodes(selectedCPTCodes) {\n      let getExistingFirstServiceLine;\n\n      if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\n        getExistingFirstServiceLine = this.serviceLine.controls[0]; // copy this values to auto fill to new adding service\n        /// filter  Unused  servicelines based on selected CPT and remove it from servicelines controls.\n\n        if (selectedCPTCodes.length > 0 && this.serviceLine.controls.length > 0) {\n          const servicelineNoMatchedFromSelectedCPTCodes = this.serviceLine.value.filter(serviceLineItem => !selectedCPTCodes.some(selecteItm => serviceLineItem.cpt === selecteItm.cpt)); // Remove unMatched values from serviceLines Controls.      \n\n          servicelineNoMatchedFromSelectedCPTCodes.forEach(item => {\n            if (!!item.cpt) {\n              const isViewHistoryItem = this.viewHistoryCPTList.find(cpt => cpt.cpt == item.cpt); // Find the index of the control where the 'id' matches\n\n              const index = this.serviceLine.controls.findIndex(control => control.value.cpt === item.cpt); // Remove the control if found\n\n              if (index !== -1 && !!isViewHistoryItem) {\n                let charges = this.getUnitChargesByPayerId();\n                let cptCharge = this.allCPTCharges.filter(item => item.code == item.cptCode)[0]?.charge;\n\n                if (!!cptCharge) {\n                  charges = cptCharge;\n                }\n\n                if (index == 0) {\n                  this.serviceLine.at(index).patchValue({\n                    cpt: null,\n                    unitCharges: Number.parseFloat(charges).toFixed(2)\n                  });\n                } else {\n                  this.serviceLine.removeAt(index);\n                }\n              }\n            }\n          });\n        }\n\n        let existingServiceLines = this.serviceLine.value; // get selected cptCodes except from existingservice lines.\n\n        selectedCPTCodes = selectedCPTCodes.filter(selectedItem => !existingServiceLines.some(existingItem => selectedItem.cpt === existingItem.cpt)); // if existing service line is one with dates , selected cpt is one if both are different       \n\n        if (selectedCPTCodes.length == 1 && existingServiceLines.length == 1) {\n          // if existing cpt is null update existing row of cpt.\n          if (existingServiceLines.length == 1 && !existingServiceLines[0].cpt) {\n            this.serviceLine.controls[0].patchValue({\n              cpt: selectedCPTCodes[0].cpt,\n              desc: selectedCPTCodes[0].shortDescription\n            });\n            this.OnCPTCodeChange(selectedCPTCodes[0].cpt, 0);\n            selectedCPTCodes.shift(); // 0 index remove\n          }\n        }\n      } // for new selected cpts addding to service lines.\n\n\n      selectedCPTCodes.forEach(cptCodeDetails => {\n        this.patchSelecteCPTCodesFromHistory(cptCodeDetails, getExistingFirstServiceLine);\n      });\n    }\n\n    patchSelecteCPTCodesFromHistory(cptCodeDetails, getExistingFirstServiceLine) {\n      let locationOfService;\n      let topSelectedServiceLine;\n\n      if (!!getExistingFirstServiceLine) {\n        locationOfService = getExistingFirstServiceLine.value.locationOfService;\n        topSelectedServiceLine = getExistingFirstServiceLine.getRawValue();\n      }\n\n      if (!!cptCodeDetails.cpt) {\n        // existing service line with empty cptCode patch values\n        const index = this.serviceLine.controls.findIndex(control => control.value.cpt === null);\n\n        if (index !== -1) {\n          this.serviceLine.at(index).patchValue({\n            cpt: cptCodeDetails.cpt,\n            desc: cptCodeDetails.shortDescription\n          });\n          this.OnCPTCodeChange(cptCodeDetails.cpt, index);\n        } else {\n          let unitCharges = this.getUnitChargesByPayerId();\n          let total = unitCharges;\n          let cptCharge = this.allCPTCharges.filter(item => item.code == cptCodeDetails.cpt)[0]?.charge;\n\n          if (!!cptCharge) {\n            unitCharges = cptCharge;\n            total = (cptCharge * 1).toFixed(2);\n          }\n\n          const dOSFrom = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSFrom) : new Date(this.claimFormData?.claimViewModel.claimDosfrom);\n          const dOSTo = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSTo) : new Date(this.claimFormData?.claimViewModel.claimDosto);\n          const empGroup = this.serviceLineform.group({\n            dateServiceFrom: new FormControl({\n              value: dOSFrom,\n              disabled: true\n            }, [Validators.required]),\n            dateServiceTo: new FormControl({\n              value: dOSTo,\n              disabled: true\n            }, [Validators.required]),\n            locationOfService: new FormControl(!!locationOfService ? locationOfService : null, Validators.required),\n            emg: new FormControl(null),\n            desc: new FormControl(cptCodeDetails.shortDescription),\n            cpt: new FormControl(cptCodeDetails.cpt, [Validators.required]),\n            m1: [{\n              value: '',\n              disabled: false\n            }, modifierValidator],\n            m2: [{\n              value: '',\n              disabled: false\n            }, modifierValidator],\n            m3: [{\n              value: '',\n              disabled: false\n            }, modifierValidator],\n            m4: [{\n              value: '',\n              disabled: false\n            }, modifierValidator],\n            diagnosispointer1: new FormControl('1', [Validators.required]),\n            diagnosispointer2: [{\n              value: '',\n              disabled: false\n            }],\n            diagnosispointer3: [{\n              value: '',\n              disabled: false\n            }],\n            diagnosispointer4: [{\n              value: '',\n              disabled: false\n            }],\n            charges: new FormControl(Number.parseFloat(unitCharges).toFixed(2), Validators.required),\n            dayUnitChanges: new FormControl('1', Validators.required),\n            unitCharges: new FormControl({\n              value: Number.parseFloat(unitCharges).toFixed(2),\n              disabled: false\n            }, [Validators.required]),\n            total: [{\n              value: total,\n              disabled: true\n            }],\n            ePSDT: [{\n              value: null,\n              disabled: false\n            }],\n            jRenderingProviderId: [{\n              value: topSelectedServiceLine.jRenderingProviderId,\n              disabled: true\n            }, [Validators.maxLength(10), Validators.minLength(10)]],\n            proceduceC: '',\n            proceduceCount: this.serviceLine.length + 1,\n            ndcUnitPrice: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n            lineNote: '',\n            ndcQtyQual: '',\n            anesStart: '',\n            ndcQty: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n            anesStop1: '',\n            anesStop2: '',\n            anesStop3: '',\n            ndcQual: '',\n            ndcCode: ''\n          });\n          this.serviceLine.push(empGroup);\n          this.calculateTotal.emit();\n          this.dateValidators();\n\n          if (this.serviceLine.invalid) {\n            submitValidateAllFields.validateAllFields(this.serviceLineInfo);\n            return;\n          }\n        }\n      }\n    }\n\n    getUnitChargesByPayerId() {\n      let charges = PriceCost.zeroPrice;\n\n      if (!!this.claimFormData.claimViewModel) {\n        charges = this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n      } else if (this.claimFormData.isAddClaim) {\n        charges = this.claimFormData.payerItem.payerId === \"41212\" || this.claimFormData.payerItem?.payerId === \"20133\" || this.claimFormData.payerItem?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n      }\n\n      return charges;\n    }\n\n  }\n\n  ServiceLineClaimComponent.ɵfac = function ServiceLineClaimComponent_Factory(t) {\n    return new (t || ServiceLineClaimComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AllPlaceOfServicesService), i0.ɵɵdirectiveInject(i3.SubjectService), i0.ɵɵdirectiveInject(i4.GetAllCPTCodeService), i0.ɵɵdirectiveInject(i5.MatDialog), i0.ɵɵdirectiveInject(i6.CacheService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i7.NgxSpinnerService));\n  };\n\n  ServiceLineClaimComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ServiceLineClaimComponent,\n    selectors: [[\"app-service-line-claim\"]],\n    inputs: {\n      serviceLineInfos: \"serviceLineInfos\",\n      memberProfile: \"memberProfile\",\n      claimFormData: \"claimFormData\",\n      claimData: \"claimData\",\n      allPlaceOfServices: \"allPlaceOfServices\"\n    },\n    outputs: {\n      diagnosisPointerBlur: \"diagnosisPointerBlur\",\n      dosFromChanged: \"dosFromChanged\",\n      calculateTotal: \"calculateTotal\"\n    },\n    decls: 58,\n    vars: 8,\n    consts: [[1, \"service-line-dashboard\", 3, \"formGroup\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"form-title\"], [1, \"col-md-2\", \"radio-flex\"], [1, \"col-md-4\", \"radio-flex\"], [1, \"form-check\", \"form-check-inline\", \"radio-flex\"], [\"type\", \"radio\", \"formControlName\", \"showNDC\", \"value\", \"No\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"OutsideLabYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"showNDC\", \"value\", \"Yes\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"OutsideLabNo\", 1, \"create-claim-radio-labels\"], [1, \"row\", \"mt-2\"], [1, \"col-md\"], [1, \"24\"], [1, \"table-responsive\"], [1, \"table\", \"table-bordered\", 3, \"ngStyle\"], [\"colspan\", \"2\", 2, \"font-size\", \"12px\"], [\"scope\", \"col\", \"rowspan\", \"2\", 2, \"font-size\", \"12px\"], [\"scope\", \"col\", \"rowspan\", \"2\", 2, \"font-size\", \"12px\", \"max-width\", \"6rem !important\"], [\"scope\", \"col\", \"rowspan\", \"2\", 2, \"font-size\", \"12px\", \"max-width\", \"9rem !important\"], [\"style\", \"font-size:12px;\", \"scope\", \"col\", \"rowspan\", \"2\", 4, \"ngIf\"], [\"scope\", \"col\", 2, \"font-size\", \"12px\"], [\"href\", \"javascript:void(0);\", \"id\", \"viewCptCodesHistory\", \"class\", \"view-history\", \"title\", \"View CPT History\", 3, \"click\", 4, \"ngIf\"], [\"formArrayName\", \"serviceLines\", 2, \"border\", \"inherit !important\"], [\"style\", \"display: contents;\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"class\", \"btn-primary primary-btn btn-height\", 3, \"click\", 4, \"ngIf\"], [\"href\", \"javascript:void(0);\", \"id\", \"viewCptCodesHistory\", \"title\", \"View CPT History\", 1, \"view-history\", 3, \"click\"], [1, \"fa\", \"fa-history\"], [2, \"display\", \"contents\", 3, \"formGroupName\"], [4, \"ngIf\"], [1, \"col\"], [\"matInput\", \"\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"dateServiceFrom\", \"placeholder\", \"MM/DD/YYYY\", 3, \"min\", \"max\", \"matDatepicker\", \"ngClass\", \"click\", \"blur\", 4, \"ngIf\"], [\"datepickerFrom\", \"\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"form-control\", 4, \"ngIf\"], [\"matInput\", \"\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"dateServiceTo\", \"placeholder\", \"MM/DD/YYYY\", 3, \"min\", \"max\", \"matDatepicker\", \"ngClass\", \"click\", \"blur\", 4, \"ngIf\"], [\"datepickerTo\", \"\"], [2, \"vertical-align\", \"top\"], [\"class\", \"form-control form-control-sm wdt-100\", \"placeholder\", \"Location Of Service\", \"appendTo\", \"body\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"locationOfService\", 3, \"ngClass\", \"change\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"placeholder\", \"EMG\", \"appendTo\", \"body\", \"formControlName\", \"emg\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm wdt-100\", \"placeholder\", \"CPT  Type 3 letter\", \"appendTo\", \"body\", \"bindLabel\", \"shortDescription\", \"bindValue\", \"cpt\", \"formControlName\", \"cpt\", 3, \"virtualScroll\", \"ngClass\", \"ngModelChange\", 4, \"ngIf\"], [2, \"max-width\", \"20rem !important\"], [1, \"table-flex\", \"m-2\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"m1\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"M1\", \"maxlength\", \"2\", 3, \"disabled\", \"id\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"m2\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M2\", 3, \"disabled\", \"id\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"m3\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M3\", 3, \"disabled\", \"id\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"m4\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M4\", 3, \"disabled\", \"id\", \"ngClass\", 4, \"ngIf\"], [1, \"table-flex\", \"mt-2\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"diagnosispointer1\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D1\", 3, \"disabled\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"diagnosispointer2\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D2\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"diagnosispointer3\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D3\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"diagnosispointer4\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D4\", 3, \"ngClass\", 4, \"ngIf\"], [2, \"max-width\", \"6rem !important\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"numbersTwoDecimalOnly\", \"\", \"formControlName\", \"unitCharges\", 3, \"ngClass\", \"blur\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"numbersOnly\", \"\", \"formControlName\", \"dayUnitChanges\", 3, \"ngClass\", \"blur\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"placeholder\", \"\", \"formControlName\", \"total\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"placeholder\", \"EPSDT\", \"appendTo\", \"body\", \"formControlName\", \"ePSDT\", 4, \"ngIf\"], [1, \"d-flex\", \"mt-2\", 2, \"font-size\", \"12px\", 3, \"ngClass\"], [2, \"max-width\", \"9rem !important\"], [1, \"mt-2\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"placeholder\", \"\", \"maxlength\", \"10\", \"required\", \"\", \"formControlName\", \"jRenderingProviderId\", 3, \"ngClass\", \"input\", 4, \"ngIf\"], [2, \"font-size\", \"12px\", \"vertical-align\", \"middle\"], [\"class\", \"form-control form-control-sm\", \"placeholder\", \"Procedure Code Type\", \"formControlName\", \"proceduceC\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"proceduceCount\", 4, \"ngIf\"], [\"rowspan\", \"2\", \"colspan\", \"11\", 2, \"font-size\", \"12px\"], [1, \"row\", 2, \"margin\", \"0\", \"margin-top\", \"0.5rem !important\"], [1, \"col-1\", 3, \"ngClass\"], [1, \"col-1\", \"no-padding\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"lineNote\", \"placeholder\", \"LINE NOTE\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"anesStart\", \"placeholder\", \"Anes Start\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"anesStop1\", \"placeholder\", \"ANES STOP\", 4, \"ngIf\"], [1, \"col-3\", \"no-padding\"], [\"class\", \"form-control form-control-sm\", \"appendTo\", \"body\", \"placeholder\", \"NDC Qualifer\", \"bindLabel\", \"description\", \"bindValue\", \"qualifier\", \"formControlName\", \"ndcQual\", 3, \"items\", \"searchFn\", \"ngClass\", \"change\", 4, \"ngIf\"], [1, \"col-1\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"ndcCode\", \"placeholder\", \"NDC Code\", 3, \"ngClass\", \"change\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"ndcQty\", \"numbersThreeDecimalOnly\", \"\", \"placeholder\", \"NDC Qty\", 3, \"ngClass\", \"change\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"appendTo\", \"body\", \"placeholder\", \"NDC Qty Qual\", \"bindLabel\", \"description\", \"bindValue\", \"qualifier\", \"formControlName\", \"ndcQtyQual\", 3, \"items\", \"searchFn\", \"ngClass\", \"change\", \"clear\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"anesStop2\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"anesStop3\", 4, \"ngIf\"], [\"placeholder\", \"Procedure Code Type\", \"formControlName\", \"proceduceC\", 1, \"form-control\", \"form-control-sm\"], [1, \"form-control\"], [\"formControlName\", \"proceduceCount\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"lineNote\", \"placeholder\", \"LINE NOTE\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"anesStart\", \"placeholder\", \"Anes Start\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"anesStop1\", \"placeholder\", \"ANES STOP\", 1, \"form-control\", \"form-control-sm\"], [\"appendTo\", \"body\", \"placeholder\", \"NDC Qualifer\", \"bindLabel\", \"description\", \"bindValue\", \"qualifier\", \"formControlName\", \"ndcQual\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"searchFn\", \"ngClass\", \"change\"], [\"assignto\", \"\"], [\"ng-option-tmp\", \"\", \"ng-label-tmp\", \"\"], [1, \"invalid-feedback\"], [\"formControlName\", \"ndcCode\", \"placeholder\", \"NDC Code\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\"], [\"formControlName\", \"ndcQty\", \"numbersThreeDecimalOnly\", \"\", \"placeholder\", \"NDC Qty\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"NDC Qty Qual\", \"bindLabel\", \"description\", \"bindValue\", \"qualifier\", \"formControlName\", \"ndcQtyQual\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"searchFn\", \"ngClass\", \"change\", \"clear\"], [\"formControlName\", \"anesStop2\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"anesStop3\", 1, \"form-control\", \"form-control-sm\"], [2, \"font-size\", \"12px\", 3, \"ngClass\"], [2, \"font-size\", \"12px\"], [\"class\", \"form-control form-control-sm\", \"numbersThreeDecimalOnly\", \"\", \"formControlName\", \"ndcUnitPrice\", \"placeholder\", \"NDC Unit Price \", 3, \"ngClass\", 4, \"ngIf\"], [\"numbersThreeDecimalOnly\", \"\", \"formControlName\", \"ndcUnitPrice\", \"placeholder\", \"NDC Unit Price \", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"matInput\", \"\", \"formControlName\", \"dateServiceFrom\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\", 3, \"min\", \"max\", \"matDatepicker\", \"ngClass\", \"click\", \"blur\"], [\"matInput\", \"\", \"formControlName\", \"dateServiceTo\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\", 3, \"min\", \"max\", \"matDatepicker\", \"ngClass\", \"click\", \"blur\"], [\"placeholder\", \"Location Of Service\", \"appendTo\", \"body\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"locationOfService\", 1, \"form-control\", \"form-control-sm\", \"wdt-100\", 3, \"ngClass\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [\"placeholder\", \"EMG\", \"appendTo\", \"body\", \"formControlName\", \"emg\", 1, \"form-control\", \"form-control-sm\"], [\"placeholder\", \"CPT  Type 3 letter\", \"appendTo\", \"body\", \"bindLabel\", \"shortDescription\", \"bindValue\", \"cpt\", \"formControlName\", \"cpt\", 1, \"form-control\", \"form-control-sm\", \"wdt-100\", 3, \"virtualScroll\", \"ngClass\", \"ngModelChange\"], [\"formControlName\", \"m1\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"M1\", \"maxlength\", \"2\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"id\", \"ngClass\"], [\"formControlName\", \"m2\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M2\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"id\", \"ngClass\"], [\"formControlName\", \"m3\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M3\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"id\", \"ngClass\"], [\"formControlName\", \"m4\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M4\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"id\", \"ngClass\"], [\"formControlName\", \"diagnosispointer1\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D1\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"ngClass\"], [\"formControlName\", \"diagnosispointer2\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D2\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"formControlName\", \"diagnosispointer3\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D3\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"formControlName\", \"diagnosispointer4\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D4\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"numbersTwoDecimalOnly\", \"\", \"formControlName\", \"unitCharges\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"blur\"], [\"type\", \"text\", \"numbersOnly\", \"\", \"formControlName\", \"dayUnitChanges\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"blur\"], [\"type\", \"text\", \"placeholder\", \"\", \"formControlName\", \"total\", 1, \"form-control\", \"form-control-sm\"], [\"placeholder\", \"EPSDT\", \"appendTo\", \"body\", \"formControlName\", \"ePSDT\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"\", \"maxlength\", \"10\", \"required\", \"\", \"formControlName\", \"jRenderingProviderId\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"input\"], [1, \"d-flex\", \"mt-2\"], [\"type\", \"button\", \"matTooltip\", \"Remove\", \"matTooltipPosition\", \"above\", \"class\", \"remove-button\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"matTooltip\", \"Remove\", \"matTooltipPosition\", \"above\", 1, \"remove-button\", 3, \"click\"], [\"type\", \"button\", 1, \"btn-primary\", \"primary-btn\", \"btn-height\", 3, \"click\"], [1, \"material-icons\", \"icon-margin\", \"icon-height\", \"icon-align\"]],\n    template: function ServiceLineClaimComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"p\", 3);\n        i0.ɵɵtext(4, \"24. Service Line\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(5, \"div\", 4);\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"input\", 7);\n        i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_Template_input_change_8_listener() {\n          return ctx.hideNDC();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"label\", 8);\n        i0.ɵɵtext(10, \"Hide NDC\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 6)(12, \"input\", 9);\n        i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_Template_input_change_12_listener() {\n          return ctx.showNDC();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"label\", 10);\n        i0.ɵɵtext(14, \"Show NDC\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(15, \"div\", 11)(16, \"div\", 12)(17, \"section\", 13)(18, \"div\", 14)(19, \"table\", 15)(20, \"thead\")(21, \"tr\")(22, \"th\", 16);\n        i0.ɵɵtext(23, \"A. Date(S) Of Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"th\", 17);\n        i0.ɵɵtext(25, \"B. Place Of Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"th\", 17);\n        i0.ɵɵtext(27, \"C. EMG\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"th\", 16);\n        i0.ɵɵtext(29, \"D. Procedures,Services Or Supplies\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"th\", 17);\n        i0.ɵɵtext(31, \"E. Diagnosis Pointer\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"th\", 18);\n        i0.ɵɵtext(33, \"F. Unit Charges ($)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"th\", 18);\n        i0.ɵɵtext(35, \"G. Days And Unit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"th\", 18);\n        i0.ɵɵtext(37, \"Charges ($)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"th\", 17);\n        i0.ɵɵtext(39, \"H. EPSDT\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"th\", 17);\n        i0.ɵɵtext(41, \"I. ID Qual\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"th\", 19);\n        i0.ɵɵtext(43, \"J. Rendering Provider ID\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(44, ServiceLineClaimComponent_th_44_Template, 1, 0, \"th\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"tr\")(46, \"th\", 21);\n        i0.ɵɵtext(47, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(48, \"th\", 21);\n        i0.ɵɵtext(49, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"th\", 21);\n        i0.ɵɵtext(51, \"CPT/HCPCS \");\n        i0.ɵɵtemplate(52, ServiceLineClaimComponent_a_52_Template, 2, 0, \"a\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"th\", 21);\n        i0.ɵɵtext(54, \"Modifier\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(55, \"tbody\", 23);\n        i0.ɵɵtemplate(56, ServiceLineClaimComponent_span_56_Template, 121, 75, \"span\", 24);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(57, ServiceLineClaimComponent_button_57_Template, 4, 0, \"button\", 25);\n        i0.ɵɵelementEnd()()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.serviceLineInfo);\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c2, !ctx.claimFormData.isViewClaim ? \"max-content\" : \"\"));\n        i0.ɵɵadvance(25);\n        i0.ɵɵproperty(\"ngIf\", ctx.serviceLine.length > 1 && ctx.claimFormData.isEditClaim);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isEditClaim || ctx.claimFormData.isAddClaim);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.serviceLine.controls);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isEditClaim || ctx.claimFormData.isAddClaim);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgStyle, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i9.NgSelectComponent, i9.NgOptionComponent, i9.NgOptionTemplateDirective, i9.NgLabelTemplateDirective, i10.MatTooltip, i11.OnlyNumberDirective, i12.OnlyWithDeciamlaThreeNumberDirective, i13.OnlyWithDeciamlaTwoNumberDirective, i14.MatDatepicker, i14.MatDatepickerInput, i8.UpperCasePipe, i8.DatePipe],\n    styles: [\".create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.search-icon-alignment[_ngcontent-%COMP%]{position:relative}.search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%], .claim-title[_ngcontent-%COMP%]{font-size:16px}}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}td[_ngcontent-%COMP%]{padding-right:.15rem;padding-left:.15rem;max-width:12rem!important}th[_ngcontent-%COMP%]{max-width:12rem!important}.no-padding[_ngcontent-%COMP%]{padding:0!important}.service-line-dashboard[_ngcontent-%COMP%]     .ng-select-container{height:100%!important}.service-line-dashboard[_ngcontent-%COMP%]     .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.form-control[_ngcontent-%COMP%]{height:1.36rem!important}tr[_ngcontent-%COMP%]{border-color:#dee2e6!important}table[_ngcontent-%COMP%]{border-bottom:1px solid #dee2e6!important}  .ng-option{white-space:break-spaces!important}.remove-button[_ngcontent-%COMP%]{background-color:transparent!important;border:none!important}.service-line-view-label[_ngcontent-%COMP%]{color:#617798;font-weight:600}span.form-control[_ngcontent-%COMP%]{text-align:center!important}.view-history[_ngcontent-%COMP%]{padding:2px 3px;background-color:#0d6efd;color:#fff}.view-history[_ngcontent-%COMP%]:hover{background-color:#191970!important}\"]\n  });\n  return ServiceLineClaimComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}