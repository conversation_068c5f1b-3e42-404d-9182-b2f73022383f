{"ast": null, "code": "import { DateFormatter, DateFormatterWithTime } from 'src/app/shared/functions/dateFormatterFunction';\nimport { DOSSortingDashboard, SortDateValues } from 'src/app/shared/functions/sort-for-dates';\nimport { OpenClaimActionRender } from \"../shared/Renderer/claim-grid/OpenClaimActionRenderer\";\nimport { DispatchedClaimActionRender } from \"../shared/Renderer/claim-grid/DispatchedClaimActionRenderer\";\nimport { UnAckByCHClaimActionRenderer } from \"../shared/Renderer/claim-grid/UnAckByCHClaimActionRenderer\";\nimport { RejectedByCHActionRenderer } from \"../shared/Renderer/claim-grid/RejectedByCHActionRenderer\";\nimport { EobRecievedClaimActionRenderer } from \"../shared/Renderer/claim-grid/EOBRecievedClaimActionRenderer\";\nimport ChildClaimIndicatorRenderer from \"../shared/Renderer/claim-grid/ChildClaimIndicatorRenderer\";\nimport { AcceptedClaimActionRenderer } from \"../shared/Renderer/claim-grid/AcceptedClaimActionRenderer\";\nimport { AcceptedClaimLockerActionRenderer } from \"../shared/Renderer/claim-grid/AcceptedClaimLockerActionRenderer\";\nimport { CLAIM_TYPE, COMMON_METHODS, COMMON_VALUES } from \"./common-static\";\nimport InActiveClaimColorIndicatorRenderer from \"../shared/Renderer/claim-grid/InActiveClaimColorIndicatorRenderer\";\nimport ChildClaimDeActiveClaimIndicatorRenderer from \"../shared/Renderer/claim-grid/ClaimColorIndicatorRenderer\";\nimport { OnHoldReasonActionRenderer } from \"../shared/Renderer/claim-grid/OnHoldReasonActionRenderer\";\nimport InActiveClaimStatusColorIndicatorRenderer from \"../shared/Renderer/claim-grid/InActiveClaimStatusColorIndicatorRenderer\";\nimport { EobReportsReasonRenderer } from \"../shared/Renderer/claim-grid/EobReportsReasonRenderer\";\nimport { EobReportAnchorRenderer } from \"../shared/Renderer/claim-grid/EobReportAnchorRenderer\";\nimport { UnAckByPayerActionRenderer } from \"../shared/Renderer/claim-grid/UnAckByPayerActionRenderer\";\nimport { DispatchedResubmsnExclaimarkRenderer } from \"../shared/Renderer/claim-grid/DispatchedResubmsnExclaimarkRenderer\";\nimport { ResubmissionExclaimarkRenderer } from \"../shared/Renderer/claim-grid/ResubmissionExclaimarkRenderer\";\nimport { PendingResubmissionExclaimarkRenderer } from \"../shared/Renderer/claim-grid/PendingResubmissionExclaimarkRenderer\";\nimport { CustomDateFilterComponent } from \"./custom-date-filter.component\";\nexport let CLAIM_GRID_DEFCOLUMNS = /*#__PURE__*/(() => {\n  class CLAIM_GRID_DEFCOLUMNS {\n    static OPEN_COLUMN_DEFS(privMoveAllOpenToAccepted, isPrivMoveSelectedOpenToAccepted, billerPrivMoveAllOpenToAccepted) {\n      const OPEN_COLUMN_DEFS = [{\n        headerName: ' ',\n        width: 5,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        minWidth: 5,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => params?.data?.claimId > 0 && ShowHideCheckboxClaimsGrid.showHideCheckbox(params) && (privMoveAllOpenToAccepted || isPrivMoveSelectedOpenToAccepted || billerPrivMoveAllOpenToAccepted) ? true : false,\n        headerCheckboxSelection: privMoveAllOpenToAccepted,\n        sortable: false,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 40,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          if (params?.node && params.node.id != null) {\n            // For infinite row model, use the node id which represents the actual row number\n            return parseInt(params.node.id) + 1;\n          }\n\n          return '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: false,\n        pinned: 'left',\n        field: 'claimControlNumber',\n        sortable: true,\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: true,\n        field: 'payer',\n        tooltipField: 'payer',\n        sortable: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: true,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        sortable: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: true,\n        filter: CustomDateFilterComponent,\n        field: 'dos',\n        tooltipField: \"dos\",\n        sortable: true,\n        filterValueGetter: params => {\n          return COMMON_METHODS.validateDateForFilter(params.data.dosFrom);\n        },\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        }\n\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 210,\n        resizable: true,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        sortable: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 160,\n        resizable: false,\n        cellClass: 'align-right',\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        sortable: true,\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Claim Type',\n        minWidth: 150,\n        resizable: true,\n        field: 'claimType',\n        tooltipField: \"claimType\",\n        sortable: true\n      }, {\n        headerName: 'Created On',\n        minWidth: 150,\n        filter: 'agDateColumnFilter',\n        resizable: true,\n        field: 'dateCreated',\n        sortable: true,\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return COMMON_METHODS.validateDateForFilter(params?.data?.dateCreated);\n        }\n      }, {\n        headerName: 'Age(DOC)',\n        minWidth: 150,\n        resizable: false,\n        field: 'age',\n        sortable: true\n      }, {\n        headerName: 'Created By',\n        minWidth: 200,\n        resizable: false,\n        field: 'createdBy',\n        filter: true,\n        tooltipField: \"createdBy\",\n        sortable: true\n      }, {\n        headerName: 'Action',\n        minWidth: 115,\n        resizable: false,\n        lockVisible: true,\n        cellRenderer: OpenClaimActionRender,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return OPEN_COLUMN_DEFS;\n    }\n\n    static ACCEPTED_COLUMN_DEFS(isClaimBillingMngmntGenrtEDI837File) {\n      const ACCEPTED_COLUMN_DEFS = [{\n        headerName: '',\n        width: 3,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        minWidth: 2,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: AcceptedClaimLockerActionRenderer\n      }, {\n        headerName: '',\n        minWidth: 3,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => params?.data?.claimId > 0 && ShowHideCheckboxClaimsGridAccept.showHideCheckbox(params, isClaimBillingMngmntGenrtEDI837File),\n        headerCheckboxSelection: isClaimBillingMngmntGenrtEDI837File,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 15,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        pinned: 'left',\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'payer',\n        tooltipField: 'payer',\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: false,\n        field: 'dos',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        tooltipField: \"dos\",\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        cellClass: 'align-right',\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Claim Type',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'claimType',\n        tooltipField: \"claimType\"\n      }, {\n        headerName: 'Created By',\n        minWidth: 200,\n        resizable: false,\n        field: 'createdBy',\n        sortable: true,\n        filter: true,\n        tooltipField: \"createdBy\"\n      }, {\n        headerName: 'Created On',\n        minWidth: 150,\n        resizable: true,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: 'dateCreated',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params?.data?.dateCreated);\n        }\n      }, {\n        headerName: 'Age(DOC)',\n        minWidth: 150,\n        resizable: false,\n        field: 'age',\n        sortable: true,\n        filter: true\n      }, {\n        headerName: 'Action',\n        minWidth: 115,\n        resizable: true,\n        hide: false,\n        lockVisible: false,\n        cellRenderer: AcceptedClaimActionRenderer,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        pinned: 'right'\n      }];\n      return ACCEPTED_COLUMN_DEFS;\n    }\n\n    static DISPATCHED_COLUMN_DEFS(isClaimsBillingManagementResubmission) {\n      const DISPATCHED_COLUMN_DEFS = [{\n        headerName: '',\n        width: 3,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        width: 50,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: DispatchedResubmsnExclaimarkRenderer\n      }, {\n        headerName: '',\n        minWidth: 5,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => params?.data?.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission ? true : false,\n        headerCheckboxSelection: isClaimsBillingManagementResubmission,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 15,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        sortable: true,\n        filter: true,\n        resizable: false,\n        pinned: 'left',\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        sortable: true,\n        filter: true,\n        resizable: false,\n        field: 'payer',\n        tooltipField: 'payer',\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        sortable: true,\n        filter: true,\n        resizable: false,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 220,\n        sortable: true,\n        filter: true,\n        resizable: false,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: 'dos',\n        tooltipField: \"dos\",\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        cellClass: 'align-right',\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Sent To',\n        minWidth: 200,\n        resizable: false,\n        field: 'sentTo',\n        sortable: true,\n        filter: true,\n        tooltipField: \"sentTo\"\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: true,\n        field: 'sentOn',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Action',\n        minWidth: 110,\n        resizable: false,\n        lockVisible: true,\n        cellRenderer: DispatchedClaimActionRender,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return DISPATCHED_COLUMN_DEFS;\n    }\n\n    static REJECTED_BY_CH_COLUMN_DEFS(isClaimsBillingManagementResubmission) {\n      const REJECTED_BY_CH_COLUMN_DEFS = [{\n        headerName: '',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        width: 50,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: 'claimId',\n        pinned: 'left',\n        cellRenderer: ResubmissionExclaimarkRenderer\n      }, {\n        headerName: '',\n        minWidth: 5,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => !!params.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && params.data.parent_patientctrlno == null && isClaimsBillingManagementResubmission ? true : false,\n        headerCheckboxSelection: isClaimsBillingManagementResubmission,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 15,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        pinned: 'left',\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        field: 'payer',\n        tooltipField: 'payer',\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 200,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: true,\n        field: 'dos',\n        tooltipField: \"dos\",\n        sortable: true,\n        filter: 'agDateColumnFilter',\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        cellClass: 'align-right',\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Created On',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: 'dateCreated',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dateCreated);\n        }\n      }, {\n        headerName: 'Sent To',\n        minWidth: 200,\n        resizable: false,\n        field: 'sentTo',\n        sortable: true,\n        filter: true,\n        tooltipField: \"sentTo\"\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: 'sentOn',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Rejected On',\n        minWidth: 150,\n        resizable: false,\n        field: '_999ProcessedOn_CH',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        valueFormatter: params => {\n          return params?.data?.fileType == '005010X231A1' ? params?.data?._999ProcessedOn_CH : params?.data?._277ProcessedOn_CH;\n        },\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.statusModifiedDate);\n        }\n      }, {\n        headerName: 'Rejected In',\n        minWidth: 200,\n        resizable: false,\n        field: 'fileType',\n        filter: true,\n        tooltipField: \"fileType\",\n        sortable: true,\n        valueFormatter: params => {\n          return params?.data?.fileType == '005010X231A1' ? '999' : params?.data?.fileType == '005010X214' ? '277' : null;\n        }\n      }, {\n        headerName: 'Action',\n        minWidth: 150,\n        resizable: false,\n        lockVisible: true,\n        cellRenderer: RejectedByCHActionRenderer,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return REJECTED_BY_CH_COLUMN_DEFS;\n    }\n\n    static ACK_BY_CH_COLUMN_DEFS(isClaimsBillingManagementResubmission) {\n      let ACK_BY_CH_COLUMN_DEFS = [{\n        headerName: '',\n        width: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: true,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        width: 55,\n        filter: false,\n        lockPosition: 'left',\n        resizable: true,\n        field: '',\n        pinned: 'left',\n        cellRenderer: PendingResubmissionExclaimarkRenderer\n      }, {\n        headerName: '',\n        minWidth: 5,\n        lockPosition: 'left',\n        filter: false,\n        resizable: true,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => !!params && params?.data?.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission ? true : false,\n        headerCheckboxSelection: isClaimsBillingManagementResubmission,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 15,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: false,\n        pinned: 'left',\n        sortable: true,\n        filter: true,\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: false,\n        field: 'payer',\n        tooltipField: 'payer',\n        sortable: true,\n        filter: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: false,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        sortable: true,\n        filter: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: false,\n        field: 'dos',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        tooltipField: \"dos\",\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        cellClass: 'align-right',\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: 'sentOn',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Accepted On',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: '_999ProcessedOn_CH',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data._999ProcessedOn_CH);\n        }\n      }, {\n        headerName: 'Action',\n        minWidth: 110,\n        resizable: false,\n        lockVisible: true,\n        cellRenderer: DispatchedClaimActionRender,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return ACK_BY_CH_COLUMN_DEFS;\n    }\n\n    static ACCEPTED_BY_CH_COLUMN_DEFS(isClaimsBillingManagementResubmission) {\n      let ACCEPTED_BY_CH_COLUMN_DEFS = [{\n        headerName: '',\n        width: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: true,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        width: 55,\n        filter: false,\n        lockPosition: 'left',\n        resizable: true,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ResubmissionExclaimarkRenderer\n      }, {\n        headerName: '',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => params?.data?.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission ? true : false,\n        headerCheckboxSelection: isClaimsBillingManagementResubmission,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 15,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        pinned: 'left',\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'payer',\n        tooltipField: 'payer',\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: false,\n        field: 'dos',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        tooltipField: \"dos\",\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        cellClass: 'align-right',\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: false,\n        field: 'sentOn',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Accepted On',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: '_277ProcessedOn_CH',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data._277ProcessedOn_CH);\n        }\n      }, {\n        headerName: 'Action',\n        minWidth: 110,\n        resizable: false,\n        lockVisible: true,\n        cellRenderer: DispatchedClaimActionRender,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return ACCEPTED_BY_CH_COLUMN_DEFS;\n    }\n\n    static ACK_BY_PAYER_COLUMN_DEFS(isClaimsBillingManagementResubmission) {\n      const ACK_PAYER_COLUMN_DEFS = [{\n        headerName: '',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        width: 50,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: PendingResubmissionExclaimarkRenderer\n      }, {\n        headerName: '',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => !!params.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && params.data.parent_patientctrlno == null && isClaimsBillingManagementResubmission ? true : false,\n        headerCheckboxSelection: isClaimsBillingManagementResubmission,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: true,\n        pinned: 'left',\n        sortable: true,\n        filter: true,\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: true,\n        field: 'payer',\n        sortable: true,\n        filter: true,\n        tooltipField: 'payer',\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: true,\n        field: 'memberFullName',\n        sortable: true,\n        filter: true,\n        tooltipField: \"memberFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 200,\n        resizable: true,\n        field: 'renderingProviderFullName',\n        sortable: true,\n        filter: true,\n        tooltipField: \"renderingProviderFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: true,\n        field: 'dos',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        tooltipField: \"dos\",\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: true,\n        cellClass: 'align-right',\n        sortable: true,\n        filter: true,\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: true,\n        field: 'sentOn',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Accepted On',\n        minWidth: 150,\n        resizable: true,\n        field: 'acceptedOn',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.acceptedOn);\n        }\n      }, {\n        headerName: 'Action',\n        minWidth: 110,\n        resizable: true,\n        lockVisible: true,\n        cellRenderer: DispatchedClaimActionRender,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return ACK_PAYER_COLUMN_DEFS;\n    }\n\n    static ACCEPTED_BY_PAYER(isClaimsBillingManagementResubmission) {\n      let ACCEPTED_BY_PAYER = [{\n        headerName: '',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        width: 50,\n        lockPosition: 'left',\n        filter: false,\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: DispatchedResubmsnExclaimarkRenderer\n      }, {\n        headerName: '',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => params?.data?.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission ? true : false,\n        headerCheckboxSelection: isClaimsBillingManagementResubmission,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        pinned: 'left',\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'payer',\n        tooltipField: 'payer',\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 200,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: false,\n        filter: 'agDateColumnFilter',\n        sortable: true,\n        field: 'dos',\n        tooltipField: \"dos\",\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: true,\n        cellClass: 'align-right',\n        sortable: true,\n        filter: true,\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: true,\n        field: 'sentOn',\n        filter: 'agDateColumnFilter',\n        sortable: true,\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Accepted On',\n        minWidth: 150,\n        resizable: true,\n        field: '_277ProcessedOn_Payer',\n        filter: 'agDateColumnFilter',\n        sortable: true,\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data._277ProcessedOn_Payer);\n        }\n      }, {\n        headerName: 'Action',\n        minWidth: 110,\n        resizable: true,\n        lockVisible: true,\n        cellRenderer: DispatchedClaimActionRender,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return ACCEPTED_BY_PAYER;\n    }\n\n    static REJECTED_BY_PAYER_COLUMN_DEFS(isClaimsBillingManagementResubmission) {\n      const REJECTED_BY_PAYER_COLUMN_DEFS = [{\n        headerName: '',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        minWidth: 50,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: PendingResubmissionExclaimarkRenderer\n      }, {\n        headerName: '',\n        minWidth: 10,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => !!params?.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission ? true : false,\n        headerCheckboxSelection: isClaimsBillingManagementResubmission,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: false,\n        pinned: 'left',\n        sortable: true,\n        filter: true,\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: false,\n        field: 'payer',\n        tooltipField: 'payer',\n        sortable: true,\n        filter: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: false,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        sortable: true,\n        filter: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 200,\n        resizable: false,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        sortable: true,\n        filter: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: false,\n        field: 'dos',\n        tooltipField: \"dos\",\n        sortable: true,\n        filter: 'agDateColumnFilter',\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: false,\n        cellClass: 'align-right',\n        field: 'claimAmount',\n        sortable: true,\n        filter: true,\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: false,\n        field: 'sentOn',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Rejected On',\n        minWidth: 150,\n        resizable: false,\n        field: '_277ProcessedOn_Payer',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data._277ProcessedOn_Payer);\n        }\n      }, {\n        headerName: 'Action',\n        minWidth: 150,\n        resizable: false,\n        lockVisible: true,\n        cellRenderer: RejectedByCHActionRenderer,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return REJECTED_BY_PAYER_COLUMN_DEFS;\n    }\n\n    static PENDING_COLUMN_DEFS(isClaimsBillingManagementResubmission) {\n      const PENDING = [{\n        headerName: '',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        minWidth: 50,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: PendingResubmissionExclaimarkRenderer\n      }, {\n        headerName: '',\n        minWidth: 50,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => !!params.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission ? true : false,\n        headerCheckboxSelection: isClaimsBillingManagementResubmission,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: false,\n        pinned: 'left',\n        sortable: true,\n        filter: true,\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: false,\n        field: 'payer',\n        tooltipField: 'payer',\n        sortable: true,\n        filter: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: false,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        sortable: true,\n        filter: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 200,\n        resizable: false,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        sortable: true,\n        filter: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: false,\n        field: 'dos',\n        tooltipField: \"dos\",\n        sortable: true,\n        filter: 'agDateColumnFilter',\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: true,\n        cellClass: 'align-right',\n        sortable: true,\n        filter: true,\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Created On',\n        minWidth: 150,\n        resizable: true,\n        field: 'dateCreated',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dateCreated);\n        }\n      }, {\n        headerName: 'Sent To',\n        minWidth: 200,\n        resizable: true,\n        field: 'sentTo',\n        tooltipField: \"sentTo\",\n        sortable: true,\n        filter: true,\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: true,\n        field: 'sentOn',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Pend On',\n        minWidth: 150,\n        resizable: true,\n        field: '_277PendOnDate',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data._277PendOnDate);\n        }\n      }, {\n        headerName: 'Action',\n        minWidth: 110,\n        resizable: false,\n        lockVisible: true,\n        cellRenderer: DispatchedClaimActionRender,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return PENDING;\n    }\n\n    static EOB_RECIEVED(isClaimsBillingManagementResubmission) {\n      const EOB_RECIEVED = [{\n        headerName: '',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        minWidth: 50,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: PendingResubmissionExclaimarkRenderer\n      }, {\n        headerName: '',\n        minWidth: 15,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => !!params?.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission ? true : false,\n        headerCheckboxSelection: isClaimsBillingManagementResubmission,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        pinned: 'left',\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'payer',\n        tooltipField: 'payer',\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 200,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: false,\n        field: 'dos',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        tooltipField: \"dos\",\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        cellClass: 'align-right',\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: false,\n        field: 'sentOn',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Paid Amount',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        cellClass: 'align-right',\n        field: 'paidAmount',\n        tooltipField: \"paidAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.paidAmount)\n      }, {\n        headerName: 'Paid On',\n        minWidth: 150,\n        resizable: false,\n        field: '_835PaidOnDate',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data._835PaidOnDate);\n        }\n      }, {\n        headerName: 'Action',\n        minWidth: 145,\n        resizable: true,\n        lockVisible: true,\n        cellRenderer: EobRecievedClaimActionRenderer,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return EOB_RECIEVED;\n    }\n\n    static DENIED_BY_PAYER(isClaimsBillingManagementResubmission) {\n      const DENIED_BY_PAYER = [{\n        headerName: '',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n      }, {\n        headerName: '',\n        minWidth: 50,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        cellRenderer: PendingResubmissionExclaimarkRenderer\n      }, {\n        headerName: '',\n        minWidth: 15,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        checkboxSelection: params => !!params.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission ? true : false,\n        headerCheckboxSelection: isClaimsBillingManagementResubmission,\n        headerComponent: 'selectAllComponent'\n      }, {\n        headerName: 'S.No',\n        minWidth: 5,\n        filter: false,\n        lockPosition: 'left',\n        resizable: false,\n        field: '',\n        pinned: 'left',\n        valueGetter: params => {\n          return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n        },\n        cellClass: 'ag-grid-row-number-cell'\n      }, {\n        headerName: 'Claim ID',\n        minWidth: 160,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        pinned: 'left',\n        field: 'claimControlNumber',\n        tooltipField: 'claimControlNumber'\n      }, {\n        headerName: 'Payer',\n        minWidth: 200,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        field: 'payer',\n        tooltipField: 'payer',\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Member',\n        minWidth: 200,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        field: 'memberFullName',\n        tooltipField: \"memberFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'Rendering Provider',\n        minWidth: 200,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        field: 'renderingProviderFullName',\n        tooltipField: \"renderingProviderFullName\",\n        valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n      }, {\n        headerName: 'DOS',\n        minWidth: 200,\n        resizable: true,\n        field: 'dos',\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        tooltipField: \"dos\",\n\n        comparator(valueA, valueB, nodeA, nodeB) {\n          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: true,\n        cellClass: 'align-right',\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: 'sentOn',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Accepted On',\n        minWidth: 150,\n        resizable: false,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dos);\n        }\n      }, {\n        headerName: 'Claimed Amount',\n        minWidth: 150,\n        resizable: true,\n        sortable: true,\n        filter: true,\n        cellClass: 'align-right',\n        field: 'claimAmount',\n        tooltipField: \"claimAmount\",\n        valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n      }, {\n        headerName: 'Created On',\n        minWidth: 150,\n        resizable: true,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: 'dateCreated',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.dateCreated);\n        }\n      }, {\n        headerName: 'Sent On',\n        minWidth: 150,\n        resizable: true,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: 'sentOn',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data.sentOn);\n        }\n      }, {\n        headerName: 'Denied On',\n        minWidth: 150,\n        resizable: true,\n        sortable: true,\n        filter: 'agDateColumnFilter',\n        field: '_835PaidOnDate',\n        cellRenderer: params => {\n          return DateFormatter(params.value);\n        },\n        tooltipValueGetter: params => {\n          return DateFormatter(params.value);\n        },\n\n        comparator(valueA, valueB) {\n          return SortDateValues(valueA, valueB);\n        },\n\n        filterValueGetter: params => {\n          return DateFormatter(params.data._835PaidOnDate);\n        }\n      }, {\n        headerName: 'Action',\n        minWidth: 145,\n        resizable: true,\n        lockVisible: true,\n        cellRenderer: EobRecievedClaimActionRenderer,\n        sortable: false,\n        type: 'centerAligned',\n        filter: false,\n        hide: false,\n        pinned: 'right'\n      }];\n      return DENIED_BY_PAYER;\n    }\n\n  }\n\n  CLAIM_GRID_DEFCOLUMNS.ONHOLD_COLUMN_DEFS = [{\n    headerName: '',\n    minWidth: 10,\n    lockPosition: 'left',\n    filter: false,\n    resizable: false,\n    field: '',\n    pinned: 'left',\n    cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n  }, {\n    headerName: 'S.No',\n    minWidth: 15,\n    lockPosition: 'left',\n    filter: false,\n    resizable: false,\n    field: '',\n    pinned: 'left',\n    valueGetter: params => {\n      return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n    },\n    cellClass: 'ag-grid-row-number-cell'\n  }, {\n    headerName: 'Claim ID',\n    minWidth: 160,\n    resizable: false,\n    pinned: 'left',\n    field: 'claimControlNumber',\n    sortable: true,\n    tooltipField: 'claimControlNumber'\n  }, {\n    headerName: 'Payer',\n    minWidth: 250,\n    resizable: false,\n    field: 'payer',\n    tooltipField: 'payer',\n    sortable: true,\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'Member',\n    minWidth: 200,\n    resizable: true,\n    field: 'memberFullName',\n    tooltipField: \"memberFullName\",\n    sortable: true,\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'Rendering Provider',\n    minWidth: 200,\n    resizable: true,\n    field: 'renderingProviderFullName',\n    tooltipField: \"renderingProviderFullName\",\n    sortable: true,\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'DOS',\n    minWidth: 200,\n    filter: 'agDateColumnFilter',\n    resizable: false,\n    sortable: true,\n    field: 'dos',\n    tooltipField: \"dos\",\n\n    comparator(valueA, valueB, nodeA, nodeB) {\n      return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params.data.dos);\n    }\n  }, {\n    headerName: 'Claimed Amount',\n    minWidth: 155,\n    resizable: false,\n    cellClass: 'align-right',\n    sortable: true,\n    field: 'claimAmount',\n    tooltipField: \"claimAmount\",\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n  }, {\n    headerName: 'Created On',\n    minWidth: 150,\n    resizable: false,\n    filter: 'agDateColumnFilter',\n    sortable: true,\n    field: 'dateCreated',\n    cellRenderer: params => {\n      return DateFormatter(params.value);\n    },\n    tooltipValueGetter: params => {\n      return DateFormatter(params.value);\n    },\n\n    comparator(valueA, valueB) {\n      return SortDateValues(valueA, valueB);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params?.data?.dateCreated);\n    }\n  }, {\n    headerName: 'Created By',\n    minWidth: 200,\n    resizable: false,\n    field: 'createdBy',\n    sortable: true,\n    filter: true,\n    tooltipField: \"createdBy\"\n  }, {\n    headerName: 'Reviewed By',\n    minWidth: 200,\n    resizable: false,\n    field: 'reviewedBy',\n    sortable: true,\n    filter: true,\n    tooltipField: \"reviewedBy\"\n  }, {\n    headerName: 'OnHold on',\n    minWidth: 180,\n    resizable: false,\n    filter: 'agDateColumnFilter',\n    sortable: true,\n    field: 'statusModifiedDate',\n    cellRenderer: params => {\n      return DateFormatter(params.value);\n    },\n    tooltipValueGetter: params => {\n      return DateFormatter(params.value);\n    },\n\n    comparator(valueA, valueB) {\n      return SortDateValues(valueA, valueB);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params?.data?.statusModifiedDate);\n    }\n  }, {\n    headerName: 'OnHold Reason',\n    minWidth: 180,\n    resizable: true,\n    lockVisible: true,\n    field: 'statusReason',\n    cellRenderer: OnHoldReasonActionRenderer,\n    sortable: true,\n    type: 'centerAligned',\n    filter: true,\n    hide: false,\n    pinned: 'right',\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'Action',\n    minWidth: 115,\n    resizable: false,\n    lockVisible: true,\n    cellRenderer: OpenClaimActionRender,\n    sortable: false,\n    type: 'centerAligned',\n    filter: false,\n    hide: false,\n    pinned: 'right'\n  }];\n  CLAIM_GRID_DEFCOLUMNS.RESUBMISSION_COLUMN_DEFS = [{\n    headerName: '',\n    minWidth: 5,\n    filter: false,\n    lockPosition: 'left',\n    resizable: false,\n    field: '',\n    pinned: 'left',\n    cellRenderer: InActiveClaimColorIndicatorRenderer\n  }, {\n    headerName: 'S.No',\n    minWidth: 15,\n    lockPosition: 'left',\n    filter: false,\n    resizable: false,\n    field: '',\n    pinned: 'left',\n    valueGetter: params => {\n      return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n    },\n    cellClass: 'ag-grid-row-number-cell'\n  }, {\n    headerName: 'Claim ID',\n    minWidth: 160,\n    resizable: false,\n    sortable: true,\n    pinned: 'left',\n    field: 'claimControlNumber',\n    tooltipField: 'claimControlNumber'\n  }, {\n    headerName: 'Payer',\n    minWidth: 200,\n    resizable: false,\n    sortable: true,\n    field: 'payer',\n    tooltipField: 'payer',\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'Member',\n    minWidth: 200,\n    resizable: true,\n    sortable: true,\n    field: 'memberFullName',\n    tooltipField: \"memberFullName\",\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'Rendering Provider',\n    minWidth: 200,\n    resizable: true,\n    sortable: true,\n    field: 'renderingProviderFullName',\n    tooltipField: \"renderingProviderFullName\",\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'DOS',\n    minWidth: 200,\n    resizable: true,\n    field: 'dos',\n    sortable: true,\n    filter: 'agDateColumnFilter',\n    tooltipField: \"dos\",\n\n    comparator(valueA, valueB, nodeA, nodeB) {\n      return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params.data.dos);\n    }\n  }, {\n    headerName: 'Claim Type',\n    minWidth: 150,\n    resizable: false,\n    sortable: true,\n    field: 'claimType',\n    tooltipField: \"claimType\"\n  }, {\n    headerName: 'Claimed Amount',\n    minWidth: 155,\n    resizable: false,\n    sortable: true,\n    cellClass: 'align-right',\n    field: 'claimAmount',\n    tooltipField: \"claimAmount\",\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n  }, {\n    headerName: 'Parent Claim ID',\n    minWidth: 170,\n    resizable: true,\n    sortable: true,\n    pinned: 'left',\n    field: 'parent_patientctrlno',\n    tooltipField: \"parent_patientctrlno\"\n  }, {\n    headerName: 'Created On',\n    minWidth: 150,\n    sortable: true,\n    resizable: true,\n    filter: 'agDateColumnFilter',\n    field: 'dateCreated',\n    cellRenderer: params => {\n      return DateFormatter(params.value);\n    },\n    tooltipValueGetter: params => {\n      return DateFormatter(params.value);\n    },\n\n    comparator(valueA, valueB) {\n      return SortDateValues(valueA, valueB);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params.data.dateCreated);\n    }\n  }, {\n    headerName: 'Created By',\n    minWidth: 200,\n    resizable: true,\n    field: 'createdBy',\n    filter: true,\n    sortable: true,\n    tooltipField: \"createdBy\"\n  }, {\n    headerName: 'Action',\n    minWidth: 115,\n    resizable: true,\n    lockVisible: true,\n    cellRenderer: OpenClaimActionRender,\n    sortable: false,\n    type: 'centerAligned',\n    filter: false,\n    hide: false,\n    pinned: 'right'\n  }];\n  CLAIM_GRID_DEFCOLUMNS.UN_ACK_BY_CH_COLUMN_DEFS = [{\n    headerName: '',\n    width: 3,\n    lockPosition: 'left',\n    filter: false,\n    resizable: false,\n    field: '',\n    pinned: 'left',\n    cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n  }, {\n    headerName: 'S.No',\n    minWidth: 15,\n    lockPosition: 'left',\n    filter: false,\n    resizable: false,\n    field: '',\n    pinned: 'left',\n    valueGetter: params => {\n      return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n    },\n    cellClass: 'ag-grid-row-number-cell'\n  }, {\n    headerName: 'Claim ID',\n    minWidth: 160,\n    resizable: true,\n    pinned: 'left',\n    field: 'claimControlNumber',\n    sortable: true,\n    filter: true,\n    tooltipField: 'claimControlNumber'\n  }, {\n    headerName: 'Payer',\n    minWidth: 200,\n    resizable: true,\n    field: 'payer',\n    tooltipField: 'payer',\n    sortable: true,\n    filter: true,\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'Member',\n    minWidth: 200,\n    resizable: true,\n    field: 'memberFullName',\n    tooltipField: \"memberFullName\",\n    sortable: true,\n    filter: true,\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'Rendering Provider',\n    minWidth: 200,\n    resizable: true,\n    field: 'renderingProviderFullName',\n    tooltipField: \"renderingProviderFullName\",\n    sortable: true,\n    filter: true,\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'DOS',\n    minWidth: 200,\n    resizable: false,\n    field: 'dos',\n    tooltipField: \"dos\",\n    sortable: true,\n    filter: 'agDateColumnFilter',\n\n    comparator(valueA, valueB, nodeA, nodeB) {\n      return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params.data.dos);\n    }\n  }, {\n    headerName: 'Claimed Amount',\n    minWidth: 155,\n    resizable: false,\n    cellClass: 'align-right',\n    field: 'claimAmount',\n    tooltipField: \"claimAmount\",\n    sortable: true,\n    filter: true,\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n  }, {\n    headerName: 'Sent To',\n    minWidth: 200,\n    resizable: false,\n    field: 'sentTo',\n    tooltipField: \"sentTo\",\n    sortable: true,\n    filter: true\n  }, {\n    headerName: 'Sent On',\n    minWidth: 150,\n    resizable: false,\n    field: 'sentOn',\n    sortable: true,\n    filter: 'agDateColumnFilter',\n    cellRenderer: params => {\n      return DateFormatter(params.value);\n    },\n    tooltipValueGetter: params => {\n      return DateFormatter(params.value);\n    },\n\n    comparator(valueA, valueB) {\n      return SortDateValues(valueA, valueB);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params.data.sentOn);\n    }\n  }, {\n    headerName: 'Unack for (Days)',\n    minWidth: 150,\n    resizable: false,\n    field: 'unackfor',\n    tooltipField: \"unackfor\",\n    sortable: true,\n    filter: true\n  }, {\n    headerName: 'Action',\n    minWidth: 80,\n    resizable: false,\n    lockVisible: true,\n    cellRenderer: UnAckByCHClaimActionRenderer,\n    sortable: false,\n    type: 'centerAligned',\n    filter: false,\n    hide: false,\n    pinned: 'right'\n  }];\n  CLAIM_GRID_DEFCOLUMNS.UN_ACK_BY_PAYER_COLUMN_DEFS = [{\n    headerName: '',\n    minWidth: 5,\n    filter: false,\n    lockPosition: 'left',\n    resizable: false,\n    field: '',\n    pinned: 'left',\n    cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer\n  }, {\n    headerName: 'S.No',\n    minWidth: 15,\n    lockPosition: 'left',\n    filter: false,\n    resizable: false,\n    field: '',\n    pinned: 'left',\n    valueGetter: params => {\n      return params?.node?.id != null ? parseInt(params.node.id) + 1 : '';\n    },\n    cellClass: 'ag-grid-row-number-cell'\n  }, {\n    headerName: 'Claim ID',\n    minWidth: 160,\n    resizable: false,\n    pinned: 'left',\n    sortable: true,\n    filter: true,\n    field: 'claimControlNumber',\n    tooltipField: 'claimControlNumber'\n  }, {\n    headerName: 'Payer',\n    minWidth: 200,\n    resizable: false,\n    sortable: true,\n    filter: true,\n    field: 'payer',\n    tooltipField: 'payer',\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'Member',\n    minWidth: 200,\n    resizable: false,\n    sortable: true,\n    filter: true,\n    field: 'memberFullName',\n    tooltipField: \"memberFullName\",\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'Rendering Provider',\n    minWidth: 200,\n    resizable: false,\n    sortable: true,\n    filter: true,\n    field: 'renderingProviderFullName',\n    tooltipField: \"renderingProviderFullName\",\n    valueFormatter: params => COMMON_METHODS.truncateTextFormatter(params)\n  }, {\n    headerName: 'DOS',\n    minWidth: 200,\n    resizable: false,\n    field: 'dos',\n    sortable: true,\n    filter: 'agDateColumnFilter',\n    tooltipField: \"dos\",\n\n    comparator(valueA, valueB, nodeA, nodeB) {\n      return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params.data.dos);\n    }\n  }, {\n    headerName: 'Claimed Amount',\n    minWidth: 150,\n    resizable: false,\n    sortable: true,\n    filter: true,\n    cellClass: 'align-right',\n    field: 'claimAmount',\n    tooltipField: \"claimAmount\",\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount)\n  }, {\n    headerName: 'Sent On',\n    minWidth: 150,\n    resizable: false,\n    field: 'sentOn',\n    sortable: true,\n    filter: 'agDateColumnFilter',\n    cellRenderer: params => {\n      return DateFormatter(params.value);\n    },\n    tooltipValueGetter: params => {\n      return DateFormatter(params.value);\n    },\n\n    comparator(valueA, valueB) {\n      return SortDateValues(valueA, valueB);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params.data.sentOn);\n    }\n  }, {\n    headerName: 'Accepted On',\n    minWidth: 150,\n    resizable: false,\n    sortable: true,\n    filter: 'agDateColumnFilter',\n    field: 'acceptedOn',\n    cellRenderer: params => {\n      return DateFormatter(params.value);\n    },\n    tooltipValueGetter: params => {\n      return DateFormatter(params.value);\n    },\n\n    comparator(valueA, valueB) {\n      return SortDateValues(valueA, valueB);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params.data.acceptedOn);\n    }\n  }, {\n    headerName: 'Action',\n    minWidth: 110,\n    resizable: false,\n    lockVisible: true,\n    cellRenderer: UnAckByPayerActionRenderer,\n    sortable: false,\n    type: 'centerAligned',\n    filter: false,\n    hide: false,\n    pinned: 'right'\n  }];\n  return CLAIM_GRID_DEFCOLUMNS;\n})();\nexport let EOB_REPORT_GRID_COLUMNS = /*#__PURE__*/(() => {\n  class EOB_REPORT_GRID_COLUMNS {}\n\n  EOB_REPORT_GRID_COLUMNS.EOB_REPORT_COLDEF = [{\n    headerName: 'Primary Payer',\n    minWidth: 180,\n    resizable: true,\n    filter: false,\n    field: 'payerName'\n  }, {\n    headerName: 'Secondary Payer',\n    minWidth: 180,\n    resizable: true,\n    filter: false,\n    field: 'secondaryPayerName'\n  }, {\n    headerName: 'Patient Name',\n    minWidth: 190,\n    resizable: true,\n    filter: false,\n    field: 'patientName'\n  }, {\n    headerName: 'DOS',\n    minWidth: 150,\n    resizable: true,\n    field: 'dateOfService',\n    filter: false,\n    cellRenderer: params => {\n      return DateFormatter(params.value);\n    },\n    tooltipValueGetter: params => {\n      return DateFormatter(params.value);\n    },\n\n    comparator(valueA, valueB) {\n      return SortDateValues(valueA, valueB);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params.data.dateCreated);\n    }\n  }, {\n    headerName: 'CPT',\n    minWidth: 100,\n    resizable: true,\n    filter: false,\n    field: 'cpt'\n  }, {\n    headerName: 'Billed',\n    minWidth: 100,\n    resizable: true,\n    cellClass: 'align-right',\n    filter: false,\n    field: 'billed',\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.billed)\n  }, {\n    headerName: 'Allowed',\n    minWidth: 80,\n    cellClass: 'align-right',\n    resizable: true,\n    filter: false,\n    field: 'allowed',\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.allowed)\n  }, {\n    headerName: 'ADJ',\n    minWidth: 80,\n    cellClass: 'align-right',\n    resizable: true,\n    filter: false,\n    tooltipField: 'adj',\n    field: \"adj\",\n    cellRenderer: EobReportAnchorRenderer\n  }, {\n    headerName: 'DED',\n    cellClass: 'align-right',\n    minWidth: 80,\n    resizable: true,\n    filter: false,\n    field: 'ded',\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.ded)\n  }, {\n    headerName: 'COINS/PR',\n    minWidth: 110,\n    cellClass: 'align-right',\n    resizable: true,\n    filter: false,\n    field: 'coIns',\n    tooltipField: \"coIns\",\n    cellRenderer: EobReportAnchorRenderer,\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.coIns)\n  }, {\n    headerName: 'PMT/AMT',\n    cellClass: 'align-right',\n    minWidth: 110,\n    resizable: true,\n    filter: false,\n    field: 'paymentAmount',\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.paymentAmount)\n  }, {\n    headerName: 'Penalty',\n    minWidth: 110,\n    cellClass: 'align-right',\n    resizable: true,\n    filter: false,\n    field: 'penalty',\n    tooltipField: \"penalty\",\n    cellRenderer: EobReportAnchorRenderer,\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.penalty)\n  }, {\n    headerName: 'Paid',\n    minWidth: 80,\n    cellClass: 'align-right',\n    resizable: true,\n    filter: false,\n    field: 'paid',\n    valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.paid)\n  }, {\n    headerName: 'Reason',\n    minWidth: 200,\n    resizable: true,\n    filter: false,\n    cellRenderer: EobReportsReasonRenderer\n  }];\n  return EOB_REPORT_GRID_COLUMNS;\n})();\nexport let CLAIM_RESUBMIT_GRID_COLUMNS = /*#__PURE__*/(() => {\n  class CLAIM_RESUBMIT_GRID_COLUMNS {}\n\n  CLAIM_RESUBMIT_GRID_COLUMNS.CLAIM_RESUBMIT_COL_DEF = [{\n    headerName: '',\n    width: 10,\n    filter: false,\n    lockPosition: 'left',\n    resizable: true,\n    field: 'claimControlNumber',\n    pinned: 'left',\n    cellRenderer: InActiveClaimStatusColorIndicatorRenderer\n  }, {\n    headerName: 'Claim ID',\n    minWidth: 160,\n    resizable: true,\n    pinned: 'left',\n    field: 'patientControlNumber',\n    tooltipField: 'patientControlNumber'\n  }, // { headerName: 'DOS', minWidth: 110, resizable: true,   field: 'dos', tooltipField: \"dos\", cellRenderer: params => {\n  //   return DateFormatter(params.value)\n  // }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {\n  //   return SortDateValues(valueA, valueB)\n  // }, filterValueGetter: params => {\n  //   return DateFormatter(params.data.dosFrom)\n  // } },\n  {\n    headerName: 'DOS',\n    minWidth: 200,\n    resizable: true,\n    field: 'dos',\n    tooltipField: \"dos\"\n  }, {\n    headerName: 'Member',\n    minWidth: 150,\n    resizable: true,\n    field: 'memberName',\n    tooltipField: \"memberName\"\n  }, {\n    headerName: 'Rendering Provider',\n    minWidth: 200,\n    resizable: true,\n    field: 'providerName',\n    tooltipField: \"providerName\"\n  }, {\n    headerName: 'Created By',\n    minWidth: 200,\n    resizable: true,\n    field: 'createdBy',\n    filter: true,\n    tooltipField: \"createdBy\"\n  }, {\n    headerName: 'Create Date',\n    minWidth: 150,\n    resizable: true,\n    field: 'createdDate',\n    tooltipField: \"createdDate\",\n    cellRenderer: params => {\n      return DateFormatter(params.value);\n    },\n    tooltipValueGetter: params => {\n      return DateFormatter(params.value);\n    },\n\n    comparator(valueA, valueB) {\n      return SortDateValues(valueA, valueB);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatter(params.data.dateCreated);\n    }\n  }, {\n    headerName: 'Current Status',\n    minWidth: 150,\n    resizable: true,\n    field: 'status',\n    tooltipField: \"status\"\n  }];\n  return CLAIM_RESUBMIT_GRID_COLUMNS;\n})();\nexport let ON_HOLD_CATEGORY_DESCRIPTION_COLUMNS = /*#__PURE__*/(() => {\n  class ON_HOLD_CATEGORY_DESCRIPTION_COLUMNS {}\n\n  ON_HOLD_CATEGORY_DESCRIPTION_COLUMNS.CATEGORY_DESCRIPTION_COL_DEF = [{\n    headerName: 'Category',\n    minWidth: 250,\n    resizable: true,\n    filter: false,\n    field: 'reason',\n    tooltipField: \"reason\"\n  }, {\n    headerName: 'Description',\n    minWidth: 280,\n    resizable: true,\n    filter: false,\n    field: 'description',\n    tooltipField: \"description\"\n  }, {\n    headerName: 'Modified Date',\n    minWidth: 200,\n    resizable: true,\n    filter: false,\n    field: 'lastModifiedDate',\n    cellRenderer: params => {\n      return DateFormatterWithTime(params.value);\n    },\n    tooltipValueGetter: params => {\n      return DateFormatterWithTime(params.value);\n    },\n\n    comparator(valueA, valueB) {\n      return SortDateValues(valueA, valueB);\n    },\n\n    filterValueGetter: params => {\n      return DateFormatterWithTime(params.data.dateCreated);\n    }\n  }];\n  return ON_HOLD_CATEGORY_DESCRIPTION_COLUMNS;\n})();\nexport class ShowHideCheckboxClaimsGrid {\n  static showHideCheckbox(paramsDetails) {\n    let params = paramsDetails.data; // if (params.resubmissionCount >= 50) {\n    //   return false;\n    // }\n\n    if (params.claimStatusCode == CLAIM_TYPE.ntr) {\n      return true;\n    }\n\n    if (params.claimStatusCode == CLAIM_TYPE.unackByCH || params.claimStatusCode == CLAIM_TYPE.unAckByPayer) {\n      return false;\n    } else if (params.claimStatusCode == CLAIM_TYPE.accepted) {\n      if (params.isLocked) {\n        return false;\n      }\n\n      if (params.clearingHouseId == null || params.clearingHouseId == \"\") {\n        return false;\n      }\n    } else if (params.claimStatusCode == CLAIM_TYPE.open) {\n      if (params.claimId > 0 && params.isActive && params.source && params.source.toUpperCase() == COMMON_VALUES.GATEWAY) {\n        return true;\n      } else {\n        return false;\n      }\n    } else if (params.claimStatusCode == CLAIM_TYPE.dispatched || params.claimStatusCode == CLAIM_TYPE.acceptedByPayer || params.claimStatusCode == CLAIM_TYPE.pending || params.claimStatusCode == CLAIM_TYPE.eobReceived || params.claimStatusCode == CLAIM_TYPE.rejectedByPayer) {\n      if (params.resubmissionCount < COMMON_VALUES.maximumResubmissionCount) {\n        return true;\n      } else {\n        return false;\n      }\n    } else if (params.claimStatusCode == CLAIM_TYPE.rejectedByCH || params.claimStatusCode == CLAIM_TYPE.acknolwedgedByPayer) {\n      if (params.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && params.parent_patientctrlno == null) {\n        return true;\n      } else {\n        return false;\n      }\n    } // else \n    // {\n    //   console.log('open isActive else',params)\n    //   if(params.parent_patientctrlno!=null)\n    //   {\n    //     return false;\n    //   }\n    //   return true;\n    // }\n\n\n    return true;\n  }\n\n}\nexport class ShowHideCheckboxClaimsGridAccept {\n  static showHideCheckbox(paramsDetails, isClaimBillingMngmntGenrtEDI837File) {\n    let params = paramsDetails?.data;\n    if (params?.clearingHouseId == null || params?.isLocked == true && isClaimBillingMngmntGenrtEDI837File) return false;else return isClaimBillingMngmntGenrtEDI837File;\n  }\n\n}\nexport let GridCustomizeTableConstants = /*#__PURE__*/(() => {\n  class GridCustomizeTableConstants {}\n\n  GridCustomizeTableConstants.Global_Search_GridCustomizeTable = [{\n    name: '',\n    field: 'claimControlNumber',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Claim Id',\n    field: 'claimId',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Member First Name',\n    field: 'displayMemberFirstName',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Member Last Name',\n    field: 'displayMemberLastName',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Member ID',\n    field: 'subscribeId',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'IPA',\n    field: 'ipaName',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Federal Tax ID',\n    field: 'taxId',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Date Of Birth',\n    field: 'patientDOB',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'DOS From',\n    field: 'dosFrom',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'DOS To',\n    field: 'dosTo',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'DOC',\n    field: 'doc',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Billing Provider First Name',\n    field: 'billingProviderFirstName',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Billing Provider Last Name',\n    field: 'billingProviderLastName',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Billing Provider NPI',\n    field: 'billingProviderNpi',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Rendering Provider First Name',\n    field: 'renderingProviderFirstName',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Rendering Provider Last Name',\n    field: 'renderingProviderLastName',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Rendering Provider NPI',\n    field: 'renderingProviderNPI',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Age',\n    field: 'age',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'CPT Code(s)',\n    field: 'cptCodes',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'ICD Code(s)',\n    field: 'icdCodes',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Insurance Name',\n    field: 'insuranceName',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Bucket Name',\n    field: 'bucketName',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'POS',\n    field: 'placeOfService',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Biller Name',\n    field: 'billerName',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Source',\n    field: 'source',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'MBI',\n    field: 'mbi',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Unique Encounter Number',\n    field: 'uniqueEncounterNumber',\n    selected: true,\n    disabled: false\n  }, {\n    name: 'Resubmission Count',\n    field: 'resubmissionCount',\n    selected: true,\n    disabled: false\n  }];\n  return GridCustomizeTableConstants;\n})();", "map": null, "metadata": {}, "sourceType": "module"}