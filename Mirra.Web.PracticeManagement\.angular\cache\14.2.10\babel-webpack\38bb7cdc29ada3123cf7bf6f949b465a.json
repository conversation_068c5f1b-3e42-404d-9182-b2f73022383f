{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"ngx-spinner\";\nimport * as i3 from \"src/app/services/Shared/user-data.service\";\nimport * as i4 from \"src/app/services/ReferringProvider/referring-provider.service\";\nimport * as i5 from \"@angular/material/dialog\";\nexport let ReferingProviderModalComponent = /*#__PURE__*/(() => {\n  class ReferingProviderModalComponent {\n    constructor(formBuilder, spinnder, userDataService, referringProviderService, dialogRef, modalData) {\n      this.formBuilder = formBuilder;\n      this.spinnder = spinnder;\n      this.userDataService = userDataService;\n      this.referringProviderService = referringProviderService;\n      this.dialogRef = dialogRef;\n      this.modalData = modalData;\n      this.error = '';\n      this.referringProviderResult = [];\n      this.referringProviderDetail = {};\n      this.SelectedRow = [];\n      this.referringProviderDetail.ipaCode = this.userDataService.getUserIPA();\n      this.referringProviderDetail.sortBy = \"ProviderNPI\";\n      this.referringProviderDetail.sortOrder = \"ASC\";\n      this.referringProviderDetail.uuid = JSON.parse(localStorage.getItem(\"uuid\"));\n      this.referringProviderDetail.username = JSON.parse(localStorage.getItem(\"email\"));\n      this.referringProviderDetail.index = 0;\n      this.referringProviderDetail.limit = 100;\n      this.displayedColumns = [{\n        field: 'planName',\n        header: 'planName'\n      }, {\n        field: 'name',\n        header: 'name'\n      }, {\n        field: 'taxId',\n        header: 'taxId'\n      }, {\n        field: 'providerFirstAddress',\n        header: 'providerFirstAddress'\n      }, {\n        field: 'ipaName',\n        header: 'ipaName'\n      }, {\n        field: 'select',\n        header: 'select'\n      }]; //  this.displayedColumns=['planName','name','taxId','providerFirstAddress','ipaName','select'];\n    }\n\n    getValue(set, element) {\n      return element.set;\n    }\n\n    ngOnInit() {\n      this.ModalForm = this.formBuilder.group({\n        name: ['', []],\n        providerNPI: ['', []],\n        ipaName: ['', []],\n        providerFirstAddress: ['', [Validators.maxLength(8)]],\n        planName: ['', []],\n        taxId: ['', []]\n      });\n\n      if (this.modalData.ReferringProviderId != null && this.modalData.ReferringProviderId != '') {\n        this.referringProviderDetail.providerNPI = this.modalData.ReferringProviderId;\n        this.ModalForm.controls.providerNPI.setValue(this.modalData.ReferringProviderId);\n        this.spinnder.show();\n        this.fetchMemberResult();\n      }\n    }\n\n    onChange(val) {\n      this.SelectedRow[0] = val;\n      this.SetSelected();\n    }\n\n    SetValues(name, value) {\n      value = value.trim();\n\n      switch (name) {\n        case 'Name':\n          this.referringProviderDetail.searchProvider_Name = value;\n          break;\n\n        case 'NPI':\n          this.referringProviderDetail.providerNPI = value;\n          break;\n\n        case 'Tax ID':\n          if (value == '') this.referringProviderDetail.taxId = null;else this.referringProviderDetail.taxId = value;\n          break;\n          break;\n\n        case 'Plan Name':\n          this.referringProviderDetail.planName = value;\n          break;\n\n        case 'IPA Name':\n          this.referringProviderDetail.ipaName = value;\n          break;\n\n        case 'Address':\n          this.referringProviderDetail.address1 = value;\n          break;\n      }\n    }\n\n    Search() {\n      this.spinnder.show();\n      this.fetchMemberResult();\n    }\n\n    InputTypeSelect(inputType) {\n      return inputType === 'select';\n    }\n\n    InputTypeNumber(inputType) {\n      return inputType === \"number\";\n    }\n\n    RowsGreaterThanZero() {\n      return this.length > 0;\n    }\n\n    SetSelected() {\n      if (this.SelectedRow != null && this.SelectedRow.length != 0) this.dialogRef.close(this.SelectedRow);else return;\n    }\n\n    ClearSearch() {\n      this.spinnder.show();\n      this.referringProviderDetail.address1 = \"\";\n      this.referringProviderDetail.searchProvider_Name = \"\";\n      this.referringProviderDetail.ipaName = \"\";\n      this.referringProviderDetail.planName = \"\";\n      this.referringProviderDetail.taxId = null;\n      this.referringProviderDetail.providerNPI = \"\";\n      this.fetchMemberResult();\n    }\n\n    fetchMemberResult() {\n      /* this.referringProviderService.fetchProviderResult(this.referringProviderDetail).pipe(first())\r\n       .subscribe(\r\n         data => {\r\n         this.referringProviderResult = data;\r\n         this.length = this.referringProviderResult.length;\r\n         this.spinnder.hide();\r\n         },\r\n         error => {\r\n         this.error = error;\r\n         this.loading = false;\r\n       });*/\n    }\n\n    onClose(response) {\n      this.dialogRef.close(response);\n    }\n\n  }\n\n  ReferingProviderModalComponent.ɵfac = function ReferingProviderModalComponent_Factory(t) {\n    return new (t || ReferingProviderModalComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.NgxSpinnerService), i0.ɵɵdirectiveInject(i3.UserDataService), i0.ɵɵdirectiveInject(i4.ReferringProviderService), i0.ɵɵdirectiveInject(i5.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n  };\n\n  ReferingProviderModalComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ReferingProviderModalComponent,\n    selectors: [[\"app-refering-provider-modal\"]],\n    decls: 1,\n    vars: 0,\n    template: function ReferingProviderModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\");\n      }\n    },\n    styles: [\"@charset \\\"UTF-8\\\";.Insured-Modal-Heading[_ngcontent-%COMP%]{font-weight:700;font-size:22px;margin-left:-10px}.Grey-Color[_ngcontent-%COMP%]{color:#646464}input[_ngcontent-%COMP%]{outline:none;border:1px solid #D9DADE;font-family:Poppins;font-size:12px;width:132px;border-radius:8px;height:34px}select[_ngcontent-%COMP%]{padding:0 10px;font-size:12px;font-family:Poppins;height:34px;outline:none;border:1px solid #D9DADE;width:132px;border-radius:8px}tr.mat-header-row[_ngcontent-%COMP%]{border-radius:8px 8px 0 0;top:0;position:sticky;z-index:100}th[_ngcontent-%COMP%]{color:#272d3b!important;font-family:Poppins-Bold;background-color:#ededed!important;opacity:1;border:1px solid #EDEDEE!important}td[_ngcontent-%COMP%]{font-family:Poppins;color:#272d3b;font-size:12px;letter-spacing:0px;opacity:1;text-overflow:ellipsis;line-height:33px;padding:11px 0 0 13px!important}  .custom-frame .mat-checkbox-background,   .custom-frame .mat-checkbox-frame{border-radius:70%!important}th.mat-sort-header-sorted[_ngcontent-%COMP%]{color:#000}.mat-row[_ngcontent-%COMP%]:hover   .mat-cell[_ngcontent-%COMP%]{background-color:#eff9ff}.mat-checkbox-checked[_ngcontent-%COMP%]{display:inline}.Table-Width-Styling[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content}mat-table[_ngcontent-%COMP%]{overflow-y:auto;height:48vh;width:98%!important;box-shadow:none;background:#FFFFFF 0% 0% no-repeat padding-box;border:1px solid #DEDFE1;border-radius:8px;opacity:1}.Input-Label[_ngcontent-%COMP%]{color:#646464;font-family:Poppins}.btn[_ngcontent-%COMP%]{width:80px;height:34px;font-family:Poppins-SemiBold;box-shadow:0 0 8px #0000001a;border-radius:4px;opacity:1}.btn-outline-custom[_ngcontent-%COMP%]{color:#0074bc;border-color:#0074bc}.NoDataBox[_ngcontent-%COMP%]{text-align:center;height:17vw;width:98%!important;box-shadow:none;background:#FFFFFF 0% 0% no-repeat padding-box;border:1px solid #DEDFE1;border-radius:8px;opacity:1;background-color:#f7f7f7}.FirstHeadingNoData[_ngcontent-%COMP%]{color:#646464;font-size:32px;margin-top:7%}.SecondHeadingNoData[_ngcontent-%COMP%]{color:#646464;margin-top:2%}.btn-outline-custom[_ngcontent-%COMP%]:active{color:#fff;background-color:#0074bc;border-color:#0074bc}.Right-Align[_ngcontent-%COMP%]{text-align:end;justify-content:flex-end;margin-right:10px}.SpaceBtwnBtns[_ngcontent-%COMP%]{background-color:#0074bc;color:#fff;margin-right:15px}.Close-Icon[_ngcontent-%COMP%]{margin-top:-10px;margin-left:10px;padding-right:0;text-align:right}.col-2[_ngcontent-%COMP%]{flex:0 0 auto;width:146px}.Total-Records[_ngcontent-%COMP%]{padding-top:17px;text-align:end;color:#0074bc;font-family:Poppins;font-size:15px;opacity:1}th[_ngcontent-%COMP%]{padding-left:10px!important;padding-top:11px!important}.Modal-Close-Image[_ngcontent-%COMP%]{cursor:pointer;width:26px}.CheckboxHeader[_ngcontent-%COMP%]{margin-left:10px}  .mat-sort-header-container:not(.mat-sort-header-sorted) .mat-sort-header-arrow{opacity:.54!important;transform:translateY(0)!important}.OverflowYInitial[_ngcontent-%COMP%]{overflow-y:initial!important}.WidthOfCheckBox[_ngcontent-%COMP%]{width:8%}.DOCDateRangePicker[_ngcontent-%COMP%]{width:150px;height:28px}.DOCDateRangePicker[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]{margin-top:-10px}.DOCInputBox[_ngcontent-%COMP%]{width:150px;height:28px;background:#FFFFFF 0% 0% no-repeat padding-box;border:1px solid #D5D7E3;border-radius:8px;opacity:1}[_ngcontent-%COMP%]:ng-deep   .e-calendar[_ngcontent-%COMP%]{overflow:hidden!important}.DOCDateRangePicker1[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]{margin-top:0}.DOCInputBox1[_ngcontent-%COMP%]{width:135px;height:33px;background:#FFFFFF 0% 0% no-repeat padding-box;border:1px solid #D5D7E3;border-radius:8px;opacity:1}input[type=radio][_ngcontent-%COMP%]{color:#fff!important;border-radius:50%;-webkit-appearance:none;appearance:none;border:1px solid #d3d3d3;width:22px;height:22px;content:none;outline:none;margin:0 2px 0 0;padding:0!important;box-shadow:none}input[type=radio][_ngcontent-%COMP%]:checked{width:22px;height:22px;-webkit-appearance:none;appearance:none;outline:none;padding:0;content:none;border:none;box-shadow:none}input[type=radio][_ngcontent-%COMP%]:checked:before{background-color:#0074bc!important;color:#fff!important;content:\\\"\\\\a0\\\\2713\\\\a0\\\"!important;border:1px solid #d3d3d3;font-weight:700;font-size:16px;border-radius:50%;width:22px;height:22px;box-shadow:none;border:none;padding:0}.sr-only[_ngcontent-%COMP%]{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);border:0}[_nghost-%COMP%]     .pi-sort-alt:before{content:\\\"\\\\f0dc\\\"!important;font-family:FontAwesome!important}  .pi-sort-amount-down:before{content:\\\"\\\\f0dc\\\"!important;font-family:FontAwesome!important}  .pi-sort-amount-up-alt:before{content:\\\"\\\\f0dc\\\"!important;font-family:FontAwesome!important}[_nghost-%COMP%]     .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon{color:initial!important}  .p-datatable-scrollable-body{min-height:200px}.Width10[_ngcontent-%COMP%]{width:10%}.Width15[_ngcontent-%COMP%]{width:15%}.Width25[_ngcontent-%COMP%]{width:25%}.Width17[_ngcontent-%COMP%]{width:17%}.Width23[_ngcontent-%COMP%]{width:23%}.Width2[_ngcontent-%COMP%]{width:2%}.Width5[_ngcontent-%COMP%]{width:5%}  .e-calendar .e-content td.e-today.e-selected span.e-day, .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-today.e-selected[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%]{background-color:#0074bc!important;border:1px solid #0074BC!important;box-shadow:inset 0 0 0 2px #fff!important;color:#fff!important}  .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-today span.e-day, .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-start-date.e-selected.e-range-hover.e-today[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%]{background-color:#0074bc!important;color:#fff!important}  .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-today:hover span.e-day, .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-range-hover.e-focused-date.e-today[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-range-hover.e-start-date.e-selected.e-today[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-range-hover.e-end-date.e-selected.e-today[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-range-hover.e-today[_ngcontent-%COMP%]:hover   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-range-hover.e-focused-date.e-today[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-range-hover.e-start-date.e-selected.e-today[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-range-hover.e-end-date.e-selected.e-today[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%]{border:1px solid #0074BC!important}  .e-daterangepicker.e-popup .e-calendar .e-content.e-month .e-today.e-range-hover span, .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content.e-month[_ngcontent-%COMP%]   .e-today.e-range-hover[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background-color:#eee!important;border:1px solid #0074BC!important;color:#0074bc!important}  .e-calendar .e-content td.e-today span.e-day, .e-calendar[_ngcontent-kbk-c96][_ngcontent-%COMP%]   .e-content[_ngcontent-kbk-c96][_ngcontent-%COMP%]   td.e-focused-date.e-today[_ngcontent-kbk-c96][_ngcontent-%COMP%]   span.e-day[_ngcontent-kbk-c96][_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-kbk-c96][_ngcontent-%COMP%]   .e-calendar[_ngcontent-kbk-c96][_ngcontent-%COMP%]   .e-content[_ngcontent-kbk-c96][_ngcontent-%COMP%]   td.e-today[_ngcontent-kbk-c96][_ngcontent-%COMP%]   span.e-day[_ngcontent-kbk-c96][_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-kbk-c96][_ngcontent-%COMP%]   .e-calendar[_ngcontent-kbk-c96][_ngcontent-%COMP%]   .e-content[_ngcontent-kbk-c96][_ngcontent-%COMP%]   td.e-focused-date.e-today[_ngcontent-kbk-c96][_ngcontent-%COMP%]   span.e-day[_ngcontent-kbk-c96][_ngcontent-%COMP%]{background:none;border:1px solid #0074BC!important;border-radius:50%;color:#0074bc!important}  .e-calendar .e-content td.e-today span.e-day, .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-focused-date.e-today[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-today[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-focused-date.e-today[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%]{background:none;border:1px solid #0074BC!important;border-radius:50%;color:#0074bc!important}  .e-calendar .e-content td.e-today.e-selected:hover span.e-day, .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-selected[_ngcontent-%COMP%]:hover   span.e-day[_ngcontent-%COMP%], .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-selected.e-focused-date[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-today.e-selected[_ngcontent-%COMP%]:hover   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-selected[_ngcontent-%COMP%]:hover   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-selected.e-focused-date[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%]{background-color:#0074bc!important;color:#fff!important}  .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day, .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-start-date.e-selected.e-range-hover[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-end-date.e-selected.e-range-hover[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-start-date.e-selected.e-range-hover[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%]{background-color:#0074bc!important;color:#fff!important}  .e-daterangepicker.e-popup .e-presets .e-list-item.e-active, .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-presets[_ngcontent-%COMP%]   .e-list-item.e-active[_ngcontent-%COMP%]{color:#0074bc!important}  .e-date-range-wrapper .e-input-group-icon.e-icons.e-active{color:#0074bc!important}  .e-input-group:not(.e-float-icon-left):not(.e-float-input):before, .e-input-group[_ngcontent-%COMP%]:not(.e-float-icon-left):not(.e-float-input):after, .e-input-group.e-float-icon-left[_ngcontent-%COMP%]:not(.e-float-input)   .e-input-in-wrap[_ngcontent-%COMP%]:before, .e-input-group.e-float-icon-left[_ngcontent-%COMP%]:not(.e-float-input)   .e-input-in-wrap[_ngcontent-%COMP%]:after, .e-input-group.e-control-wrapper[_ngcontent-%COMP%]:not(.e-float-icon-left):not(.e-float-input):before, .e-input-group.e-control-wrapper[_ngcontent-%COMP%]:not(.e-float-icon-left):not(.e-float-input):after, .e-input-group.e-control-wrapper.e-float-icon-left[_ngcontent-%COMP%]:not(.e-float-input)   .e-input-in-wrap[_ngcontent-%COMP%]:before, .e-input-group.e-control-wrapper.e-float-icon-left[_ngcontent-%COMP%]:not(.e-float-input)   .e-input-in-wrap[_ngcontent-%COMP%]:after{background-color:#0074bc!important}  .e-btn.e-flat.e-primary, .e-css.e-btn.e-flat.e-primary[_ngcontent-%COMP%]{color:#fff!important;border-color:transparent;background-color:#0074bc!important}  .e-btn.e-flat.e-primary:active, .e-css.e-btn.e-flat.e-primary[_ngcontent-%COMP%]:active{background-color:#0074bc1f!important;border-color:transparent;color:#0074bc!important}  .e-calendar .e-content td.e-today.e-selected:hover span.e-day, .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-selected[_ngcontent-%COMP%]:hover   span.e-day[_ngcontent-%COMP%], .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-selected.e-focused-date[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-today.e-selected[_ngcontent-%COMP%]:hover   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-selected[_ngcontent-%COMP%]:hover   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-selected.e-focused-date[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%]{background-color:#0074bc!important}  .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day, .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-start-date.e-selected.e-range-hover[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-end-date.e-selected.e-range-hover[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-start-date.e-selected.e-range-hover[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%]{background-color:#0074bc!important}  .e-calendar .e-content td.e-selected span.e-day, .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-content[_ngcontent-%COMP%]   td.e-selected[_ngcontent-%COMP%]   span.e-day[_ngcontent-%COMP%]{background-color:#0074bc!important}[_ngcontent-%COMP%]:ng-deep   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar-container[_ngcontent-%COMP%]   .e-left-container[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar-container[_ngcontent-%COMP%]   .e-left-container[_ngcontent-%COMP%]   .e-left-calendar[_ngcontent-%COMP%]{overflow:hidden!important}[_ngcontent-%COMP%]:ng-deep   .e-calendar[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]{overflow:hidden!important}  .e-daterangepicker.e-popup .e-calendar-container .e-left-container, .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-calendar-container[_ngcontent-%COMP%]   .e-left-container[_ngcontent-%COMP%]   .e-calendar[_ngcontent-%COMP%]   .e-left-calendar[_ngcontent-%COMP%]   .e-lib[_ngcontent-%COMP%]   .e-keyboard[_ngcontent-%COMP%]{overflow:hidden!important}[_ngcontent-%COMP%]:ng-deep   .e-daterangepicker.e-popup[_ngcontent-%COMP%], .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]{box-shadow:0 0 8px #0000001a!important;border-radius:8px!important}  .e-daterangepicker.e-popup.e-preset-wrapper, .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup.e-preset-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 8px #0000001a!important;border-radius:8px!important}  .e-daterangepicker.e-popup .e-footer, .e-bigger.e-small[_ngcontent-%COMP%]   .e-daterangepicker.e-popup[_ngcontent-%COMP%]   .e-footer[_ngcontent-%COMP%]{border-radius:8px 0}\"]\n  });\n  return ReferingProviderModalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}