.ModalWidth {
    max-width: 800px;
}

.SemiBold {
    font-family: "Poppins-SemiBold";
}

.Heading {
    text-align: center;
    font-size: 17px;
}
.Error {
    color: red;
    font-family: "Poppins-SemiBold";
}

.btn {
    height: 35px;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
}

.reset {
    background-color: white;
    color: #0074bc;
    border: solid 1px #0074bc;
}

.MarginRight {
    margin-right: 7px;
}

.Modal-Close-Image {
    cursor: pointer;
}


.radioClass {
    height: 16px;
    width: 16px;
}

.radiopClass {
    height: 16px;
    width: 16px;
}

.originalIcon{
    height: 16px; 
    width: 16px;
    margin-right: 5px;
}


.customIcon{
    height: 16px;
    width: 16px; 
    margin-right: 5px;
    margin-top:3px
}


input[type=checkbox] {
    /* Hide original inputs */
    opacity: 0.001;
    visibility: visible;
    position: absolute;
    z-index: 1000;
}

input[type=checkbox]+label+p {
    height: 16px;
    width: 16px;
}

input[type=checkbox]+label>p {
    transition: 100ms all;
    height: 16px;
    width: 16px;
}

input[type=checkbox]:checked+label>p {
    z-index: -1000;
    background-repeat: no-repeat;
    border-style: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16.17' height='16.505' viewBox='0 0 14.17 13.505'%3E%3Cg id='Checkbox' transform='translate(0 0.001)'%3E%3Crect id='Rectangle' width='14.17' height='13.505' rx='3' transform='translate(0 -0.001)' fill='%230074bc'/%3E%3Cpath id='check' d='M10.97,4.97a.75.75,0,1,1,1.071,1.05L8.049,11.01a.75.75,0,0,1-1.08.02L4.324,8.384a.75.75,0,1,1,1.06-1.06L7.478,9.417l3.473-4.425.02-.022Z' transform='translate(-1.085 -0.745)' fill='%23fff' fill-rule='evenodd'/%3E%3C/g%3E%3C/svg%3E%0A");
}

input[type=checkbox]+label>p {
    width: 16px;
    height: 16px;
    border: #999;
    border-width: 1px;
    border-style: solid;
    border-radius: 5px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16.17' height='16.505' viewBox='0 0 8.17 6.505'%3E%3Cpath id='check' d='M10.97,4.97a.75.75,0,1,1,1.071,1.05L8.049,11.01a.75.75,0,0,1-1.08.02L4.324,8.384a.75.75,0,1,1,1.06-1.06L7.478,9.417l3.473-4.425.02-.022Z' transform='translate(-4.085 -4.745)' fill='%23f4f7fc'/%3E%3C/svg%3E%0A");
}


.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}



