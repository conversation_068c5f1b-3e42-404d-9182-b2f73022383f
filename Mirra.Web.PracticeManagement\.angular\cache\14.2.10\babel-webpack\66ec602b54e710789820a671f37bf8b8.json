{"ast": null, "code": "import { BillingProviderPopupComponent } from '../../popups/billing-provider-popup/billing-provider-popup.component';\nimport { SearchInsuredPopupComponent } from '../../popups/search-insured-popup/search-insured-popup.component';\nimport { RenderingProviderPopupComponent } from '../../rendering-provider-popup/rendering-provider-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"angular-ng-autocomplete\";\nimport * as i5 from \"@angular/material/icon\";\n\nfunction FfsComponent_ng_template_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"a\", 117);\n  }\n\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r5.name, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction FfsComponent_ng_template_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 117);\n  }\n\n  if (rf & 2) {\n    const notFound_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"innerHTML\", notFound_r6, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction FfsComponent_tr_872_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 8)(3, \"div\", 118)(4, \"div\", 119)(5, \"input\", 120);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_5_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.dynamicArray[i_r8].from = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 121)(7, \"input\", 122);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.dynamicArray[i_r8].from = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"td\")(9, \"div\", 8)(10, \"div\", 27)(11, \"input\", 122);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_11_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.dynamicArray[i_r8].to = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"td\")(13, \"div\", 8)(14, \"div\", 27)(15, \"input\", 123);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_15_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.dynamicArray[i_r8].poservice = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"td\")(17, \"div\", 8)(18, \"div\", 27)(19, \"select\", 124);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_select_ngModelChange_19_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.dynamicArray[i_r8].emg = $event);\n    });\n    i0.ɵɵelementStart(20, \"option\", 61);\n    i0.ɵɵtext(21, \"Open this select menu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"option\", 62);\n    i0.ɵɵtext(23, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\", 125);\n    i0.ɵɵtext(25, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"option\", 126);\n    i0.ɵɵtext(27, \"Three\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(28, \"td\")(29, \"div\", 8)(30, \"div\", 27)(31, \"input\", 127);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_31_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.dynamicArray[i_r8].cpt = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"td\")(33, \"div\", 128)(34, \"div\", 27)(35, \"input\", 129);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_35_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.dynamicArray[i_r8].modifier = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 27)(37, \"input\", 130);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_37_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.dynamicArray[i_r8].modifier = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 27)(39, \"input\", 131);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_39_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.dynamicArray[i_r8].modifier = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 27)(41, \"input\", 132);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_41_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.dynamicArray[i_r8].modifier = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"td\")(43, \"div\", 128)(44, \"div\", 27)(45, \"select\", 124);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_select_ngModelChange_45_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.dynamicArray[i_r8].dp = $event);\n    });\n    i0.ɵɵelementStart(46, \"option\", 61);\n    i0.ɵɵtext(47, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"option\", 62);\n    i0.ɵɵtext(49, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"option\", 125);\n    i0.ɵɵtext(51, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"option\", 126);\n    i0.ɵɵtext(53, \"Three\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 27)(55, \"select\", 124);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_select_ngModelChange_55_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.dynamicArray[i_r8].dp = $event);\n    });\n    i0.ɵɵelementStart(56, \"option\", 61);\n    i0.ɵɵtext(57, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"option\", 62);\n    i0.ɵɵtext(59, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"option\", 125);\n    i0.ɵɵtext(61, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"option\", 126);\n    i0.ɵɵtext(63, \"Three\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(64, \"div\", 27)(65, \"select\", 124);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_select_ngModelChange_65_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.dynamicArray[i_r8].dp = $event);\n    });\n    i0.ɵɵelementStart(66, \"option\", 61);\n    i0.ɵɵtext(67, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"option\", 62);\n    i0.ɵɵtext(69, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"option\", 125);\n    i0.ɵɵtext(71, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"option\", 126);\n    i0.ɵɵtext(73, \"Three\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(74, \"div\", 27)(75, \"select\", 124);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_select_ngModelChange_75_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.dynamicArray[i_r8].dp = $event);\n    });\n    i0.ɵɵelementStart(76, \"option\", 61);\n    i0.ɵɵtext(77, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"option\", 62);\n    i0.ɵɵtext(79, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"option\", 125);\n    i0.ɵɵtext(81, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"option\", 126);\n    i0.ɵɵtext(83, \"Three\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(84, \"td\")(85, \"div\", 8)(86, \"div\", 27)(87, \"input\", 133);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_87_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.dynamicArray[i_r8].uc = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(88, \"td\")(89, \"div\", 8)(90, \"div\", 27)(91, \"input\", 133);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_91_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.dynamicArray[i_r8].du = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(92, \"td\")(93, \"div\", 8)(94, \"div\", 27)(95, \"select\", 124);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_select_ngModelChange_95_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.dynamicArray[i_r8].um = $event);\n    });\n    i0.ɵɵelementStart(96, \"option\", 61);\n    i0.ɵɵtext(97, \"Open this select menu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"option\", 62);\n    i0.ɵɵtext(99, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"option\", 125);\n    i0.ɵɵtext(101, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"option\", 126);\n    i0.ɵɵtext(103, \"Three\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(104, \"td\")(105, \"div\", 134)(106, \"div\", 135)(107, \"input\", 133);\n    i0.ɵɵlistener(\"ngModelChange\", function FfsComponent_tr_872_Template_input_ngModelChange_107_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.dynamicArray[i_r8].rpid = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(108, \"div\", 21)(109, \"mat-icon\", 136);\n    i0.ɵɵlistener(\"click\", function FfsComponent_tr_872_Template_mat_icon_click_109_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.deleteRow(i_r8));\n    });\n    i0.ɵɵtext(110, \"close\");\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].from);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].from);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].to);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].poservice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].emg);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].cpt);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].modifier);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].modifier);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].modifier);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].modifier);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].dp);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].dp);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].dp);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].dp);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].uc);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].du);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].um);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.dynamicArray[i_r8].rpid);\n  }\n}\n\nexport let FfsComponent = /*#__PURE__*/(() => {\n  class FfsComponent {\n    constructor(dialog) {\n      this.dialog = dialog;\n      this.dynamicArray = [];\n      this.newDynamic = {};\n      this.keyword = 'name';\n      this.countries = [{\n        id: 1,\n        name: 'Albania'\n      }, {\n        id: 2,\n        name: 'Belgium'\n      }, {\n        id: 3,\n        name: 'Denmark'\n      }, {\n        id: 4,\n        name: 'Montenegro'\n      }, {\n        id: 5,\n        name: 'Turkey'\n      }, {\n        id: 6,\n        name: 'Ukraine'\n      }, {\n        id: 7,\n        name: 'Macedonia'\n      }, {\n        id: 8,\n        name: 'Slovenia'\n      }, {\n        id: 9,\n        name: 'Georgia'\n      }, {\n        id: 10,\n        name: 'India'\n      }, {\n        id: 11,\n        name: 'Russia'\n      }, {\n        id: 12,\n        name: 'Switzerland'\n      }];\n    }\n\n    ngOnInit() {\n      this.newDynamic = {\n        from: \"\",\n        to: \"\",\n        poservice: \"\",\n        emg: \"\",\n        cpt: \"\",\n        modifier: \"\",\n        dp: \"\",\n        uc: \"\",\n        du: \"\",\n        um: \"\",\n        epsdt: \"\",\n        idqual: \"\",\n        rpid: \"\"\n      };\n      this.dynamicArray.push(this.newDynamic);\n    }\n\n    addRow() {\n      this.newDynamic = {\n        from: \"\",\n        to: \"\",\n        poservice: \"\",\n        emg: \"\",\n        cpt: \"\",\n        modifier: \"\",\n        dp: \"\",\n        uc: \"\",\n        du: \"\",\n        um: \"\",\n        epsdt: \"\",\n        idqual: \"\",\n        rpid: \"\"\n      };\n      this.dynamicArray.push(this.newDynamic);\n      console.log(this.dynamicArray);\n      return true;\n    }\n\n    deleteRow(index) {\n      if (this.dynamicArray.length == 1) {\n        return false;\n      } else {\n        this.dynamicArray.splice(index, 1);\n        return true;\n      }\n    }\n\n    selectEvent(item) {// do something with selected item\n    }\n\n    onChangeSearch(search) {// fetch remote data from here\n      // And reassign the 'data' which is binded to 'data' property.\n    }\n\n    onFocused(e) {// do something\n    }\n\n    searchInsuredId() {\n      this.dialog.open(SearchInsuredPopupComponent, {\n        height: '70%',\n        width: '80%',\n        panelClass: 'custom-dialog-containers'\n      });\n    }\n\n    searchBillingProvider() {\n      this.dialog.open(BillingProviderPopupComponent, {\n        height: '70%',\n        width: '80%',\n        panelClass: 'custom-dialog-containers'\n      });\n    }\n\n    searchRenderingProvider() {\n      this.dialog.open(RenderingProviderPopupComponent, {\n        height: '70%',\n        width: '80%',\n        panelClass: 'custom-dialog-containers'\n      });\n    }\n\n  }\n\n  FfsComponent.ɵfac = function FfsComponent_Factory(t) {\n    return new (t || FfsComponent)(i0.ɵɵdirectiveInject(i1.MatDialog));\n  };\n\n  FfsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FfsComponent,\n    selectors: [[\"app-ffs\"]],\n    decls: 1243,\n    vars: 5,\n    consts: [[1, \"container-fluid\"], [1, \"mat-card\", \"mt-3\", \"create-claim-form-styles\"], [1, \"create-claim\"], [1, \"create-claim-title\"], [1, \"create-claim-action\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [1, \"row\"], [1, \"col-md\", \"form-border\"], [1, \"row\", \"mt-2\"], [1, \"col-md\", \"claim-title\"], [1, \"col-md\"], [\"for\", \"flexCheckDefault\", 1, \"\"], [\"for\", \"flexCheckDefault\", 1, \"create-claims-labels\"], [\"type\", \"text\", \"placeholder\", \"Valor Health Plan\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Payer ID\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Address Line - 1\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Address Line - 2\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"City\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"State\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Payer Zip\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-md-8\", \"form-border\"], [1, \"col-md-3\"], [1, \"form-title\"], [1, \"form-check\", \"form-check-inline\", \"radio-flex\"], [\"type\", \"radio\", \"name\", \"inlineRadioOptions\", \"id\", \"inlineRadio1\", \"value\", \"option1\", 1, \"form-check-input\"], [\"for\", \"inlineRadio1\", 1, \"create-claim-radio-labels\"], [1, \"col-md-4\", \"form-border\"], [1, \"col\"], [1, \"search-icon-alignment\"], [\"placeholder\", \"Enter Name\", \"historyIdentifier\", \"countries\", 1, \"form-control\", \"form-control-sm\", 3, \"data\", \"searchKeyword\", \"itemTemplate\", \"notFoundTemplate\", \"selected\", \"inputChanged\", \"inputFocused\"], [\"itemTemplate\", \"\"], [\"notFoundTemplate\", \"\"], [1, \"search-icons\", 3, \"click\"], [1, \"2\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Last Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Middle Name\", 1, \"form-control\", \"form-control-sm\"], [1, \"3\", \"bd\"], [\"type\", \"date\", \"name\", \"birthday\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"radio\", \"name\", \"inlineRadioOptions\", \"id\", \"inlineRadio2\", \"value\", \"option2\", 1, \"form-check-input\"], [\"for\", \"inlineRadio2\", 1, \"create-claim-radio-labels\"], [1, \"5\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Stae\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Zip\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Telephone\", 1, \"form-control\", \"form-control-sm\"], [1, \"6\", \"bd\"], [1, \"col\", \"radio-flex\"], [1, \"8\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [1, \"9\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Other Insured\\u2019s Policy Or Group Number\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-4\"], [1, \"col-8\"], [\"type\", \"text\", \"placeholder\", \"Reserved For NUCC Use\", 1, \"form-control\", \"form-control-sm\"], [1, \"10\", \"bd\"], [1, \"col-md\", \"radio-flex\"], [1, \"\"], [1, \"14\", \"bd\"], [\"aria-label\", \"Default select example\", 1, \"form-select\", \"form-select-sm\"], [\"selected\", \"\"], [\"value\", \"1\"], [1, \"15\", \"bd\"], [1, \"17\", \"bd\"], [1, \"bd\"], [1, \"col-md-2\"], [1, \"col-md-4\"], [1, \"19\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Additional Claim Information\", 1, \"form-control\", \"form-control-sm\"], [1, \"21\", \"bd\"], [1, \"form-select\", \"form-select-sm\"], [\"type\", \"text\", \"placeholder\", \"Resubmission Code\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Reference Number\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Prior Authorization Number\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup Address 1\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup Address 2\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup City\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup State\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Zip Code\", 1, \"form-control\", \"form-control-sm\"], [1, \"24\"], [1, \"table-responsive\"], [1, \"table\", \"table-bordered\"], [\"colspan\", \"2\", 2, \"font-size\", \"12px\"], [\"scope\", \"col\", 2, \"font-size\", \"12px\"], [\"scope\", \"col\"], [4, \"ngFor\", \"ngForOf\"], [1, \"action-flex\"], [\"disabled\", \"\", 1, \"btn-common-danger\"], [1, \"btn-primary\", 3, \"click\"], [\"type\", \"text\", \"placeholder\", \"Search\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Patient Account Number\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Total Charge\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Amount Paid\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Reserved For NUCC\", 1, \"form-control\", \"form-control-sm\"], [1, \"31\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\"], [1, \"row\", \"mt-2\", \"mt-3\"], [\"type\", \"text\", \"placeholder\", \"NPI\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Provider ID\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"CLIA\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Mammography Certificate\", 1, \"form-control\", \"form-control-sm\"], [1, \"32\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Facility Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Address Line 1\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Address Line 2\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Facility ID\", 1, \"form-control\", \"form-control-sm\"], [1, \"33\"], [1, \"col\", \"search-icon-alignment\"], [\"type\", \"text\", \"placeholder\", \"Billing provider\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"ZIP Code\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Taxonomy Code\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Rendering Provider\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Provider Specialty\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Provider NPI\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Provider PIN\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Qual\", 1, \"form-control\", \"form-control-sm\"], [3, \"innerHTML\"], [1, \"col-2\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"value\", \"\", \"id\", \"flexCheckDefault\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-10\"], [\"type\", \"date\", \"name\", \"birthday\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"POS\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"aria-label\", \"Default select example\", 1, \"form-select\", \"form-select-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"type\", \"text\", \"placeholder\", \"CPT\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table-flex\", \"mt-2\"], [\"type\", \"text\", \"placeholder\", \"A\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"B\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"C\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"D\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [1, \"d-flex\", \"mt-2\"], [1, \"col-md-9\"], [2, \"color\", \"red\", 3, \"click\"]],\n    template: function FfsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵtext(4, \" Create Claim (CMS 1500) \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 4)(6, \"button\", 5);\n        i0.ɵɵtext(7, \"Save\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9);\n        i0.ɵɵtext(12, \" Current Claim Status: \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9);\n        i0.ɵɵtext(15, \" Created on \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 8)(17, \"div\", 9);\n        i0.ɵɵtext(18, \" Created By \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 8)(20, \"div\", 10);\n        i0.ɵɵelement(21, \"label\", 11);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(22, \"div\", 7)(23, \"div\", 8)(24, \"div\", 9);\n        i0.ɵɵtext(25, \" Held On \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 8)(27, \"div\", 9);\n        i0.ɵɵtext(28, \" Held By \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(29, \"div\", 8)(30, \"div\", 9);\n        i0.ɵɵtext(31, \" Reason for holding \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 8)(33, \"div\", 9);\n        i0.ɵɵtext(34, \" Description \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(35, \"div\", 7)(36, \"div\", 8)(37, \"div\", 10)(38, \"label\", 12);\n        i0.ɵɵtext(39, \"Payer Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(40, \"input\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"div\", 10)(42, \"label\", 12);\n        i0.ɵɵtext(43, \"Payer ID\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(44, \"input\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"div\", 8)(46, \"div\", 10)(47, \"label\", 12);\n        i0.ɵɵtext(48, \"1st Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(49, \"input\", 15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(50, \"div\", 8)(51, \"div\", 10)(52, \"label\", 12);\n        i0.ɵɵtext(53, \"2nd Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(54, \"input\", 16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(55, \"div\", 8)(56, \"div\", 10)(57, \"label\", 12);\n        i0.ɵɵtext(58, \"City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(59, \"div\", 10)(60, \"label\", 12);\n        i0.ɵɵtext(61, \"State\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(62, \"div\", 10)(63, \"label\", 12);\n        i0.ɵɵtext(64, \"Payer Zip\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(65, \"div\", 6)(66, \"div\", 10);\n        i0.ɵɵelement(67, \"input\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"div\", 10);\n        i0.ɵɵelement(69, \"input\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"div\", 10);\n        i0.ɵɵelement(71, \"input\", 19);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(72, \"div\", 6)(73, \"div\", 20)(74, \"div\", 6)(75, \"div\", 21)(76, \"p\", 22);\n        i0.ɵɵtext(77, \"1. Medicare\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(78, \"div\", 23);\n        i0.ɵɵelement(79, \"input\", 24);\n        i0.ɵɵelementStart(80, \"label\", 25);\n        i0.ɵɵtext(81, \"Medicare\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(82, \"div\", 21)(83, \"p\", 22);\n        i0.ɵɵtext(84, \"Medicaid\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(85, \"div\", 23);\n        i0.ɵɵelement(86, \"input\", 24);\n        i0.ɵɵelementStart(87, \"label\", 25);\n        i0.ɵɵtext(88, \"Medicaid\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(89, \"div\", 21)(90, \"p\", 22);\n        i0.ɵɵtext(91, \"Tricare\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(92, \"div\", 23);\n        i0.ɵɵelement(93, \"input\", 24);\n        i0.ɵɵelementStart(94, \"label\", 25);\n        i0.ɵɵtext(95, \"Tricare\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(96, \"div\", 21)(97, \"p\", 22);\n        i0.ɵɵtext(98, \"CHAMPVA\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(99, \"div\", 23);\n        i0.ɵɵelement(100, \"input\", 24);\n        i0.ɵɵelementStart(101, \"label\", 25);\n        i0.ɵɵtext(102, \"CHAMPVA\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(103, \"div\", 21)(104, \"p\", 22);\n        i0.ɵɵtext(105, \"Group Health Plan\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(106, \"div\", 23);\n        i0.ɵɵelement(107, \"input\", 24);\n        i0.ɵɵelementStart(108, \"label\", 25);\n        i0.ɵɵtext(109, \"Group Health Plan\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(110, \"div\", 21)(111, \"p\", 22);\n        i0.ɵɵtext(112, \"FECA BLK LUNG\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(113, \"div\", 23);\n        i0.ɵɵelement(114, \"input\", 24);\n        i0.ɵɵelementStart(115, \"label\", 25);\n        i0.ɵɵtext(116, \"FECA BLK LUNG\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(117, \"div\", 21)(118, \"p\", 22);\n        i0.ɵɵtext(119, \"Others\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(120, \"div\", 23);\n        i0.ɵɵelement(121, \"input\", 24);\n        i0.ɵɵelementStart(122, \"label\", 25);\n        i0.ɵɵtext(123, \"Others\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(124, \"div\", 26)(125, \"div\", 27)(126, \"p\", 22);\n        i0.ɵɵtext(127, \"1A. Insured\\u2019s I.D. Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(128, \"div\", 28)(129, \"ng-autocomplete\", 29);\n        i0.ɵɵlistener(\"selected\", function FfsComponent_Template_ng_autocomplete_selected_129_listener($event) {\n          return ctx.selectEvent($event);\n        })(\"inputChanged\", function FfsComponent_Template_ng_autocomplete_inputChanged_129_listener($event) {\n          return ctx.onChangeSearch($event);\n        })(\"inputFocused\", function FfsComponent_Template_ng_autocomplete_inputFocused_129_listener($event) {\n          return ctx.onFocused($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(130, FfsComponent_ng_template_130_Template, 1, 1, \"ng-template\", null, 30, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(132, FfsComponent_ng_template_132_Template, 1, 1, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(134, \"mat-icon\", 32);\n        i0.ɵɵlistener(\"click\", function FfsComponent_Template_mat_icon_click_134_listener() {\n          return ctx.searchInsuredId();\n        });\n        i0.ɵɵtext(135, \"search\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(136, \"div\", 6)(137, \"div\", 7)(138, \"section\", 33)(139, \"div\", 8)(140, \"div\", 27)(141, \"p\", 22);\n        i0.ɵɵtext(142, \"2. Patient Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(143, \"div\", 8)(144, \"div\", 27)(145, \"label\", 12);\n        i0.ɵɵtext(146, \"Last Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(147, \"div\", 27)(148, \"label\", 12);\n        i0.ɵɵtext(149, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(150, \"div\", 27)(151, \"label\", 12);\n        i0.ɵɵtext(152, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(153, \"div\", 6)(154, \"div\", 27);\n        i0.ɵɵelement(155, \"input\", 34);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(156, \"div\", 27);\n        i0.ɵɵelement(157, \"input\", 35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(158, \"div\", 27);\n        i0.ɵɵelement(159, \"input\", 36);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(160, \"div\", 7)(161, \"section\", 37)(162, \"div\", 8)(163, \"div\", 27)(164, \"p\", 22);\n        i0.ɵɵtext(165, \"3. Patient Birth Date\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(166, \"div\", 8)(167, \"div\", 27)(168, \"label\", 12);\n        i0.ɵɵtext(169, \"Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(170, \"div\", 27)(171, \"label\", 12);\n        i0.ɵɵtext(172, \"Sex\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(173, \"div\", 6)(174, \"div\", 27);\n        i0.ɵɵelement(175, \"input\", 38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(176, \"div\", 27)(177, \"div\", 39);\n        i0.ɵɵelement(178, \"input\", 24);\n        i0.ɵɵelementStart(179, \"label\", 25);\n        i0.ɵɵtext(180, \"Male\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(181, \"div\", 39);\n        i0.ɵɵelement(182, \"input\", 40);\n        i0.ɵɵelementStart(183, \"label\", 41);\n        i0.ɵɵtext(184, \"Female\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(185, \"div\", 39);\n        i0.ɵɵelement(186, \"input\", 40);\n        i0.ɵɵelementStart(187, \"label\", 41);\n        i0.ɵɵtext(188, \"Other\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(189, \"div\", 7)(190, \"div\", 8)(191, \"p\", 22);\n        i0.ɵɵtext(192, \"4. Insured\\u2019s Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(193, \"div\", 8)(194, \"div\", 27)(195, \"label\", 12);\n        i0.ɵɵtext(196, \"Last Name \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(197, \"div\", 27)(198, \"label\", 12);\n        i0.ɵɵtext(199, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(200, \"div\", 27)(201, \"label\", 12);\n        i0.ɵɵtext(202, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(203, \"div\", 6)(204, \"div\", 27);\n        i0.ɵɵelement(205, \"input\", 34);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(206, \"div\", 27);\n        i0.ɵɵelement(207, \"input\", 35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(208, \"div\", 27);\n        i0.ɵɵelement(209, \"input\", 36);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(210, \"div\", 6)(211, \"div\", 7)(212, \"section\", 42)(213, \"div\", 8)(214, \"p\", 22);\n        i0.ɵɵtext(215, \"5.Patient Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(216, \"div\", 8)(217, \"div\", 27)(218, \"label\", 12);\n        i0.ɵɵtext(219, \"Address Line - 1 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(220, \"div\", 27)(221, \"label\", 12);\n        i0.ɵɵtext(222, \"Address Line - 2\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(223, \"div\", 27)(224, \"label\", 12);\n        i0.ɵɵtext(225, \"City\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(226, \"div\", 6)(227, \"div\", 27);\n        i0.ɵɵelement(228, \"input\", 43);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(229, \"div\", 27);\n        i0.ɵɵelement(230, \"input\", 43);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(231, \"div\", 27);\n        i0.ɵɵelement(232, \"input\", 43);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(233, \"div\", 8)(234, \"div\", 27)(235, \"label\", 12);\n        i0.ɵɵtext(236, \"State \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(237, \"div\", 27)(238, \"label\", 12);\n        i0.ɵɵtext(239, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(240, \"div\", 27)(241, \"label\", 12);\n        i0.ɵɵtext(242, \"Telephone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(243, \"div\", 6)(244, \"div\", 27);\n        i0.ɵɵelement(245, \"input\", 44);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(246, \"div\", 27);\n        i0.ɵɵelement(247, \"input\", 45);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(248, \"div\", 27);\n        i0.ɵɵelement(249, \"input\", 46);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(250, \"div\", 7)(251, \"section\", 47)(252, \"div\", 8)(253, \"div\", 27)(254, \"p\", 22);\n        i0.ɵɵtext(255, \"6. Patient Relationship To Insured\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(256, \"div\", 8)(257, \"div\", 48)(258, \"div\", 23);\n        i0.ɵɵelement(259, \"input\", 24);\n        i0.ɵɵelementStart(260, \"label\", 25);\n        i0.ɵɵtext(261, \"Self\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(262, \"div\", 23);\n        i0.ɵɵelement(263, \"input\", 40);\n        i0.ɵɵelementStart(264, \"label\", 41);\n        i0.ɵɵtext(265, \"Spouse\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(266, \"div\", 23);\n        i0.ɵɵelement(267, \"input\", 40);\n        i0.ɵɵelementStart(268, \"label\", 41);\n        i0.ɵɵtext(269, \"Child\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(270, \"div\", 23);\n        i0.ɵɵelement(271, \"input\", 40);\n        i0.ɵɵelementStart(272, \"label\", 41);\n        i0.ɵɵtext(273, \"Other\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(274, \"section\", 49)(275, \"div\", 8)(276, \"div\", 27)(277, \"p\", 22);\n        i0.ɵɵtext(278, \"8. Reserved For NUCC Use\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(279, \"div\", 8)(280, \"div\", 27);\n        i0.ɵɵelement(281, \"input\", 50);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(282, \"div\", 8)(283, \"div\", 27);\n        i0.ɵɵelement(284, \"input\", 50);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(285, \"div\", 7)(286, \"div\", 8)(287, \"p\", 22);\n        i0.ɵɵtext(288, \"7.Insured\\u2019s Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(289, \"div\", 8)(290, \"div\", 27)(291, \"label\", 12);\n        i0.ɵɵtext(292, \"Address Line - 1 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(293, \"div\", 27)(294, \"label\", 12);\n        i0.ɵɵtext(295, \"Address Line - 2\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(296, \"div\", 27)(297, \"label\", 12);\n        i0.ɵɵtext(298, \"City\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(299, \"div\", 6)(300, \"div\", 27);\n        i0.ɵɵelement(301, \"input\", 43);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(302, \"div\", 27);\n        i0.ɵɵelement(303, \"input\", 43);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(304, \"div\", 27);\n        i0.ɵɵelement(305, \"input\", 43);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(306, \"div\", 8)(307, \"div\", 27)(308, \"label\", 12);\n        i0.ɵɵtext(309, \"State \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(310, \"div\", 27)(311, \"label\", 12);\n        i0.ɵɵtext(312, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(313, \"div\", 27)(314, \"label\", 12);\n        i0.ɵɵtext(315, \"Telephone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(316, \"div\", 6)(317, \"div\", 27);\n        i0.ɵɵelement(318, \"input\", 44);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(319, \"div\", 27);\n        i0.ɵɵelement(320, \"input\", 45);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(321, \"div\", 27);\n        i0.ɵɵelement(322, \"input\", 46);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(323, \"div\", 6)(324, \"div\", 7)(325, \"section\", 51)(326, \"div\", 8)(327, \"div\", 27)(328, \"p\", 22);\n        i0.ɵɵtext(329, \"9. Other Insured\\u2019s Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(330, \"div\", 8)(331, \"div\", 27)(332, \"label\", 12);\n        i0.ɵɵtext(333, \"Last Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(334, \"div\", 27)(335, \"label\", 12);\n        i0.ɵɵtext(336, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(337, \"div\", 27)(338, \"label\", 12);\n        i0.ɵɵtext(339, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(340, \"div\", 6)(341, \"div\", 27);\n        i0.ɵɵelement(342, \"input\", 34);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(343, \"div\", 27);\n        i0.ɵɵelement(344, \"input\", 35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(345, \"div\", 27);\n        i0.ɵɵelement(346, \"input\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(347, \"div\", 8)(348, \"div\", 27)(349, \"label\", 12);\n        i0.ɵɵtext(350, \"A. Other Insured\\u2019s Policy Or Group Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(351, \"input\", 52);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(352, \"div\", 8)(353, \"div\", 53)(354, \"label\", 12);\n        i0.ɵɵtext(355, \"B. Reserved For NUCC Use\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(356, \"div\", 54)(357, \"label\", 12);\n        i0.ɵɵtext(358, \"C. Reserved For NUCC Use\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(359, \"div\", 6)(360, \"div\", 53);\n        i0.ɵɵelement(361, \"input\", 55);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(362, \"div\", 54);\n        i0.ɵɵelement(363, \"input\", 55);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(364, \"div\", 8)(365, \"div\", 27)(366, \"label\", 12);\n        i0.ɵɵtext(367, \"A. Other Insured\\u2019s Policy Or Group Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(368, \"input\", 52);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(369, \"div\", 7)(370, \"section\", 56)(371, \"div\", 8)(372, \"div\", 27)(373, \"p\", 22);\n        i0.ɵɵtext(374, \"10. Is Patient\\u2019s Condition Related To\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(375, \"div\", 8)(376, \"div\", 27)(377, \"label\", 12);\n        i0.ɵɵtext(378, \"10.A. Employment? (Current Or Previous)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(379, \"div\", 6)(380, \"div\", 48)(381, \"div\", 23);\n        i0.ɵɵelement(382, \"input\", 24);\n        i0.ɵɵelementStart(383, \"label\", 25);\n        i0.ɵɵtext(384, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(385, \"div\", 23);\n        i0.ɵɵelement(386, \"input\", 40);\n        i0.ɵɵelementStart(387, \"label\", 41);\n        i0.ɵɵtext(388, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(389, \"div\", 8)(390, \"div\", 27)(391, \"label\", 12);\n        i0.ɵɵtext(392, \"10.B. Auto Accident?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(393, \"div\", 6)(394, \"div\", 48)(395, \"div\", 23);\n        i0.ɵɵelement(396, \"input\", 24);\n        i0.ɵɵelementStart(397, \"label\", 25);\n        i0.ɵɵtext(398, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(399, \"div\", 23);\n        i0.ɵɵelement(400, \"input\", 40);\n        i0.ɵɵelementStart(401, \"label\", 41);\n        i0.ɵɵtext(402, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(403, \"div\", 8)(404, \"div\", 27)(405, \"label\", 12);\n        i0.ɵɵtext(406, \"10.C. Other Accidents?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(407, \"div\", 6)(408, \"div\", 48)(409, \"div\", 23);\n        i0.ɵɵelement(410, \"input\", 24);\n        i0.ɵɵelementStart(411, \"label\", 25);\n        i0.ɵɵtext(412, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(413, \"div\", 23);\n        i0.ɵɵelement(414, \"input\", 40);\n        i0.ɵɵelementStart(415, \"label\", 41);\n        i0.ɵɵtext(416, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(417, \"div\", 8)(418, \"div\", 27)(419, \"label\", 12);\n        i0.ɵɵtext(420, \"10.D Claims Codes(Designed By NUCC)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(421, \"div\", 8)(422, \"div\", 48)(423, \"div\", 23);\n        i0.ɵɵelement(424, \"input\", 24);\n        i0.ɵɵelementStart(425, \"label\", 25);\n        i0.ɵɵtext(426, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(427, \"div\", 23);\n        i0.ɵɵelement(428, \"input\", 40);\n        i0.ɵɵelementStart(429, \"label\", 41);\n        i0.ɵɵtext(430, \"No\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(431, \"div\", 7)(432, \"div\", 8)(433, \"div\", 10)(434, \"p\", 22);\n        i0.ɵɵtext(435, \"11. Insured\\u2019s Policy Group Or FECA Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(436, \"input\", 50);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(437, \"div\", 8)(438, \"div\", 10)(439, \"label\", 12);\n        i0.ɵɵtext(440, \" A. Insured\\u2019s Date Of Birth\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(441, \"div\", 10)(442, \"label\", 12);\n        i0.ɵɵtext(443, \" Sex\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(444, \"div\", 6)(445, \"div\", 10);\n        i0.ɵɵelement(446, \"input\", 38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(447, \"div\", 10)(448, \"div\", 39);\n        i0.ɵɵelement(449, \"input\", 24);\n        i0.ɵɵelementStart(450, \"label\", 25);\n        i0.ɵɵtext(451, \"Male\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(452, \"div\", 39);\n        i0.ɵɵelement(453, \"input\", 40);\n        i0.ɵɵelementStart(454, \"label\", 41);\n        i0.ɵɵtext(455, \"Female\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(456, \"div\", 39);\n        i0.ɵɵelement(457, \"input\", 40);\n        i0.ɵɵelementStart(458, \"label\", 41);\n        i0.ɵɵtext(459, \"Others\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(460, \"div\", 8)(461, \"div\", 10)(462, \"label\", 12);\n        i0.ɵɵtext(463, \" B.Other Claim ID (Designated By NUCC)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(464, \"input\", 50);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(465, \"div\", 8)(466, \"div\", 10)(467, \"label\", 12);\n        i0.ɵɵtext(468, \" C. Insurance Plan Name Or Program Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(469, \"input\", 50);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(470, \"div\", 8)(471, \"div\", 10)(472, \"label\", 12);\n        i0.ɵɵtext(473, \" D. Is There Another Health Benefit Plan\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(474, \"div\", 10);\n        i0.ɵɵelement(475, \"label\", 12);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(476, \"div\", 8)(477, \"div\", 57)(478, \"div\", 23);\n        i0.ɵɵelement(479, \"input\", 24);\n        i0.ɵɵelementStart(480, \"label\", 25);\n        i0.ɵɵtext(481, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(482, \"div\", 23);\n        i0.ɵɵelement(483, \"input\", 40);\n        i0.ɵɵelementStart(484, \"label\", 41);\n        i0.ɵɵtext(485, \"No\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(486, \"div\", 10);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(487, \"div\", 6)(488, \"div\", 7)(489, \"section\", 58)(490, \"div\", 8)(491, \"p\", 22);\n        i0.ɵɵtext(492, \"12. Patients Or Authorized Persons Signature\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(493, \"div\", 6)(494, \"div\", 48)(495, \"div\", 23);\n        i0.ɵɵelement(496, \"input\", 24);\n        i0.ɵɵelementStart(497, \"label\", 25);\n        i0.ɵɵtext(498, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(499, \"div\", 23);\n        i0.ɵɵelement(500, \"input\", 24);\n        i0.ɵɵelementStart(501, \"label\", 25);\n        i0.ɵɵtext(502, \"No\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(503, \"div\", 27)(504, \"label\", 12);\n        i0.ɵɵtext(505, \" Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(506, \"input\", 38);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(507, \"div\", 7);\n        i0.ɵɵelementStart(508, \"div\", 7)(509, \"div\", 8)(510, \"p\", 22);\n        i0.ɵɵtext(511, \"13. Insured Or Authorized Persons Signature\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(512, \"div\", 6)(513, \"div\", 48)(514, \"div\", 23);\n        i0.ɵɵelement(515, \"input\", 24);\n        i0.ɵɵelementStart(516, \"label\", 25);\n        i0.ɵɵtext(517, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(518, \"div\", 23);\n        i0.ɵɵelement(519, \"input\", 40);\n        i0.ɵɵelementStart(520, \"label\", 41);\n        i0.ɵɵtext(521, \"No\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(522, \"div\", 6)(523, \"div\", 7)(524, \"section\", 59)(525, \"div\", 8)(526, \"p\", 22);\n        i0.ɵɵtext(527, \"14. Date Of Current Illness,Injury Or Pregnancy (LMP)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(528, \"div\", 6)(529, \"div\", 27)(530, \"label\", 12);\n        i0.ɵɵtext(531, \" Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(532, \"input\", 38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(533, \"div\", 27)(534, \"label\", 12);\n        i0.ɵɵtext(535, \" Qualifier\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(536, \"select\", 60)(537, \"option\", 61);\n        i0.ɵɵtext(538, \"Select\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(539, \"option\", 62);\n        i0.ɵɵtext(540, \"431-onset\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(541, \"div\", 7)(542, \"section\", 63)(543, \"div\", 8)(544, \"div\", 27)(545, \"p\", 22);\n        i0.ɵɵtext(546, \"15. Other Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(547, \"div\", 6)(548, \"div\", 27)(549, \"label\", 41);\n        i0.ɵɵtext(550, \"Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(551, \"input\", 38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(552, \"div\", 27)(553, \"label\", 41);\n        i0.ɵɵtext(554, \"Qualifier\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(555, \"select\", 60)(556, \"option\", 61);\n        i0.ɵɵtext(557, \"Select\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(558, \"option\", 62);\n        i0.ɵɵtext(559, \"431-onset\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(560, \"div\", 7)(561, \"div\", 8)(562, \"p\", 22);\n        i0.ɵɵtext(563, \"16. Patient Unable To Work In Current Occupation\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(564, \"div\", 6)(565, \"div\", 27)(566, \"label\", 12);\n        i0.ɵɵtext(567, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(568, \"input\", 38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(569, \"div\", 27)(570, \"label\", 12);\n        i0.ɵɵtext(571, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(572, \"input\", 38);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(573, \"div\", 6)(574, \"div\", 7)(575, \"section\", 64)(576, \"div\", 8)(577, \"div\", 27)(578, \"p\", 22);\n        i0.ɵɵtext(579, \"17. Name Of The Referring Provider Or Other Source\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(580, \"div\", 8)(581, \"div\", 27)(582, \"label\", 12);\n        i0.ɵɵtext(583, \"Last Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(584, \"div\", 27)(585, \"label\", 12);\n        i0.ɵɵtext(586, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(587, \"div\", 27)(588, \"label\", 12);\n        i0.ɵɵtext(589, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(590, \"div\", 6)(591, \"div\", 27);\n        i0.ɵɵelement(592, \"input\", 34);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(593, \"div\", 27);\n        i0.ɵɵelement(594, \"input\", 35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(595, \"div\", 27);\n        i0.ɵɵelement(596, \"input\", 36);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(597, \"div\", 7)(598, \"section\", 65)(599, \"div\", 8)(600, \"div\", 66)(601, \"p\", 22);\n        i0.ɵɵtext(602, \"17A.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(603, \"div\", 67)(604, \"label\", 12);\n        i0.ɵɵtext(605, \"Payer Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(606, \"div\", 67)(607, \"label\", 12);\n        i0.ɵɵtext(608, \"Payer Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(609, \"div\", 6);\n        i0.ɵɵelement(610, \"div\", 66);\n        i0.ɵɵelementStart(611, \"div\", 67)(612, \"select\", 60)(613, \"option\", 61);\n        i0.ɵɵtext(614, \"Select\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(615, \"option\", 62);\n        i0.ɵɵtext(616, \"431-onset\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(617, \"div\", 67);\n        i0.ɵɵelement(618, \"input\", 38);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(619, \"div\", 8)(620, \"div\", 66)(621, \"p\", 22);\n        i0.ɵɵtext(622, \"17B.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(623, \"div\", 67)(624, \"label\", 12);\n        i0.ɵɵtext(625, \"Payer Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(626, \"div\", 67)(627, \"label\", 12);\n        i0.ɵɵtext(628, \"Payer Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(629, \"div\", 6);\n        i0.ɵɵelement(630, \"div\", 66);\n        i0.ɵɵelementStart(631, \"div\", 67);\n        i0.ɵɵelement(632, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(633, \"div\", 67);\n        i0.ɵɵelement(634, \"input\", 50);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(635, \"div\", 7)(636, \"div\", 8)(637, \"p\", 22);\n        i0.ɵɵtext(638, \"18. Hospitalization Dates Related To Current Services\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(639, \"div\", 6)(640, \"div\", 27)(641, \"label\", 12);\n        i0.ɵɵtext(642, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(643, \"input\", 38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(644, \"div\", 27)(645, \"label\", 12);\n        i0.ɵɵtext(646, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(647, \"input\", 38);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(648, \"div\", 6)(649, \"div\", 20)(650, \"section\", 68)(651, \"div\", 8)(652, \"div\", 27)(653, \"p\", 22);\n        i0.ɵɵtext(654, \"19. Additional Claim Information (Designated By NUCC)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(655, \"div\", 6)(656, \"div\", 27);\n        i0.ɵɵelement(657, \"input\", 69);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(658, \"div\", 26)(659, \"div\", 8)(660, \"p\", 22);\n        i0.ɵɵtext(661, \"20. Outside Lab\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(662, \"div\", 6)(663, \"div\", 48)(664, \"div\", 23);\n        i0.ɵɵelement(665, \"input\", 24);\n        i0.ɵɵelementStart(666, \"label\", 25);\n        i0.ɵɵtext(667, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(668, \"div\", 23);\n        i0.ɵɵelement(669, \"input\", 40);\n        i0.ɵɵelementStart(670, \"label\", 41);\n        i0.ɵɵtext(671, \"No\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(672, \"div\", 6)(673, \"div\", 20)(674, \"section\", 70)(675, \"div\", 8)(676, \"div\", 27)(677, \"p\", 22);\n        i0.ɵɵtext(678, \"21. Diagnosis Or Nature Of Illness Or Injury. (Relate A-L To Service Line Below (24E)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(679, \"div\", 6)(680, \"div\", 27)(681, \"label\", 12);\n        i0.ɵɵtext(682, \"A(1)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(683, \"div\", 27)(684, \"label\", 12);\n        i0.ɵɵtext(685, \"B(2)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(686, \"div\", 27)(687, \"label\", 12);\n        i0.ɵɵtext(688, \"C(3)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(689, \"div\", 27)(690, \"label\", 12);\n        i0.ɵɵtext(691, \"D(4)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(692, \"div\", 6)(693, \"div\", 27);\n        i0.ɵɵelement(694, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(695, \"div\", 27);\n        i0.ɵɵelement(696, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(697, \"div\", 27);\n        i0.ɵɵelement(698, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(699, \"div\", 27);\n        i0.ɵɵelement(700, \"input\", 50);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(701, \"div\", 8)(702, \"div\", 27)(703, \"label\", 12);\n        i0.ɵɵtext(704, \"E(5)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(705, \"div\", 27)(706, \"label\", 12);\n        i0.ɵɵtext(707, \"F(6)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(708, \"div\", 27)(709, \"label\", 12);\n        i0.ɵɵtext(710, \"G(7)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(711, \"div\", 27)(712, \"label\", 12);\n        i0.ɵɵtext(713, \"H(8)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(714, \"div\", 6)(715, \"div\", 27);\n        i0.ɵɵelement(716, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(717, \"div\", 27);\n        i0.ɵɵelement(718, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(719, \"div\", 27);\n        i0.ɵɵelement(720, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(721, \"div\", 27);\n        i0.ɵɵelement(722, \"input\", 50);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(723, \"div\", 8)(724, \"div\", 27)(725, \"label\", 12);\n        i0.ɵɵtext(726, \"I(9)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(727, \"div\", 27)(728, \"label\", 12);\n        i0.ɵɵtext(729, \"J(10)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(730, \"div\", 27)(731, \"label\", 12);\n        i0.ɵɵtext(732, \"K(11)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(733, \"div\", 27)(734, \"label\", 12);\n        i0.ɵɵtext(735, \"L(12)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(736, \"div\", 6)(737, \"div\", 27);\n        i0.ɵɵelement(738, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(739, \"div\", 27);\n        i0.ɵɵelement(740, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(741, \"div\", 27);\n        i0.ɵɵelement(742, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(743, \"div\", 27);\n        i0.ɵɵelement(744, \"input\", 50);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(745, \"section\", 70)(746, \"div\", 8)(747, \"div\", 10)(748, \"label\", 12);\n        i0.ɵɵtext(749, \"Place of service 2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(750, \"select\", 71)(751, \"option\", 61);\n        i0.ɵɵtext(752, \"Select\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(753, \"div\", 8)(754, \"div\", 10)(755, \"label\", 12);\n        i0.ɵɵtext(756, \"EMG 2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(757, \"select\", 71)(758, \"option\", 61);\n        i0.ɵɵtext(759, \"Select\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(760, \"div\", 8)(761, \"div\", 10)(762, \"label\", 12);\n        i0.ɵɵtext(763, \"EPSDT 2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(764, \"select\", 71)(765, \"option\", 61);\n        i0.ɵɵtext(766, \"Select\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(767, \"div\", 26)(768, \"div\", 8)(769, \"p\", 22);\n        i0.ɵɵtext(770, \"22. Resubmission Code\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(771, \"div\", 6)(772, \"div\", 10)(773, \"label\", 12);\n        i0.ɵɵtext(774, \"Resubmission Code\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(775, \"div\", 10)(776, \"label\", 12);\n        i0.ɵɵtext(777, \"Reference Number\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(778, \"div\", 8)(779, \"div\", 10);\n        i0.ɵɵelement(780, \"input\", 72);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(781, \"div\", 10);\n        i0.ɵɵelement(782, \"input\", 73);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(783, \"div\", 8)(784, \"p\", 22);\n        i0.ɵɵtext(785, \"23. Prior Authorization Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(786, \"div\", 8)(787, \"div\", 10)(788, \"label\", 12);\n        i0.ɵɵtext(789, \"Prior Authorization Number\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(790, \"div\", 6)(791, \"div\", 10);\n        i0.ɵɵelement(792, \"input\", 74);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(793, \"div\", 8)(794, \"div\", 10)(795, \"label\", 12);\n        i0.ɵɵtext(796, \"Ambulance Pickup Address 1\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(797, \"div\", 10)(798, \"label\", 12);\n        i0.ɵɵtext(799, \"Ambulance Pickup Address 2\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(800, \"div\", 6)(801, \"div\", 10);\n        i0.ɵɵelement(802, \"input\", 75);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(803, \"div\", 10);\n        i0.ɵɵelement(804, \"input\", 76);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(805, \"div\", 8)(806, \"div\", 10)(807, \"label\", 12);\n        i0.ɵɵtext(808, \"Ambulance Pickup City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(809, \"div\", 10)(810, \"label\", 12);\n        i0.ɵɵtext(811, \"Ambulance Pickup State\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(812, \"div\", 6)(813, \"div\", 10);\n        i0.ɵɵelement(814, \"input\", 77);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(815, \"div\", 10);\n        i0.ɵɵelement(816, \"input\", 78);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(817, \"div\", 8)(818, \"div\", 10)(819, \"label\", 12);\n        i0.ɵɵtext(820, \"Ambulance Pickup Zip Code\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(821, \"div\", 6)(822, \"div\", 10);\n        i0.ɵɵelement(823, \"input\", 79);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(824, \"div\", 58)(825, \"div\", 6)(826, \"div\", 7)(827, \"div\", 6)(828, \"p\", 22);\n        i0.ɵɵtext(829, \"24. Service Line\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(830, \"div\", 8)(831, \"div\", 10)(832, \"section\", 80)(833, \"div\", 81)(834, \"table\", 82)(835, \"thead\")(836, \"tr\")(837, \"th\", 83);\n        i0.ɵɵtext(838, \"D.O.S\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(839, \"th\", 84);\n        i0.ɵɵtext(840, \"P.O.S\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(841, \"th\", 84);\n        i0.ɵɵtext(842, \"EMG\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(843, \"th\", 83);\n        i0.ɵɵtext(844, \"Procedures\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(845, \"th\", 84);\n        i0.ɵɵtext(846, \"Diagnosis Pointer\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(847, \"th\", 84);\n        i0.ɵɵtext(848, \"Unit Charge\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(849, \"th\", 84);\n        i0.ɵɵtext(850, \"Unit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(851, \"th\", 84);\n        i0.ɵɵtext(852, \"EPSDT\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(853, \"th\", 84);\n        i0.ɵɵtext(854, \"Rendering Provider ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(855, \"tr\")(856, \"th\", 84);\n        i0.ɵɵtext(857, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(858, \"th\", 84);\n        i0.ɵɵtext(859, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(860, \"th\", 85)(861, \"th\", 85);\n        i0.ɵɵelementStart(862, \"th\", 84);\n        i0.ɵɵtext(863, \"CPT/HCPCS\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(864, \"th\", 84);\n        i0.ɵɵtext(865, \"Modifier\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(866, \"th\", 85)(867, \"th\", 85)(868, \"th\", 85)(869, \"th\", 85)(870, \"th\", 85);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(871, \"tbody\");\n        i0.ɵɵtemplate(872, FfsComponent_tr_872_Template, 111, 18, \"tr\", 86);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(873, \"div\", 87)(874, \"div\")(875, \"button\", 88);\n        i0.ɵɵtext(876, \"Delete\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(877, \"div\")(878, \"button\", 89);\n        i0.ɵɵlistener(\"click\", function FfsComponent_Template_button_click_878_listener() {\n          return ctx.addRow();\n        });\n        i0.ɵɵtext(879, \"Add\");\n        i0.ɵɵelementEnd()()()()()()()()()();\n        i0.ɵɵelementStart(880, \"div\", 6)(881, \"div\", 7)(882, \"div\", 8)(883, \"div\", 10)(884, \"p\", 22);\n        i0.ɵɵtext(885, \"25. Federal Tax Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(886, \"div\", 57)(887, \"div\", 23);\n        i0.ɵɵelement(888, \"input\", 24);\n        i0.ɵɵelementStart(889, \"label\", 25);\n        i0.ɵɵtext(890, \"SSN\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(891, \"div\", 23);\n        i0.ɵɵelement(892, \"input\", 40);\n        i0.ɵɵelementStart(893, \"label\", 41);\n        i0.ɵɵtext(894, \"EIN\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(895, \"div\", 6)(896, \"div\", 10);\n        i0.ɵɵelement(897, \"input\", 90);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(898, \"div\", 7)(899, \"div\", 8)(900, \"div\", 10)(901, \"p\", 22);\n        i0.ɵɵtext(902, \"26. Patient Account Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(903, \"div\", 10)(904, \"p\", 22);\n        i0.ɵɵtext(905, \"27. Acceptance Assignment?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(906, \"div\", 6)(907, \"div\", 10);\n        i0.ɵɵelement(908, \"input\", 91);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(909, \"div\", 10)(910, \"div\", 23);\n        i0.ɵɵelement(911, \"input\", 24);\n        i0.ɵɵelementStart(912, \"label\", 25);\n        i0.ɵɵtext(913, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(914, \"div\", 23);\n        i0.ɵɵelement(915, \"input\", 40);\n        i0.ɵɵelementStart(916, \"label\", 41);\n        i0.ɵɵtext(917, \"No\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(918, \"div\", 7)(919, \"div\", 8)(920, \"div\", 10)(921, \"p\", 22);\n        i0.ɵɵtext(922, \"28. Total Charge($)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(923, \"div\", 10)(924, \"p\", 22);\n        i0.ɵɵtext(925, \"29. Amount Paid\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(926, \"div\", 10)(927, \"p\", 22);\n        i0.ɵɵtext(928, \"30. Reserved For NUCC\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(929, \"div\", 6)(930, \"div\", 10);\n        i0.ɵɵelement(931, \"input\", 92);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(932, \"div\", 10);\n        i0.ɵɵelement(933, \"input\", 93);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(934, \"div\", 10);\n        i0.ɵɵelement(935, \"input\", 94);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(936, \"div\", 6)(937, \"div\", 7)(938, \"section\", 95)(939, \"div\", 8)(940, \"div\", 27);\n        i0.ɵɵelement(941, \"p\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(942, \"div\", 48)(943, \"div\", 23);\n        i0.ɵɵelement(944, \"input\", 24);\n        i0.ɵɵelementStart(945, \"label\", 25);\n        i0.ɵɵtext(946, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(947, \"div\", 23);\n        i0.ɵɵelement(948, \"input\", 24);\n        i0.ɵɵelementStart(949, \"label\", 25);\n        i0.ɵɵtext(950, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(951, \"div\", 8)(952, \"div\", 27)(953, \"label\", 12);\n        i0.ɵɵtext(954, \"Provider Signature Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(955, \"div\", 27);\n        i0.ɵɵelement(956, \"input\", 96);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(957, \"div\", 8)(958, \"div\", 27)(959, \"label\", 12);\n        i0.ɵɵtext(960, \"Date Of Initial Treatment\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(961, \"div\", 27);\n        i0.ɵɵelement(962, \"input\", 96);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(963, \"div\", 8)(964, \"div\", 27)(965, \"label\", 12);\n        i0.ɵɵtext(966, \"Latest Visit OrConsultation Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(967, \"div\", 27);\n        i0.ɵɵelement(968, \"input\", 96);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(969, \"div\", 8)(970, \"div\", 27)(971, \"label\", 12);\n        i0.ɵɵtext(972, \"Supervising Physician\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(973, \"div\", 27);\n        i0.ɵɵelement(974, \"input\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(975, \"div\", 97)(976, \"div\", 27);\n        i0.ɵɵelement(977, \"label\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(978, \"div\", 27);\n        i0.ɵɵelement(979, \"input\", 35);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(980, \"div\", 97)(981, \"div\", 27);\n        i0.ɵɵelement(982, \"label\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(983, \"div\", 27);\n        i0.ɵɵelement(984, \"input\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(985, \"div\", 97)(986, \"div\", 27)(987, \"label\", 12);\n        i0.ɵɵtext(988, \"Supervising Physician NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(989, \"div\", 27);\n        i0.ɵɵelement(990, \"input\", 98);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(991, \"div\", 97)(992, \"div\", 27)(993, \"label\", 12);\n        i0.ɵɵtext(994, \"Ordering Physician\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(995, \"div\", 27);\n        i0.ɵɵelement(996, \"input\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(997, \"div\", 97)(998, \"div\", 27);\n        i0.ɵɵelement(999, \"label\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1000, \"div\", 27);\n        i0.ɵɵelement(1001, \"input\", 35);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1002, \"div\", 97)(1003, \"div\", 27);\n        i0.ɵɵelement(1004, \"label\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1005, \"div\", 27);\n        i0.ɵɵelement(1006, \"input\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1007, \"div\", 97)(1008, \"div\", 27)(1009, \"label\", 12);\n        i0.ɵɵtext(1010, \"Ordering Physician NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1011, \"div\", 27);\n        i0.ɵɵelement(1012, \"input\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1013, \"div\", 97)(1014, \"div\", 27)(1015, \"label\", 12);\n        i0.ɵɵtext(1016, \"Ordering Physician ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1017, \"div\", 27);\n        i0.ɵɵelement(1018, \"input\", 98);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1019, \"div\", 97)(1020, \"div\", 27)(1021, \"label\", 12);\n        i0.ɵɵtext(1022, \"Accident Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1023, \"div\", 27);\n        i0.ɵɵelement(1024, \"input\", 99);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1025, \"div\", 97)(1026, \"div\", 27)(1027, \"label\", 12);\n        i0.ɵɵtext(1028, \"CLIA\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1029, \"div\", 27);\n        i0.ɵɵelement(1030, \"input\", 100);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1031, \"div\", 97)(1032, \"div\", 27)(1033, \"label\", 12);\n        i0.ɵɵtext(1034, \"Mammography Certificate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1035, \"div\", 27);\n        i0.ɵɵelement(1036, \"input\", 101);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1037, \"div\", 7)(1038, \"section\", 102)(1039, \"div\", 8)(1040, \"div\", 27)(1041, \"p\", 22);\n        i0.ɵɵtext(1042, \" 32. Service Facility Location And Information\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1043, \"div\", 97)(1044, \"div\", 27)(1045, \"label\", 12);\n        i0.ɵɵtext(1046, \"Facility Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1047, \"div\", 27);\n        i0.ɵɵelement(1048, \"input\", 103);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1049, \"div\", 97)(1050, \"div\", 27)(1051, \"label\", 12);\n        i0.ɵɵtext(1052, \"Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1053, \"div\", 27);\n        i0.ɵɵelement(1054, \"input\", 104);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1055, \"div\", 97)(1056, \"div\", 27);\n        i0.ɵɵelement(1057, \"label\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1058, \"div\", 27);\n        i0.ɵɵelement(1059, \"input\", 105);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1060, \"div\", 97)(1061, \"div\", 27)(1062, \"label\", 12);\n        i0.ɵɵtext(1063, \"City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1064, \"div\", 27);\n        i0.ɵɵelement(1065, \"input\", 17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1066, \"div\", 97)(1067, \"div\", 27)(1068, \"label\", 12);\n        i0.ɵɵtext(1069, \"State\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1070, \"div\", 27);\n        i0.ɵɵelement(1071, \"input\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1072, \"div\", 97)(1073, \"div\", 27)(1074, \"label\", 12);\n        i0.ɵɵtext(1075, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1076, \"div\", 27);\n        i0.ɵɵelement(1077, \"input\", 79);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1078, \"div\", 97)(1079, \"div\", 27)(1080, \"label\", 12);\n        i0.ɵɵtext(1081, \"a. NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1082, \"div\", 27);\n        i0.ɵɵelement(1083, \"input\", 90);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1084, \"div\", 97)(1085, \"div\", 27)(1086, \"label\", 12);\n        i0.ɵɵtext(1087, \"b. Facility ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1088, \"div\", 27);\n        i0.ɵɵelement(1089, \"input\", 106);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1090, \"div\", 97)(1091, \"div\", 27)(1092, \"label\", 12);\n        i0.ɵɵtext(1093, \"Ambulance Drop off Address 1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(1094, \"input\", 35);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1095, \"div\", 97)(1096, \"div\", 27)(1097, \"label\", 12);\n        i0.ɵɵtext(1098, \" Ambulance Drop off Address 1\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1099, \"div\", 27)(1100, \"label\", 12);\n        i0.ɵɵtext(1101, \"Ambulance Drop off Address 2\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1102, \"div\", 97)(1103, \"div\", 27);\n        i0.ɵɵelement(1104, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1105, \"div\", 27);\n        i0.ɵɵelement(1106, \"input\", 50);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1107, \"div\", 97)(1108, \"div\", 27)(1109, \"label\", 12);\n        i0.ɵɵtext(1110, \"Ambulance Drop off City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1111, \"div\", 27)(1112, \"label\", 12);\n        i0.ɵɵtext(1113, \"Ambulance Drop off State\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1114, \"div\", 97)(1115, \"div\", 27);\n        i0.ɵɵelement(1116, \"input\", 50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1117, \"div\", 27);\n        i0.ɵɵelement(1118, \"input\", 50);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1119, \"div\", 97)(1120, \"div\", 27)(1121, \"label\", 12);\n        i0.ɵɵtext(1122, \"Ambulance Drop off Zip Code\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(1123, \"input\", 50);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1124, \"div\", 7)(1125, \"section\", 107)(1126, \"div\", 8)(1127, \"div\", 27)(1128, \"p\", 22);\n        i0.ɵɵtext(1129, \" 33. Billing Provider Info And Phone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1130, \"div\", 97)(1131, \"div\", 27)(1132, \"label\", 12);\n        i0.ɵɵtext(1133, \" Billing Provider\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1134, \"div\", 108);\n        i0.ɵɵelement(1135, \"input\", 109);\n        i0.ɵɵelementStart(1136, \"mat-icon\", 32);\n        i0.ɵɵlistener(\"click\", function FfsComponent_Template_mat_icon_click_1136_listener() {\n          return ctx.searchBillingProvider();\n        });\n        i0.ɵɵtext(1137, \"search\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1138, \"div\", 97)(1139, \"div\", 27);\n        i0.ɵɵelement(1140, \"label\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1141, \"div\", 27);\n        i0.ɵɵelement(1142, \"input\", 35);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1143, \"div\", 97)(1144, \"div\", 27);\n        i0.ɵɵelement(1145, \"label\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1146, \"div\", 27);\n        i0.ɵɵelement(1147, \"input\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1148, \"div\", 97)(1149, \"div\", 27)(1150, \"label\", 12);\n        i0.ɵɵtext(1151, \"Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1152, \"div\", 27);\n        i0.ɵɵelement(1153, \"input\", 104);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1154, \"div\", 97)(1155, \"div\", 27);\n        i0.ɵɵelement(1156, \"label\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1157, \"div\", 27);\n        i0.ɵɵelement(1158, \"input\", 105);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1159, \"div\", 97)(1160, \"div\", 27)(1161, \"label\", 12);\n        i0.ɵɵtext(1162, \"City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1163, \"div\", 27);\n        i0.ɵɵelement(1164, \"input\", 17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1165, \"div\", 97)(1166, \"div\", 27)(1167, \"label\", 12);\n        i0.ɵɵtext(1168, \" State\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1169, \"div\", 27);\n        i0.ɵɵelement(1170, \"input\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1171, \"div\", 97)(1172, \"div\", 27)(1173, \"label\", 12);\n        i0.ɵɵtext(1174, \"Zip Code\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1175, \"div\", 27);\n        i0.ɵɵelement(1176, \"input\", 110);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1177, \"div\", 97)(1178, \"div\", 27)(1179, \"label\", 12);\n        i0.ɵɵtext(1180, \"Telephone\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1181, \"div\", 27);\n        i0.ɵɵelement(1182, \"input\", 46);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1183, \"div\", 97)(1184, \"div\", 27)(1185, \"label\", 12);\n        i0.ɵɵtext(1186, \"Specialty/Taxonomy\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1187, \"div\", 27);\n        i0.ɵɵelement(1188, \"input\", 111);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1189, \"div\", 97)(1190, \"div\", 27)(1191, \"label\", 12);\n        i0.ɵɵtext(1192, \" Rendering Provider (Last,First,MI)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1193, \"div\", 108);\n        i0.ɵɵelement(1194, \"input\", 112);\n        i0.ɵɵelementStart(1195, \"mat-icon\", 32);\n        i0.ɵɵlistener(\"click\", function FfsComponent_Template_mat_icon_click_1195_listener() {\n          return ctx.searchRenderingProvider();\n        });\n        i0.ɵɵtext(1196, \"search\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1197, \"div\", 97)(1198, \"div\", 27);\n        i0.ɵɵelement(1199, \"label\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1200, \"div\", 27);\n        i0.ɵɵelement(1201, \"input\", 35);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1202, \"div\", 97)(1203, \"div\", 27);\n        i0.ɵɵelement(1204, \"label\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1205, \"div\", 27);\n        i0.ɵɵelement(1206, \"input\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1207, \"div\", 97)(1208, \"div\", 27)(1209, \"label\", 12);\n        i0.ɵɵtext(1210, \"Provider Specialty\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1211, \"div\", 27);\n        i0.ɵɵelement(1212, \"input\", 113);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1213, \"div\", 97)(1214, \"div\", 27)(1215, \"label\", 12);\n        i0.ɵɵtext(1216, \" Provider NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1217, \"div\", 27);\n        i0.ɵɵelement(1218, \"input\", 114);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1219, \"div\", 97)(1220, \"div\", 27)(1221, \"label\", 12);\n        i0.ɵɵtext(1222, \" Provider PIN\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1223, \"div\", 27);\n        i0.ɵɵelement(1224, \"input\", 115);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1225, \"div\", 97)(1226, \"div\", 27)(1227, \"label\", 12);\n        i0.ɵɵtext(1228, \" A. Billing/Group NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1229, \"div\", 27);\n        i0.ɵɵelement(1230, \"input\", 50);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1231, \"div\", 97)(1232, \"div\", 27)(1233, \"label\", 12);\n        i0.ɵɵtext(1234, \" Provider ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1235, \"div\", 27);\n        i0.ɵɵelement(1236, \"input\", 99);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1237, \"div\", 97)(1238, \"div\", 27)(1239, \"label\", 12);\n        i0.ɵɵtext(1240, \" Id Qual\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1241, \"div\", 27);\n        i0.ɵɵelement(1242, \"input\", 116);\n        i0.ɵɵelementEnd()()()()()()();\n      }\n\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(131);\n\n        const _r2 = i0.ɵɵreference(133);\n\n        i0.ɵɵadvance(129);\n        i0.ɵɵproperty(\"data\", ctx.countries)(\"searchKeyword\", ctx.keyword)(\"itemTemplate\", _r0)(\"notFoundTemplate\", _r2);\n        i0.ɵɵadvance(743);\n        i0.ɵɵproperty(\"ngForOf\", ctx.dynamicArray);\n      }\n    },\n    dependencies: [i2.NgForOf, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel, i4.AutocompleteComponent, i5.MatIcon],\n    styles: [\".create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.create-claim-form-styles[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.create-claim-form-styles[_ngcontent-%COMP%]   .form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.create-claim-form-styles[_ngcontent-%COMP%]   .search-icon-alignment[_ngcontent-%COMP%]{position:relative}.create-claim-form-styles[_ngcontent-%COMP%]   .search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.create-claim-form-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px;color:#b2c0d4}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.create-claim-form-styles[_ngcontent-%COMP%]   .table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.create-claim-form-styles[_ngcontent-%COMP%]   .radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}@media (min-width: 1300px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}\"]\n  });\n  return FfsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}