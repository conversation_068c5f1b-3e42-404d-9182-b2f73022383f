import { Injectable } from "@angular/core";
import { forkJoin, map, Observable, of } from "rxjs";
import { ProviderManagementService } from "src/app/services/ProviderManagement/provider-management.service";
import * as JSLZString from 'lz-string';
import { AllStatesBySearchstringService } from 'src/app/services/ClaimForm/all-states-by-searchstring.service';
import { LocalStorageKey } from "../constant/constatnt";
import { CacheService } from "src/app/services/cache-service/cache.service";

// shared/dropdown.service.ts
@Injectable({
  providedIn: 'root'
})
export class MasterSharedService {
  constructor(
    private providerManagementService: ProviderManagementService,
    private cacheService: CacheService,
    private stateService: AllStatesBySearchstringService
  ) { }

  getFacilityMasterData(): Observable<any> {


    return forkJoin({
      facilityTypes: this.providerManagementService.fetchAllFacilityTypes(),
      
    
      states: this.getOrFetch('allStates', () =>
        this.stateService.fetchchAllStatesBySearchstring()
      ),
      countries: this.getOrFetch('allCountries', () =>
        this.providerManagementService.fetchCountriesData('').pipe(map(res => res?.content || []))
      ),
      counties: this.getOrFetch('allCounties', () =>
        this.providerManagementService.fetchCountiesData('').pipe(map(res => res?.content || []))
      ),
      insurance: this.getOrFetch('insuranceCompaniesOrAllPlans', () =>
        this.providerManagementService.fetchPlansData('').pipe(map(res => res?.content || []))
      )
    });
  }


  private getOrFetch(key: string, fetchFn: () => Observable<any>): Observable<any> {
    const cached = localStorage.getItem(key);
    if (cached) {
      return of(JSON.parse(JSLZString.decompress(cached)));
    }
    return fetchFn();
  }


}
