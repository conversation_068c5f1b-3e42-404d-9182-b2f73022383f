{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/Navigation/navigation-service.service\";\nimport * as i2 from \"ngx-spinner\";\nimport * as i3 from \"@angular/material/dialog\";\nexport let ErrorMessagePopUpComponent = /*#__PURE__*/(() => {\n  class ErrorMessagePopUpComponent {\n    constructor(navigationService, spinnder, dialogRef, modalData) {\n      this.navigationService = navigationService;\n      this.spinnder = spinnder;\n      this.dialogRef = dialogRef;\n      this.modalData = modalData;\n    }\n\n    ngOnInit() {}\n\n    OnClick() {\n      this.navigationService.toggle(this.modalData.RedirectTo);\n      this.dialogRef.close(true);\n    }\n\n  }\n\n  ErrorMessagePopUpComponent.ɵfac = function ErrorMessagePopUpComponent_Factory(t) {\n    return new (t || ErrorMessagePopUpComponent)(i0.ɵɵdirectiveInject(i1.NavigationServiceService), i0.ɵɵdirectiveInject(i2.NgxSpinnerService), i0.ɵɵdirectiveInject(i3.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n  };\n\n  ErrorMessagePopUpComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ErrorMessagePopUpComponent,\n    selectors: [[\"app-error-message-pop-up\"]],\n    decls: 8,\n    vars: 2,\n    consts: [[1, \"row\"], [1, \"col\", \"ErrorText\"], [1, \"col\"], [1, \"btn\", 3, \"click\"]],\n    template: function ErrorMessagePopUpComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 0)(2, \"div\", 1);\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 0)(5, \"div\", 2)(6, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function ErrorMessagePopUpComponent_Template_button_click_6_listener() {\n          return ctx.OnClick();\n        });\n        i0.ɵɵtext(7);\n        i0.ɵɵelementEnd()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.modalData.MessageToDisplay, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.modalData.MessageToDisplayOnButton);\n      }\n    },\n    styles: [\".ErrorText[_ngcontent-%COMP%]{height:50px;font-size:19px;font-family:Poppins-Medium;text-align:center;margin-top:5%}.btn[_ngcontent-%COMP%]{border-color:#0074bc;box-shadow:0 0 8px #0000001a;height:35px;background-color:#0074bc;color:#fff;border-radius:8px;font-size:14px;font-family:Poppins-SemiBold;width:max-content}.Close-Icon1[_ngcontent-%COMP%]{cursor:pointer;padding:7px 20px 0 0;text-align:right}.Remove[_ngcontent-%COMP%]{color:#0074bc;background-color:#fff;border:1px solid #0074BC}.Remove[_ngcontent-%COMP%]:hover{color:#fff;background-color:#0074bc;border:solid 1px #0074bc}.TriangleIcon[_ngcontent-%COMP%]{font-size:30px;color:#f08a14}.AlertStyling[_ngcontent-%COMP%]{font-family:Poppins-SemiBold;font-size:24px;padding-left:1%}.ErrorText1[_ngcontent-%COMP%]{height:20px;font-size:19px;font-family:Poppins-Medium;text-align:center}\"]\n  });\n  return ErrorMessagePopUpComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}