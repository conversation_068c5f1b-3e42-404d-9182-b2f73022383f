/* Main div styling */
.MainFlexRow{
    flex-wrap: wrap; margin-top: 10px; padding:5px; 
}
/* applies on full form */

.Full-Form {
    margin-left: 5px;
    font-weight: bolder;
    padding: 5px;
    /* box-shadow: 0px 0px 8px #0000001A; */
    /* border-radius: 8px; */
    opacity: 1;
    background: #F4F7FC 0% 0% no-repeat padding-box;
}


/* for setting first heading box*/

.Form-Header-Row {
    /* position: sticky;
            top: 0px; */
    background-color: white;
    // height: 50px;
    align-items: center;
    box-shadow: 0px 0px 8px #0000001A;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
}


/* for setting heading of first header */

.Form-Header {
    font-size: 23px;
    justify-content: start;
    color: #272D3B;
    font-family: 'Poppins-SemiBold';
}


/* basic form background setting */

.Form-Body {
    margin-right: 5px;
    box-shadow: 0px 0px 8px #0000001A;
    background-color: white;
    padding: 5px;
}


.FilesHeaderBtn{
    margin-right: 5px;
    height: 43px;
    font-size: 12px;
    background-color: #dddddd;
    padding: 0px;
    font-family: "Poppins-Bold";
}
.col-10{
    padding: 0px;
}

.TextEnd{
    text-align: end;
}

.btn:active{
    background-color: blue!important;
    color: white!important;
}

::ng-deep .CustomCss .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-labels .mat-tab-label-active {
    color: white;
    background-color: #0074BC!important;
}

::ng-deep  .CustomCss .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-label {
    color: #000000;
    font-family: 'Poppins-SemiBold';
    background-color: #F2F2F2!important;
    border:none!important;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    margin-left: 5px;
    height: 40px;
    padding: 0px 7px !important;
}

::ng-deep .mat-tab-list {
    background-color: transparent;
}

.SerialNoWidth{
    width: 132px!important;
}
.TitleWidth{
    width: 226px!important;
}