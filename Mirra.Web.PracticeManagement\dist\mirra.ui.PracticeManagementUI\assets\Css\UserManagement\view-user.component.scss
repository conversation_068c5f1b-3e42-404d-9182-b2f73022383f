.Form-Body {
    box-shadow: 0px 0px 8px #0000001a;
    border: 1px solid #CCCCCC;
    border-radius: 8px;
    opacity: 1;
    background-color: white;
    padding: 5px 10px 5px 5px;
    margin: 5px 5px;
}


// basic styling for all tds in table
.TableTd{
    padding: 1rem 0.5rem!important;
    color:#272D3B;
    font-size: 12px;
    font-family:'Poppins';
}

// background effect on hover on table row
.TableTr:hover{
    background-color:#e3f2fd;
}

.subscriberID{
    padding-left: 12px;
}
// style for checkbox and paginator of primeng
::ng-deep .p-checkbox-box{
    border-radius: 50%!important;
}
::ng-deep .p-paginator {
    justify-content: flex-end!important;
}

/* basic setting for all input type */

input[type="text"],input[type="number"],input[type="email"]
{
    background-color: white;
    padding-left: 5px;
    border-radius:6px;
    border:1px solid #CCCCCC!important;
    height: 30px;
    outline:none;
    min-width: 40px;
    width: 90%;
    font-size:10px;
    font-family: 'Poppins-SemiBold';
}

select {
    box-shadow: none!important;
    background-color: white!important;
    padding: 0px;
    border: 1px solid #dddddd !important;
    font-size: 10px;
    font-family: 'Poppins-SemiBold';
    height: 30px;
    width: 90%;
    cursor: pointer;
    padding-left: 5px;
    outline: none;
}

// styling for input when on focus
input[type="text"]:focus,input[type="number"]:focus,input[type="email"]:focus
{
    border:1px solid #61abd4!important;
}

// styling for table headers
th{
        padding-right: 0px!important;
        padding-left: 0px!important;
        background-color: white!important;
}


/* for setting second heading box*/

.Form-Header-2 {
    font-size: 18px;
    font-family: 'Poppins-SemiBold';
    background-color: white;
    padding: 10px;
    align-items: center;
}

.Form-Header-2-Heading{
    color:#0074BC;
}

// p-table{
//     margin-left: 10px!important;
// }

.TextRight{
    text-align: end;
    justify-content: flex-end;
}

.MarginRight{
    margin-right: 10px;
}

.MarginLeft{
    margin-left: 8px;
}

.btn{
    height: 35px;
    color: white;
    box-shadow: 0px 0px 8px #0000001A;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074BC;
    font-family: 'Poppins-SemiBold';
    font-size: 14px;
}

.BillerProductivitySection{
    background: #F8F8F8 0% 0% no-repeat padding-box;
    border: 1px solid #CCCCCC;
    border-radius: 6px;
    opacity: 1;
    margin: 12px 5px;
    padding: 8px 0px;
    font-family: 'Poppins-SemiBold';
}

.BillerHeadingRow1{
    color: #646464;
    font-size: 12px;
    font-family: 'Poppins';
}

.SecondBoxHeader{
    background: #E5E5E5 0% 0% no-repeat padding-box;
    border-radius: 6px 6px 0px 0px;
    opacity: 1;
    font-size: 12px;
    font-family: 'Poppins-SemiBold';
}

.SecondBoxBody{
    font-size: 12px;
    font-family: 'Poppins';
    background: #F8F8F8 0% 0% no-repeat padding-box;
border-radius: 0px 0px 6px 6px;
opacity: 1;
}

.BillerHeading{
    color: #005488;
    font-size: 16px;
}

input[type="date"]{
    background-color: white;
    padding-left: 5px;
    outline: none;
    border: 1px solid #D9DADE;
    border-radius: 8px;
    height:30px;
    opacity: 1;
    width: 90%;
    color: #000000;
    font-size: 12px;
    font-family: 'Poppins-Medium';
}

input[type="date"]::-webkit-calendar-picker-indicator {
    color: rgba(0, 0, 0, 0);
    opacity: 1;
    display: block;
    background: url('../../Images/Claims/CalendarIcon.svg') no-repeat;
    width: 9px;
    height: 15px;
    border-width: thin;
}


.DateLabel{
    font-size: 12px;
    color: #646464;
    font-family: 'Poppins-Medium';
}


.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

.MainFlexRow{
    flex-wrap: wrap; margin-top: 10px; padding:5px; 
}
/* applies on full form */

.Full-Form {
    margin-left: 5px;
    font-weight: bolder;
    padding: 5px;
    /* box-shadow: 0px 0px 8px #0000001A; */
    /* border-radius: 8px; */
    opacity: 1;
    background: #F4F7FC 0% 0% no-repeat padding-box;
}


/* for setting first heading box*/

.Form-Header-Row {
    /* position: sticky;
            top: 0px; */
    background-color: white;
    // height: 50px;
    align-items: center;
    box-shadow: 0px 0px 8px #0000001A;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
}


/* for setting heading of first header */

.Form-Header {
    font-size: 23px;
    justify-content: start;
    color: #272D3B;
    font-family: 'Poppins-SemiBold';
}

.NestedHeading{
    color: #5D5D5D;
    font-family: 'Poppins-SemiBold';
    font-size: 19px;
}

.inner-addon {
    position: relative;
}

.inner-addon .ArrowDown {
    position: absolute;
    padding: 10px;
}


/* align icon */

.left-addon .ArrowDown {
    left: 0px;
}

.right-addon .ArrowDown {
    pointer-events: none;
    right: 0;
}
.right-addonSelect .ArrowDown {
    pointer-events: none;
    right: 7%;
}


::ng-deep .mat-option {
    height: auto!important;
    padding: 6px 0px 6px 6px!important;
    overflow: hidden!important;
    font-size: 10px!important;
    font-family: 'Poppins'!important;
}

::ng-deep .mat-option-text {
    overflow: initial!important;
}

::ng-deep .mat-select-panel mat-option.mat-option {
    margin: 1rem 0;
    overflow: visible;
    line-height: initial;
    word-wrap: break-word;
    white-space: pre-wrap;
}

 ::ng-deep .mat-option-text.mat-option-text {
    white-space: normal;
    line-height: normal;
}

.right-addon1 .ArrowDown {
    pointer-events: none;
    margin: -4px 0px 0px 9.5%;
}

.SpinnerCustomDesign {
    height: 12px;
    width: 12px;
    margin-left: 10%;
}

.ArrowDown{
    color: #C9C9C9;
    margin-top: -5px;
    cursor: pointer;
    z-index: 100;
    position: relative;
}

input[type="file"] {
    width: 130px;
}

.custom-file-input::-webkit-file-upload-button {
    visibility: hidden;
}
.custom-file-input::before {
    font-family: "Poppins-SemiBold";
    font-weight: 900;
    // content: "\f093     Choose file";
    content: "Choose file";
    display: inline-block;
    cursor: pointer;
    padding: 5px 12px;
    border: none;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: white;
    color: #0074bc;
    border: solid 1px #0074bc;
    font-size: 14px;
}
.custom-file-input:hover::before ,.reset:hover{
    color: white;
    border-color: #0074bc;
    background-color: #0074bc;
}
.custom-file-input:active::before {
    background: -webkit-linear-gradient(top, #e3e3e3, #f9f9f9);
}


.PlusIcon{
    font-size: 32px;
    margin-top: -15px!important;
    display: flex;
}

.Cancel{
    background-color: white;
    border-color: #0074bc;
    color: #0074bc;
}

object{
    pointer-events: none;
}


::ng-deep .p-datatable-resizable .p-datatable-tbody > tr > td,
.p-datatable-resizable .p-datatable-tfoot > tr > td,
.p-datatable-resizable .p-datatable-thead > tr > th {
    text-overflow: ellipsis !important;
    font-family: 'Poppins';
}

.Form-Body {
    box-shadow: 0px 0px 8px #0000001A;
    margin-right: 5px;
    padding: 5px;
    border: 1px solid #CCCCCC;
    border-radius: 8px;
    opacity: 1;
    background-color: white;
}

th{
    font-size: 12px;
    font-family: 'Poppins-SemiBold';
}

input.ng-invalid.ng-touched , .RedBorder::before {
    border: 1px solid red!important;
}

select.ng-invalid.ng-touched {
    border: 1px solid red!important;
}

 

 

::ng-deep .mat-option {
    height: auto!important;
    padding: 6px 0px 6px 6px!important;
    overflow: hidden!important;
    font-size: 10px!important;
    font-family: 'Poppins'!important;
}

::ng-deep .mat-option-text {
    overflow: initial!important;
}

::ng-deep .mat-select-panel mat-option.mat-option {
    margin: 1rem 0;
    overflow: visible;
    line-height: initial;
    word-wrap: break-word;
    white-space: pre-wrap;
}

 ::ng-deep .mat-option-text.mat-option-text {
    white-space: normal;
    line-height: normal;
}


.PaddingRight{
    padding-right: 22px!important;
}

.Width50{
    width: 50%!important;
}

.Poppins{
    font-family: 'Poppins';
}

.GreyBg{
    background: #F8F8F8 0% 0% no-repeat padding-box;
}

.TextOverFlowNoEllipse{
    text-overflow: initial!important
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-down:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-up-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}


.radioClass {
    height: 16px;
    width: 16px;
}

.radiopClass {
    height: 16px;
    width: 16px;
}

.originalIcon{
    height: 16px; 
    width: 16px;
    margin-right: 5px;
}


.customIcon{
    height: 16px;
    width: 16px; 
    margin-right: 5px;
    margin-top:3px
}


input[type=checkbox] {
    /* Hide original inputs */
    opacity: 0.001;
    visibility: visible;
    position: absolute;
    z-index: 1000;
}

input[type=checkbox]+label+p {
    height: 16px;
    width: 16px;
}

input[type=checkbox]+label>p {
    transition: 100ms all;
    height: 16px;
    width: 16px;
}

input[type=checkbox]:checked+label>p {
    z-index: -1000;
    background-repeat: no-repeat;
    border-style: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16.17' height='16.505' viewBox='0 0 14.17 13.505'%3E%3Cg id='Checkbox' transform='translate(0 0.001)'%3E%3Crect id='Rectangle' width='14.17' height='13.505' rx='3' transform='translate(0 -0.001)' fill='%230074bc'/%3E%3Cpath id='check' d='M10.97,4.97a.75.75,0,1,1,1.071,1.05L8.049,11.01a.75.75,0,0,1-1.08.02L4.324,8.384a.75.75,0,1,1,1.06-1.06L7.478,9.417l3.473-4.425.02-.022Z' transform='translate(-1.085 -0.745)' fill='%23fff' fill-rule='evenodd'/%3E%3C/g%3E%3C/svg%3E%0A");
}

input[type=checkbox]+label>p {
    width: 16px;
    height: 16px;
    border: #999;
    border-width: 1px;
    border-style: solid;
    border-radius: 5px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16.17' height='16.505' viewBox='0 0 8.17 6.505'%3E%3Cpath id='check' d='M10.97,4.97a.75.75,0,1,1,1.071,1.05L8.049,11.01a.75.75,0,0,1-1.08.02L4.324,8.384a.75.75,0,1,1,1.06-1.06L7.478,9.417l3.473-4.425.02-.022Z' transform='translate(-4.085 -4.745)' fill='%23f4f7fc'/%3E%3C/svg%3E%0A");
}


