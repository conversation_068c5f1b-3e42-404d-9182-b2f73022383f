{"ast": null, "code": "export function changeRowColor(params) {\n  if (params.node.rowIndex % 2 === 0) {\n    return {\n      'background-color': '#f1f0f0'\n    };\n  } else {\n    return {\n      'background-color': 'white'\n    };\n  }\n}\nexport let GRID_CONFIG = /*#__PURE__*/(() => {\n  class GRID_CONFIG {}\n\n  GRID_CONFIG.configDetails = {\n    pagination: 25,\n    localeText: {\n      page: 'Page',\n      of: 'of',\n      to: 'to',\n      more: '...',\n      totalRows: 'Total Rows: '\n    },\n    gridOptions: {\n      rowHeight: 30,\n      getRowStyle: changeRowColor,\n      defaultColDef: {\n        initialWidth: 100,\n        sortable: true,\n        filter: true,\n        floatingFilter: true,\n        suppressMenu: true,\n        suppressCellSelection: true,\n        wrapText: true,\n        autoHeight: true,\n        lockVisible: true,\n        floatingFilterComponentParams: {\n          suppressFilterButton: true\n        }\n      },\n      style: {\n        width: '100%',\n        height: '100%',\n        flex: '1 1 auto'\n      },\n      pagination: {\n        enable: true,\n        size: 25\n      },\n      totalRowsCount: 0\n    }\n  };\n  GRID_CONFIG.scrollConfigDetails = {\n    gridOptions: {\n      rowHeight: 30,\n      getRowStyle: changeRowColor,\n      defaultColDef: {\n        initialWidth: 100,\n        sortable: false,\n        filter: true,\n        floatingFilter: true,\n        suppressMenu: true,\n        suppressCellSelection: true,\n        wrapText: true,\n        autoHeight: true,\n        lockVisible: true,\n        floatingFilterComponentParams: {\n          suppressFilterButton: true\n        }\n      },\n      style: {\n        width: '50%',\n        height: '100%',\n        flex: '1 1 auto'\n      },\n      suppressHorizontalScroll: false,\n      rowModelType: 'infinite',\n      cacheBlockSize: 50,\n      maxBlocksInCache: 2,\n      enableCellTextSelection: true,\n      enableRangeSelection: true,\n      suppressCopySingleCellRanges: false\n    },\n    checkboxScrollGridOptions: {\n      rowHeight: 30,\n      getRowStyle: changeRowColor,\n      defaultColDef: {\n        initialWidth: 100,\n        sortable: false,\n        filter: true,\n        floatingFilter: true,\n        suppressMenu: true,\n        suppressCellSelection: true,\n        wrapText: true,\n        autoHeight: false,\n        lockVisible: true,\n        floatingFilterComponentParams: {\n          suppressFilterButton: true\n        }\n      },\n      style: {\n        width: '50%',\n        height: '100%',\n        flex: '1 1 auto'\n      },\n      enableCellTextSelection: true,\n      suppressMovableColumns: true,\n      suppressLoadingOverlay: true,\n      stopEditingWhenCellsLoseFocus: true,\n      suppressDragLeaveHidesColumns: true,\n      suppressHorizontalScroll: false,\n      singleClickEdit: true,\n      suppressRowClickSelection: true,\n      rowSelection: 'multiple',\n      cacheBlockSize: 50,\n      maxBlocksInCache: 10,\n      rowModelType: 'infinite' // pagination: false,\n      // paginationAutoPageSize: false,\n      //getRowId: (params: GetRowIdParams) => params.data.id.toString(),\n\n    }\n  };\n  GRID_CONFIG.scrollConfigDetails_SearchClaim = {\n    gridOptions: {\n      rowHeight: 30,\n      getRowStyle: changeRowColor,\n      defaultColDef: {\n        initialWidth: 100,\n        sortable: false,\n        filter: true,\n        floatingFilter: true,\n        suppressMenu: true,\n        suppressCellSelection: true,\n        wrapText: true,\n        autoHeight: true,\n        lockVisible: true,\n        floatingFilterComponentParams: {\n          suppressFilterButton: true\n        }\n      },\n      style: {\n        width: '50%',\n        height: '100%',\n        flex: '1 1 auto'\n      },\n      suppressHorizontalScroll: false,\n      rowModelType: 'infinite',\n      cacheBlockSize: 50,\n      maxBlocksInCache: 2,\n      enableCellTextSelection: true,\n      enableRangeSelection: true,\n      suppressCopySingleCellRanges: false\n    },\n    checkboxScrollGridOptions: {\n      rowHeight: 30,\n      getRowStyle: changeRowColor,\n      defaultColDef: {\n        initialWidth: 100,\n        sortable: false,\n        filter: true,\n        floatingFilter: true,\n        suppressMenu: true,\n        suppressCellSelection: true,\n        wrapText: true,\n        autoHeight: true,\n        lockVisible: true,\n        floatingFilterComponentParams: {\n          suppressFilterButton: true\n        }\n      },\n      style: {\n        width: '50%',\n        height: '100%',\n        flex: '1 1 auto'\n      },\n      enableCellTextSelection: true,\n      suppressMovableColumns: true,\n      suppressLoadingOverlay: true,\n      stopEditingWhenCellsLoseFocus: true,\n      suppressDragLeaveHidesColumns: true,\n      suppressHorizontalScroll: false,\n      singleClickEdit: true,\n      suppressRowClickSelection: true,\n      rowSelection: 'multiple',\n      cacheBlockSize: 50,\n      maxBlocksInCache: 10,\n      rowModelType: 'infinite' // pagination: false,\n      // paginationAutoPageSize: false,\n      //getRowId: (params: GetRowIdParams) => params.data.id.toString(),\n\n    }\n  };\n  return GRID_CONFIG;\n})();", "map": null, "metadata": {}, "sourceType": "module"}