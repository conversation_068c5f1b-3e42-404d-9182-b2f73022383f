{"ast": null, "code": "import { map } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/services/api.service\";\nexport let ClaimService = /*#__PURE__*/(() => {\n  class ClaimService {\n    constructor(appService) {\n      this.appService = appService;\n    }\n\n    getClaimById(claimId) {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: true,\n        IsDebugLogRequired: true\n      };\n      return this.appService.getCall('/claim/getclaimbyid?claimId=' + claimId, headerData, null).pipe(map(response => {\n        if (response.statusCode == 200) {\n          return response.content;\n        }\n\n        return null;\n      }));\n    }\n\n    createClaim(claim) {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: true,\n        IsDebugLogRequired: true\n      };\n      return this.appService.postCall('/claim/create', headerData, claim).pipe(map(response => {\n        if (response.statusCode == 200) {\n          return response.content;\n        }\n\n        return null;\n      }));\n    }\n\n    updateNotes(claim) {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: true,\n        IsDebugLogRequired: true\n      };\n      return this.appService.postCall('/claim/updateNotes', headerData, claim);\n    }\n\n    dublicateClaimCheck(claim) {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: true,\n        IsDebugLogRequired: true\n      };\n      return this.appService.postCall('/claim/checkduplicateclaims', headerData, claim).pipe(map(response => {\n        if (response.statusCode == 200) {\n          return response.content;\n        }\n\n        return null;\n      }));\n    }\n\n    claimStatusChange(claim) {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: true,\n        IsDebugLogRequired: true\n      };\n      return this.appService.postCall('/claim/changestatus', headerData, claim).pipe(map(response => {\n        if (response.statusCode == 200) {\n          return response.message;\n        }\n\n        return null;\n      }));\n    }\n\n    validateAddress(claim) {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: true,\n        IsDebugLogRequired: true\n      };\n      return this.appService.postCall('/claim/getmembervalidationforclaimmovefromopen', headerData, claim).pipe(map(response => {\n        if (response.statusCode == 200) {\n          return response.content;\n        }\n\n        return null;\n      }));\n    }\n\n    getClaimOnHoldReason() {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: true,\n        IsDebugLogRequired: true\n      };\n      return this.appService.getCall('/claim/GetClaimOnHoldReasonCategory?statusCode=OH', headerData, null).pipe(map(response => {\n        if (response.statusCode == 200) {\n          return response.content;\n        }\n\n        return null;\n      }));\n    }\n\n    get277PendingStatus(claim) {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: true,\n        IsDebugLogRequired: true\n      };\n      return this.appService.getCall('/claim/get277PendingStatusInformation?claimId=' + claim, headerData, claim).pipe(map(response => {\n        if (response.statusCode == 200) {\n          return response.content;\n        }\n\n        return null;\n      }));\n    }\n\n    getReasonCodeClaimRejection(claimId) {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: true,\n        IsDebugLogRequired: true\n      };\n      return this.appService.getCall('/claim/get277ReasoncodeClaimRejectionDescription?claimId=' + claimId, headerData, claimId).pipe(map(response => {\n        if (response.statusCode == 200) {\n          return response.content;\n        }\n\n        return null;\n      }));\n    }\n\n  }\n\n  ClaimService.ɵfac = function ClaimService_Factory(t) {\n    return new (t || ClaimService)(i0.ɵɵinject(i1.HttpService));\n  };\n\n  ClaimService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClaimService,\n    factory: ClaimService.ɵfac,\n    providedIn: 'root'\n  });\n  return ClaimService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}