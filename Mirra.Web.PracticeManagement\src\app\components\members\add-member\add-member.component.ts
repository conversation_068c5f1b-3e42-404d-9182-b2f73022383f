import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, ValidationErrors, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ConfigurationMaster } from 'src/app/classmodels/ResponseModel/MasterData/ConfigurationMaster';
import { AllStatesBySearchstringService } from 'src/app/services/ClaimForm/all-states-by-searchstring.service';
import { MemberService } from 'src/app/services/Member/member.service';
import { NotificationService } from 'src/app/services/Notification/notification.service';
import { ProviderManagementService } from 'src/app/services/ProviderManagement/provider-management.service';
import { FilePreviewDialogComponent } from 'src/app/shared/components/file-preview-dialog/file-preview-dialog.component';
import { checkIfFileTypeValid } from 'src/app/shared/functions/checkIfFileTypeValid';
import { submitValidateAllFields } from 'src/app/shared/functions/submitFormRelatedFunctions';
import { MasterdataService } from 'src/app/shared/services/masterdata.service';
import { SubjectService } from 'src/app/shared/services/subject.service';
import { MemberAlreadyExistsComponent } from '../member-already-exists/member-already-exists.component';
import { ProviderService } from 'src/app/services/Providers/provider.service';
import { debounceTime, distinctUntilChanged, first, Subject,takeUntil } from 'rxjs';
import { NavTabFromDetails, Tabs } from 'src/app/common/nav-constant';
import { noWhitespaceValidator, trimSpaces } from 'src/app/shared/functions/customFormValidators';
import * as JSLZString from 'lz-string';
import { GlobalService } from 'src/app/shared/services/global.service';
import { PREVILEGES } from 'src/app/common/common-static';
import * as moment from 'moment';
import { AddressMismatchComponent } from 'src/app/shared/components/address-mismatch/address-mismatch.component';
@Component({
  selector: 'app-add-member',
  templateUrl: './add-member.component.html',
  styleUrls: ['./add-member.component.scss']
})
export class AddMemberComponent implements OnInit {
  citySearchSubject = new Subject<string>();
  addForm;
  today = new Date();
  genderData: ConfigurationMaster[] = [];
  citiesData;
  statesData;
  countiesData;
  countriesData;
  ipaData;
  attachmentTypesData;
  attachments = [];
  memberToAdd: any = {};
  insuranceCompaniesData;
  planData;
  pcpData;
  mainHeading = 'Add Member';
  mode = 'add';
  memberDataForView;
  showActivityLog = false;
  addTab = false;
  isViewEditBtnShow: boolean = false;
  destroy$: Subject<boolean> = new Subject<boolean>();

  constructor(private formBuilder: FormBuilder, public datepipe: DatePipe, private masterDataService: MasterdataService, private providerManagementService: ProviderManagementService,
    private stateService: AllStatesBySearchstringService, private notificationService: NotificationService, private dialog: MatDialog,
    private memberService: MemberService, private subService: SubjectService, private providerService: ProviderService,  
    private readonly globalService: GlobalService) {
    this.getPrivileges();
  }

  ngOnInit(): void {
    this.createFormGroup();
    this.getAllMasterData();
    if (!!this.subService.currentTab && this.subService.currentTab.name == 'Add New Member') {
      this.addTab = true;
    }
    this.subService.getSelectedMemberInfoForView().pipe(first()).subscribe((res) => {
      if (!!res && !this.addTab) {
        if (this.mode == 'add') {
          this.mode = res.mode;
        }
        if (this.mode == 'edit') {
          this.mainHeading = 'Edit Member'
        } else if (this.mode == 'view') {
          this.mainHeading = 'View Member';
        }
        this.memberService.fetchParticularMember(res.data.uniqueMemberID, res.data.planCode, res.data.effectiveDate, this.mode).subscribe((data) => {
          if (!(!!data)) {
            this.notificationService.showWarning('', 'Issue getting Member Details!', 4000);
          } else {
            if (!(!!this.memberDataForView)) {
              this.memberDataForView = data;
              //this.attachments = JSON.parse(JSON.stringify(data.documents));
              this.patchValueToForm(data);
              this.memberToAdd = JSON.parse(JSON.stringify(this.memberDataForView));
              if (this.mode == 'view') {
                // this.setDatesInViewMode(data);
                this.addForm.disable();
              }
              if (this.mode == 'edit') {
                this.getPCPDetails();
                this.getPlanData();
                this.addForm.controls.subscriberID.disable();
                // if (!(!!this.attachments) || this.attachments.length == 0) {
                //   this.attachments = [];
                //   this.addNewAttachment();
                // }
              }
            }
          }
          this.subService.resetSelectedMemberInfoForView();
        })
      }
    })
    this.subService.getRefreshMemberViewAfterEdit().pipe(takeUntil(this.destroy$)).subscribe((res) => {
      if (this.mode == 'view' && !!this.memberDataForView && !!res && this.memberDataForView.memberprofileId == res.memberprofileId && this.memberDataForView.subscriberID == res.subscriberID) {
        this.memberService.fetchParticularMember(res.memberprofileId.toString(), res.planCode, res.effectiveDate, this.mode).subscribe((data) => {
          if (!(!!data)) {
            this.notificationService.showWarning('', 'Issue getting Member Details!', 4000);
          } else {
            this.memberDataForView = data;
           // this.attachments = JSON.parse(JSON.stringify(data.documents));
            this.patchValueToForm(data);
            this.memberToAdd = JSON.parse(JSON.stringify(this.memberDataForView));
            this.addForm.disable();
          }
          this.subService.resetRefreshMemberViewAfterEdit();
        })
      }
    })

    // Set up city search subscription
    this.citySearchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(searchTerm => {
      this.searchCities(searchTerm);
    });
  }
  get f() { return this.addForm.controls; }
  createFormGroup() {
    this.addForm = this.formBuilder.group({
      subscriberID: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      lastName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      firstName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      middleName: new FormControl(null),
      gender: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      dateOfBirth: new FormControl(null, [Validators.required]),
      personEffectiveDate: new FormControl(null, [Validators.required]),
      personTerminationDate: new FormControl(this.datepipe.transform('12/31/9999', 'yyyy-MM-dd'), [Validators.required]),
      addressLine1: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      addressLine2: new FormControl(null),
      addressEffectiveDate: new FormControl(null, [Validators.required]),
      addressTerminationDate: new FormControl(this.datepipe.transform('12/31/9999', 'yyyy-MM-dd'), [Validators.required]),
      relationshipName: new FormControl({ value: 'SELF', disabled: true }, [Validators.required, noWhitespaceValidator]),
      planEffectiveDate: new FormControl(null, [Validators.required]),
      planTerminationDate: new FormControl(this.datepipe.transform('12/31/9999', 'yyyy-MM-dd'), [Validators.required]),
      pcpEffectiveDate: new FormControl(null, [Validators.required]),
      pcpTerminationDate: new FormControl(this.datepipe.transform('12/31/9999', 'yyyy-MM-dd'), [Validators.required]),
      city: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      state: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      country: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      county: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      zipCode: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(9)]),
      number: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(10)]),
      ipaName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      insuranceCompanyName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      planName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
      pcpName: new FormControl(null, [Validators.required, noWhitespaceValidator]),
    })
    this.dateValidatorForDates();
  }
  patchValueToForm(data) {
    this.addForm.patchValue({
      subscriberID: data.subscriberID,
      lastName: data.lastName,
      firstName: data.firstName,
      middleName: data.middleName,
      gender: !!data.gender ? data.gender.toUpperCase() : data.gender,
      dateOfBirth: !!data.dateOfBirth ? this.datepipe.transform(data.dateOfBirth, 'yyyy-MM-dd') : data.dateOfBirth,
      personEffectiveDate: !!data.personEffectiveDate ? this.datepipe.transform(data.personEffectiveDate, 'yyyy-MM-dd') : data.personEffectiveDate,
      personTerminationDate: !!data.personTerminationDate ? this.datepipe.transform(data.personTerminationDate, 'yyyy-MM-dd') : data.personTerminationDate,
      addressLine1: data.addressLine1,
      addressLine2: data.addressLine2,
      addressEffectiveDate: !!data.addressEffectiveDate ? this.datepipe.transform(data.addressEffectiveDate, 'yyyy-MM-dd') : data.addressEffectiveDate,
      addressTerminationDate: !!data.addressTerminationDate ? this.datepipe.transform(data.addressTerminationDate, 'yyyy-MM-dd') : data.addressTerminationDate,
      relationshipName: data.relationshipName,
      planEffectiveDate: !!data.planEffectiveDate ? this.datepipe.transform(data.planEffectiveDate, 'yyyy-MM-dd') : data.planEffectiveDate,
      planTerminationDate: !!data.planTerminationDate ? this.datepipe.transform(data.planTerminationDate, 'yyyy-MM-dd') : data.planTerminationDate,
      pcpEffectiveDate: !!data.pcpEffectiveDate ? this.datepipe.transform(data.pcpEffectiveDate, 'yyyy-MM-dd') : data.pcpEffectiveDate,
      pcpTerminationDate: !!data.pcpTerminationDate ? this.datepipe.transform(data.pcpTerminationDate, 'yyyy-MM-dd') : data.pcpTerminationDate,
      city: data.city,
      state: data.state,
      country: data.country,
      county: data.county,
      zipCode: data.zipCode,
      number: data.number,
      ipaName: data.ipaName,
      insuranceCompanyName: data.insuranceCompanyName,
      planName: data.planName,
      pcpName: data.pcpName,
    })
  }
  setDatesInViewMode(data) {
    this.addForm.controls.dateOfBirth.setValue(!!data.dateOfBirth ? this.datepipe.transform(data.dateOfBirth, 'MM-dd-yyyy') : data.dateOfBirth);
    this.addForm.controls.personEffectiveDate.setValue(!!data.personEffectiveDate ? this.datepipe.transform(data.personEffectiveDate, 'MM-dd-yyyy') : data.personEffectiveDate);
    this.addForm.controls.personTerminationDate.setValue(!!data.personTerminationDate ? this.datepipe.transform(data.personTerminationDate, 'MM-dd-yyyy') : data.personTerminationDate);
    this.addForm.controls.addressEffectiveDate.setValue(!!data.addressEffectiveDate ? this.datepipe.transform(data.addressEffectiveDate, 'MM-dd-yyyy') : data.addressEffectiveDate);
    this.addForm.controls.addressTerminationDate.setValue(!!data.addressTerminationDate ? this.datepipe.transform(data.addressTerminationDate, 'MM-dd-yyyy') : data.addressTerminationDate);
    this.addForm.controls.planEffectiveDate.setValue(!!data.planEffectiveDate ? this.datepipe.transform(data.planEffectiveDate, 'MM-dd-yyyy') : data.planEffectiveDate);
    this.addForm.controls.planTerminationDate.setValue(!!data.planTerminationDate ? this.datepipe.transform(data.planTerminationDate, 'MM-dd-yyyy') : data.planTerminationDate);
    this.addForm.controls.pcpEffectiveDate.setValue(!!data.pcpEffectiveDate ? this.datepipe.transform(data.pcpEffectiveDate, 'MM-dd-yyyy') : data.pcpEffectiveDate);
    this.addForm.controls.pcpTerminationDate.setValue(!!data.pcpTerminationDate ? this.datepipe.transform(data.pcpTerminationDate, 'MM-dd-yyyy') : data.pcpTerminationDate);

  }
  getAllMasterData() {
    this.masterDataService.fetchchMasterData('Gender').subscribe((res) => {
      this.genderData = res;
    })
     // Initialize cities data as empty - will be populated by search
     this.citiesData = [];
    let state = localStorage.getItem('allStates');
    if (!!state) {
      this.statesData = JSON.parse(JSLZString.decompress(state));
    } else {
      this.stateService.fetchchAllStatesBySearchstring().subscribe((res) => {
        this.statesData = res;
      })
    }
    let countries = localStorage.getItem('allCountries')
    if (!!countries) {
      this.countriesData = JSON.parse(JSLZString.decompress(countries));
    } else {
      this.providerManagementService.fetchCountriesData("").subscribe((res) => {
        this.countriesData = !!res.content ? res.content : [];
      })
    }
    let counties = localStorage.getItem('allCounties')
    if (!!counties) {
      this.countiesData = JSON.parse(JSLZString.decompress(counties));
    } else {
      this.providerManagementService.fetchCountiesData("").subscribe((res) => {
        this.countiesData = !!res.content ? res.content : [];
      })
    }
    this.providerManagementService.fetchIPAData('').subscribe((res) => {
      this.ipaData = !!res.content ? res.content : [];
    })
    this.masterDataService.fetchchMasterData('AttatchmentTypes').subscribe((data) => {
      this.attachmentTypesData = data;
      this.addNewAttachment();
    });
    let insuranceData = localStorage.getItem('insuranceCompaniesOrAllPlans')
    if (!!insuranceData) {
      this.insuranceCompaniesData = JSON.parse(JSLZString.decompress(insuranceData));
    } else {
      this.providerManagementService.fetchPlansData('').subscribe((res) => {
        this.insuranceCompaniesData = !!res.content ? res.content : [];
      })
    }
  }
  checkIfSubscriberIdAlreadyExists() {
    let id = this.addForm.controls.subscriberID.value;
    let toCheck = true;
    if (this.mode == 'edit' && id == this.memberDataForView.subscriberID) {
      toCheck = false;
    }
    if (!!id && id.length > 0 && toCheck) {
      this.memberService.CheckSubscriberIDExists(id).subscribe((res) => {
        if (!!res) {
          this.notificationService.showError('', 'Subscriber ID already exists', 4000);
          this.dialog.open(MemberAlreadyExistsComponent, { data: res, width: '-webkit-fill-available' });
          this.addForm.controls.subscriberID.setValue(null);
        }
      })
    }
  }
  changeCitySelect(selectedCity) {
    if (!!selectedCity) {
      this.addForm.controls.country.setValue(selectedCity.countryName);
      this.addForm.controls.county.setValue(selectedCity.countyName);
      this.addForm.controls.state.setValue(selectedCity.stateCode);
      this.memberToAdd.cityCode = selectedCity.cityCode;
      this.memberToAdd.countryCode = selectedCity.countryCode;
      this.memberToAdd.countyCode = selectedCity.countyMDMCode;
    } else {
      if (!!this.memberToAdd.cityCode) {
        this.memberToAdd.cityCode = null;
      }
    }
  }

  searchCities(searchTerm: string) {
    // Only search if user has typed 2 or more characters
    if (!searchTerm || searchTerm.length < 2) {
      this.citiesData = [];
      return;
    }

    this.providerManagementService.fetchCitiesBySearchTerm(searchTerm).subscribe((res) => {
      if (!!res && !!res.content) {
        this.citiesData = JSON.parse(res.content);
      } else {
        this.citiesData = [];
      }
    }, (error) => {
      console.error('Error searching cities:', error);
      this.citiesData = [];
    });
  }
  changeCountySelect(selectedCounty) {
    if (!!selectedCounty) {
      this.memberToAdd.countyCode = selectedCounty.countyMDMCode;
    } else {
      this.memberToAdd.countyCode = null;
    }
  }
  changeIPASelect(selectedIPA) {
    if (!!selectedIPA) {
      this.memberToAdd.ipaCode = selectedIPA.mdmCode;
      this.resetPCPFields();
      this.getPCPDetails();
    } else {
      this.memberToAdd.ipaCode = null;
      this.resetPCPFields();
    }
  }
  getPCPDetails() {
    if (!!this.memberToAdd.ipaCode && !!this.memberToAdd.insuranceCompanyCode) {
      let requestBody = {
        searchTerm: "",
        ipa: this.memberToAdd.ipaCode,
        companyName: this.memberToAdd.insuranceCompanyCode
      }
      this.providerService.GetAllProviderDetailsByIPA(requestBody).subscribe((res) => {
        if (!!res && !!res.searchResult && res.searchResult.length > 0) {
          this.pcpData = res.searchResult;
        } else {
          this.notificationService.showWarning('', 'No PCP found for selected Insurance Company and IPA.', 4000);
        }
      })
    }
  }
  changeCountrySelect(selectedCountry) {
    if (!!selectedCountry) {
      this.memberToAdd.countryCode = selectedCountry.countryCode;
    } else {
      this.memberToAdd.countryCode = null;
    }
  }
  getPlanData() {
    if (!!this.memberToAdd.insuranceCompanyCode) {
      this.memberService.getPlansByInsuranceCompanyCode(this.memberToAdd.insuranceCompanyCode).subscribe((res) => {
        this.planData = !!res.content ? res.content : [];
        if (!(!!this.planData && this.planData.length > 0)) {
          this.notificationService.showWarning('', 'No Plan found for selected Insurance Company.', 4000);
        }
      });
    }
  }
  changeInsuranceCompanySelect(selectedInsuranceCompany) {
    if (!!selectedInsuranceCompany) {
      this.memberToAdd.insuranceCompanyCode = selectedInsuranceCompany.mdmCode;
      this.memberService.getPlansByInsuranceCompanyCode(this.memberToAdd.insuranceCompanyCode).subscribe((res) => {
        this.planData = !!res.content ? res.content : [];
        this.addForm.controls.planName.reset();
        this.memberToAdd.planCode = null;
        if (!(!!this.planData && this.planData.length > 0)) {
          this.notificationService.showWarning('', 'No Plan found for selected Insurance Company.', 4000);
        }
      });
      this.resetPCPFields();
      this.getPCPDetails();
    } else {
      this.memberToAdd.insuranceCompanyCode = null;
      this.addForm.controls.planName.reset();
      this.memberToAdd.planCode = null;
      this.planData = [];
      this.resetPCPFields();
    }
  }
  checkIfPlanNameCanBeSelected() {
    const selectedInsuranceCompany = this.addForm.controls.insuranceCompanyName.value;
    if (!(!!selectedInsuranceCompany && selectedInsuranceCompany.length > 0)) {
      this.notificationService.showWarning('Please select "Insurance Company" first.', 'Invalid Selection', 4000);
    }
  }
  checkIfPCPNameCanBeSelected() {
    const selectedInsuranceCompany = this.addForm.controls.insuranceCompanyName.value;
    const selectedIPAName = this.addForm.controls.ipaName.value;
    if (!(!!selectedInsuranceCompany && selectedInsuranceCompany.length > 0 && !!selectedIPAName && selectedIPAName.length > 0)) {
      this.notificationService.showWarning('Please select "Insurance Company" and "IPA Name" first.', 'Invalid Selection', 4000);
    }
  }
  resetPCPFields() {
    this.addForm.controls.pcpName.reset();
    this.pcpData = [];
    this.memberToAdd.npi = null;
    this.memberToAdd.uniqueProviderID = null;
  }
  changePlanNameSelect(selectedPlan) {
    if (!!selectedPlan) {
      this.memberToAdd.planCode = selectedPlan.planCode;
    } else {
      this.memberToAdd.planCode = null;
    }
  }
  changePCPNameSelect(selectedPCP) {
    if (!!selectedPCP) {
      this.memberToAdd.npi = selectedPCP.npiNumber;
      this.memberToAdd.uniqueProviderID = selectedPCP.uniqueProviderId;
    } else {
      this.memberToAdd.npi = null;
      this.memberToAdd.uniqueProviderID = null;
    }
  }
  addMember() {
    if (this.addForm.invalid) {
      submitValidateAllFields.validateAllFields(this.addForm);
      let errors = {
        required: false,
        zipCodeMaxLength: false,
        phoneNumberMaxLength: false,
        invalidEffectiveDate: false,
        invalidMemberDate: false
      }
      Object.keys(this.addForm.controls).forEach(key => {
        const controlErrors: ValidationErrors = this.addForm.get(key).errors;
        if (controlErrors != null) {
          Object.keys(controlErrors).forEach(keyError => {
            // if (keyError == 'required' || keyError == 'whitespace') {
            //   errors.required = errors.required || controlErrors[keyError]
            // };
            // if (keyError == 'maxlength' && key == 'zipCode') {
            //   errors.zipCodeMaxLength = true;
            // }
            // if (keyError == 'maxlength' && key == 'number') {
            //   errors.phoneNumberMaxLength = true;
            // }
            if (keyError == 'invalidEffectiveDate') {
              errors.invalidEffectiveDate = true;
            }
            if (keyError == 'invalidMemberDate') {
              errors.invalidMemberDate = true
            }
          });
        }
      });
      // if (errors.required) {
      //   this.notificationService.showError('', 'Please fill all the required (*) fields.', 4000)
      // }
      // if (errors.zipCodeMaxLength) {
      //   this.notificationService.showError('', "'Zip Code' should be less than or equal to 9 characters in length.", 4000);
      // }
      // if (errors.phoneNumberMaxLength) {
      //   this.notificationService.showError('', "'Phone Number' should be less than or equal to 10 characters in length.", 4000);
      // }
      if (errors.invalidEffectiveDate) {
        this.notificationService.showError('', "'Addr Eff Date', 'Plan Eff Date' and 'PCP Eff From' should be greater than or equal to 'MBR Eff Date'.", 4000);
      }
      if (errors.invalidMemberDate) {
        this.notificationService.showError('', "'MBR Eff Date' should be greater than or equal to 'Date of Birth'.", 4000);
      }
      return;
    } 
    let patientrequestbody = {
      addressLineOne: this.addForm.controls["addressLine1"].value,
      city: this.addForm.controls["city"].value,
      state: this.addForm.controls["state"].value,
      givenZipcode: this.addForm.controls["zipCode"].value,
    };
    this.providerManagementService.GetCorrectAddress(patientrequestbody).subscribe(res => {

      if (!!res && !!res.content && res.content.isSmartAddressFound && !res.content.isAdderssCorrect) {
        const dialogRef = this.dialog.open(AddressMismatchComponent, {
          width: '800px',
          data: [
            { entity: 'Member', address: res.content }
          ]
        });
        dialogRef.afterClosed().subscribe((resp: any) => {
          if (!!resp && resp.isMapAddresss) {
            this.addForm.controls["addressLine1"].setValue(res.content.addressLineOne);
            this.addForm.controls["city"].setValue(res.content.city);
            this.addForm.controls["state"].setValue(res.content.state);
            this.addForm.controls["zipCode"].setValue(res.content.zipcode);
            this.saveMember();
          }
          else if(!!resp  && resp.isMapAddresss == false){
            this.saveMember();
          }
        });
      }
      else {
        this.saveMember();
      }

    });
  }


  saveMember() {
    let documents = [];
    for (const attachment of this.attachments) {
      if (!!attachment.document) {
        documents.push(attachment);
      }
    }
    const memberToAddCopy = JSON.parse(JSON.stringify(this.memberToAdd));
    this.memberToAdd = JSON.parse(JSON.stringify(this.addForm.value));
    this.memberToAdd.pcpEffDate = this.memberToAdd.pcpEffectiveDate && (moment.isMoment(this.memberToAdd.pcpEffectiveDate) || this.memberToAdd.pcpEffectiveDate.includes('T')) ? moment(this.memberToAdd.pcpEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.pcpEffectiveDate;
    this.memberToAdd.pcpTermDate = this.memberToAdd.pcpTerminationDate && (moment.isMoment(this.memberToAdd.pcpTerminationDate) || this.memberToAdd.pcpTerminationDate.includes('T')) ? moment(this.memberToAdd.pcpTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.pcpTerminationDate;
    this.memberToAdd.planEffDate = this.memberToAdd.planEffectiveDate && (moment.isMoment(this.memberToAdd.planEffectiveDate) || this.memberToAdd.planEffectiveDate.includes('T')) ? moment(this.memberToAdd.planEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.planEffectiveDate;
    this.memberToAdd.planTermDate = this.memberToAdd.planTerminationDate && (moment.isMoment(this.memberToAdd.planTerminationDate) || this.memberToAdd.planTerminationDate.includes('T')) ? moment(this.memberToAdd.planTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.planTerminationDate;
    this.memberToAdd.dateOfBirth = this.memberToAdd.dateOfBirth && (moment.isMoment(this.memberToAdd.dateOfBirth) || this.memberToAdd.dateOfBirth.includes('T')) ? moment(this.memberToAdd.dateOfBirth).format('YYYY-MM-DD') : this.memberToAdd.dateOfBirth;
    this.memberToAdd.personEffectiveDate = this.memberToAdd.personEffectiveDate && (moment.isMoment(this.memberToAdd.personEffectiveDate) || this.memberToAdd.personEffectiveDate.includes('T')) ? moment(this.memberToAdd.personEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.personEffectiveDate;
    this.memberToAdd.personTerminationDate = this.memberToAdd.personTerminationDate && (moment.isMoment(this.memberToAdd.personTerminationDate) || this.memberToAdd.personTerminationDate.includes('T')) ? moment(this.memberToAdd.personTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.personTerminationDate;
    this.memberToAdd.addressEffectiveDate = this.memberToAdd.addressEffectiveDate && (moment.isMoment(this.memberToAdd.addressEffectiveDate) || this.memberToAdd.addressEffectiveDate.includes('T')) ? moment(this.memberToAdd.addressEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.addressEffectiveDate;
    this.memberToAdd.addressTerminationDate = this.memberToAdd.addressTerminationDate && (moment.isMoment(this.memberToAdd.addressTerminationDate) || this.memberToAdd.addressTerminationDate.includes('T')) ? moment(this.memberToAdd.addressTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.addressTerminationDate;
    this.memberToAdd.planEffectiveDate = this.memberToAdd.planEffectiveDate && (moment.isMoment(this.memberToAdd.planEffectiveDate) || this.memberToAdd.planEffectiveDate.includes('T')) ? moment(this.memberToAdd.planEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.planEffectiveDate;
    this.memberToAdd.planTerminationDate = this.memberToAdd.planTerminationDate && (moment.isMoment(this.memberToAdd.planTerminationDate) || this.memberToAdd.planTerminationDate.includes('T')) ? moment(this.memberToAdd.planTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.planTerminationDate;
    this.memberToAdd.pcpEffectiveDate = this.memberToAdd.pcpEffectiveDate && (moment.isMoment(this.memberToAdd.pcpEffectiveDate) || this.memberToAdd.pcpEffectiveDate.includes('T')) ? moment(this.memberToAdd.pcpEffectiveDate).format('YYYY-MM-DD') : this.memberToAdd.pcpEffectiveDate;
    this.memberToAdd.pcpTerminationDate = this.memberToAdd.pcpTerminationDate && (moment.isMoment(this.memberToAdd.pcpTerminationDate) || this.memberToAdd.pcpTerminationDate.includes('T')) ? moment(this.memberToAdd.pcpTerminationDate).format('YYYY-MM-DD') : this.memberToAdd.pcpTerminationDate;
    this.memberToAdd.stateSubdivisionCode = this.memberToAdd.state;
    this.memberToAdd.stateCode = this.memberToAdd.state;
    this.memberToAdd.relationshipCode = 'PTR00000001';
    this.memberToAdd.createdBy = localStorage.getItem('userName');
    this.memberToAdd.cityCode = memberToAddCopy.cityCode;
    this.memberToAdd.countryCode = memberToAddCopy.countryCode;
    this.memberToAdd.countyCode = memberToAddCopy.countyCode;
    this.memberToAdd.ipaCode = memberToAddCopy.ipaCode;
    this.memberToAdd.insuranceCompanyCode = memberToAddCopy.insuranceCompanyCode;
    this.memberToAdd.planCode = memberToAddCopy.planCode;
    this.memberToAdd.npi = memberToAddCopy.npi;
    this.memberToAdd.uniqueProviderID = memberToAddCopy.uniqueProviderID;
    this.memberToAdd.relationshipName = 'SELF';
    this.memberToAdd.planName = !!this.memberToAdd.planName ? this.memberToAdd.planName.toUpperCase() : this.memberToAdd.planName;
    this.memberToAdd.documents = documents;
    const fileArray = [];
    for (const attachment of documents) {
      fileArray.push(attachment.document);
    }
    this.memberToAdd = trimSpaces(this.memberToAdd);
    if (this.mode == 'add') {
      this.memberService.AddMemberDetails(this.memberToAdd, fileArray).subscribe((res) => {
        if (res.statusCode == 200) {
          this.notificationService.showSuccess('', 'Member Added Successfully.', 4000);
          this.subService.setRefreshMemberInfo(true);
          this.memberToAdd = {};
          this.attachments = [];
          this.addNewAttachment();
          this.addForm.reset();
          this.cancel();
        } else {
          this.notificationService.showError('', 'Member could not be added. Please try again later.', 4000);
        }
      }, (error) => {
        this.notificationService.showError('', 'Member could not be added. Please try again later.', 4000);
      })
    }
    if (this.mode == 'edit') {
      this.memberToAdd.subscriberID = this.memberDataForView.subscriberID;
      this.memberToAdd.personId = this.memberDataForView.personId;
      this.memberToAdd.addressId = this.memberDataForView.addressId;

      this.memberService.UpdateMemberDetails(this.memberToAdd, fileArray).subscribe((res) => {
        if (res.statusCode == 200) {
          this.notificationService.showSuccess('', 'Member Updated Successfully', 4000);
          this.subService.setRefreshMemberInfo(true);
          let data = {
            memberprofileId: this.memberDataForView.memberprofileId,
            planCode: this.memberToAdd.planCode,
            subscriberID: this.memberDataForView.subscriberID
          }
          this.subService.setRefreshMemberViewAfterEdit(data);
          this.memberToAdd = {};
          this.attachments = [];
          this.addNewAttachment();
          this.addForm.reset();
          this.cancel();
        } else {
          this.notificationService.showError('', 'Member could not be updated. Please try again later.', 4000);
        }
      }, (error) => {
        this.notificationService.showError('', 'Member could not be updated. Please try again later.', 4000);
      })
    }
  }
  addNewAttachment() {
    let newDocument: any = {};
    newDocument.title = this.attachmentTypesData[0].keyItem;
    newDocument.categoryName = this.attachmentTypesData[0].keyItem;
    this.attachments.push(newDocument);
  }
  checkFileValidity(e, attachment) {
    if (!!e.target.files && e.target.files.length > 0) {
      if (checkIfFileTypeValid(e, ['jpeg', 'pdf', 'png', 'jpg'])) {
        attachment.document = e.target.files[0];
        attachment.description = e.target.files[0].name;
      } else {
        e.target.value = null;
        attachment.document = null;
        attachment.description = null;
        this.notificationService.showError('', 'Only jpeg, pdf, png, jpg files are allowed', 4000);
      }
    } else {
      e.target.value = null;
      attachment.document = null;
      attachment.description = null;
    }
  }
  removeAttachment(index) {
    this.attachments.splice(index, 1);
  }
  preview(attachment) {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      let modalData = {
        url: e.target.result,
        messageToDisplay: 'Preview File',
        messageToDisplayOnButton: 'Close'
      }
      this.dialog.open(FilePreviewDialogComponent, {
        data: modalData, width: '50vw',
        height: '90vh'
      });
    };
    reader.readAsDataURL(attachment.document);
  }
  cancel() {
    let tabName = '';
    if (this.mode == 'add') {
      tabName = 'Add New Member';
    }
    if (this.mode == 'edit') {
      tabName = 'Edit Member - ' + this.memberService.getTabNameForViewAndEditMember(this.memberDataForView);
    }
    if (this.mode == 'view') {
      tabName = 'View Member - ' + this.memberService.getTabNameForViewAndEditMember(this.memberDataForView);
    }
    this.subService.setCloseTabRefresh(tabName);
  }
  getSmallerDate(date) {
    const date1 = new Date(date).getTime();
    const date2 = this.today.getTime();
    if (date1 > date2) {
      return this.today;
    } else if (date2 > date1) {
      return date;
    } else {
      return this.today;
    }
  }
  dateValidatorForDates() {
    this.addForm.controls['personEffectiveDate'].valueChanges.pipe(distinctUntilChanged()).subscribe((personEffectiveDate) => {
      if (!!personEffectiveDate) {
        if (!!this.addForm.controls.addressEffectiveDate.value) {
          if (new Date(personEffectiveDate).getTime() > (new Date(this.addForm.controls.addressEffectiveDate.value).getTime())) {
            this.addForm.controls.addressEffectiveDate.setErrors({ invalidEffectiveDate: true });
          } else {
            this.addForm.controls.addressEffectiveDate.setErrors(null);
          }
        }
        if (!!this.addForm.controls.planEffectiveDate.value) {
          if (new Date(personEffectiveDate).getTime() > (new Date(this.addForm.controls.planEffectiveDate.value).getTime())) {
            this.addForm.controls.planEffectiveDate.setErrors({ invalidEffectiveDate: true });
          } else {
            this.addForm.controls.planEffectiveDate.setErrors(null);
          }
        }
        if (!!this.addForm.controls.pcpEffectiveDate.value) {
          if (new Date(personEffectiveDate).getTime() > (new Date(this.addForm.controls.pcpEffectiveDate.value).getTime())) {
            this.addForm.controls.pcpEffectiveDate.setErrors({ invalidEffectiveDate: true });
          } else {
            this.addForm.controls.pcpEffectiveDate.setErrors(null);
          }
        }
        if (!!this.addForm.controls.dateOfBirth.value) {
          if (new Date(personEffectiveDate).getTime() < (new Date(this.addForm.controls.dateOfBirth.value).getTime())) {
            this.addForm.controls.personEffectiveDate.setErrors({ invalidMemberDate: true });
          } else {
            this.addForm.controls.personEffectiveDate.setErrors(null);
          }
        } else {
          this.addForm.controls.personEffectiveDate.setErrors(null);
        }
      } else {
        if (!!this.addForm.controls.addressEffectiveDate.value) {
          this.addForm.controls.addressEffectiveDate.setErrors(null);
        }
        if (!!this.addForm.controls.planEffectiveDate.value) {
          this.addForm.controls.planEffectiveDate.setErrors(null);
        }
        if (!!this.addForm.controls.pcpEffectiveDate.value) {
          this.addForm.controls.pcpEffectiveDate.setErrors(null);
        }
      }
    })
    this.addForm.controls['addressEffectiveDate'].valueChanges.pipe(distinctUntilChanged()).subscribe((addressEffectiveDate) => {
      if (!!addressEffectiveDate) {
        if (!!this.addForm.controls.personEffectiveDate.value) {
          if (new Date(addressEffectiveDate).getTime() < (new Date(this.addForm.controls.personEffectiveDate.value).getTime())) {
            this.addForm.controls.addressEffectiveDate.setErrors({ invalidEffectiveDate: true });
          } else {
            this.addForm.controls.addressEffectiveDate.setErrors(null);
          }
        } else {
          this.addForm.controls.addressEffectiveDate.setErrors(null);
        }
      }
    })
    this.addForm.controls['planEffectiveDate'].valueChanges.pipe(distinctUntilChanged()).subscribe((planEffectiveDate) => {
      if (!!planEffectiveDate) {
        if (!!this.addForm.controls.personEffectiveDate.value) {
          if (new Date(planEffectiveDate).getTime() < (new Date(this.addForm.controls.personEffectiveDate.value).getTime())) {
            this.addForm.controls.planEffectiveDate.setErrors({ invalidEffectiveDate: true });
          } else {
            this.addForm.controls.planEffectiveDate.setErrors(null);
          }
        } else {
          this.addForm.controls.planEffectiveDate.setErrors(null);
        }
      }
    })
    this.addForm.controls['pcpEffectiveDate'].valueChanges.pipe(distinctUntilChanged()).subscribe((pcpEffectiveDate) => {
      if (!!pcpEffectiveDate) {
        if (!!this.addForm.controls.personEffectiveDate.value) {
          if (new Date(pcpEffectiveDate).getTime() < (new Date(this.addForm.controls.personEffectiveDate.value).getTime())) {
            this.addForm.controls.pcpEffectiveDate.setErrors({ invalidEffectiveDate: true });
          } else {
            this.addForm.controls.pcpEffectiveDate.setErrors(null);
          }
        } else {
          this.addForm.controls.pcpEffectiveDate.setErrors(null);
        }
      }
    })
    this.addForm.controls['dateOfBirth'].valueChanges.pipe(distinctUntilChanged()).subscribe((dateOfBirth) => {
      if (!!dateOfBirth) {
        if (!!this.addForm.controls.personEffectiveDate.value) {
          if (new Date(dateOfBirth).getTime() > (new Date(this.addForm.controls.personEffectiveDate.value).getTime())) {
            this.addForm.controls.personEffectiveDate.setErrors({ invalidMemberDate: true });
          } else {
            this.addForm.controls.personEffectiveDate.setErrors(null);
          }
        }
      } else {
        if (!!this.addForm.controls.personEffectiveDate.value) {
          this.addForm.controls.personEffectiveDate.setErrors(null);
        }
      }
    })
  }
  checkIfAddAttachmentDisable() {
    let disable = false;
    for (const attachment of this.attachments) {
      if (!(!!attachment.document)) {
        disable = disable || true;
      }
    }
    return disable;
  }
  openActivityLog() {
    this.showActivityLog = true;
  }
  closeActivityLog() {
    this.showActivityLog = false;
  }
  openEditMember() {
    let nav = new NavTabFromDetails();
    nav.name = Tabs.editMember;
    nav.data = this.memberService.getTabNameForViewAndEditMember(this.memberDataForView)
    this.subService.passValue(nav);
    // this.subService.passValue('Edit Member - ' + this.memberService.getTabNameForViewAndEditMember(this.memberDataForView));
    this.cancel();
    const dataNeeded = {
      data: this.memberDataForView,
      mode: 'edit'
    }
    this.subService.setSelectedMemberInfoForView(dataNeeded);
  }


  getPrivileges() {
    const privielagesDetails = this.globalService.getPrivilegesByRole();
    this.isViewEditBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.MemberHouse_MemberManagement_Claims_EditMember).length > 0 ? true : false;
  }


  zipCodeOnBlur(ev: any) {
    if (ev.target.value.length == 5 || ev.target.value.length == 9) {
      this.getLocationDetailsByAddresLineAndZipcode()
    }
  }

  getLocationDetailsByAddresLineAndZipcode(){
    let request ={
      payToAddressLine1:this.addForm.controls.addressLine1.value.trim(),
      payToZipCode:this.addForm.controls.zipCode.value
    }

    this.providerManagementService.getLocationDetailsByStreetAndZipCode(request).subscribe((resp:any)=>{
           if(resp.statusCode == 200){
                if(!!resp.content){
                   let countryDetails =this.countriesData.find(country=>country.countryCode == "US");

                  if(!!resp.content.payToCity && !!resp.content.payToState ){
                       this.addForm.patchValue({
                        addressLine1:resp.content.payToAddressLine1,
                        city:resp.content.payToCity,
                        zipCode:resp.content.payToZipCode,
                        state:resp.content.payToState,                        
                        country:!!countryDetails?countryDetails.name:null
                       });
                  }

                }
           }
    })
       
  }

  ngOnDestroy(){
    this.destroy$.next(true);
    this.destroy$.unsubscribe();
  }
}
