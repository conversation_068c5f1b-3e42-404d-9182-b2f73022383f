{"ast": null, "code": "import { FormControl, Validators } from '@angular/forms';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../../../../shared/directives/numbers-and-alphabets-with-space.directive\";\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction InsureInfoComponent_input_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 13);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r0.isFormReadOnly)(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, (ctx_r0.f.nm103SubscriberLastOrOrganizationName == null ? null : ctx_r0.f.nm103SubscriberLastOrOrganizationName.invalid) && (ctx_r0.f.nm103SubscriberLastOrOrganizationName == null ? null : ctx_r0.f.nm103SubscriberLastOrOrganizationName.errors)));\n  }\n}\n\nfunction InsureInfoComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1, \" The Last Name field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction InsureInfoComponent_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r2.f.nm103SubscriberLastOrOrganizationName.value && ctx_r2.f.nm103SubscriberLastOrOrganizationName.value.length > 0 ? ctx_r2.f.nm103SubscriberLastOrOrganizationName.value : \"-\");\n  }\n}\n\nfunction InsureInfoComponent_input_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 16);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r3.isFormReadOnly)(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, (ctx_r3.f.nm104SubscriberFirst == null ? null : ctx_r3.f.nm104SubscriberFirst.invalid) && (ctx_r3.f.nm104SubscriberFirst == null ? null : ctx_r3.f.nm104SubscriberFirst.errors)));\n  }\n}\n\nfunction InsureInfoComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1, \" The First Name field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction InsureInfoComponent_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r5.f.nm104SubscriberFirst.value && ctx_r5.f.nm104SubscriberFirst.value.length > 0 ? ctx_r5.f.nm104SubscriberFirst.value : \"-\");\n  }\n}\n\nfunction InsureInfoComponent_input_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 17);\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r6.isFormReadOnly);\n  }\n}\n\nfunction InsureInfoComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r7.f.nm105SubscriberMiddle.value && ctx_r7.f.nm105SubscriberMiddle.value.length > 0 ? ctx_r7.f.nm105SubscriberMiddle.value : \"-\");\n  }\n}\n\nexport let InsureInfoComponent = /*#__PURE__*/(() => {\n  class InsureInfoComponent {\n    constructor(insureform) {\n      this.insureform = insureform;\n      this.isFormReadOnly = true;\n    }\n\n    ngOnInit() {\n      this.patchValue();\n    }\n\n    get f() {\n      return this.insureInfo.controls;\n    }\n\n    createForm() {\n      this.insureInfo = this.insureform.group({\n        nm104SubscriberFirst: new FormControl('', Validators.required),\n        nm105SubscriberMiddle: new FormControl(''),\n        nm103SubscriberLastOrOrganizationName: new FormControl('', Validators.required)\n      });\n      return this.insureInfo;\n    }\n\n    patchValue() {\n      if (this.claimFormData.isAddClaim) {\n        this.insureInfo.patchValue({\n          nm104SubscriberFirst: !this.insuredPerson?.firstName ? '' : this.insuredPerson?.firstName.trim(),\n          nm105SubscriberMiddle: !this.insuredPerson?.middleName ? '' : this.insuredPerson?.middleName.trim(),\n          nm103SubscriberLastOrOrganizationName: !this.insuredPerson?.lastName ? '' : this.insuredPerson?.lastName.trim()\n        }); // submitValidateAllFields.validateDisableControl(this.insureInfo, [\"\"]);\n      } else if (this.claimFormData.isEditClaim || this.claimFormData.isViewClaim) {\n        this.insureInfo.patchValue({\n          nm104SubscriberFirst: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.nm104SubscriberFirst,\n          nm105SubscriberMiddle: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.nm105SubscriberMiddle,\n          nm103SubscriberLastOrOrganizationName: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.nm103SubscriberLastOrOrganizationName\n        });\n        this.insureInfo.disable();\n      }\n\n      if (!!this.insureInfo.controls.nm104SubscriberFirst.value) {\n        this.insureInfo.controls.nm104SubscriberFirst.setValue(this.insureInfo.controls.nm104SubscriberFirst.value.trim().replace(/[^a-z A-Z0-9]/g, ''));\n      }\n\n      if (!!this.insureInfo.controls.nm105SubscriberMiddle.value) {\n        this.insureInfo.controls.nm105SubscriberMiddle.setValue(this.insureInfo.controls.nm105SubscriberMiddle.value.trim().replace(/[^a-z A-Z0-9]/g, ''));\n      }\n\n      if (!!this.insureInfo.controls.nm103SubscriberLastOrOrganizationName.value) {\n        this.insureInfo.controls.nm103SubscriberLastOrOrganizationName.setValue(this.insureInfo.controls.nm103SubscriberLastOrOrganizationName.value.trim().replace(/[^a-z A-Z0-9]/g, ''));\n      }\n    }\n\n    validateForm() {\n      if (this.insureInfo.invalid) {\n        submitValidateAllFields.validateAllFields(this.insureInfo);\n        return false;\n      }\n\n      return true;\n    }\n\n  }\n\n  InsureInfoComponent.ɵfac = function InsureInfoComponent_Factory(t) {\n    return new (t || InsureInfoComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n  };\n\n  InsureInfoComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: InsureInfoComponent,\n    selectors: [[\"app-insure-info\"]],\n    inputs: {\n      insuredPerson: \"insuredPerson\",\n      claimFormData: \"claimFormData\"\n    },\n    decls: 26,\n    vars: 9,\n    consts: [[3, \"formGroup\"], [1, \"row\", \"mt-2\"], [1, \"form-title\"], [1, \"col\"], [\"for\", \"InsuredLastName\", 1, \"create-claims-labels\"], [\"for\", \"InsuredFirstName\", 1, \"create-claims-labels\"], [\"for\", \"InsuredMiddleInitial\", 1, \"create-claims-labels\"], [1, \"row\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm103SubscriberLastOrOrganizationName\", \"id\", \"InsuredLastName\", \"name\", \"InsuredLastName\", \"placeholder\", \"Last Name\", 3, \"readonly\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"form-control\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm104SubscriberFirst\", \"placeholder\", \"First Name\", 3, \"readonly\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm105SubscriberMiddle\", \"placeholder\", \"Middle Name\", 3, \"readonly\", 4, \"ngIf\"], [\"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm103SubscriberLastOrOrganizationName\", \"id\", \"InsuredLastName\", \"name\", \"InsuredLastName\", \"placeholder\", \"Last Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"ngClass\"], [1, \"invalid-feedback\"], [1, \"form-control\"], [\"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm104SubscriberFirst\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"ngClass\"], [\"type\", \"text\", \"numbersAndAlphabetsWithSpace\", \"\", \"formControlName\", \"nm105SubscriberMiddle\", \"placeholder\", \"Middle Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"]],\n    template: function InsureInfoComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"p\", 2);\n        i0.ɵɵtext(3, \"4. Insured\\u2019s Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 1)(5, \"div\", 3)(6, \"label\", 4);\n        i0.ɵɵtext(7, \"Last Name \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 3)(9, \"label\", 5);\n        i0.ɵɵtext(10, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 3)(12, \"label\", 6);\n        i0.ɵɵtext(13, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(14, \"div\", 7)(15, \"div\", 3);\n        i0.ɵɵtemplate(16, InsureInfoComponent_input_16_Template, 1, 4, \"input\", 8);\n        i0.ɵɵtemplate(17, InsureInfoComponent_div_17_Template, 2, 0, \"div\", 9);\n        i0.ɵɵtemplate(18, InsureInfoComponent_span_18_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 3);\n        i0.ɵɵtemplate(20, InsureInfoComponent_input_20_Template, 1, 4, \"input\", 11);\n        i0.ɵɵtemplate(21, InsureInfoComponent_div_21_Template, 2, 0, \"div\", 9);\n        i0.ɵɵtemplate(22, InsureInfoComponent_span_22_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"div\", 3);\n        i0.ɵɵtemplate(24, InsureInfoComponent_input_24_Template, 1, 1, \"input\", 12);\n        i0.ɵɵtemplate(25, InsureInfoComponent_span_25_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.insureInfo);\n        i0.ɵɵadvance(16);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.nm103SubscriberLastOrOrganizationName == null ? null : ctx.f.nm103SubscriberLastOrOrganizationName.invalid) && ctx.f.nm103SubscriberLastOrOrganizationName.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.nm104SubscriberFirst == null ? null : ctx.f.nm104SubscriberFirst.invalid) && ctx.f.nm104SubscriberFirst.errors);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.NumbersAndAlphabetsWithSpaceDirective],\n    styles: [\".create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.search-icon-alignment[_ngcontent-%COMP%]{position:relative}.search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%], .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}input[readonly][_ngcontent-%COMP%]{background-color:#e9ecef;opacity:1;border:1px solid #ced4da}\"]\n  });\n  return InsureInfoComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}