.Form-Header-Row {
    /* position: sticky;
            top: 0px; */
    background-color: rgb(255, 255, 255);
    // height: 50px;
    align-items: center;
    box-shadow: 0px 0px 8px #0000001a;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
}

.bodyMargin {
    margin-left: 10px;
}

.Form-Header-2 {
    font-size: 18px;
    font-family: "Poppins-SemiBold";
    padding: 10px;
    align-items: center;
}

.Form-Header-2-Heading {
    color: #0074bc;
}

.SearchproviderSection {
    background: #fffefe 0% 0% no-repeat padding-box;
    border: 1px solid #ffffff;
    border-radius: 6px;
    opacity: 1;
    margin: 12px 0px;
    padding: 8px 0px;
}

.inner-addon {
    position: relative;
}
.inner-addon .ArrowDown {
    position: absolute;
    padding: 10px;
}

.right-addon .ArrowDown {
    pointer-events: none;
    right: -3%;
}

.ArrowDown {
    color: #c9c9c9;
    cursor: pointer;
    z-index: 100;
    position: relative;
    margin-top: -4px;
}

.HeadingRow1 {
    color: #4a4949;
    font-size: 12px;
    font-family: "Poppins";
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}
:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}
.MainFlexRow {
    flex-wrap: wrap;
    margin-top: 10px;
    padding: 5px;
}

::ng-deep .p-datatable .p-datatable-thead > tr > th {
    background-color: #ffffff;
}

/* basic setting for all input type */

input[type="text"],
input[type="number"] {
    padding-left: 5px;
    border-radius: 6px;
    border: 1px solid #cccccc !important;
    height: 30px;
    outline: none;
    min-width: 40px;
    width: 90%;
    font-size: 10px;
    color: #727272;
    font-family: "Poppins-SemiBold";
}

.providerSpecialty {
    width: 100% !important;
}

// styling for input when on focus
input[type="text"]:focus,
input[type="number"]:focus {
    border: 1px solid #61abd4 !important;
}

.btn {
    height: 35px;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
    margin-bottom: 5%;
}

.reset {
    color: #0074bc;
    background-color: white;
    border: 1px solid #0074bc;
}

.reset:hover {
    color: white;
    border-color: #0074bc;
    background-color: #0074bc;
}

.MarginRight {
    margin-right: 10px;
}

.marginBottom {
    margin-bottom: 0px;
}

.MarginLeft {
    margin-left: 8px;
}

::ng-deep .mat-option {
    height: auto !important;
    padding: 6px 0px 6px 6px !important;
    overflow: hidden !important;
    font-size: 10px !important;
    font-family: "Poppins" !important;
}

::ng-deep .mat-option-text {
    overflow: initial !important;
}

::ng-deep .mat-select-panel mat-option.mat-option {
    margin: 1rem 0;
    overflow: visible;
    line-height: initial;
    word-wrap: break-word;
    white-space: pre-wrap;
}

::ng-deep .mat-option-text.mat-option-text {
    white-space: normal;
    line-height: normal;
}

.custom-select {
    width: 100px;
    border-radius: 6px;
    border: 1px solid #cccccc !important;
    color: #757575;
    outline: none;
    font-size: 12px;
    margin-right: 28px;
    height: 29px;
}

.Form-Body {
    box-shadow: 0px 0px 8px #0000001a;
    padding: 5px;
    border: 1px solid #cccccc;
    border-radius: 8px;
    opacity: 1;
    background-color: white;
    margin-left: 0px;
}

.Width75 {
    width: 75% !important;
}

.TableTd {
    text-overflow: ellipsis;
    color: #272d3b;
    font-size: 12px;
    font-family: "Poppins";
}

.ParentOfObject {
    cursor: pointer;
    position: relative;
    z-index: 1 !important;
}

object {
    position: relative;
    z-index: -1 !important;
}

.TableTr:hover {
    background-color: #e3f2fd;
}

.ThBtn {
    font-family: "Poppins-Bold";
    width: 9%;
    color: #5d5d5d !important;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-down:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-up-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .p-checkbox-box {
    border-radius: 50% !important;
}

::ng-deep .p-paginator {
    justify-content: flex-end !important;
}

::ng-deep .p-datatable .p-sortable-column .p-sortable-column-icon {
    margin-left: 0rem;
}

::ng-deep .p-datatable .p-datatable-thead > tr > th {
    padding: 6px;
}

 

 

.paddingRightIcon{
    padding-right: 22px;
}