// Main div styling
.MainFlexRow {
    flex-wrap: wrap;
    margin-top: 10px;
    padding: 5px
}


/* applies on full form */

.Full-Form {
    margin-left: 5px;
    font-weight: bolder;
    padding: 5px;
    /* box-shadow: 0px 0px 8px #0000001A; */
    /* border-radius: 8px; */
    opacity: 1;
    background: #F4F7FC 0% 0% no-repeat padding-box;
}


/* .Form-Body-Col-31,.Form-Body-Col-2,.Form-Body-Col-1{
    min-width: 514px;
} */


/* for setting first heading box*/

.Form-Header-Row {
    /* position: sticky;
            top: 0px; */
    background-color: white;
    height: 50px;
    align-items: center;
    box-shadow: 0px 0px 8px #0000001A;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    padding-left: 0px;
}


/* for setting heading of first header */

.Form-Header {
    font-size: 23px;
    justify-content: start;
    color: #272D3B;
    font-family: 'Poppins-SemiBold';
}


/* all header buttons design */

.Header-Buttons {
    text-align: end;
}


/* common setup for all buttons */

.btn{
    border: none;
    color: white;
    box-shadow: 0px 0px 8px #0000001A;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074BC;
    font-family: 'Poppins-SemiBold';
    font-size: 14px;
    cursor: pointer;
}

.btn1{
    border: 1px solid #0074BC!important;
    color: #0074BC!important;
    background-color: white!important;
}

.InnerRowInput{
    height: 25px!important;
    width: 77%!important;
}
.InnerRowInputHavingBtn{
    width: 99%!important;
}

// .btn-primary {
//     background-color: #0074BC;
//     color: white;
// }


/* text inside button setting in second header */

.Btn-Text {
    font-family: 'Poppins-SemiBold';
    justify-content: center;
    font-size: 12px;
    text-align: center;
    float: right;
}

.Medium {
    font-family: 'Poppins-Medium'!important;
}

.Regular {
    font-family: 'Poppins'!important;
}

.Bold {
    font-family: 'Poppins-Bold'!important;
}

.MontserratBold {
    font-family: 'Montserrat-Bold'!important;
}


/* basic form background setting */

.Form-Body {
    box-shadow: 0px 0px 8px #0000001A;
    background-color: white;
    padding: 5px;
}

// basic styling for all tds in table
.ToggleRow>td ,.TableTr>td {
    color: #272D3B;
    font-size: 12px;
    font-family: 'Poppins-SemiBold';
    padding: 1rem 0rem!important;
}

// background effect on hover on table row
.TableTr:hover {
    background-color: #E3F2FD;;
}

// style for checkbox and paginator of primeng
::ng-deep .p-checkbox-box {
    border: 2px solid #AAAAAA!important;
    border-radius: 50%!important;
}

::ng-deep .p-paginator {
    justify-content: flex-end!important;
}


/* basic setting for all input type */
input{
    padding-left: 5px;
}
input[type="text"],select {
    border-radius: 6px;
    border: 1px solid #CCCCCC!important;
    height: 30px;
    outline: none;
    min-width: 40px;
    width: 90%;
    font-size: 10px;
    font-family: 'Montserrat-Bold';
    margin-left: 5px;
    padding-left: 5px;
    background-color: white!important;
    box-shadow: none!important;
}

/* Change the white to any color */
input:-webkit-autofill,
input:-webkit-autofill:hover, 
input:-webkit-autofill:focus, 
input:-webkit-autofill:active
{
 -webkit-box-shadow: 0 0 0 30px white inset !important;
 box-shadow: 0 0 0 30px white inset !important;
}

// styling for input when on focus
input[type="text"]:focus,select:focus {
    border: 1px solid #61abd4!important;
}

// styling for table headers
th {
    padding-right: 0px!important;
    padding-left: 0px!important;
    background-color: white!important;
}

// styling for button in table header
.CustomHeading {
    text-align: center!important;
    font-size: 12px;
    white-space: unset!important;
    font-family: 'Poppins-SemiBold';
}
.SemiBold{
    font-family: 'Poppins-SemiBold';
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC"!important;
    font-family: "FontAwesome"!important;
  }
  :host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon{
      color: initial!important;
  }

  ::ng-deep .p-datatable .p-sortable-column .p-sortable-column-icon{
    margin-left: 0rem;
}

.marginRight{
    margin-right: 3%;
}

.RequiredWidth{
    width: 9.5%;
}

.ToggleRow{
    line-height: 24px;
    background-color: #f9f8f8!important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

.InnerRowToggle{
    
    text-align: center!important;
}
.InnerRowToggle > span{
    cursor: pointer;
    font-family: 'Poppins'!important;
    font-size: 24px!important;
}

 

 

.ServiceLineDisplayInputs{
    width: 1%!important;
}

.DOSDateRangePicker .e-input-group {
    margin-top: -10px;
}

.DOSInputBox {
    width: 87%;
    height: 31px;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #D5D7E3;
    border-radius: 8px;
    opacity: 1;
}

.e-input-group:not(.e-float-icon-left):not(.e-float-input)::before, .e-input-group:not(.e-float-icon-left):not(.e-float-input)::after, .e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before, .e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after, .e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::before, .e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::after, .e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before, .e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after{
    background: transparent!important;
}

.e-input-group:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left), .e-input-group.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error) .e-input-in-wrap, .e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left), .e-input-group.e-control-wrapper.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error) .e-input-in-wrap, .e-float-input.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error) .e-input-in-wrap, .e-float-input.e-control-wrapper.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error) .e-input-in-wrap{
    border: none!important;
    font-family: "Poppins-Medium"!important;
    padding-left: 5px!important;
    color: #272D3B!important;
}

.e-control input.e-input, .e-control .e-input-group input, .e-control .e-input-group input.e-input, .e-control .e-input-group.e-control-wrapper input, .e-control .e-input-group.e-control-wrapper input.e-input, .e-control .e-float-input input, .e-control .e-float-input.e-control-wrapper input, .e-control.e-input-group input, .e-control.e-input-group input.e-input, .e-control.e-input-group.e-control-wrapper input, .e-control.e-input-group.e-control-wrapper input.e-input, .e-control.e-float-input input, .e-control.e-float-input.e-control-wrapper input
{
    font-family: 'Poppins-SemiBold';
    color: #272D3B;
}


input.ng-invalid.ng-touched {
    border: 1px solid red!important;
}

select.ng-invalid.ng-touched {
    border: 1px solid red!important;
}

ejs-daterangepicker{
    border-radius: 8px;
    padding: 9px 0px 7px 0px;
    border: none;
}

ejs-daterangepicker.ng-invalid.ng-touched {
    border: 1px solid red!important;
}

.inner-addon {
    position: relative;
}


/* style icon */

.inner-addon .fa {
    position: absolute;
    padding: 10px;
    pointer-events: none;
}


/* align icon */

.left-addon .fa {
    left: 0px;
}

.right-addon .fa {
    right: 0;
}

::ng-deep .mat-option {
    height: auto!important;
    padding: 6px 0px 6px 6px!important;
    overflow: hidden!important;
    font-size: 10px!important;
    font-family: 'Poppins'!important;
}

::ng-deep .mat-option-text {
    overflow: initial!important;
}

::ng-deep .mat-select-panel mat-option.mat-option {
    margin: 1rem 0;
    overflow: visible;
    line-height: initial;
    word-wrap: break-word;
    white-space: pre-wrap;
}

 ::ng-deep .mat-option-text.mat-option-text {
    white-space: normal;
    line-height: normal;
}

::ng-deep .e-daterangepicker.e-popup{
    box-shadow: 0px 0px 8px #0000001A!important;
}
:ng-deep .e-daterangepicker.e-popup, .e-bigger.e-small .e-daterangepicker.e-popup{
    box-shadow: 0px 0px 8px #0000001A!important;
border-radius: 8px!important;
}

::ng-deep .e-daterangepicker.e-popup.e-preset-wrapper, .e-bigger.e-small .e-daterangepicker.e-popup.e-preset-wrapper{
    box-shadow: 0px 0px 8px #0000001A!important;
        border-radius: 8px!important;
}

::ng-deep .e-daterangepicker.e-popup .e-footer, .e-bigger.e-small .e-daterangepicker.e-popup .e-footer{
    border-radius: 8px 0;
}

// style for checkbox and paginator of primeng
::ng-deep .p-checkbox .p-checkbox-box.p-highlight{
    border-color: #0074BC!important;
    background: #0074BC!important;

}

::ng-deep.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: #0074BC!important;
}

:host ::ng-deep .p-multiselect {
    width: 89%;
    border-radius: 6px;
    font-family: "Montserrat-Bold";
    font-size: 10px;
}

:host ::ng-deep .multiselect-custom-virtual-scroll .p-multiselect {
    min-width: 20rem;
}

:host ::ng-deep .multiselect-custom {
    .p-multiselect-label {
        padding-top: .25rem;
        padding-bottom: .25rem;
    }

    .country-item-value {
        padding: .25rem .5rem;
        border-radius: 3px;
        display: inline-flex;
        margin-right: .5rem;
        background-color: var(--primary-color);
        color: var(--primary-color-text);

        img.flag {
            width: 17px;
        }
    }

    .country-placeholder {
        padding: 0.25rem;
    }
}

:host ::ng-deep .p-multiselect-panel .p-multiselect-items {
    font-family: 'Poppins';
    font-size: 12px;
}

.MultiSelectRow{
    height: 57px;
    padding-top: 8%;
    border-bottom: 1px solid #dddddd;
    min-width: 1065%;
}

.MultiSelectLabel{
    margin:0px 0.7% 0 2%;
    color: black;
    font-size: 12px;
    font-family: "Poppins-SemiBold";
    padding: 0.4rem 0rem 1rem 0rem !important;
}