{"ast": null, "code": "import { ElementRef } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nexport let DisplayUpperSaveLowerDirective = /*#__PURE__*/(() => {\n  class DisplayUpperSaveLowerDirective {\n    constructor(el, control) {\n      this.el = el;\n      this.control = control;\n    }\n\n    onInput() {\n      const upper = this.el.nativeElement.value.toUpperCase();\n      this.el.nativeElement.value = upper;\n    }\n\n    onBlur() {\n      const lower = this.el.nativeElement.value.toLowerCase();\n\n      if (this.control && this.control.control) {\n        this.control.control.setValue(lower); // Update model value in lowercase\n      }\n    }\n\n  }\n\n  DisplayUpperSaveLowerDirective.ɵfac = function DisplayUpperSaveLowerDirective_Factory(t) {\n    return new (t || DisplayUpperSaveLowerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.NgControl));\n  };\n\n  DisplayUpperSaveLowerDirective.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n    type: DisplayUpperSaveLowerDirective,\n    selectors: [[\"\", \"appDisplayUpperSaveLower\", \"\"]],\n    hostBindings: function DisplayUpperSaveLowerDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function DisplayUpperSaveLowerDirective_input_HostBindingHandler($event) {\n          return ctx.onInput($event);\n        })(\"blur\", function DisplayUpperSaveLowerDirective_blur_HostBindingHandler() {\n          return ctx.onBlur();\n        });\n      }\n    }\n  });\n  return DisplayUpperSaveLowerDirective;\n})();", "map": null, "metadata": {}, "sourceType": "module"}