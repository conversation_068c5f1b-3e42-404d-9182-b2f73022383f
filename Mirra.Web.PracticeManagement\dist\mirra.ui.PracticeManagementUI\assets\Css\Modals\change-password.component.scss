.ErrorText {
    height: 50px;
    font-size: 19px;
    font-family: "Poppins-SemiBold";
    /* vertical-align: middle; */
    text-align: center;
}

.btn {
    border-color: #0074bc;
    box-shadow: 0px 0px 8px #0000001a;
    height: 35px;
    background-color: #0074bc;
    color: white;
    border-radius: 8px;
    font-size: 14px;
    font-family: "Poppins-SemiBold";
    width: max-content;
}

.ClearBtn {
    border-color: #0074bc !important;
    background-color: white;
    margin-right: 11%;
    color: #0074bc;
}

input {
    padding: 0px 35px 0px 5px;
    background: #ffffff 0% 0% no-repeat padding-box;
    border: 1px solid #cccccc;
    border-radius: 8px;
    opacity: 1;
    outline: none;
    width: 40%;
    height: 34px;
    font-family: "Poppins";
    font-size: 14px;
}

.InputCol {
    display: block;
    text-align: center;
}

.ValidationMessage {
    font-family: "Poppins-Medium";
    font-size: 14px;
    color: #646464;
}
.CrossIcon {
    color: #f0142f;
    font-size: 14px;
}

.TickIcon {
    font-size: 14px;
    color: #21d59b;
}

.MarginBtnRow {
    margin: 3% 0px 2% 7%;
}

.BottomHeading {
    margin-right: 2%;
    font-size: 35px;
}

.TriangleIcon {
    color: #f08a14;
}
.WidthOfMessageRow {
    width: 265px !important;
    margin-top: 3% !important;
}

.WhiteColor {
    color: white;
}

.MessageCol {
    display: inline;
    text-align: center;
    font-family: "Poppins";
}

.Modal-Close-Image {
    cursor: pointer;
}
.MarginForInput {
    margin-top: -8px !important;
}

.inner-addon {
    position: relative;
}

/* style icon */

.inner-addon .EyeIcon {
    position: absolute;
    padding: 10px;
}

/* align icon */

.left-addon .EyeIcon {
    left: 0px;
}

.right-addon .EyeIcon {
    right: 8px;
    bottom: -14px;
}

.EyeIcon {
    cursor: pointer;
    z-index: 100;
    position: relative;
}

object {
    z-index: -1;
    position: relative;
}

.PositiveZIndex {
    z-index: 1 !important;
}
