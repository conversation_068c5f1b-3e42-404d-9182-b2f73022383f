import { Component } from '@angular/core';
import { IFilterAngularComp } from 'ag-grid-angular';
import { IDoesFilterPassParams, IFilterParams } from 'ag-grid-community';
import { COMMON_METHODS } from './common-static';

@Component({
  selector: 'custom-date-filter',
  template: `
    <div>
      <input 
        type="date" 
        [(ngModel)]="dateValue" 
        (ngModelChange)="onDateChange($event)"
        class="form-control"
        [class.is-invalid]="hasError"
      />
      <div *ngIf="hasError" class="invalid-feedback d-block">
        Please enter a valid date (4-digit year required)
      </div>
    </div>
  `,
  styles: [`
    .invalid-feedback {
      font-size: 12px;
      color: #dc3545;
      margin-top: 4px;
    }
    .is-invalid {
      border-color: #dc3545;
    }
  `]
})
export class CustomDateFilterComponent implements IFilterAngularComp {
  private params: IFilterParams;
  public dateValue: string = '';
  public hasError: boolean = false;
  private validDate: Date | null = null;

  agInit(params: IFilterParams): void {
    this.params = params;
  }

  onDateChange(value: string): void {
    this.dateValue = value;
    this.validDate = COMMON_METHODS.validateDateForFilter(value);
    this.hasError = value && !this.validDate;
    
    // Only trigger filter change if date is valid or empty
    if (!value || this.validDate) {
      this.params.filterChangedCallback();
    }
  }

  isFilterActive(): boolean {
    return !!this.validDate;
  }

  doesFilterPass(params: IDoesFilterPassParams): boolean {
    if (!this.validDate) return true;
    
    const cellValue = COMMON_METHODS.validateDateForFilter(params.data.dosFrom);
    if (!cellValue) return false;
    
    return cellValue.toDateString() === this.validDate.toDateString();
  }

  getModel(): any {
    return this.validDate ? { dateFrom: this.validDate } : null;
  }

  setModel(model: any): void {
    if (model && model.dateFrom) {
      this.validDate = new Date(model.dateFrom);
      this.dateValue = this.validDate.toISOString().split('T')[0];
      this.hasError = false;
    } else {
      this.dateValue = '';
      this.validDate = null;
      this.hasError = false;
    }
  }
}