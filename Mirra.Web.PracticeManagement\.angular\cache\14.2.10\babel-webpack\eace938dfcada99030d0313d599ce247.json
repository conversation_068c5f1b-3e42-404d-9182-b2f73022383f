{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { MatDialog } from '@angular/material/dialog';\nimport { map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/material/dialog\";\nexport let GetClaimInfoByIdService = /*#__PURE__*/(() => {\n  class GetClaimInfoByIdService {\n    constructor(httpClient, dialog) {\n      this.httpClient = httpClient;\n      this.dialog = dialog;\n      this.claimForm837P = {};\n      this.ClaimFormType = '';\n      this.headerData = {};\n      this.appUrl = environment.apiUrl;\n    }\n\n    SetClaimIdForPreview(claimid, claimStatus) {\n      this.claimId = claimid;\n      this.ClaimStatus = claimStatus;\n    }\n\n    fetchchGetClaimInfoByIdService() {\n      this.headerData = {\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email'))\n      };\n      const httpOptions = {\n        params: {\n          'claimId': this.claimId\n        },\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      };\n      return this.httpClient.get(`${environment.apiUrl}/Claim/GetClaimInfoById`, httpOptions).pipe(map(response => {\n        if (response.content == null) {\n          return null;\n        } else {\n          this.claimForm837P = response.content;\n          return this.claimForm837P;\n        }\n      }));\n    }\n\n    saveNotes(objClaimForm837P) {\n      this.headerData = {\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email'))\n      };\n      const httpOptions = {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      };\n      return this.httpClient.post(`${environment.apiUrl}/Claim/saveNotes`, JSON.stringify(objClaimForm837P), {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      }).pipe(map(response => {\n        if (response.content == null) {\n          return null;\n        } else {\n          this.claimForm837P = response.content;\n          return this.claimForm837P;\n        }\n      }));\n    }\n\n    GetClaimRejectionReasonDescription() {\n      this.headerData = {\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email'))\n      };\n      const httpOptions = {\n        params: {\n          'claimId': this.claimForm837P.patientCtrlNo\n        },\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      };\n      return this.httpClient.get(`${environment.apiUrl}/Claim/GetClaimRejectionReasonDescription`, httpOptions).pipe(map(response => {\n        return response;\n      }));\n    }\n\n  }\n\n  GetClaimInfoByIdService.ɵfac = function GetClaimInfoByIdService_Factory(t) {\n    return new (t || GetClaimInfoByIdService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.MatDialog));\n  };\n\n  GetClaimInfoByIdService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: GetClaimInfoByIdService,\n    factory: GetClaimInfoByIdService.ɵfac,\n    providedIn: 'root'\n  });\n  return GetClaimInfoByIdService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}