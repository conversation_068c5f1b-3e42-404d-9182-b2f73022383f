{"ast": null, "code": "export var UserPermissionEnum = /*#__PURE__*/(() => {\n  (function (UserPermissionEnum) {\n    UserPermissionEnum[UserPermissionEnum[\"AddProvider\"] = 1] = \"AddProvider\";\n    UserPermissionEnum[UserPermissionEnum[\"SearchProvider\"] = 2] = \"SearchProvider\";\n    UserPermissionEnum[UserPermissionEnum[\"CreateClaim\"] = 3] = \"CreateClaim\";\n    UserPermissionEnum[UserPermissionEnum[\"AddMember\"] = 4] = \"AddMember\";\n    UserPermissionEnum[UserPermissionEnum[\"SearchMember\"] = 5] = \"SearchMember\";\n    UserPermissionEnum[UserPermissionEnum[\"MemberHouse_MemberManagement_Claims_SearchMember\"] = 6] = \"MemberHouse_MemberManagement_Claims_SearchMember\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_AcceptAClaim\"] = 7] = \"CLAIMS_BillingManagement_AcceptAClaim\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ActivateaClaim\"] = 8] = \"CLAIMS_BillingManagement_ActivateaClaim\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_AddandUpdateClearingHouse\"] = 9] = \"CLAIMS_BillingManagement_AddandUpdateClearingHouse\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_AddEditClearingHouse\"] = 10] = \"CLAIMS_BillingManagement_AddEditClearingHouse\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_AddEditProviderGroup\"] = 11] = \"CLAIMS_BillingManagement_AddEditProviderGroup\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_DeactivateaClaim\"] = 12] = \"CLAIMS_BillingManagement_DeactivateaClaim\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_DownloadEDIFile\"] = 13] = \"CLAIMS_BillingManagement_DownloadEDIFile\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ExportBillerProductivityReporttoExcel\"] = 14] = \"CLAIMS_BillingManagement_ExportBillerProductivityReporttoExcel\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ExportEOBreportToExcel\"] = 15] = \"CLAIMS_BillingManagement_ExportEOBreportToExcel\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_Generate835EDIfile\"] = 16] = \"CLAIMS_BillingManagement_Generate835EDIfile\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_GenerateCustomReport\"] = 17] = \"CLAIMS_BillingManagement_GenerateCustomReport\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_GenerateEDI837File\"] = 18] = \"CLAIMS_BillingManagement_GenerateEDI837File\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_GetClaimList\"] = 19] = \"CLAIMS_BillingManagement_GetClaimList\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_GetFileListIrespectiveOfFileType\"] = 20] = \"CLAIMS_BillingManagement_GetFileListIrespectiveOfFileType\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_MoveAllOpenToAccepted\"] = 21] = \"CLAIMS_BillingManagement_MoveAllOpenToAccepted\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_MoveSelectedOpenToAccepted\"] = 22] = \"CLAIMS_BillingManagement_MoveSelectedOpenToAccepted\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_OnHoldAClaim\"] = 23] = \"CLAIMS_BillingManagement_OnHoldAClaim\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ReadBillerproductivityReport\"] = 24] = \"CLAIMS_BillingManagement_ReadBillerproductivityReport\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ReadlistofEOBfile\"] = 25] = \"CLAIMS_BillingManagement_ReadlistofEOBfile\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ReadProviderproductivityReport\"] = 26] = \"CLAIMS_BillingManagement_ReadProviderproductivityReport\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ReadSpecific837fileClaims\"] = 27] = \"CLAIMS_BillingManagement_ReadSpecific837fileClaims\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_RejectAClaim\"] = 28] = \"CLAIMS_BillingManagement_RejectAClaim\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_Resubmission\"] = 29] = \"CLAIMS_BillingManagement_Resubmission\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_UpdateaClaim\"] = 30] = \"CLAIMS_BillingManagement_UpdateaClaim\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_UploadFiles\"] = 31] = \"CLAIMS_BillingManagement_UploadFiles\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ViewaClaim\"] = 32] = \"CLAIMS_BillingManagement_ViewaClaim\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ViewAdmin\"] = 33] = \"CLAIMS_BillingManagement_ViewAdmin\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ViewClearingHouse\"] = 34] = \"CLAIMS_BillingManagement_ViewClearingHouse\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ViewReportsLibrary\"] = 35] = \"CLAIMS_BillingManagement_ViewReportsLibrary\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_ViewTrackingDashBoard\"] = 36] = \"CLAIMS_BillingManagement_ViewTrackingDashBoard\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_AddRole\"] = 37] = \"UserManagement_AddRole\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_AddUser\"] = 38] = \"UserManagement_AddUser\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_EditRole\"] = 39] = \"UserManagement_EditRole\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_EditUser\"] = 40] = \"UserManagement_EditUser\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_ViewAllRoles\"] = 41] = \"UserManagement_ViewAllRoles\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_ViewAllUsers\"] = 42] = \"UserManagement_ViewAllUsers\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_ViewRole\"] = 43] = \"UserManagement_ViewRole\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_ViewUser\"] = 44] = \"UserManagement_ViewUser\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_ViewAllUsersIPAMapping\"] = 45] = \"UserManagement_ViewAllUsersIPAMapping\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_AddUserIPA\"] = 46] = \"UserManagement_AddUserIPA\";\n    UserPermissionEnum[UserPermissionEnum[\"UserManagement_EditUserIPA\"] = 47] = \"UserManagement_EditUserIPA\";\n    UserPermissionEnum[UserPermissionEnum[\"CLAIMS_BillingManagement_MoveAllOpenToAccepted_Biller\"] = 48] = \"CLAIMS_BillingManagement_MoveAllOpenToAccepted_Biller\";\n    UserPermissionEnum[UserPermissionEnum[\"ProviderVilla_ProviderBridge_EditProvider\"] = 49] = \"ProviderVilla_ProviderBridge_EditProvider\";\n    UserPermissionEnum[UserPermissionEnum[\"ProviderVilla_ProviderBridge_EditFacility\"] = 50] = \"ProviderVilla_ProviderBridge_EditFacility\";\n    UserPermissionEnum[UserPermissionEnum[\"MULTIPLE_Claims\"] = 51] = \"MULTIPLE_Claims\";\n  })(UserPermissionEnum || (UserPermissionEnum = {}));\n\n  return UserPermissionEnum;\n})();", "map": null, "metadata": {}, "sourceType": "module"}