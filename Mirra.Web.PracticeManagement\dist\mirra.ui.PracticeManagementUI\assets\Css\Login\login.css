/* styling for main login container */
.container {
    top: 0px;
    left: 0px;
    height: 96%;
    width: 90%;
    opacity: 1;
}

/* Change the white to any color */
input:-webkit-autofill,
input:-webkit-autofill:hover, 
input:-webkit-autofill:focus, 
input:-webkit-autofill:active
{
 -webkit-box-shadow: 0 0 0 30px white inset !important;
 box-shadow: 0 0 0 30px white inset !important;
}

/* styles for left container having image and text */
.leftContainer {
    padding-top: 8%;
    margin-left: 12%;
    height: 100%;
    width: 100%;
}

/* styling for heading=Welcome to */
.welcomeTxt {
    color: #0074BC;
    font-size: 1.8rem;
}

/* styles for heading Billing Gateway 2.0 */
.headingOne {
    color: #5A607F;
    font-size: 2.6rem;
    font-weight: bold;
}

/* designing for introduction image */
.introImage {
    margin-top: 7%;
    width: 500px;
    height: 330px;
    padding-top: 3%;
    background: transparent url('src/assets/Images/Login/introImage.svg');
    background-size: 100% 100%;
    /* opacity: 0.7; */
}

/* styling for right container having input box */
.rightContainer {
    padding-top: 5%;
    margin-left: 12%;
    display: inline-block;
    height: 65%;
    width: 70%;
    align-items: center;
}

/* styling for Main Access Healthcare logo */
.accessImage {
    width: 75%;
    height: 130px;
    background: transparent url('src/assets/Images/Login/<EMAIL>');
    background-size: 100% 100%;
    padding: 5%;
    margin-left: 2%;
    display: inline-block;
}

/* styling for login form */
.loginForm {
    padding-left: 12%;
    padding-top: 5%;
    padding-right: 8%;
    margin-top: 8%;
    background-color: white;
    box-shadow: 0px 4px 40px #0000001A;
    border-radius: 10px;
    width: 340px;
    height: 360px;
}

/* styling for login label */
.loginLabel {
    color: #0074BC;
    font-size: 1.2rem;
    font-weight: bold;
    margin-top: 12%;
}

/* styling for Mirra Healthcare logo */
.mirraLogo {
    width: 80%;
    height: 90%;
    background: transparent url('src/assets/Images/Login/mirralogo.svg');
    background-size: 100% 100%;
    opacity: 1;
}

.loginfirstRow {
    height: 22%;
    width: 100%;
}

/* styling for email label */
.emailLabel {
    color: #8A9EAD;
    margin-top: 2%;
    font-size: 0.2;
}

/* styling for email input */
.emailInput {
    font-family:'Poppins';
    width:90%;
    color: #1D2226;
    height: 5%;
    border-style: none;
    outline: none;

}

/* styling for email input while focused(active) */
.emailInput :focus {
    outline: none!important;
    border: none!important;
}

/* styling for email bottom Border */
.emailBorder {
    width: 90%;
    text-align: center;
    height: 0.8%;
    border-bottom-color: #1B1B1B;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    opacity: 0.1;
}

/* styling for password eye Button */
.passwordButton {
    border-style: none;
    background-color: transparent;
}

/* styling for password input */
.passwordInput {
    font-family: 'Poppins';
    color: #1D2226;
    height: 100%;
    width: 100%;
    border-style: none;
    outline: none;
}

/* styling for Forget Password text above Login button */
.forgetPasswordTxt {
    margin-top: 3%;
    color: #0074BC;
    text-align:right;
}

/* styling for login button container*/
.loginButtonContainer {
    padding: 2%;
    height: 16%;
    width: 90%;
    text-align: center;
    margin-left: 7%;
}

/* styling for login button */
.loginButton {
    background-color: #0074bc;
    height: 45px;
    width: 100%;
    border-style: none;
    border-radius: 5px;
    vertical-align: middle;
    padding-top: 8px;
}

/* styling for login text */
.loginText {
    color: white;
    vertical-align: middle;
    text-align: right;
     vertical-align: middle;
     margin-top: 4px!important;
     margin-left: 6%;
}

/* for making text Poppins Regular, Bold,.SemiBold,Meduim */
.Bold {
    font-family: "Poppins-Bold", Arial, Helvetica, sans-serif;
}

.Regular {
    font-family: 'Poppins';
}

.Medium {
    font-family: 'Poppins-Medium';
}

.SemiBold {
    font-family: 'Poppins-SemiBold';
}

/* styling for anchor attribute */
a{
    text-decoration: none;
    color: #0074BC;
}

/* styling for form validation message */
.warn{
    height: 17px;font-size: 14px;color: red;
    font-family: 'Poppins';
}

/* for applying width 50px to welocome text box */
.Height50{
    height: 50px;
}

/* for giving red color to form validation message */
.RedColor{
    color: red;
}

/* styling for spinner in Login button appear on loading */
.SpinnerStyling{
    margin-left: 0px!important;margin-top: 4px!important;
    width:40px!important;
}

.TextRight{
    text-align: right;
}

.CursorPointer{
    cursor: pointer;
}

object{
    position: relative;
    z-index: -1;
}

.ParentOfObject{
    position: relative;
    z-index: 1;
}

 

 
