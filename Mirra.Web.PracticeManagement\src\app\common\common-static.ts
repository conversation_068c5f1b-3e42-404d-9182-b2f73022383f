import { FormGroup } from "@angular/forms";
import { Tabs } from "./nav-constant";
import Swal from "sweetalert2";
import { ValueFormatterParams } from "ag-grid-community";

export class ssnMask {
    static ssnMaskApply(value: string) {
        return `${'X'.repeat(3)}${'-'}${'X'.repeat(2)}${'-'}${value.substr(value.length - 5, value.length)}`;
    }
}


export enum CLAIM_STATUS {
    INIT = 'INIT',
    RECEIVED = 'RECEIVED',
    RECEIVEDPAYER = 'RECEIVEDPAYER',
    PAYMENT = 'PAYMENT'
}

export enum CLAIM_TYPE {
    open = 'ON',
    onHold = 'OH',
    accepted = 'AC',
    dispatched = 'DP',
    unackByCH = 'UACH',
    rejectedByCH = 'RCH',
    acknolwedgedByCH = 'ANCH',  //Acknolwedged By CH
    unAckByPayer = 'UAP',
    rejectedByPayer = 'RP',
    acknolwedgedByPayer = 'AP',
    acceptedByPayer = 'ABP',
    pending = 'Pend',
    eobReceived = 'EOB',
    deniedByPayer = 'DBP',
    ntr = 'NTR', //NEED TO RESUBMISSION
    acceptedByCH = 'ABCH', 	//Accepted By Clearing House
    acceptedBucket = 'accepted'
}

export enum COMMON_VALUES {
    all = 'All',
    resubmittedClaims = 'Resubmitted Claims',
    deactivatedClaims = 'Deactivated Claims',
    includeAllClaims = 'INCLUDE ALL CLAIMS',
    includeLatestClaims = 'INCLUDE LATEST CLAIMS',
    maximumResubmissionCount = 50,
    fileType = '005010X231A1',
    _999 = 999,
    _277 = 277,
    GATEWAY = 'GATEWAY',
    Resubmit_File_PatientControlNumberLimit = 10,
    inactivelogin ='inactivelogin',
    invalid ='invalid',
    patientInsured ='Patient & Insured'

}

export class YearsValuesByCurentYerar {
    static getYears() {
        let years: any[] = []
        let selectedYear = new Date().getFullYear();
        for (let i = 0; i < 3; i++) {
            let year = selectedYear - i;
            years.push({ id: i, year: year });
            years = years.sort();
        }

        years.push({ id: years.length, year: 'All' });
        return years;

    }

    static getMonthsNames() {
        const months = [
            { id: 0, name: "All", code: "All" },
            { id: 1, name: "January", code: "Jan" },
            { id: 2, name: "February", code: "Feb" },
            { id: 3, name: "March", code: "Mar" },
            { id: 4, name: "April", code: "Apr" },
            { id: 5, name: "May", code: "May" },
            { id: 6, name: "June", code: "Jun" },
            { id: 7, name: "July", code: "Jul" },
            { id: 8, name: "August", code: "Aug" },
            { id: 9, name: "September", code: "Sep" },
            { id: 10, name: "October", code: "Oct" },
            { id: 11, name: "November", code: "Nov" },
            { id: 12, name: "December", code: "Dec" }];
        return months;
    }

}

export class ClaimsActions {

    static claimsTypeDashboardFilter() {
        const claimTypes = [
            { id: 1, name: 'INCLUDE ALL CLAIMS' },
            { id: 2, name: 'INCLUDE LATEST CLAIMS' }
        ]

        return claimTypes
    }
}

export class COMMON_METHODS {
    static currencyFormatter(currency) {
        let resp:any=null;
         if(typeof currency === 'number'  && !isNaN(currency)){
            const currencyVallue = currency ? Number(currency) : 0;
            var sansDec = currencyVallue?.toFixed(2);
            var formatted = sansDec.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                resp= '$' + `${formatted}`;
         }
            return resp;
    }
    static removeDollarSign(amountString: string | null | undefined): string {
        const dollarRegex = /\$/;
        if (!!amountString && dollarRegex.test(amountString)) {
            return amountString.replace(/\$/g, '');
        }
        return amountString;
    }

    static getClaimsStatusArray() {
        return [
            Tabs.open,
            Tabs.onHold,
            Tabs.resubmission,
            Tabs.dispatched,
            Tabs.accepted,
            Tabs.unAckByCH,
            Tabs.ackByCH,
            Tabs.rejectedByCH,
            Tabs.acceptedByCH,
            Tabs.unAckByPayer,
            Tabs.ackByPayer,
            Tabs.rejectedByPayer,
            Tabs.acceptedByPayer,
            Tabs.pending,
            Tabs.eobReceived,
            Tabs.deniedByPayer,
        ]
    }

    static getCountries(){
        const countries = [
            { id: 1, countryCode: "US",countryName: "United States" }
        ]
        return countries;
    }

    static hasAlphabet(input: string): boolean {
        return /[a-zA-Z]/.test(input);
      }

    static truncateTextFormatter(params: ValueFormatterParams, length: number = 15): string {
        const value = params.value;
        // Ensure value is a string and not null/undefined
        if (value && typeof value === 'string' && value.length > length) {
            return value.substring(0, length) + '...';
        }
        return value;
    }
    static validateDateForFilter(dateValue: any): Date | null {

        if (!dateValue) return null;

        const date = new Date(dateValue);


        // Comprehensive date validation
        if (isNaN(date.getTime())) return null; // Invalid date

        const year = date.getFullYear();
        const month = date.getMonth() + 1; // getMonth() returns 0-11
        const day = date.getDate();

        // Validate year (must be exactly 4 digits and reasonable range)
        if (year < 2010 || year > new Date().getFullYear()) {

            return null;
        }

        // Validate month (1-12)
        if (month < 1 || month > 12) return null;

        // Validate day (1-31, but also check for valid day in month)
        if (day < 1 || day > 31) return null;

        // Additional check: ensure the date is actually valid for the month/year
        const reconstructedDate = new Date(year, month - 1, day);
        if (reconstructedDate.getFullYear() !== year ||
            reconstructedDate.getMonth() !== (month - 1) ||
            reconstructedDate.getDate() !== day) {
            return null;
        }

        console.log('Valid date returned:', date);
        return date;
    }

}

export enum VALIDATION_PATTERNS {
    emailPattern = '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}$',
   
    emailMaxLength = '60',
}


export class FromDateToDateCompareValidations {
    //scenario :2
    //====================
    // startDate allows
    //   Today,s Date
    // Back Dated
    // future dated aloowed
    static startDateNoMaxLimitChangeEvent(form: FormGroup, startDateValue: any, startDateControl: string, endDateControl: string) {
        let endMinDate: Date;
        endMinDate = new Date(startDateValue)
        let startDate = new Date(startDateValue);
        let endDateValue = form.get(endDateControl).value;
        if (startDateValue && endDateValue && startDate) {
            let endDate = new Date(endDateValue);
            if (startDate > endDate) {
                form.controls[startDateControl].setErrors({ 'startDateGreaterError': true });
            }
            else {
                form.controls[startDateControl].setErrors(null);
                form.controls[endDateControl].setErrors(null);
            }
        }
        return endMinDate;
    }

    // End Date
    //Today,s Date
    // Back Dated
    // future dated

    // startDate not future Date.
    static endDateNoStartDateLimitChangeEvent(form: FormGroup, endDateControl: string, startDateControl: string, endDateValue: any) {
        let endDate = new Date(endDateValue);
        let startDate = new Date(form.get(startDateControl).value);
        if (endDateValue && endDate && startDate) {
            if (startDate <= endDate) {
                form.controls[startDateControl].setErrors(null);
            }
            else if (startDate > endDate) {
                form.controls[endDateControl].setErrors({ 'endDateGreaterError': true });
            }
        }
    }

}

export enum DateValidationErrors {
    minEndDateErrorMsg = ' Effective To Date must be greater than Effective From Date. ',
    maxStartDateErrorMsg = 'Start Date should not be greater than current date.',
    maxEndDateErrorMsg = 'End Date should not be greater than current date.',
    startDateGreaterErrorMsg = 'Effective From Date should not be greater than Effective To Date.',
    invalidDateErrorMsg = 'Please select valid date.',
}

export enum PREVILEGES {
    CLAIMS_BillingManagement_CreateClaim = 'CLAIMS_BillingManagement_CreateClaim',
    CLAIMS_BillingManagement_AddFacility = 'CLAIMS_BillingManagement_AddFacility',
    CLAIMS_BillingManagement_AddProvider = 'CLAIMS_BillingManagement_AddProvider',
    CLAIMS_BillingManagement_AddMember = 'CLAIMS_BillingManagement_AddMember',
    MemberHouse_MemberManagement_Claims_EditMember = 'MemberHouse_MemberManagement_Claims_EditMember',
    MemberHouse_MemberManagement_Claims_ViewMember = 'MemberHouse_MemberManagement_Claims_ViewMember',
    CLAIMS_BillingManagement_DeactivateaClaim = 'CLAIMS_BillingManagement_DeactivateaClaim',
    CLAIMS_BillingManagement_DownloadEDIFile = 'CLAIMS_BillingManagement_DownloadEDIFile',
    CLAIMS_BillingManagement_GenerateEDI837File = 'CLAIMS_BillingManagement_GenerateEDI837File',
    CLAIMS_BillingManagement_GetFileListIrespectiveOfFileType = 'CLAIMS_BillingManagement_GetFileListIrespectiveOfFileType',
    CLAIMS_BillingManagement_ReadlistofEOBfile = 'CLAIMS_BillingManagement_ReadlistofEOBfile',
    CLAIMS_BillingManagement_ViewTrackingDashBoard = 'CLAIMS_BillingManagement_ViewTrackingDashBoard',
    CLAIMS_BillingManagement_ViewAdmin = 'CLAIMS_BillingManagement_ViewAdmin',
    CLAIMS_BillingManagement_SearchProvider = 'CLAIMS_BillingManagement_SearchProvider',
    MemberHouse_MemberManagement_Claims_SearchMember = 'MemberHouse_MemberManagement_Claims_SearchMember',
    CLAIMS_BillingManagement_AcceptAClaim = 'CLAIMS_BillingManagement_AcceptAClaim',
    CLAIMS_BillingManagement_ActivateaClaim = 'CLAIMS_BillingManagement_ActivateaClaim',
    UserManagement_ViewAllUsers = 'UserManagement_ViewAllUsers',
    UserManagement_ViewAllRoles = 'UserManagement_ViewAllRoles',
    UserManagement_ViewAllUsersIPAMapping = 'UserManagement_ViewAllUsersIPAMapping',
    UserManagement_AddRole = 'UserManagement_AddRole',
    UserManagement_EditRole = 'UserManagement_EditRole',
    UserManagement_ViewRole = 'UserManagement_ViewRole',
    UserManagement_AddUser = 'UserManagement_AddUser',
    UserManagement_EditUser = 'UserManagement_EditUser',
    UserManagement_ViewUser = 'UserManagement_ViewUser',
    UserManagement_AddUserIPA = 'UserManagement_AddUserIPA',
    UserManagement_EditUserIPA = 'UserManagement_EditUserIPA',
    ProviderVilla_ProviderBridge_EditProvider = 'ProviderVilla_ProviderBridge_EditProvider',
    CLAIMS_BillingManagement_MoveAllOpenToAccepted_Biller = 'CLAIMS_BillingManagement_MoveAllOpenToAccepted_Biller',
    CLAIMS_BillingManagement_MoveAllOpenToAccepted = 'CLAIMS_BillingManagement_MoveAllOpenToAccepted',
    CLAIMS_BillingManagement_MoveSelectedOpenToAccepted = 'CLAIMS_BillingManagement_MoveSelectedOpenToAccepted',
    CLAIMS_BillingManagement_OnHoldAClaim = 'CLAIMS_BillingManagement_OnHoldAClaim',
    CLAIMS_BillingManagement_Resubmission = 'CLAIMS_BillingManagement_Resubmission',
    CLAIMS_BillingManagement_UpdateaClaim = 'CLAIMS_BillingManagement_UpdateaClaim',
    CLAIMS_BillingManagement_UploadFiles = 'CLAIMS_BillingManagement_UploadFiles',
    CLAIMS_BillingManagement_ViewReportsLibrary = 'CLAIMS_BillingManagement_ViewReportsLibrary',
    CLAIMS_BillingManagement_ExportEOBreportToExcel='CLAIMS_BillingManagement_ExportEOBreportToExcel'
}


export enum ClaimFormType{
    addClaim=1,
    viewClaim=2,
    editClaim=3,
    loadClaim=4,
    memberClaim=5
}

export class Insurance {

    static InsuranceTypes() {
        const insuranceTypes = [
            { id: 1, name: 'MEDICARE',value:'MB' },
            { id: 2, name: 'MEDICAID',value:'MC' },
            { id: 3, name: 'TRICARE',value:'TR' },
            { id: 4, name: 'CHAMPVA',value:'CH' },
            { id: 5, name: 'GROUP HEALTH PLAN',value:'GR' },
            { id: 6, name: 'FECA BLK LUNG',value:'FE' },
            { id: 7, name: 'OTHERS',value:'CI' }
        ]

        return insuranceTypes;
    }
}

export enum ValidationMsgs{
  no_data_available ='<span  class="uppar-case"> No Data Available.</span>',
  overlayLoadingTemplate ='<span class="ag-overlay-loading-center uppar-case" >Please wait while your rows are loading.</span>',
  overlayTemplate='<span class="ag-overlay-loading-center">Please wait while your rows are loading.</span>',
  success ='Success'
}

export class SwalFire {

  public static confirmCancelSwlAlert(msg: string) {
    return Swal.fire({
      title: 'Confirm !',
      html: msg,
      //icon: 'success',
      showDenyButton: true,
      //showCancelButton: true,
      confirmButtonColor: 'rgb(54, 69, 116)',
      denyButtonColor: 'rgb(54, 69, 116)',
      confirmButtonText: 'Confirm',
      denyButtonText: 'Cancel',
      allowOutsideClick: false,
      showCloseButton: true,  
    })

  }
}
export enum DebounceTime{
    citiesDebounceTime=800
}
