{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport let CustomDateFilterModule = /*#__PURE__*/(() => {\n  class CustomDateFilterModule {}\n\n  CustomDateFilterModule.ɵfac = function CustomDateFilterModule_Factory(t) {\n    return new (t || CustomDateFilterModule)();\n  };\n\n  CustomDateFilterModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CustomDateFilterModule\n  });\n  CustomDateFilterModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule]\n  });\n  return CustomDateFilterModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}