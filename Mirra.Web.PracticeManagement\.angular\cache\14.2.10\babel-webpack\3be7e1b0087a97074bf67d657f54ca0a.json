{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../UserAuthentication/UserAuthentication.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/shared/services/api.service\";\nexport let ClaimReportServiceService = /*#__PURE__*/(() => {\n  class ClaimReportServiceService {\n    constructor(userAuthenticationService, http, apiService) {\n      this.userAuthenticationService = userAuthenticationService;\n      this.http = http;\n      this.apiService = apiService;\n      this.headerData = {};\n      this.appUrl = environment.apiUrl;\n    }\n\n    setClaimListText(claimlistText) {\n      this.claimListText = claimlistText;\n    }\n\n    getClaimListText() {\n      return this.claimListText;\n    }\n\n    fetchClaimList(searchCreteria) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      return this.http.post(`${environment.apiUrl}/Claim/GetClaimsList`, JSON.stringify(searchCreteria), {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      }).pipe(map(response => {\n        if (response.statusCode == 200) {\n          this.claimList = response.content;\n        }\n\n        return this.claimList;\n      }));\n    }\n\n    fetchUserIPAs() {\n      this.headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('EDIMicroServiceBaseUrl')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      let params = new HttpParams();\n      var headers = new Headers();\n      let uuidData = JSON.parse(localStorage.getItem('uuid'));\n      params = params.append('uuid', uuidData);\n      return this.apiService.getCall('/Dashboard/getipas?uuid=' + uuidData, this.headerData, null).pipe(map(response => {\n        return response;\n      }));\n    }\n\n    fetchReport(searchCreteria) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      return this.http.post(`${environment.apiUrl}/Dashboard/trackdashboard`, JSON.stringify(searchCreteria), {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      }).pipe(map(response => {\n        // login successful if there's a jwt token in the response\n        if (response.statusCode == 200) {\n          // store user details and jwt token in local storage to keep user logged in between page refreshes\n          this.claimReportList = response.content;\n        }\n\n        return this.claimReportList;\n      }));\n    }\n\n    generateClaims(selectedClaims) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      return this.http.post(`${environment.apiUrl}/DispatchClaim/DispatchClaims`, JSON.stringify(selectedClaims), {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      }).pipe(map(response => {\n        return response; // login successful if there's a jwt token in the response\n\n        if (response.statusCode == 200) {\n          // store user details and jwt token in local storage to keep user logged in between page refreshes\n          return response;\n        }\n      }));\n    }\n\n    resubmitClaimCountDetail(claimId) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      let httpOptions = {\n        params: {\n          'claimId': claimId,\n          'status': status\n        },\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      };\n      return this.http.get(`${environment.apiUrl}/Claim/GetAllChildClaimsByClaimID?claimId=` + claimId, {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      }).pipe(map(response => {\n        return response;\n      }));\n    }\n\n    resubmitClaim(claimId) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      let bodyContent = {\n        claimId: claimId.toString(),\n        userFirstName: JSON.parse(localStorage.getItem('userFirstName')),\n        userLastName: JSON.parse(localStorage.getItem('userLastName')),\n        userMiddleName: JSON.parse(localStorage.getItem('userMiddleName'))\n      };\n      return this.http.post(`${environment.apiUrl}/Claim/ResubmitClaim`, bodyContent, {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      }).pipe(map(response => {\n        if (response.statusCode == 200) {\n          return response;\n        }\n\n        return null;\n      }));\n    }\n\n    changeClaimStatus(claimId, status) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      const httpOptions = {\n        params: {\n          'claimId': claimId,\n          'status': status\n        },\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      };\n      return this.http.get(`${environment.apiUrl}/Claim/ChangeClaimStatus`, httpOptions).pipe(map(response => {\n        return response; // login successful if there's a jwt token in the response\n\n        if (response.statusCode == 200) {\n          // store user details and jwt token in local storage to keep user logged in between page refreshes\n          return response;\n        }\n      }));\n    }\n\n    MoveClaimFromResubmittedToOpen(claimId) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      const httpOptions = {\n        params: {\n          'claimId': claimId\n        },\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      };\n      return this.http.get(`${environment.apiUrl}/Claim/MoveClaimFromResubmittedToOpen`, httpOptions).pipe(map(response => {\n        return response; // login successful if there's a jwt token in the response\n\n        if (response.statusCode == 200) {\n          // store user details and jwt token in local storage to keep user logged in between page refreshes\n          return response;\n        }\n      }));\n    }\n\n    getClaimList(searchCreteria) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      }; // let request=`{\"pFromDos\":\"2023-01-01T00:00:00\",\"pToDos\":\"2023-09-07T00:00:00\",\"iPACode\":\",undefined,,IPA00000001,IPA00000006,IPA00000007,IPA00000008,IPA00000009,,IPA00000004,,IPA00000010,IPA00000011,IPA00000005,\",\"pYear\":0,\"paidAmount\":0,\"claimFormStatusCode\":\"ON\",\"tempCount\":\"113\",\"index\":0}`\n      //`{\"pFromDos\":\"2023-01-01T00:00:00\",\"pToDos\":\"2023-09-12T00:00:00\",\"iPACode\":\",undefined,,IPA00000001,IPA00000006,IPA00000007,IPA00000008,IPA00000009,,IPA00000004,,IPA00000010,IPA00000011,IPA00000005,\",\"pYear\":0,\"paidAmount\":0,\"claimFormStatusCode\":\"ON\",\"tempCount\":\"91\",\"index\":0}`\n\n      return this.http.post(`${environment.apiUrl}/claim/GetClaimsList`, searchCreteria, {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData),\n          'X-Skip-Loader': 'true'\n        })\n      });\n    }\n\n    getUnattendClaimsCount(searchCreteria) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      }; // let request=`{\"pFromDos\":\"2023-01-01T00:00:00\",\"pToDos\":\"2023-09-07T00:00:00\",\"iPACode\":\",undefined,,IPA00000001,IPA00000006,IPA00000007,IPA00000008,IPA00000009,,IPA00000004,,IPA00000010,IPA00000011,IPA00000005,\",\"pYear\":0,\"paidAmount\":0,\"claimFormStatusCode\":\"ON\",\"tempCount\":\"113\",\"index\":0}`\n      //`{\"pFromDos\":\"2023-01-01T00:00:00\",\"pToDos\":\"2023-09-12T00:00:00\",\"iPACode\":\",undefined,,IPA00000001,IPA00000006,IPA00000007,IPA00000008,IPA00000009,,IPA00000004,,IPA00000010,IPA00000011,IPA00000005,\",\"pYear\":0,\"paidAmount\":0,\"claimFormStatusCode\":\"ON\",\"tempCount\":\"91\",\"index\":0}`\n\n      return this.http.post(`${environment.apiUrl}/claim/GetUnattendClaimsCount`, searchCreteria, {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      });\n    }\n\n    getClaimOnHoldReasons(claimId) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      const httpOptions = {\n        params: {\n          'claimId': claimId\n        },\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      };\n      return this.http.get(`${environment.apiUrl}/Claim/GetClaimOnHoldReasons`, httpOptions).pipe(map(response => {\n        return response; // login successful if there's a jwt token in the response\n      }));\n    }\n\n    getClaimByGlobalSearch(formValue) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      return this.http.post(`${environment.apiUrl}/global-search/GetClaimsGlobalSearch`, formValue, {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      });\n    }\n\n    getGlobalPageSearch(formValue) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      return this.http.post(`${environment.apiUrl}/global-search/GetGlobalPageSearch`, formValue, {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      });\n    }\n\n    extractReportSFTPPath(formValue) {\n      this.headerData = {\n        ServiceBaseUrl: this.userAuthenticationService.EDIMicroServiceBaseUrl,\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      return this.http.post(`${environment.apiUrl}/global-search/extractReportSFTPPath`, formValue, {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(this.headerData)\n        })\n      });\n    }\n\n  }\n\n  ClaimReportServiceService.ɵfac = function ClaimReportServiceService_Factory(t) {\n    return new (t || ClaimReportServiceService)(i0.ɵɵinject(i1.UserAuthenticationService), i0.ɵɵinject(i2.HttpClient), i0.ɵɵinject(i3.HttpService));\n  };\n\n  ClaimReportServiceService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClaimReportServiceService,\n    factory: ClaimReportServiceService.ɵfac,\n    providedIn: 'root'\n  });\n  return ClaimReportServiceService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}