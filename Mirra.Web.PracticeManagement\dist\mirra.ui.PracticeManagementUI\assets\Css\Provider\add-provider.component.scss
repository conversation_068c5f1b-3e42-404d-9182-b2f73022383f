.radioClass {
    height: 20px;
    width: 20px;
}

.radiopClass {
    height: 20px;
    width: 20px;
}

input[type=radio] {
    /* Hide original inputs */
    /* visibility: hidden; */
    opacity: 0.001;
    position: absolute;
    z-index: 1000;
}

input[type=radio]+label+p {
    height: 20px;
    width: 20px;
}

input[type=radio]+label>p {
    transition: 100ms all;
}

input[type=radio]:checked+label>p {
    z-index: -1000;
    background-image: url("data:image/svg+xml,%3Csvg id='Radio_button' data-name='Radio button' xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Ccircle id='Ellipse_54' data-name='Ellipse 54' cx='10' cy='10' r='10' fill='%230074bc'/%3E%3Cpath id='Icon_feather-check' data-name='Icon feather-check' d='M14.845,9,8.764,15.081,6,12.317' transform='translate(-0.454 -2.408)' fill='none' stroke='%23f5fbff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'/%3E%3C/svg%3E%0A");
}

input[type=radio]+label>p {
    width: 15px;
    height: 15px;
    background-image: url("data:image/svg+xml,%3Csvg id='Radio_button' data-name='Radio button' xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cg id='Ellipse_54' data-name='Ellipse 54' fill='%23fcfcfc' stroke='%23a1a7c4' stroke-width='2'%3E%3Ccircle cx='10' cy='10' r='10' stroke='none'/%3E%3Ccircle cx='10' cy='10' r='9' fill='none'/%3E%3C/g%3E%3Cpath id='Icon_feather-check' data-name='Icon feather-check' d='M14.845,9,8.764,15.081,6,12.317' transform='translate(-0.454 -2.408)' fill='none' stroke='%23f5fbff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'/%3E%3C/svg%3E%0A");
}

.IconAlign {
    height: 20px !important;
    width: 20px !important;
    margin-right: 5px !important;
}

.body {
    background-color: white;
    border-radius: 8px;
    margin-top: 10px;
    height: 1000px;
    box-shadow: 0px 0px 8px #0000001a;
}

.heading {
    color: #0074bc;
    font-size: 22px;
    padding-left: 10px;
    padding-top: 20px;
    font-family: "Poppins-SemiBold";
}

.customInputForProvider {
    width: 80% !important;
}

input[type="text"],
input[type="number"],
input[type="email"] {
    padding-left: 5px;
    border-radius: 6px;
    border: 1px solid #cccccc !important;
    height: 30px;
    outline: none;
    min-width: 40px;
    width: 65%;
    font-size: 10px;
    font-family: "Poppins-SemiBold";
    background-color: #fff;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus {
    border: 1px solid #61abd4 !important;
}

input[type="date"] {
    background-color: white;
    padding-left: 5px;
    outline: none;
    border: 1px solid #d9dade;
    border-radius: 8px;
    height: 30px;
    opacity: 1;
    width: 90%;
    color: #000000;
    font-size: 12px;
    font-family: "Poppins-Medium";
}

input[type="date"]::-webkit-calendar-picker-indicator {
    color: rgba(0, 0, 0, 0);
    opacity: 1;
    display: block;
    background: url("../../Images/Claims/CalendarIcon.svg") no-repeat;
    width: 9px;
    height: 15px;
    border-width: thin;
}

input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

select {
    box-shadow: none !important;
    background-color: white !important;
    padding: 0px;
    border: 1px solid #cccccc !important;
    border-radius: 6px;
    font-size: 10px;
    font-family: "Poppins-SemiBold";
    height: 30px;
    width: 80%;
    cursor: pointer;
    padding-left: 5px;
    outline: none;
}

select.ng-invalid.ng-touched {
    border: 1px solid red !important;
}

.section {
    background: #f8f8f8 0% 0% no-repeat padding-box;
}

.PaddingRight {
    padding-right: 17px !important;
}

.inner-addon {
    position: relative;
}

.inner-addon-dropdown {
    position: relative;
}

.ArrowDown {
    color: #c9c9c9;
}

.ArrowDownNew {
    color: #c9c9c9;
}

.inner-addon .ArrowDown {
    position: absolute;
    padding: 3%;
}

.inner-addon-dropdown .ArrowDownNew {
    position: absolute;
    padding: 1%;
}

.right-addon .ArrowDown {
    pointer-events: none;
    right: 8%;
    margin-right: 14%;
}

.right-addon-for-provider .ArrowDown {
    pointer-events: none;
    right: 21%;
}

.right-addon-for-dropdown .ArrowDownNew {
    pointer-events: none;
    right: 20%;
}

.btn {
    border: none;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
}

.backBtn {
    border-color: #0074bc !important;
    background-color: white;
    margin-right: 3%;
    color: #0074bc;
    border: 1px solid;
    font-size: 12px;
    margin-left: 10%;
}

.backBtn:hover {
    border-color: #0074bc !important;
    background-color: white;
}

.border {
    border: 1px solid #cccccc !important;
    border-radius: 6px;
    opacity: 1;
    margin: 1%;
    padding: 1%;
}

.practiceInformationBorder {
    border: 1px solid #cccccc !important;
    border-radius: 6px;
    opacity: 1;
    padding: 1%;
}

.billingInformationBorder {
    border: 1px solid #cccccc !important;
    border-radius: 6px;
    opacity: 1;
    padding: 1%;
}

.TextRight {
    text-align: end;
}

.textRight {
    text-align: end;
    justify-content: flex-end;
}

.MarginRight {
    margin-right: 10px;
}

.MarginLeft {
    margin-left: 8px;
}

.btnMargin {
    margin-left: 80px;
}

a {
    font-family: "Poppins-SemiBold";
    color: #646464;
    font-size: 12px;
    cursor: pointer;
}

.demographicForm {
    margin-left: 2%;
}

.practiceInformationForm {
    margin-left: 2%;
}

.billingInformationForm {
    margin-left: 2%;
}

.billingInformationPartialForm {
    margin-left: 2%;
}

.SpinnerCustomDesign {
    height: 12px;
    width: 12px;
    margin-left: 10%;
}

.iconMargin {
    margin-left: 3%;
}

.descList {
    background-color: #e3f2fd;
}

.section-heading {
    color: #5d5d5d;
    font-family: "Poppins-SemiBold";
    font-size: 15px;
    padding: 0;
}

.col {
    color: #646464;
    font-size: 12px;
    font-family: "Poppins-semibold";
}

.effective-date {
    margin-left: 8%;
}

.termination-date {
    margin-left: 8%;
}

input.ng-invalid.ng-touched,
.RedBorder::before {
    border: 1px solid red !important;
}

select.ng-invalid.ng-touched {
    border: 1px solid red !important;
}

::ng-deep .mat-option {
    height: auto !important;
    padding: 6px 0px 6px 6px !important;
    overflow: hidden !important;
    font-size: 10px !important;
    font-family: "Poppins" !important;
}

::ng-deep .mat-option-text {
    overflow: initial !important;
}

::ng-deep .mat-select-panel mat-option.mat-option {
    margin: 1rem 0;
    overflow: visible;
    line-height: initial;
    word-wrap: break-word;
    white-space: pre-wrap;
}

::ng-deep .mat-option-text.mat-option-text {
    white-space: normal;
    line-height: normal;
}

.upperGap{
    margin-top:2%;
}

.colFont {
    color: #646464;
    font-size: 12px;
    font-family: "Poppins-semibold";
}