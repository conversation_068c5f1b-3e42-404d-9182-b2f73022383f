{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nexport let FfsSearchRadioRenderer = /*#__PURE__*/(() => {\n  class FfsSearchRadioRenderer {\n    constructor(dialog) {\n      this.dialog = dialog;\n    }\n\n    agInit(params) {\n      this.params = params;\n    }\n\n    refresh() {\n      return false;\n    }\n\n  }\n\n  FfsSearchRadioRenderer.ɵfac = function FfsSearchRadioRenderer_Factory(t) {\n    return new (t || FfsSearchRadioRenderer)(i0.ɵɵdirectiveInject(i1.MatDialog));\n  };\n\n  FfsSearchRadioRenderer.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FfsSearchRadioRenderer,\n    selectors: [[\"FfsSearchRadioRenderer\"]],\n    decls: 2,\n    vars: 0,\n    consts: [[1, \"form-check\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault1\", 1, \"form-check-input\", \"mt-2\"]],\n    template: function FfsSearchRadioRenderer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"input\", 1);\n        i0.ɵɵelementEnd();\n      }\n    }\n  });\n  return FfsSearchRadioRenderer;\n})();", "map": null, "metadata": {}, "sourceType": "module"}