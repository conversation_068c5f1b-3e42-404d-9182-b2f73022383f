input[type="text"]
{
    outline: none;
    margin: 0;
    font-size: 12px;
    width: 108px;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #CCCCCC;
    border-radius: 6px;
    height: 27px;
    opacity: 1;
    font-family: 'Poppins';
}

.SerialNoHeadingWidth{
    width: 143px;
}

.TitleHeadingWidth{
    width: 250px;
}

.CenterAligned{
    text-align: center!important;
}

.Form-Body{
    margin: 5px;
}
.ThBtn{
    font-family: "Poppins-Bold";
    width: 5%;
    color: #5d5d5d !important;
}

.btn{
    height: 33px;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074BC;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
    border: none;
}

.RightText{
    text-align: right;
}

.Form-Header-2-Heading{
    color: #0074BC;
    font-family: 'Poppins-SemiBold';
    font-size: 20px;
    opacity: 1;
}
th{
    padding: 10px 0px!important;
    background-color: white!important;
}

td{
    white-space: pre-wrap!important;
    padding: 10px 0px!important;
    font-size: 12px;
    font-family: 'Poppins';
}

.Form-Header-2{
    padding: 5px;
}

::ng-deep .CustomForm p-table{
    box-shadow: 0px 0px 8px #0000001a;
    border: 1px solid #CCCCCC;
    border-radius: 8px;
    opacity: 1;
    padding: 5px!important;
}

.TextRight{
    text-align: end;
}

.MarginRight{
    margin-right: 10px;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC"!important;
    font-family: "FontAwesome"!important;
  }
  :host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon{
      color: initial!important;
  }
