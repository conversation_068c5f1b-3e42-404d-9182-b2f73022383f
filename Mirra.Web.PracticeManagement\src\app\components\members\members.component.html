<div class="main-card card search-member">
    <div class="appointment-flex">
        <div class="appointment-header">
            <div>
                <h3 class="appointment-title">Search Member1</h3>
            </div>
        </div>
        <div class="appointment-actions" *ngIf="isAddMemberBtnShow">
            <button type="button" class="btn-primary primary-btn btn-height" (click)="addMember()">
                <i class="material-icons icon-margin icon-height icon-align" style="padding: 0 !important;">person_add_alt_1</i>Add New Member</button>
        </div>
    </div>
    <div class="card search-card">
        <div class="row">
            <div class="col">
                <label for="subscriberId">Subscriber ID</label>
                <input type="text" id="subscriberId" placeholder="Subscriber ID" [(ngModel)]="searchVariables.subscriberID" autocomplete="off" class="form-control">
            </div>
            <div class="col-2">
                <label for="lastName">Member Last Name</label>
                <input type="text" id="lastName" placeholder="Last Name" [(ngModel)]="searchVariables.lastName" autocomplete="off" class="form-control">
            </div>
            <div class="col-2">
                <label for="firstName">Member First Name</label>
                <input type="text" id="firstName" placeholder="First Name" [(ngModel)]="searchVariables.firstName" autocomplete="off" class="form-control">
            </div>
            <div class="col" style="max-width: 15% !important;">
                <label for="pcp">Member PCP</label>
                <ng-select #memberPCPSelect [items]="memberPCPs" class="form-control ng-select" bindValue="fullName" bindLabel="fullName" (change)="changePCPSelect($event)"
                placeholder="Select" [(ngModel)]="searchVariables.pcp"></ng-select>
            </div>
            <div class="col">
                <label for="dob">DOB</label>
                <input matInput class="form-control" [max]="today" [matDatepicker]="datepicker" id="dob" autocomplete="off"
                (click)="datepicker.open()" placeholder="MM/DD/YYYY" [(ngModel)]="searchVariables.dateOfBirth"
                style="height: 38px; border: 1px solid #ced4da; border-radius: 4px; padding: 6px 12px; font-size: 14px;">
                <mat-datepicker #datepicker></mat-datepicker>
                <!-- <input type="date" placeholder="DOB" id="dob" autocomplete="off" placeholder="MM-DD-YYYY" max="{{today | date:'yyyy-MM-dd'}}" [(ngModel)]="searchVariables.dateOfBirth" class="form-control"> -->
            </div>
            <div class="col-2">
                <label for="insurance">Insurance Company</label>
                <ng-select #insuranceCompanySelect [items]="insuranceCompanies" class="form-control ng-select" bindValue="payerName" bindLabel="payerName" (change)="changeInsuranceCompanySelect($event)"
                placeholder="Select" [(ngModel)]="searchVariables.insuranceCompanyName"></ng-select>            
            </div>
            <div class="col-1 align-self-center">
                <label></label>
                <div>
                    <button type="button" class="btn-primary" [matTooltip]="checkIfSearchDisabled() ? 'Please fill at least one field to search.': 'Search'" (click)="searchMembers()" matTooltipPosition="above" matTooltipClass="above" [disabled]="checkIfSearchDisabled()"><i class="fa fa-search" aria-hidden="true"></i></button>
                    <button type="button" class="btn-common" matTooltip="Reset" matTooltipPosition="above" matTooltipClass="above" (click)="setSearchVariables()"><i class="fa fa-undo" aria-hidden="true"></i></button>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="!!memberSearchResult && memberSearchResult.length > 0">
        <div class="mt-2 appointment-header appointment-flex ">
            <div class="appointment-title">
            Members
            </div>
            <div style="display: flex;">
                <div class="align-self-center">
                    <div class="ellipse MarginRight"></div>
                </div>
                <div class="headingMembers">
                    Legacy Members
                </div>
            </div>
        </div>
        <div class="card mt-3 data-card">
            <ag-grid-angular id="resultsGrid" class="ag-theme-alpine ag-grid-view" [rowData]="memberSearchResult" [columnDefs]="memberCol" [gridOptions]="gridOptions" [pagination]="true" [accentedSort]="true" (gridSizeChanged)="onGridSizeChanged($event)" 
            [paginationPageSize]="paginationPageSize"></ag-grid-angular>
        </div>
    </div>
    <div class="dashboard-content mt-2" *ngIf="!!memberSearchResult && memberSearchResult.length == 0">
        <div class="content">
          <p>No data available for selected search parameters.</p>
        </div>
      </div>
</div>