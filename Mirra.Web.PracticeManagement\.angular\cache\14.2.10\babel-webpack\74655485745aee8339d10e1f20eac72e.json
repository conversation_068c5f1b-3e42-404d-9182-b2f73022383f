{"ast": null, "code": "import { BehaviorSubject, Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let NavigationServiceService = /*#__PURE__*/(() => {\n  class NavigationServiceService {\n    constructor() {\n      this._toggle = new Subject();\n      this.toggle$ = this._toggle.asObservable();\n      this.previewToggle$ = this._toggle.asObservable();\n      this.claimidArray = [];\n      this.selectedTabName = \"Home\";\n      this.tabName = new BehaviorSubject(this.selectedTabName);\n      this.tabName$ = this.tabName.asObservable();\n      this.toggleWithSearch = new Subject();\n      this.toggleWithSearch$ = this.toggleWithSearch.asObservable();\n      this.closeTab = new Subject();\n      this.closeTab$ = this.closeTab.asObservable();\n    }\n\n    changeTab(tabNumberValue) {\n      this.selectedTabName = tabNumberValue;\n      this.tabName.next(this.selectedTabName);\n    }\n\n    toggle(todo) {\n      this._toggle.next(todo);\n    }\n\n    closeTabEvent(value) {\n      this.closeTab.next(value);\n    }\n\n    toggleWithSearchEvent(search) {\n      this.toggleWithSearch.next(search);\n    }\n\n  }\n\n  NavigationServiceService.ɵfac = function NavigationServiceService_Factory(t) {\n    return new (t || NavigationServiceService)();\n  };\n\n  NavigationServiceService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: NavigationServiceService,\n    factory: NavigationServiceService.ɵfac,\n    providedIn: 'root'\n  });\n  return NavigationServiceService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}