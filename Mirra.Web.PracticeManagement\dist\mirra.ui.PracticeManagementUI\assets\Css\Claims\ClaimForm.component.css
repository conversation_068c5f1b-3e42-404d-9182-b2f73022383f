.radioClass {
    height: 20px;
    width: 20px;
}

.radiopClass {
    height: 20px;
    width: 20px;
}

input[type=radio] {
    /* Hide original inputs */
    /* visibility: hidden; */
    opacity: 0.1;
    position: absolute;
    z-index: 1000;
}

input[type=radio]+label+p {
    height: 20px;
    width: 20px;
}

input[type=radio]+label>p {
    transition: 100ms all;
}

input[type=radio]:checked+label>p {
    z-index: -1000;
    background-image: url("data:image/svg+xml,%3Csvg id='Radio_button' data-name='Radio button' xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Ccircle id='Ellipse_54' data-name='Ellipse 54' cx='10' cy='10' r='10' fill='%230074bc'/%3E%3Cpath id='Icon_feather-check' data-name='Icon feather-check' d='M14.845,9,8.764,15.081,6,12.317' transform='translate(-0.454 -2.408)' fill='none' stroke='%23f5fbff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'/%3E%3C/svg%3E%0A");
}

input[type=radio]+label>p {
    width: 15px;
    height: 15px;
    background-image: url("data:image/svg+xml,%3Csvg id='Radio_button' data-name='Radio button' xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cg id='Ellipse_54' data-name='Ellipse 54' fill='%23fcfcfc' stroke='%23a1a7c4' stroke-width='2'%3E%3Ccircle cx='10' cy='10' r='10' stroke='none'/%3E%3Ccircle cx='10' cy='10' r='9' fill='none'/%3E%3C/g%3E%3Cpath id='Icon_feather-check' data-name='Icon feather-check' d='M14.845,9,8.764,15.081,6,12.317' transform='translate(-0.454 -2.408)' fill='none' stroke='%23f5fbff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'/%3E%3C/svg%3E%0A");
}

.cursor-pointer{
    cursor: pointer!important;
}

/* Main div styling */

.MainFlexRow {
    flex-wrap: wrap;
    margin-top: 10px;
    /* padding:5px;  */
}


/* applies on full form */

.requiredAlert {
    color: red;
}

.Full-Form {
    /* margin-left: 10px; */
    font-weight: bolder;
    /* padding: 5px; */
    /* border-radius: 8px; */
    opacity: 1;
    background: #F4F7FC 0% 0% no-repeat padding-box;
}


/* for setting first heading box*/

.Form-Header-Row {
    padding-left: 7px;
    /* position: sticky;
            top: 0px; */
    background-color: white;
    height: 50px;
    align-items: center;
    box-shadow: 0px 0px 8px #0000001A;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
}


/* for setting heading of first header */

.Form-Header {
    font-size: 23px;
    justify-content: start;
    color: #272D3B;
    font-family: 'Poppins-SemiBold';
}

.fontSizeOfDetails {
    font-size: 10px!important;
}

 

 

input[type="date"]::-webkit-calendar-picker-indicator {
    color: rgba(0, 0, 0, 0);
    opacity: 1;
    display: block;
    background: url('src/assets/Images/Claims/CalendarIcon.svg') no-repeat;
    width: 9px;
    height: 15px;
    border-width: thin;
}


/* for setting second heading box*/

.Form-Header-2 {
    background-color: #EDEDED;
    padding: 10px;
    align-items: center;
    position: -webkit-sticky;
    position: sticky;
    top: 0px;
}


/* for setting heading of second header */

.Form-Header-2-Heading {
    color: #005488;
    font-size: 15px;
    font-family: 'Poppins-SemiBold';
}

.Form-Header-2-Heading-Count {
    color: #0074BC;
    font-size: 15px;
    font-family: 'Poppins-Bold';
}


/* all 4 header buttons design */

.Header-Buttons {
    text-align: end;
}


/* common setup for all buttons */

.btn {
    font-family: 'Poppins-SemiBold';
    margin-right: 20px;
    height: 35px;
    border-radius: 8px;
    background-color: #0074BC;
    /* Blue background */
    color: white;
    /* White text */
    font-size: 12px;
    /* Set a font size */
    cursor: pointer;
    /* Mouse pointer on hover */
}


/* text inside button setting in second header */

.Btn-Text {
    font-family: 'Poppins-SemiBold';
    justify-content: center;
    font-size: 12px;
    text-align: center;
    float: right;
    padding-top: 5%;
    margin-left: 5px;
}

.Medium {
    font-family: 'Poppins-Medium'!important;
}

.Regular {
    font-family: 'Poppins'!important;
}

.Bold {
    font-family: 'Poppins-Bold'!important;
}


/* Darker background on mouse-over of button */

.btn:hover {
    background-color: rgb(4, 54, 100);
}


/* basic form background setting */

.Form-Body {
    box-shadow: 0px 0px 8px #0000001A;
    background-color: white;
    /* padding: 5px; */
}


/* form divided into three columns and their common properties */

.Form-Body-Col-1,
.Form-Body-Col-2,
.Form-Body-Col-3 {
    /* padding: 12px; */
    background-color: #FAFAFA;
    /* font-weight: normal; */
}


/* all columns having smaller text and grey color */

.Grey-Color {
    font-size: 10px;
    font-family: 'Poppins';
}

.font-color {
    color: #646464;
}


/* for setting value inside text box */

.Value-Inside-Input {
    font-family: 'Poppins-Medium';
    font-size: 10px;
    color: black;
}


/* this is the req gap between 1st question and previous data in first column */

.Margin-Before-First-Question {
    margin-bottom: 62px;
}


/* this is the req gap between 1st question and previous data in second column */

.Margin-Before-First-Question-2 {
    margin-bottom: 60px;
}


/* columns requiring smaller fonts */

.Smallest-Text {
    font-size: 10px;
}


/* common class for right aligned texts */

.Right-Aligned-Text {
    text-align: end;
}

.Centre-Aligned-Text {
    text-align: center;
}


/* form columns having white seperator */

.Form-Body-Col-1,
.Form-Body-Col-2 {
    border-right: 8px solid #fff;
}


/* basic setting for all input type */

input {
    font-family: 'Poppins-Medium';
    background-color: white!important;
    outline: none;
    font-size: 9px!important;
    width: 5.5vw;
    border-radius: 5px;
    margin-bottom: 8px;
    border: 1px solid #dddddd !important;
    /* padding-left: 5px; */
}


/* all headings written in blue are marked with class Que i.e Question and here is there common styling */

.Que {
    color: #005488;
    font-size: 12px;
    font-family: 'Poppins-SemiBold';
}


/* some extra properties of Column1 of form */

.Form-Body-Col-1 {
    margin-left: 5px;
}


/* for setting search icon color to grey */

.fa-search {
    color: #b1afaf;
}

input[type="date"] {
    font-family: 'Roboto';
    outline: none;
    width: 92%;
    padding-left: 5px;
    /* margin-left: 6px; */
    font-size: 12px;
}

input[type="text"] {
    padding-left: 5px;
    font-family: 'Poppins-Medium';
    background: white!important;
    outline: none;
    width: 92%;
}

.inputBgColor {
    color: #646464;
    padding-left: 5px!important;
    background-color: white!important;
    font-family: 'Poppins-SemiBold';
}

.inputWidth {
    width: 92%;
}

.form-control:focus {
    box-shadow: none;
}

input.ng-invalid.ng-touched {
    border: 1px solid red!important;
}

select.ng-invalid.ng-touched {
    border: 1px solid red!important;
}


/* radio button to be displayed as checkbox and act as radio */

input[type="radio"] {
    color: white !important;
    border-radius: 50%;
    appearance: none;
    border: 1px solid #d3d3d3;
    width: 16px;
    height: 16px;
    content: none;
    outline: none;
    margin: 0;
    padding: 0px!important;
    margin-right: 2px;
    box-shadow: none;
}

.Padding-Left {
    padding-left: 1px!important;
}

.Padding-Smaller {
    padding: 2px!important;
    margin-left: 7px!important;
}


/* property to be added on checked radio button */

input[type="radio"]:checked {
    width: 16px;
    height: 16px;
    appearance: none;
    outline: none;
    padding: 0;
    content: none;
    border: none;
    box-shadow: none;
}


/* some more property of checked radio button */

input[type="radio"]:checked::before {
    position: absolute;
    background-color: #0074BC!important;
    color: white !important;
    content: "\00A0\2713\00A0" !important;
    border: 1px solid #d3d3d3;
    font-weight: bold;
    font-size: 10px;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    box-shadow: none;
    border: none;
    padding: 0px;
    margin-left: -1px;
    margin-top: -1px;
    padding-left: 1px;
}


/* property to add icon inside input */

.inner-addon {
    position: relative;
}


/* style icon */

.inner-addon .fa {
    position: absolute;
    padding: 10px;
    pointer-events: none;
}

/* align icon */

.left-addon .fa {
    left: 0px;
}

.right-addon .fa {
    right: 0px;
    margin-right: 6px;
    color: #C9C9C9;
}

.searchIconLogo {
    margin-top: -3px;
    margin-right: -7px !important;
}

.right-addon1 .fa {
    right: 13px;
    margin-right: 12px;
}


/* add padding  */

.left-addon input {
    padding-left: 30px;
}

.right-addon input {
    padding-right: 25px;
}

.fa-angle-down {
    margin-top: -5px;
}


/* margin required for question 15 placement */

.Margin-For-Que15 {
    /* margin-top: 110px; */
    margin-top: 107px;
}

fieldset {
    width: 92%;
    margin-right: 10px;
}

select {
    background-color: white!important;
    padding: 0px;
    border: 1px solid #dddddd !important;
    font-size: 10px;
    font-family: 'Poppins';
    height: 20px;
    width: 60px;
}


/* font for setting question number 21 */

.Medium-Font {
    font-size: 12px;
}

.Eye-Icon-Position {
    margin-left: -15px;
}

.Normal-Weight {
    font-weight: normal;
}

.Charges-Left-Margin {
    margin-left: 30px!important;
}

.Input-Smaller-Width {
    width: 25px
}

.Input-Meduim-Width {
    width: 32px;
    /* text-align: end; */
}

.Margin-For-Que24 {
    margin-top: 42px;
}

.Property-For-Que26 {
    padding-right: 0px;
    font-size: 12px;
}

.Meduim-input {
    margin-right: 5px;
    width: 75%!important;
}

.BlueColor {
    color: #005488;
}


/* left mrgin used for close icon */

.width14 {
    width: 14%!important;
    margin-right: 7px;
}

.width19 {
    width: 19%!important;
    padding-left: 0;
}

.widthDiff17 {
    width: 17%!important;
    padding-left: 0;
}

.width30 {
    width: 30%!important;
    padding: 0;
}

form {
    display: flex;
}

.PlusButton {
    width: 23px;
    height: 22px;
    color: white;
    background-color: #0074BC;
    border: none;
}

.form-control {
    appearance: auto;
    line-height: inherit;
    display: initial;
    font-weight: initial;
    padding: 0px;
}

.height27 {
    height: 27px;
}

.row {
    --bs-gutter-x: 0!important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}


/* input[type="checkbox"]{
    height: 12px;
    width: 25px;
    margin-right: 10px;
    border: 2px solid #A1A7C4!important;
    opacity: 1;
    padding: 0 6px;
    border-radius: 0
} */

input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

input[type="checkbox"]:checked {
    margin-right: 19px!important;
}

.Margin-Right {
    margin-right: 8px;
}

.Margin-Left-Que17 {
    margin-left: -10px;
}

.MarginLeft {
    margin-left: 15px;
}

.ModifierRowHeight {
    height: 15px;
}

::ng-deep .mat-option {
    height: auto!important;
    padding: 6px 0px 6px 6px!important;
    overflow: hidden!important;
    font-size: 10px!important;
    font-family: 'Poppins'!important;
}

::ng-deep .mat-option-text {
    overflow: initial!important;
}

::ng-deep .mat-select-panel mat-option.mat-option {
    margin: 1rem 0;
    overflow: visible;
    line-height: initial;
    word-wrap: break-word;
    white-space: pre-wrap;
}

 ::ng-deep .mat-option-text.mat-option-text {
    white-space: normal;
    line-height: normal;
}

.marginRightIcdIcon {
    margin-right: 5px!important;
}

.SpinnerCustomDesign {
    height: 12px;
    width: 12px;
    margin-left: 10%;
}

 ::ng-deep .mat-autocomplete-panel {
    min-width: 176px!important;
}

.heightForToggleStateInputRow {
    height: 32px;
}


/* 
::ng-deep .cdk-overlay-pane{
    width:180px
} */


/* .sticky {
    float: left;
    position: fixed;
    top: 0px;
    right: 7px;
  }
  @media screen {
      
  } */

.ClearButtonCSS {
    width: 10px!important;
    height: 10px!important;
    margin-top: -10px!important;
}

.ClearButtonCSS {
    width: 10px!important;
    height: 10px!important;
    margin-top: -5px!important;
}

.MarginRightAngleDown {
    margin-right: 12px;
}

.ResubmissionInputHeight {
    height: 27px;
}

.WhiteColor {
    color: white;
}

.headingColorChange {
    color: #005488;
    font-size: 12px;
}

.MarginForLabel {
    margin-left: 7px!important;
}

.minHeight {
    min-height: 40px;
}

.marginInBox {
    min-height: 21px;
}

@media only screen and (min-width: 768px) {
    /* For desktop: */
    .col-1 {
        width: 8.33%;
    }
    .col-2 {
        width: 16.66%;
    }
    .col-3 {
        width: 25%;
    }
    .col-4 {
        width: 33.33%;
    }
    .col-5 {
        width: 41.66%;
    }
    .col-6 {
        width: 50%;
    }
    .col-7 {
        width: 58.33%;
    }
    .col-8 {
        width: 66.66%;
    }
    .col-9 {
        width: 75%;
    }
    .col-10 {
        width: 83.33%;
    }
    .col-11 {
        width: 91.66%;
    }
    .col-12 {
        width: 100%;
    }
}

.Deactivate {
    padding-top: 3%!important;
}
.IconAlign{
    height: 20px !important; 
     width: 20px !important;
      margin-right: 5px !important;
}