{"ast": null, "code": "import { FormControl, Validators } from '@angular/forms';\nimport { Member } from 'src/app/classmodels/Member/MemberProfile';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/shared/services/subject.service\";\nimport * as i3 from \"src/app/shared/services/dateformat\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/datepicker\";\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction PatientDobComponent_input_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 20);\n    i0.ɵɵlistener(\"click\", function PatientDobComponent_input_15_Template_input_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext();\n\n      const _r1 = i0.ɵɵreference(17);\n\n      return i0.ɵɵresetView(_r1.open());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n\n    const _r1 = i0.ɵɵreference(17);\n\n    i0.ɵɵproperty(\"max\", ctx_r0.todayDate)(\"matDatepicker\", _r1)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, (ctx_r0.f.dmg02PatientBirthDate == null ? null : ctx_r0.f.dmg02PatientBirthDate.invalid) && (ctx_r0.f.dmg02PatientBirthDate.dirty || ctx_r0.f.dmg02PatientBirthDate.touched) && ((ctx_r0.f.dmg02PatientBirthDate == null ? null : ctx_r0.f.dmg02PatientBirthDate.errors == null ? null : ctx_r0.f.dmg02PatientBirthDate.errors.required) || (ctx_r0.f.dmg02PatientBirthDate == null ? null : ctx_r0.f.dmg02PatientBirthDate.errors == null ? null : ctx_r0.f.dmg02PatientBirthDate.errors.dateGreaterthanOrEqualToday))));\n  }\n}\n\nfunction PatientDobComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \" Please select date of birth \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction PatientDobComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \" Must be less than or equal to current date \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction PatientDobComponent_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r4.f.dmg02PatientBirthDate.value && ctx_r4.f.dmg02PatientBirthDate.value.length > 0 ? i0.ɵɵpipeBind2(2, 1, ctx_r4.f.dmg02PatientBirthDate.value, \"MM/dd/yyyy\") : \"-\");\n  }\n}\n\nfunction PatientDobComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" Please select Gender \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport let PatientDobComponent = /*#__PURE__*/(() => {\n  class PatientDobComponent {\n    constructor(payerform, subjectService, dateFormatService) {\n      this.payerform = payerform;\n      this.subjectService = subjectService;\n      this.dateFormatService = dateFormatService;\n      this.todayDate = new Date();\n      this.subjectService.getInsureDob().subscribe(res => {\n        if (res) {\n          if (!res.isCopyInsure) {\n            this.patientdobForm.patchValue({\n              dmg02PatientBirthDate: res.insuredDateOfBirth,\n              dmg03PatientGenderCode: res.genderCode\n            });\n          } else {\n            this.clearValue();\n          }\n\n          this.patientdobForm.disable();\n          this.subjectService.resetInsureDob();\n        }\n      });\n    }\n\n    ngOnInit() {\n      this.patchValue();\n    }\n\n    get f() {\n      return this.patientdobForm.controls;\n    }\n\n    createForm() {\n      this.patientdobForm = this.payerform.group({\n        dmg02PatientBirthDate: new FormControl('', Validators.required),\n        dmg03PatientGenderCode: new FormControl('O', Validators.required)\n      });\n      return this.patientdobForm;\n    }\n\n    patchValue() {\n      if (this.claimFormData.isAddClaim) {\n        this.patientdobForm.controls['dmg02PatientBirthDate'].setErrors({\n          required: true\n        });\n        let gender = this.memberResult?.gender === null || this.memberResult?.gender.toLocaleLowerCase() === 'other' ? 'U' : this.memberResult?.gender?.substring(0, 1);\n        this.patientdobForm.patchValue({\n          dmg02PatientBirthDate: this.formatDate(new Date(this.memberResult?.dateOfBirth)),\n          dmg03PatientGenderCode: gender\n        }); //submitValidateAllFields.validateDisableControl(this.patientdobForm, [\"dmg03PatientGenderCode\"]);\n\n        this.patientdobForm.disable();\n      } else if (this.claimFormData.isEditClaim || this.claimFormData.isViewClaim) {\n        let gender = this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.dmg03PatientGenderCode;\n        let date = this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.dmg02PatientBirthDate;\n\n        if (!(gender == 'M' || gender == 'F' || gender == 'U')) {\n          gender = null;\n          this.patientdobForm.patchValue({\n            dmg03PatientGenderCode: null\n          });\n        }\n\n        if (gender) {\n          this.patientdobForm.patchValue({\n            dmg03PatientGenderCode: gender ? gender : 'U'\n          });\n        }\n\n        if (date) {\n          this.patientdobForm.patchValue({\n            dmg02PatientBirthDate: this.dateFormatService.formatDate(new Date(date.substring(0, 4) + \"-\" + date.substring(4, 6) + \"-\" + date.substring(6, 8)))\n          });\n        }\n\n        this.patientdobForm.disable();\n      }\n\n      if (this.patientdobForm.invalid) {\n        submitValidateAllFields.validateAllFields(this.patientdobForm);\n      }\n    }\n\n    validateForm() {\n      if (this.patientdobForm.controls['dmg02PatientBirthDate'].value === 'NaN-NaN-NaN') {\n        this.patientdobForm.controls['dmg02PatientBirthDate'].patchValue('');\n      } else {\n        let dob = this.formatDate(this.patientdobForm.controls['dmg02PatientBirthDate'].value);\n        let dobMember = new Date(dob);\n\n        if (this.todayDate < dobMember) {\n          this.patientdobForm.controls['dmg02PatientBirthDate'].setErrors({\n            dateGreaterthanOrEqualToday: true\n          });\n        }\n      }\n\n      if (this.patientdobForm.invalid) {\n        submitValidateAllFields.validateAllFields(this.patientdobForm);\n        return false;\n      }\n\n      return true;\n    }\n\n    clearValue() {\n      this.patientdobForm.patchValue({\n        dmg02PatientBirthDate: '',\n        dmg03PatientGenderCode: ''\n      });\n      this.patientdobForm.enable();\n    }\n\n    formatDate(date) {\n      const d = new Date(date);\n      let month = '' + (d.getMonth() + 1);\n      let day = '' + d.getDate();\n      const year = d.getFullYear();\n      if (month.length < 2) month = '0' + month;\n      if (day.length < 2) day = '0' + day;\n      return [year, month, day].join('-');\n    }\n\n  }\n\n  PatientDobComponent.ɵfac = function PatientDobComponent_Factory(t) {\n    return new (t || PatientDobComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SubjectService), i0.ɵɵdirectiveInject(i3.DateFormatService));\n  };\n\n  PatientDobComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PatientDobComponent,\n    selectors: [[\"app-patient-dob\"]],\n    inputs: {\n      memberResult: \"memberResult\",\n      claimFormData: \"claimFormData\"\n    },\n    decls: 35,\n    vars: 15,\n    consts: [[3, \"formGroup\"], [1, \"3\", \"bd\"], [1, \"row\", \"mt-2\"], [1, \"col\"], [1, \"form-title\"], [\"for\", \"PatientDOB\", 1, \"create-claims-labels\"], [\"for\", \"PatientMale\", 1, \"create-claims-labels\"], [1, \"row\"], [\"matInput\", \"\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"dmg02PatientBirthDate\", \"placeholder\", \"MM/DD/YYYY\", 3, \"max\", \"matDatepicker\", \"ngClass\", \"click\", 4, \"ngIf\"], [\"datepickerFrom\", \"\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"form-control\", 4, \"ngIf\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"radio\", \"formControlName\", \"dmg03PatientGenderCode\", \"value\", \"M\", 1, \"form-check-input\", \"radio-align\", 3, \"ngClass\"], [\"for\", \"PatientMale\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"dmg03PatientGenderCode\", \"value\", \"F\", 1, \"form-check-input\", \"radio-align\", 3, \"ngClass\"], [\"for\", \"PatientFemale\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"dmg03PatientGenderCode\", \"value\", \"U\", 1, \"form-check-input\", \"radio-align\", 3, \"ngClass\"], [\"for\", \"PatientOthers\", 1, \"create-claim-radio-labels\"], [\"class\", \"invalid-feedback-custom\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"dmg02PatientBirthDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\", 3, \"max\", \"matDatepicker\", \"ngClass\", \"click\"], [1, \"invalid-feedback\"], [1, \"form-control\"], [1, \"invalid-feedback-custom\"]],\n    template: function PatientDobComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0)(1, \"section\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"p\", 4);\n        i0.ɵɵtext(5, \"3. Patient Birth Date\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"label\", 5);\n        i0.ɵɵtext(9, \"Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 3)(11, \"label\", 6);\n        i0.ɵɵtext(12, \"Sex\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 3);\n        i0.ɵɵtemplate(15, PatientDobComponent_input_15_Template, 1, 5, \"input\", 8);\n        i0.ɵɵelement(16, \"mat-datepicker\", null, 9);\n        i0.ɵɵtemplate(18, PatientDobComponent_div_18_Template, 2, 0, \"div\", 10);\n        i0.ɵɵtemplate(19, PatientDobComponent_div_19_Template, 2, 0, \"div\", 10);\n        i0.ɵɵtemplate(20, PatientDobComponent_span_20_Template, 3, 4, \"span\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 3)(22, \"div\", 12);\n        i0.ɵɵelement(23, \"input\", 13);\n        i0.ɵɵelementStart(24, \"label\", 14);\n        i0.ɵɵtext(25, \"Male\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 12);\n        i0.ɵɵelement(27, \"input\", 15);\n        i0.ɵɵelementStart(28, \"label\", 16);\n        i0.ɵɵtext(29, \"Female\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"div\", 12);\n        i0.ɵɵelement(31, \"input\", 17);\n        i0.ɵɵelementStart(32, \"label\", 18);\n        i0.ɵɵtext(33, \"Other\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(34, PatientDobComponent_div_34_Template, 2, 0, \"div\", 19);\n        i0.ɵɵelementEnd()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.patientdobForm);\n        i0.ɵɵadvance(15);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.dmg02PatientBirthDate == null ? null : ctx.f.dmg02PatientBirthDate.invalid) && ctx.f.dmg02PatientBirthDate.errors.required);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.dmg02PatientBirthDate == null ? null : ctx.f.dmg02PatientBirthDate.invalid) && ctx.f.dmg02PatientBirthDate.errors.dateGreaterthanOrEqualToday);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, (ctx.f.dmg03PatientGenderCode == null ? null : ctx.f.dmg03PatientGenderCode.invalid) && (ctx.f.dmg03PatientGenderCode == null ? null : ctx.f.dmg03PatientGenderCode.errors)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, (ctx.f.dmg03PatientGenderCode == null ? null : ctx.f.dmg03PatientGenderCode.invalid) && (ctx.f.dmg03PatientGenderCode == null ? null : ctx.f.dmg03PatientGenderCode.errors)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, (ctx.f.dmg03PatientGenderCode == null ? null : ctx.f.dmg03PatientGenderCode.invalid) && (ctx.f.dmg03PatientGenderCode == null ? null : ctx.f.dmg03PatientGenderCode.errors)));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.dmg03PatientGenderCode == null ? null : ctx.f.dmg03PatientGenderCode.invalid) && ctx.f.dmg03PatientGenderCode.errors.required);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.MatDatepicker, i5.MatDatepickerInput, i4.DatePipe],\n    styles: [\".create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.search-icon-alignment[_ngcontent-%COMP%]{position:relative}.search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%], .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}.form-check-inline[_ngcontent-%COMP%]{display:inline-flex!important}.form-check-inline[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{margin-right:.4rem!important}.invalid-feedback-custom[_ngcontent-%COMP%]{width:100%;margin-top:.25rem;font-size:12px;color:#dc3545}\"]\n  });\n  return PatientDobComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}