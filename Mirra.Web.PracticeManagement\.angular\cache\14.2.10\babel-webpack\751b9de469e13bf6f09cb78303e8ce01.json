{"ast": null, "code": "import { ChangeDetectorRef, EventEmitter } from '@angular/core';\nimport { FormControl, Validators } from '@angular/forms';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport { IcdViewHistoryComponent } from 'src/app/modals/icd-view-history/icd-view-history.component';\nimport { ConfirmPopupModel } from 'src/app/models/confirm-popup.model';\nimport { ConfirmMessagePopupComponent } from 'src/app/modals/confirm-message-popup/confirm-message-popup.component';\nimport { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';\nimport { LocalStorageKey } from 'src/app/shared/constant/constatnt';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/ClaimForm/get-all-icdcode.service\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"ngx-spinner\";\nimport * as i5 from \"src/app/shared/services/subject.service\";\nimport * as i6 from \"src/app/services/cache-service/cache.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@ng-select/ng-select\";\n\nfunction DiagnosisNatureComponent_section_1_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r74 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r74.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r75 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r75.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r76 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r76.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r77 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r77.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r78 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r78.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r79 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r79.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r80 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r80.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r81 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r81.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r82 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r82.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r83 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r83.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_137_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_138_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_142_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r84 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r84.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_143_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_144_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_145_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_146_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_ng_template_150_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r85 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r85.text, \" \");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_151_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_152_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_153_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please Give Valid ICD Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction DiagnosisNatureComponent_section_1_div_154_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction DiagnosisNatureComponent_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r87 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"section\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"p\", 5);\n    i0.ɵɵtext(4, \"21. Diagnosis Or Nature Of Illness Or Injury. (Relate A-L To Service Line Below (24E)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 6)(6, \"div\")(7, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function DiagnosisNatureComponent_section_1_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r86 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r86.icdViewHistory());\n    });\n    i0.ɵɵelement(8, \"span\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 9)(10, \"input\", 10);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_input_change_10_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r88 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r88.icdHandleChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"label\", 11);\n    i0.ɵɵtext(12, \"ICD9\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 9)(14, \"input\", 12);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_input_change_14_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r89 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r89.icdHandleChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"label\", 13);\n    i0.ɵɵtext(16, \"ICD10\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 14)(18, \"div\", 15)(19, \"label\", 16);\n    i0.ɵɵtext(20, \"A(1)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 15)(22, \"label\", 16);\n    i0.ɵɵtext(23, \"B(2)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 15)(25, \"label\", 16);\n    i0.ɵɵtext(26, \"C(3)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 15)(28, \"label\", 16);\n    i0.ɵɵtext(29, \"D(4)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 14)(31, \"div\", 15)(32, \"ng-select\", 17, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_32_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r90 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r90.enableIcdControl($event, 1));\n    });\n    i0.ɵɵtemplate(34, DiagnosisNatureComponent_section_1_ng_template_34_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(35, DiagnosisNatureComponent_section_1_div_35_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(36, DiagnosisNatureComponent_section_1_div_36_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(37, DiagnosisNatureComponent_section_1_div_37_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(38, DiagnosisNatureComponent_section_1_div_38_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 15)(40, \"ng-select\", 21, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_40_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r91 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r91.enableIcdControl($event, 2));\n    });\n    i0.ɵɵtemplate(42, DiagnosisNatureComponent_section_1_ng_template_42_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(43, DiagnosisNatureComponent_section_1_div_43_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(44, DiagnosisNatureComponent_section_1_div_44_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(45, DiagnosisNatureComponent_section_1_div_45_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(46, DiagnosisNatureComponent_section_1_div_46_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 15)(48, \"ng-select\", 22, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_48_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r92 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r92.enableIcdControl($event, 3));\n    });\n    i0.ɵɵtemplate(50, DiagnosisNatureComponent_section_1_ng_template_50_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(51, DiagnosisNatureComponent_section_1_div_51_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(52, DiagnosisNatureComponent_section_1_div_52_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(53, DiagnosisNatureComponent_section_1_div_53_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(54, DiagnosisNatureComponent_section_1_div_54_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 15)(56, \"ng-select\", 23, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_56_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r93 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r93.enableIcdControl($event, 4));\n    });\n    i0.ɵɵtemplate(58, DiagnosisNatureComponent_section_1_ng_template_58_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(59, DiagnosisNatureComponent_section_1_div_59_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(60, DiagnosisNatureComponent_section_1_div_60_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(61, DiagnosisNatureComponent_section_1_div_61_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(62, DiagnosisNatureComponent_section_1_div_62_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 3)(64, \"div\", 15)(65, \"label\", 16);\n    i0.ɵɵtext(66, \"E(5)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 15)(68, \"label\", 16);\n    i0.ɵɵtext(69, \"F(6)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 15)(71, \"label\", 16);\n    i0.ɵɵtext(72, \"G(7)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 15)(74, \"label\", 16);\n    i0.ɵɵtext(75, \"H(8)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"div\", 14)(77, \"div\", 15)(78, \"ng-select\", 24, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_78_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r94 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r94.enableIcdControl($event, 5));\n    });\n    i0.ɵɵtemplate(80, DiagnosisNatureComponent_section_1_ng_template_80_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(81, DiagnosisNatureComponent_section_1_div_81_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(82, DiagnosisNatureComponent_section_1_div_82_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(83, DiagnosisNatureComponent_section_1_div_83_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(84, DiagnosisNatureComponent_section_1_div_84_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(85, \"div\", 15)(86, \"ng-select\", 25, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_86_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r95 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r95.enableIcdControl($event, 6));\n    });\n    i0.ɵɵtemplate(88, DiagnosisNatureComponent_section_1_ng_template_88_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(89, DiagnosisNatureComponent_section_1_div_89_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(90, DiagnosisNatureComponent_section_1_div_90_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(91, DiagnosisNatureComponent_section_1_div_91_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(92, DiagnosisNatureComponent_section_1_div_92_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"div\", 15)(94, \"ng-select\", 26, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_94_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r96 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r96.enableIcdControl($event, 7));\n    });\n    i0.ɵɵtemplate(96, DiagnosisNatureComponent_section_1_ng_template_96_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(97, DiagnosisNatureComponent_section_1_div_97_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(98, DiagnosisNatureComponent_section_1_div_98_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(99, DiagnosisNatureComponent_section_1_div_99_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(100, DiagnosisNatureComponent_section_1_div_100_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(101, \"div\", 15)(102, \"ng-select\", 27, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_102_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r97 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r97.enableIcdControl($event, 8));\n    });\n    i0.ɵɵtemplate(104, DiagnosisNatureComponent_section_1_ng_template_104_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(105, DiagnosisNatureComponent_section_1_div_105_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(106, DiagnosisNatureComponent_section_1_div_106_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(107, DiagnosisNatureComponent_section_1_div_107_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(108, DiagnosisNatureComponent_section_1_div_108_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(109, \"div\", 3)(110, \"div\", 15)(111, \"label\", 16);\n    i0.ɵɵtext(112, \"I(9)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(113, \"div\", 15)(114, \"label\", 16);\n    i0.ɵɵtext(115, \"J(10)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(116, \"div\", 15)(117, \"label\", 16);\n    i0.ɵɵtext(118, \"K(11)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(119, \"div\", 15)(120, \"label\", 16);\n    i0.ɵɵtext(121, \"L(12)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(122, \"div\", 14)(123, \"div\", 15)(124, \"ng-select\", 28, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_124_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r98 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r98.enableIcdControl($event, 9));\n    });\n    i0.ɵɵtemplate(126, DiagnosisNatureComponent_section_1_ng_template_126_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(127, DiagnosisNatureComponent_section_1_div_127_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(128, DiagnosisNatureComponent_section_1_div_128_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(129, DiagnosisNatureComponent_section_1_div_129_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(130, DiagnosisNatureComponent_section_1_div_130_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(131, \"div\", 15)(132, \"ng-select\", 29, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_132_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r99 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r99.enableIcdControl($event, 10));\n    });\n    i0.ɵɵtemplate(134, DiagnosisNatureComponent_section_1_ng_template_134_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(135, DiagnosisNatureComponent_section_1_div_135_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(136, DiagnosisNatureComponent_section_1_div_136_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(137, DiagnosisNatureComponent_section_1_div_137_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(138, DiagnosisNatureComponent_section_1_div_138_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(139, \"div\", 15)(140, \"ng-select\", 30, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_140_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r100 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r100.enableIcdControl($event, 11));\n    });\n    i0.ɵɵtemplate(142, DiagnosisNatureComponent_section_1_ng_template_142_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(143, DiagnosisNatureComponent_section_1_div_143_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(144, DiagnosisNatureComponent_section_1_div_144_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(145, DiagnosisNatureComponent_section_1_div_145_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(146, DiagnosisNatureComponent_section_1_div_146_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(147, \"div\", 15)(148, \"ng-select\", 31, 18);\n    i0.ɵɵlistener(\"change\", function DiagnosisNatureComponent_section_1_Template_ng_select_change_148_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r101 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r101.enableIcdControl($event, 12));\n    });\n    i0.ɵɵtemplate(150, DiagnosisNatureComponent_section_1_ng_template_150_Template, 1, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(151, DiagnosisNatureComponent_section_1_div_151_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(152, DiagnosisNatureComponent_section_1_div_152_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(153, DiagnosisNatureComponent_section_1_div_153_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(154, DiagnosisNatureComponent_section_1_div_154_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(32);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes1)(\"virtualScroll\", true)(\"typeahead\", ctx_r0.icdSearch_1)(\"ngClass\", i0.ɵɵpureFunction1(97, _c0, (ctx_r0.f.iCDInput1 == null ? null : ctx_r0.f.iCDInput1.invalid) && (ctx_r0.f.iCDInput1.dirty || ctx_r0.f.iCDInput1.touched) && (ctx_r0.f.iCDInput1 == null ? null : ctx_r0.f.iCDInput1.errors)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.f.iCDInput1 == null ? null : ctx_r0.f.iCDInput1.invalid) && ctx_r0.f.iCDInput1.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput1\"] == null ? null : ctx_r0.f[\"iCDInput1\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput1\"] == null ? null : ctx_r0.f[\"iCDInput1\"].hasError(\"continued\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput1\"] == null ? null : ctx_r0.f[\"iCDInput1\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes2)(\"typeahead\", ctx_r0.icdSearch_2)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(99, _c0, (ctx_r0.f.iCDInput2 == null ? null : ctx_r0.f.iCDInput2.invalid) && (ctx_r0.f.iCDInput2.dirty || ctx_r0.f.iCDInput2.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput2\"] == null ? null : ctx_r0.f[\"iCDInput2\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput2\"] == null ? null : ctx_r0.f[\"iCDInput2\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput2\"] == null ? null : ctx_r0.f[\"iCDInput2\"].hasError(\"continued\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput2\"] == null ? null : ctx_r0.f[\"iCDInput2\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes3)(\"typeahead\", ctx_r0.icdSearch_3)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(101, _c0, (ctx_r0.f.iCDInput3 == null ? null : ctx_r0.f.iCDInput3.invalid) && (ctx_r0.f.iCDInput3.dirty || ctx_r0.f.iCDInput3.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput3\"] == null ? null : ctx_r0.f[\"iCDInput3\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput3\"] == null ? null : ctx_r0.f[\"iCDInput3\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput3\"] == null ? null : ctx_r0.f[\"iCDInput3\"].hasError(\"continued\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput3\"] == null ? null : ctx_r0.f[\"iCDInput3\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes4)(\"typeahead\", ctx_r0.icdSearch_4)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(103, _c0, (ctx_r0.f.iCDInput4 == null ? null : ctx_r0.f.iCDInput4.invalid) && (ctx_r0.f.iCDInput4.dirty || ctx_r0.f.iCDInput4.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput4\"] == null ? null : ctx_r0.f[\"iCDInput4\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput4\"] == null ? null : ctx_r0.f[\"iCDInput4\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput4\"] == null ? null : ctx_r0.f[\"iCDInput4\"].hasError(\"continued\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput4\"] == null ? null : ctx_r0.f[\"iCDInput4\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes5)(\"typeahead\", ctx_r0.icdSearch_5)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(105, _c0, (ctx_r0.f.iCDInput5 == null ? null : ctx_r0.f.iCDInput5.invalid) && (ctx_r0.f.iCDInput5.dirty || ctx_r0.f.iCDInput5.touched)))(\"virtualScroll\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput5\"] == null ? null : ctx_r0.f[\"iCDInput5\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput5\"] == null ? null : ctx_r0.f[\"iCDInput5\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput5\"] == null ? null : ctx_r0.f[\"iCDInput5\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput5\"] == null ? null : ctx_r0.f[\"iCDInput5\"].hasError(\"continued\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes6)(\"typeahead\", ctx_r0.icdSearch_6)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(107, _c0, (ctx_r0.f.iCDInput6 == null ? null : ctx_r0.f.iCDInput6.invalid) && (ctx_r0.f.iCDInput6.dirty || ctx_r0.f.iCDInput6.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput6\"] == null ? null : ctx_r0.f[\"iCDInput6\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput6\"] == null ? null : ctx_r0.f[\"iCDInput6\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput6\"] == null ? null : ctx_r0.f[\"iCDInput6\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput6\"] == null ? null : ctx_r0.f[\"iCDInput6\"].hasError(\"continued\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes7)(\"typeahead\", ctx_r0.icdSearch_7)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(109, _c0, (ctx_r0.f.iCDInput7 == null ? null : ctx_r0.f.iCDInput7.invalid) && (ctx_r0.f.iCDInput7.dirty || ctx_r0.f.iCDInput7.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput7\"] == null ? null : ctx_r0.f[\"iCDInput7\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput7\"] == null ? null : ctx_r0.f[\"iCDInput7\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput7\"] == null ? null : ctx_r0.f[\"iCDInput7\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput7\"] == null ? null : ctx_r0.f[\"iCDInput7\"].hasError(\"continued\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes8)(\"typeahead\", ctx_r0.icdSearch_8)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(111, _c0, (ctx_r0.f.iCDInput8 == null ? null : ctx_r0.f.iCDInput8.invalid) && (ctx_r0.f.iCDInput8.dirty || ctx_r0.f.iCDInput8.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput8\"] == null ? null : ctx_r0.f[\"iCDInput8\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput8\"] == null ? null : ctx_r0.f[\"iCDInput8\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput8\"] == null ? null : ctx_r0.f[\"iCDInput8\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput8\"] == null ? null : ctx_r0.f[\"iCDInput8\"].hasError(\"continued\"));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes9)(\"typeahead\", ctx_r0.icdSearch_9)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(113, _c0, (ctx_r0.f.iCDInput9 == null ? null : ctx_r0.f.iCDInput9.invalid) && (ctx_r0.f.iCDInput9.dirty || ctx_r0.f.iCDInput9.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput9\"] == null ? null : ctx_r0.f[\"iCDInput9\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput9\"] == null ? null : ctx_r0.f[\"iCDInput9\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput9\"] == null ? null : ctx_r0.f[\"iCDInput9\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput9\"] == null ? null : ctx_r0.f[\"iCDInput9\"].hasError(\"continued\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes10)(\"typeahead\", ctx_r0.icdSearch_10)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(115, _c0, (ctx_r0.f.iCDInput10 == null ? null : ctx_r0.f.iCDInput10.invalid) && (ctx_r0.f.iCDInput10.dirty || ctx_r0.f.iCDInput10.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput10\"] == null ? null : ctx_r0.f[\"iCDInput10\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput10\"] == null ? null : ctx_r0.f[\"iCDInput10\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput10\"] == null ? null : ctx_r0.f[\"iCDInput10\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput10\"] == null ? null : ctx_r0.f[\"iCDInput10\"].hasError(\"continued\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes11)(\"typeahead\", ctx_r0.icdSearch_11)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(117, _c0, (ctx_r0.f.iCDInput11 == null ? null : ctx_r0.f.iCDInput11.invalid) && (ctx_r0.f.iCDInput11.dirty || ctx_r0.f.iCDInput11.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput11\"] == null ? null : ctx_r0.f[\"iCDInput11\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput11\"] == null ? null : ctx_r0.f[\"iCDInput11\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput11\"] == null ? null : ctx_r0.f[\"iCDInput11\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput11\"] == null ? null : ctx_r0.f[\"iCDInput11\"].hasError(\"continued\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", ctx_r0.allIcdCodes12)(\"typeahead\", ctx_r0.icdSearch_12)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(119, _c0, (ctx_r0.f.iCDInput12 == null ? null : ctx_r0.f.iCDInput12.invalid) && (ctx_r0.f.iCDInput12.dirty || ctx_r0.f.iCDInput12.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput12\"] == null ? null : ctx_r0.f[\"iCDInput12\"].hasError(\"atleast\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput12\"] == null ? null : ctx_r0.f[\"iCDInput12\"].hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput12\"] == null ? null : ctx_r0.f[\"iCDInput12\"].hasError(\"invalidIcd\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"iCDInput12\"] == null ? null : ctx_r0.f[\"iCDInput12\"].hasError(\"continued\"));\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 48);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r103 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r103.f.iCDInput1.value && ctx_r103.f.iCDInput1.value.length > 0 ? ctx_r103.f.iCDInput1.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 50);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r105 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r105.f.iCDInput2.value && ctx_r105.f.iCDInput2.value.length > 0 ? ctx_r105.f.iCDInput2.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 51);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r107 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r107.f.iCDInput3.value && ctx_r107.f.iCDInput3.value.length > 0 ? ctx_r107.f.iCDInput3.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 52);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r109 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r109.f.iCDInput4.value && ctx_r109.f.iCDInput4.value.length > 0 ? ctx_r109.f.iCDInput4.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 53);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r111 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r111.f.iCDInput5.value && ctx_r111.f.iCDInput5.value.length > 0 ? ctx_r111.f.iCDInput5.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 54);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r113 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r113.f.iCDInput6.value && ctx_r113.f.iCDInput6.value.length > 0 ? ctx_r113.f.iCDInput6.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 55);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r115 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r115.f.iCDInput7.value && ctx_r115.f.iCDInput7.value.length > 0 ? ctx_r115.f.iCDInput7.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 56);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r117 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r117.f.iCDInput8.value && ctx_r117.f.iCDInput8.value.length > 0 ? ctx_r117.f.iCDInput8.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 57);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r119 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r119.f.iCDInput9.value && ctx_r119.f.iCDInput9.value.length > 0 ? ctx_r119.f.iCDInput9.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 58);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r121 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r121.f.iCDInput10.value && ctx_r121.f.iCDInput10.value.length > 0 ? ctx_r121.f.iCDInput10.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 59);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r123 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r123.f.iCDInput11.value && ctx_r123.f.iCDInput11.value.length > 0 ? ctx_r123.f.iCDInput11.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_input_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 60);\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_span_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r125 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r125.f.iCDInput12.value && ctx_r125.f.iCDInput12.value.length > 0 ? ctx_r125.f.iCDInput12.value : \"-\");\n  }\n}\n\nfunction DiagnosisNatureComponent_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"p\", 5);\n    i0.ɵɵtext(4, \"21. Diagnosis Or Nature Of Illness Or Injury. (Relate A-L To Service Line Below (24E)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 6)(6, \"div\", 9);\n    i0.ɵɵelement(7, \"input\", 33);\n    i0.ɵɵelementStart(8, \"label\", 11);\n    i0.ɵɵtext(9, \"ICD9\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 9);\n    i0.ɵɵelement(11, \"input\", 34);\n    i0.ɵɵelementStart(12, \"label\", 13);\n    i0.ɵɵtext(13, \"ICD10\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"div\", 14)(15, \"div\", 4)(16, \"label\", 16);\n    i0.ɵɵtext(17, \"A(1)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 4)(19, \"label\", 16);\n    i0.ɵɵtext(20, \"B(2)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 4)(22, \"label\", 16);\n    i0.ɵɵtext(23, \"C(3)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 4)(25, \"label\", 16);\n    i0.ɵɵtext(26, \"D(4)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 14)(28, \"div\", 4);\n    i0.ɵɵtemplate(29, DiagnosisNatureComponent_section_2_input_29_Template, 1, 0, \"input\", 35);\n    i0.ɵɵtemplate(30, DiagnosisNatureComponent_section_2_span_30_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 4);\n    i0.ɵɵtemplate(32, DiagnosisNatureComponent_section_2_input_32_Template, 1, 0, \"input\", 37);\n    i0.ɵɵtemplate(33, DiagnosisNatureComponent_section_2_span_33_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 4);\n    i0.ɵɵtemplate(35, DiagnosisNatureComponent_section_2_input_35_Template, 1, 0, \"input\", 38);\n    i0.ɵɵtemplate(36, DiagnosisNatureComponent_section_2_span_36_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 4);\n    i0.ɵɵtemplate(38, DiagnosisNatureComponent_section_2_input_38_Template, 1, 0, \"input\", 39);\n    i0.ɵɵtemplate(39, DiagnosisNatureComponent_section_2_span_39_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 3)(41, \"div\", 4)(42, \"label\", 16);\n    i0.ɵɵtext(43, \"E(5)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 4)(45, \"label\", 16);\n    i0.ɵɵtext(46, \"F(6)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 4)(48, \"label\", 16);\n    i0.ɵɵtext(49, \"G(7)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 4)(51, \"label\", 16);\n    i0.ɵɵtext(52, \"H(8)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 14)(54, \"div\", 4);\n    i0.ɵɵtemplate(55, DiagnosisNatureComponent_section_2_input_55_Template, 1, 0, \"input\", 40);\n    i0.ɵɵtemplate(56, DiagnosisNatureComponent_section_2_span_56_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 4);\n    i0.ɵɵtemplate(58, DiagnosisNatureComponent_section_2_input_58_Template, 1, 0, \"input\", 41);\n    i0.ɵɵtemplate(59, DiagnosisNatureComponent_section_2_span_59_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\", 4);\n    i0.ɵɵtemplate(61, DiagnosisNatureComponent_section_2_input_61_Template, 1, 0, \"input\", 42);\n    i0.ɵɵtemplate(62, DiagnosisNatureComponent_section_2_span_62_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 4);\n    i0.ɵɵtemplate(64, DiagnosisNatureComponent_section_2_input_64_Template, 1, 0, \"input\", 43);\n    i0.ɵɵtemplate(65, DiagnosisNatureComponent_section_2_span_65_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 3)(67, \"div\", 4)(68, \"label\", 16);\n    i0.ɵɵtext(69, \"I(9)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 4)(71, \"label\", 16);\n    i0.ɵɵtext(72, \"J(10)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 4)(74, \"label\", 16);\n    i0.ɵɵtext(75, \"K(11)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(76, \"div\", 4)(77, \"label\", 16);\n    i0.ɵɵtext(78, \"L(12)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(79, \"div\", 14)(80, \"div\", 4);\n    i0.ɵɵtemplate(81, DiagnosisNatureComponent_section_2_input_81_Template, 1, 0, \"input\", 44);\n    i0.ɵɵtemplate(82, DiagnosisNatureComponent_section_2_span_82_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"div\", 4);\n    i0.ɵɵtemplate(84, DiagnosisNatureComponent_section_2_input_84_Template, 1, 0, \"input\", 45);\n    i0.ɵɵtemplate(85, DiagnosisNatureComponent_section_2_span_85_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"div\", 4);\n    i0.ɵɵtemplate(87, DiagnosisNatureComponent_section_2_input_87_Template, 1, 0, \"input\", 46);\n    i0.ɵɵtemplate(88, DiagnosisNatureComponent_section_2_span_88_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(89, \"div\", 4);\n    i0.ɵɵtemplate(90, DiagnosisNatureComponent_section_2_input_90_Template, 1, 0, \"input\", 47);\n    i0.ɵɵtemplate(91, DiagnosisNatureComponent_section_2_span_91_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(29);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n  }\n}\n\nexport let DiagnosisNatureComponent = /*#__PURE__*/(() => {\n  class DiagnosisNatureComponent {\n    constructor(icdform, ICDService, dialog, spinner, cdRef, subjectService, cacheService) {\n      this.icdform = icdform;\n      this.ICDService = ICDService;\n      this.dialog = dialog;\n      this.spinner = spinner;\n      this.cdRef = cdRef;\n      this.subjectService = subjectService;\n      this.cacheService = cacheService;\n      this.minimumcharacterTohitAPI = 3;\n      this.allIcdCodes1 = [];\n      this.allIcdCodes2 = [];\n      this.allIcdCodes3 = [];\n      this.allIcdCodes4 = [];\n      this.allIcdCodes5 = [];\n      this.allIcdCodes6 = [];\n      this.allIcdCodes7 = [];\n      this.allIcdCodes8 = [];\n      this.allIcdCodes9 = [];\n      this.allIcdCodes10 = [];\n      this.allIcdCodes11 = [];\n      this.allIcdCodes12 = [];\n      this.icdCodes = [];\n      this.getICDCountData = new EventEmitter();\n      this.newSelectedICDsFromViewHistory = [];\n      this.emptyFormControlsIndex = [];\n      this.viewHistoryICDList = [];\n      this.icdFormCodes = ['iCDInput1', 'iCDInput2', 'iCDInput3', 'iCDInput4', 'iCDInput5', 'iCDInput6', 'iCDInput7', 'iCDInput8', 'iCDInput9', 'iCDInput10', 'iCDInput11', 'iCDInput12'];\n      this.icdSearch = new Subject();\n      this.destroy$ = new Subject();\n      this.selectedICDCodes = [];\n      this.icdValidatorsData = [];\n      this.icdSearch_1 = new Subject();\n      this.icdSearch_2 = new Subject();\n      this.icdSearch_3 = new Subject();\n      this.icdSearch_4 = new Subject();\n      this.icdSearch_5 = new Subject();\n      this.icdSearch_6 = new Subject();\n      this.icdSearch_7 = new Subject();\n      this.icdSearch_8 = new Subject();\n      this.icdSearch_9 = new Subject();\n      this.icdSearch_10 = new Subject();\n      this.icdSearch_11 = new Subject();\n      this.icdSearch_12 = new Subject();\n    }\n\n    ngOnInit() {\n      this.cacheServiceRefresh();\n      this.subjectRefresh();\n      this.spinner.show('child');\n      this.cdRef.detectChanges();\n      setTimeout(() => {\n        this.patchValue(); // Heavy synchronous form patch\n\n        this.spinner.hide('child');\n      }, 500);\n    }\n\n    createForm() {\n      this.icdInfo = this.icdform.group({\n        icdType: new FormControl(''),\n        iCDInput1: new FormControl('', Validators.required),\n        iCDInput2: new FormControl(''),\n        iCDInput3: new FormControl(''),\n        iCDInput4: new FormControl(''),\n        iCDInput5: new FormControl(''),\n        iCDInput6: new FormControl(''),\n        iCDInput7: new FormControl(''),\n        iCDInput8: new FormControl(''),\n        iCDInput9: new FormControl(''),\n        iCDInput10: new FormControl(''),\n        iCDInput11: new FormControl(''),\n        iCDInput12: new FormControl('')\n      });\n\n      if (!!this.claimFormData && !this.claimFormData.isEditClaim) {\n        this.icdInfo.controls['icdType'].disable();\n      }\n\n      return this.icdInfo;\n    }\n\n    patchValue() {\n      if (this.claimFormData.isAddClaim) {\n        //this.mapICDsToAllIcdCodes()\n        this.icdInfo = this.icdform.group({\n          icdType: this.serviceLineInfo.icdVersion,\n          iCDInput1: new FormControl(this.serviceLineInfo.icdCode1, Validators.required),\n          iCDInput2: this.serviceLineInfo.icdCode2,\n          iCDInput3: this.serviceLineInfo.icdCode3,\n          iCDInput4: this.serviceLineInfo.icdCode4,\n          iCDInput5: this.serviceLineInfo.icdCode5,\n          iCDInput6: this.serviceLineInfo.icdCode6,\n          iCDInput7: this.serviceLineInfo.icdCode7,\n          iCDInput8: this.serviceLineInfo.icdCode8,\n          iCDInput9: this.serviceLineInfo.icdCode9,\n          iCDInput10: this.serviceLineInfo.icdCode10,\n          iCDInput11: this.serviceLineInfo.icdCode11,\n          iCDInput12: this.serviceLineInfo.icdCode12\n        }); //this.patchValueAdd();\n\n        this.icdInfo.controls[\"icdType\"].disable();\n      } else if (this.claimFormData.isEditClaim) {\n        this.icdValidatorsData = this.claimData.icdValidator;\n        this.pushICDCodesFromICDViewHistoryAndSavedICDs(this.claimData?.icdDesciprtion); //this.mapICDsToAllIcdCodes()\n\n        if (this.claimFormData?.claimViewModel?.claimsProfessional837) {\n          this.fillICDData();\n        }\n      } else if (this.claimFormData.isViewClaim) {\n        this.fillICDData();\n      }\n    }\n\n    get f() {\n      return this.icdInfo.controls;\n    }\n\n    enableIcdControl(icdCode, index) {\n      if (icdCode) {\n        this.selectedICDCodesPush(icdCode);\n        let selectedValue = [];\n\n        for (let i = 1; i < 13; i++) {\n          if (index != i) if (this.icdInfo.controls['iCDInput' + i].value) {\n            selectedValue.push(this.icdInfo.controls['iCDInput' + i].value);\n          }\n        }\n\n        let diagnosiPointer = {\n          index: index,\n          validationRequired: false\n        };\n        this.getICDCountData.emit(diagnosiPointer);\n      } else {\n        let diagnosiPointer = {\n          index: index,\n          validationRequired: true\n        };\n        this.getICDCountData.emit(diagnosiPointer);\n      }\n\n      this.checkForDuplicateValues(this.icdInfo);\n      this.validateSequentialICDInputs(this.icdInfo);\n    }\n\n    getICDBySearch(searchTerm, index) {\n      if (!!searchTerm && searchTerm.length > 3) {\n        let request = {\n          searchString: searchTerm,\n          dos: this.claimFormData?.claimViewModel?.claimDosfrom ? new Date(this.claimFormData.claimViewModel.claimDosfrom) : new Date(this.claimFormData.profileMember?.dOSFrom),\n          icdVersion: this.icdInfo.controls.icdType.value\n        };\n        this.ICDService.fetchICDBySearch(request).subscribe(icdCodes => {\n          let icdCodesList = icdCodes || [];\n          this.icdCodes = icdCodesList.filter((v, i, a) => a.findIndex(t => t.text === v.text) === i);\n          this[`allIcdCodes_${index}`] = this.icdCodes;\n          this.mapICDsToAllIcdCodes();\n        });\n      }\n    }\n\n    fetchIcd() {// let icd = localStorage.getItem('allICD');\n      // if (!!icd) {\n      //   this.icdCodes = JSON.parse(JSLZString.decompress(icd));\n      //   this.fetchICD();\n      // } else {\n      //   this.ICDService.fetchAllIcd().subscribe((res) => {\n      //     if (!!res) {\n      //       this.icdCodes = res;\n      //       this.fetchICD();\n      //     }\n      //   })\n      // }\n    }\n\n    mapICDsToAllIcdCodes() {\n      for (let i = 1; i <= 12; i++) {\n        this[`allIcdCodes${i}`] = this.icdCodes;\n      }\n    }\n\n    patchControlEnable() {\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0102DiagnosisCode) {\n        this.icdInfo.controls['iCDInput' + 1].enable();\n        this.icdInfo.controls['iCDInput' + 2].enable();\n      }\n\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0202DiagnosisCode2) {\n        this.icdInfo.controls['iCDInput' + 3].enable();\n      }\n\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0302DiagnosisCode3) {\n        this.icdInfo.controls['iCDInput' + 4].enable();\n      }\n\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0402DiagnosisCode4) {\n        this.icdInfo.controls['iCDInput' + 5].enable();\n      }\n\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0502DiagnosisCode5) {\n        this.icdInfo.controls['iCDInput' + 6].enable();\n      }\n\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0602DiagnosisCode6) {\n        this.icdInfo.controls['iCDInput' + 7].enable();\n      }\n\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0702DiagnosisCode7) {\n        this.icdInfo.controls['iCDInput' + 8].enable();\n      }\n\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0802DiagnosisCode8) {\n        this.icdInfo.controls['iCDInput' + 9].enable();\n      }\n\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0902DiagnosisCode9) {\n        this.icdInfo.controls['iCDInput' + 10].enable();\n      }\n\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi1002DiagnosisCode10) {\n        this.icdInfo.controls['iCDInput' + 11].enable();\n      }\n\n      if (this.claimFormData?.claimViewModel?.claimsProfessional837?.hi1102DiagnosisCode11) {\n        this.icdInfo.controls['iCDInput' + 12].enable();\n      }\n    }\n\n    fillICDData() {\n      this.icdInfo = this.icdform.group({\n        icdType: this.claimFormData?.claimViewModel?.icdversion,\n        iCDInput1: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0102DiagnosisCode ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0102DiagnosisCode : null,\n        iCDInput2: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0202DiagnosisCode2 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0202DiagnosisCode2 : null,\n        iCDInput3: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0302DiagnosisCode3 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0302DiagnosisCode3 : null,\n        iCDInput4: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0402DiagnosisCode4 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0402DiagnosisCode4 : null,\n        iCDInput5: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0502DiagnosisCode5 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0502DiagnosisCode5 : null,\n        iCDInput6: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0602DiagnosisCode6 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0602DiagnosisCode6 : null,\n        iCDInput7: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0702DiagnosisCode7 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0702DiagnosisCode7 : null,\n        iCDInput8: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0802DiagnosisCode8 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0802DiagnosisCode8 : null,\n        iCDInput9: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0902DiagnosisCode9 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi0902DiagnosisCode9 : null,\n        iCDInput10: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi1002DiagnosisCode10 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi1002DiagnosisCode10 : null,\n        iCDInput11: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi1102DiagnosisCode11 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi1102DiagnosisCode11 : null,\n        iCDInput12: !!this.claimFormData?.claimViewModel?.claimsProfessional837?.hi1202DiagnosisCode12 ? this.claimFormData?.claimViewModel?.claimsProfessional837?.hi1202DiagnosisCode12 : null\n      });\n\n      for (let j = 1; j <= 13 - 1; j++) {//this.icdInfo.controls['iCDInput' + j].disable();\n      }\n\n      if (this.claimFormData.isEditClaim) {\n        this.icdInfo.controls['icdType'].disable();\n        this.validateICDCode();\n      } // this.patchControlEnable();\n\n\n      if (this.claimFormData.isViewClaim) {\n        this.icdInfo.disable();\n      }\n    }\n\n    validateForm() {\n      this.consecutiveCheck();\n\n      if (this.icdInfo.invalid) {\n        submitValidateAllFields.validateAllFields(this.icdInfo);\n        return false;\n      }\n\n      return true;\n    }\n\n    consecutiveCheck() {\n      if (!!!this.icdInfo.controls['iCDInput1'].value) {\n        this.icdInfo.controls['iCDInput1'].setErrors({\n          required: true\n        });\n      } // this.checkForDuplicateValues(this.icdInfo, [\n      //   'iCDInput1', 'iCDInput2', 'iCDInput3', 'iCDInput4','iCDInput5','iCDInput6','iCDInput7','iCDInput8','iCDInput9','iCDInput10','iCDInput11','iCDInput12'\n      // ]);\n      // this.validateSequentialICDInputs(this.icdInfo, [\n      //   'iCDInput1', 'iCDInput2', 'iCDInput3', 'iCDInput4','iCDInput5','iCDInput6','iCDInput7','iCDInput8','iCDInput9','iCDInput10','iCDInput11','iCDInput12'\n      // ]);\n\n    }\n\n    checkForDuplicateValues(formGroup) {\n      const valuesMap = new Map();\n      let keys = this.icdFormCodes; // Collect values and track control keys\n\n      keys.forEach(key => {\n        const value = formGroup.get(key)?.value?.trim();\n\n        if (value) {\n          if (valuesMap.has(value)) {\n            valuesMap.get(value)?.push(key);\n          } else {\n            valuesMap.set(value, [key]);\n          }\n        }\n      }); // Clear old errors first\n\n      keys.forEach(key => {\n        formGroup.get(key)?.setErrors(null);\n        formGroup.get(key)?.updateValueAndValidity({\n          onlySelf: true\n        });\n      }); // Set error on duplicates\n\n      for (let [value, duplicates] of valuesMap.entries()) {\n        if (duplicates.length > 1) {\n          duplicates.forEach(key => {\n            const control = formGroup.get(key);\n\n            if (key != 'iCDInput1') {\n              control?.setErrors({\n                dublicate: true\n              });\n            }\n          });\n        }\n      }\n    }\n\n    validateSequentialICDInputs(formGroup) {\n      let lastFilledIndex = -1;\n      let keys = this.icdFormCodes; // Step 1: Find the last non-empty ICD input\n\n      keys.forEach((key, index) => {\n        const value = formGroup.get(key)?.value?.trim();\n        if (value) lastFilledIndex = index;\n      });\n      if (lastFilledIndex === -1) return; // No values filled, no need to validate\n\n      let foundEmpty = false;\n      let orderInvalid = false; // Step 2: Clear previous errors\n\n      keys.forEach(key => {\n        const control = formGroup.get(key);\n\n        if (control?.hasError('continued')) {\n          const errors = { ...control.errors\n          };\n          delete errors['continued'];\n          control.setErrors(Object.keys(errors).length ? errors : null);\n        }\n      }); // Step 3: Validate for gaps up to lastFilledIndex\n\n      for (let i = 0; i <= lastFilledIndex; i++) {\n        const control = formGroup.get(keys[i]);\n        const value = control?.value?.trim();\n\n        if (!value) {\n          foundEmpty = true;\n        } else if (value && foundEmpty) {\n          orderInvalid = true;\n        }\n      } // Step 4: Set errors if invalid\n\n\n      if (orderInvalid) {\n        formGroup.setErrors({\n          orderInvalid: true\n        });\n\n        for (let i = 0; i <= lastFilledIndex; i++) {\n          const control = formGroup.get(keys[i]);\n\n          if (!control?.value?.trim()) {\n            const existingErrors = control.errors || {};\n            control.setErrors({ ...existingErrors,\n              continued: true\n            });\n            control?.markAsTouched();\n            control?.markAsDirty();\n          }\n        }\n      } else {\n        // Clear formGroup-level error if no issue\n        if (formGroup.hasError('continued')) {\n          const groupErrors = { ...formGroup.errors\n          };\n          delete groupErrors['continued'];\n          formGroup.setErrors(Object.keys(groupErrors).length ? groupErrors : null);\n        }\n      }\n    }\n\n    validateICDCode() {\n      if (this.claimFormData?.claimViewModel?.icdcodes) {\n        // this.ICDService.validatorICd(data).subscribe(res => {\n        let result = this.icdValidatorsData;\n\n        for (let i = 1; i <= 12; i++) {\n          if (this.icdInfo.controls['iCDInput' + i].value != null) {\n            if (this['allIcdCodes' + i].find(t => t.value === result[i - 1])) {\n              this.icdInfo.controls['iCDInput' + i].patchValue(result[i - 1]);\n            } else {\n              this.icdInfo.controls['iCDInput' + i].setErrors({\n                invalidIcd: true\n              });\n            }\n          }\n        }\n\n        this.validateFormControls(); //})\n      }\n    }\n\n    validateFormControls() {\n      if (this.icdInfo.invalid) {\n        submitValidateAllFields.validateAllFields(this.icdInfo);\n        return false;\n      }\n\n      return true;\n    }\n\n    icdViewHistory() {\n      let dosFromDate;\n      let subscriberId;\n\n      if (this.claimFormData.isEditClaim) {\n        if (this.formServiceLine.serviceLineInfo.value.serviceLines.length > 0) {\n          if (!!this.formServiceLine.serviceLineInfo.value.serviceLines[0].dateServiceFrom?._d) {\n            dosFromDate = this.formServiceLine.serviceLineInfo.value.serviceLines[0].dateServiceFrom._d;\n          } else {\n            dosFromDate = this.formServiceLine.serviceLineInfo.value.serviceLines[0].dateServiceFrom;\n          }\n\n          subscriberId = this.claimFormData.claimViewModel.subscribeId;\n        }\n      } else if (this.claimFormData.isAddClaim) {\n        dosFromDate = this.claimFormData.profileMember.dOSFrom;\n        subscriberId = this.claimFormData.profileMember.subscriberID;\n      }\n\n      let request = {\n        subscriberId: subscriberId,\n        dos: dosFromDate,\n        icdVersion: this.icdInfo.controls.icdType.value\n      };\n      this.ICDService.getICDViewHistory(request).subscribe(resp => {\n        let icdList = [];\n        let icdCodesNonHistoryCount = 0;\n\n        if (resp.statusCode == 200 && (resp.content || []).length > 0) {\n          icdList = resp.content;\n          icdList = icdList.map(icd => ({ ...icd,\n            isSelected: false\n          }));\n\n          for (let i = 1; i <= 12; i++) {\n            let selectedICD = this.icdInfo.controls['iCDInput' + i].value;\n\n            if (!!selectedICD) {\n              icdList.filter(icdItem => icdItem.icd === selectedICD).forEach(icd => {\n                icd.isSelected = true;\n              });\n\n              if (icdList.filter(icdItem => icdItem.icd === selectedICD).length == 0) {\n                icdCodesNonHistoryCount = icdCodesNonHistoryCount + 1;\n              }\n            } else {\n              icdCodesNonHistoryCount = icdCodesNonHistoryCount;\n            }\n          }\n\n          this.viewHistoryICDList = icdList;\n          this.icdDialogOpen(icdCodesNonHistoryCount);\n        } else {\n          this.icdDialogOpen(0);\n        }\n      });\n    }\n\n    icdDialogOpen(icdCodesNonHistoryCount) {\n      let dialogRef = this.dialog.open(IcdViewHistoryComponent, {\n        height: this.viewHistoryICDList.length > 0 ? '650px' : '200px',\n        width: this.viewHistoryICDList.length > 0 ? '1100px' : '800px',\n        autoFocus: false,\n        restoreFocus: false,\n        maxHeight: '90vh',\n        panelClass: 'custom-dialog-containers',\n        data: {\n          icdVersion: this.icdInfo.controls.icdType.value,\n          icdCodesNonHistoryCount: icdCodesNonHistoryCount,\n          icdList: this.viewHistoryICDList\n        }\n      });\n      dialogRef.afterClosed().subscribe(data => {\n        if (!!data && (data.selectedICDs || []).length > 0) {\n          this.pushICDCodesFromICDViewHistoryAndSavedICDs(data.selectedICDs);\n          this.clearNonMatchedICdsFromFormControls(data.selectedICDs);\n        } else if (!!data && (data.selectedICDs || []).length == 0) {\n          this.clearNonMatchedICdsFromFormControls(data.selectedICDs);\n        }\n      });\n    }\n\n    patchAllICDsEmpty() {\n      for (let i = 1; i <= 12; i++) {\n        this.icdInfo.controls['iCDInput' + i].patchValue(null);\n      }\n    }\n\n    pushICDCodesFromICDViewHistoryAndSavedICDs(icdCodes) {\n      const newIcdCodes = icdCodes.map(icdItem => {\n        return {\n          text: icdItem.displayCode_v1,\n          value: icdItem.icd,\n          addDate: icdItem.add_date,\n          termDate: icdItem.term_date\n        };\n      });\n\n      if (newIcdCodes.length > 0) {\n        let icdCodes = this.icdCodes.length > 0 ? [...this.icdCodes, ...newIcdCodes] : newIcdCodes;\n        this.icdCodes = icdCodes.filter((v, i, a) => a.findIndex(t => t.text === v.text) === i);\n        this.mapICDsToAllIcdCodes();\n      }\n    }\n\n    selectedICDCodesPush(icdCode) {\n      if (!!icdCode && !!icdCode.text) {\n        this.selectedICDCodes = this.selectedICDCodes.length > 0 ? [...this.selectedICDCodes, icdCode] : [icdCode];\n      }\n    }\n\n    clearNonMatchedICdsFromFormControls(selectedICDS) {\n      let existingICDs = [];\n      this.emptyFormControlsIndex = [];\n\n      for (let i = 1; i <= 12; i++) {\n        let existingICD = this.icdInfo.controls['iCDInput' + i].value;\n        const isMatchedICDFound = selectedICDS.find(icdItem => icdItem.icd === existingICD);\n        const isVieWHistoryyICDItem = this.viewHistoryICDList.find(icd => icd.icd === existingICD);\n\n        if (!!isMatchedICDFound) {\n          existingICDs.push(isMatchedICDFound.icd);\n        } else if (!!isVieWHistoryyICDItem && !isMatchedICDFound) {\n          this.icdInfo.controls['iCDInput' + i].patchValue(null);\n          this.icdInfo.controls['iCDInput' + i];\n          this.emptyFormControlsIndex.push(i);\n        } else if (isMatchedICDFound == null && isVieWHistoryyICDItem == null && !!existingICD) {\n          existingICDs.push(existingICD);\n        } else {\n          this.emptyFormControlsIndex.push(i);\n        }\n      }\n\n      this.newSelectedICDsFromViewHistory = selectedICDS.filter(icdItem => !existingICDs.some(icd => icdItem.icd === icd));\n      this.patchSelectedICDInfo();\n    }\n\n    patchSelectedICDInfo() {\n      for (var i = 0; i < this.newSelectedICDsFromViewHistory.length; i++) {\n        if (this.newSelectedICDsFromViewHistory.length <= this.emptyFormControlsIndex.length) {\n          this.icdInfo.controls['iCDInput' + this.emptyFormControlsIndex[i]].patchValue(this.newSelectedICDsFromViewHistory[i].icd);\n        }\n      }\n    }\n\n    icdHandleChange(ev) {\n      if (!!ev.target.value) {\n        this.cptWarningAlert();\n      }\n    }\n\n    cptWarningAlert() {\n      const confirmPopupModel = new ConfirmPopupModel();\n      confirmPopupModel.headerTitle = 'Alert';\n      confirmPopupModel.bodyDescription = 'Changing this field will change the current ICD Version and might cause you to loose the current ICD Codes !';\n      confirmPopupModel.confirmBtnTitle = 'Proceed';\n      confirmPopupModel.cancelBtnTtile = 'Cancel';\n      let dialogRef = this.dialog.open(ConfirmMessagePopupComponent, {\n        height: '200px',\n        width: '800px',\n        autoFocus: false,\n        restoreFocus: false,\n        maxHeight: '90vh',\n        panelClass: 'custom-dialog-containers',\n        data: {\n          confirmPopupModel: confirmPopupModel\n        }\n      });\n      dialogRef.afterClosed().subscribe(data => {\n        if (!!data && data.isProceed) {\n          this.patchAllICDsEmpty();\n        } else {\n          let icdValue = '';\n\n          if (this.icdInfo.controls.icdType.value === 'ICD9') {\n            icdValue = 'ICD10';\n          } else {\n            icdValue = 'ICD9';\n          }\n\n          this.icdInfo.controls[\"icdType\"].patchValue(icdValue);\n        }\n      });\n    }\n\n    subjectRefresh() {\n      this.icdSearch_1.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 1);\n      });\n      this.icdSearch_2.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 2);\n      });\n      this.icdSearch_3.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 3);\n      });\n      this.icdSearch_4.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 4);\n      });\n      this.icdSearch_5.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 5);\n      });\n      this.icdSearch_6.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 6);\n      });\n      this.icdSearch_7.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 7);\n      });\n      this.icdSearch_8.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 8);\n      });\n      this.icdSearch_9.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 9);\n      });\n      this.icdSearch_10.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 10);\n      });\n      this.icdSearch_11.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 11);\n      });\n      this.icdSearch_12.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm, 12);\n      });\n    }\n\n    cacheServiceRefresh() {\n      if (!!this.cacheService.localStorageGetItem(LocalStorageKey.selectedICDCodesForCreateClaim)) {\n        this.icdCodes = this.cacheService.localStorageGetItem(LocalStorageKey.selectedICDCodesForCreateClaim);\n        this.mapICDsToAllIcdCodes();\n      }\n    }\n\n    ngOnDestroy() {\n      this.destroy$.next(true);\n      this.destroy$.unsubscribe();\n    }\n\n  }\n\n  DiagnosisNatureComponent.ɵfac = function DiagnosisNatureComponent_Factory(t) {\n    return new (t || DiagnosisNatureComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.GetAllICDCodeService), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i4.NgxSpinnerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.SubjectService), i0.ɵɵdirectiveInject(i6.CacheService));\n  };\n\n  DiagnosisNatureComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DiagnosisNatureComponent,\n    selectors: [[\"app-diagnosis-nature\"]],\n    inputs: {\n      serviceLineInfo: \"serviceLineInfo\",\n      claimFormData: \"claimFormData\",\n      formServiceLine: \"formServiceLine\",\n      claimData: \"claimData\"\n    },\n    outputs: {\n      getICDCountData: \"getICDCountData\"\n    },\n    decls: 3,\n    vars: 3,\n    consts: [[3, \"formGroup\"], [\"class\", \"21 bd\", 4, \"ngIf\"], [1, \"21\", \"bd\"], [1, \"row\", \"mt-2\"], [1, \"col\"], [1, \"form-title\"], [1, \"col\", \"radio-flex\"], [\"href\", \"javascript:void(0);\", \"id\", \"viewicdCodesHistory\", \"title\", \"View ICD History\", 1, \"view-history\", 3, \"click\"], [1, \"fa\", \"fa-history\"], [1, \"form-check\", \"form-check-inline\", \"radio-flex\"], [\"type\", \"radio\", \"formControlName\", \"icdType\", \"value\", \"ICD9\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"OutsideLabYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"icdType\", \"value\", \"ICD10\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"OutsideLabNo\", 1, \"create-claim-radio-labels\"], [1, \"row\"], [1, \"col-3\"], [\"for\", \"flexCheckDefault\", 1, \"create-claims-labels\"], [\"placeholder\", \"1. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput1\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"virtualScroll\", \"typeahead\", \"ngClass\", \"change\"], [\"assignto\", \"\"], [\"ng-option-tmp\", \"\", \"ng-label-tmp\", \"\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"placeholder\", \"2. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput2\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"placeholder\", \"3. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput3\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"placeholder\", \"4. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput4\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"placeholder\", \"5. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput5\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"placeholder\", \"6. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput6\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"placeholder\", \"7. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput7\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"placeholder\", \"8. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput8\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"placeholder\", \"9. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput9\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"placeholder\", \"10. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput10\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"placeholder\", \"11. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput11\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"placeholder\", \"12. ICD Code Type 3 letter\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"iCDInput12\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [1, \"invalid-feedback\"], [\"type\", \"radio\", \"formControlName\", \"icdType\", \"value\", \"ICD9\", 1, \"form-check-input\"], [\"type\", \"radio\", \"formControlName\", \"icdType\", \"value\", \"ICD10\", 1, \"form-check-input\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput1\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput2\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput3\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput4\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput5\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput6\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput7\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput8\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput9\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput10\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput11\", \"placeholder\", \"\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"iCDInput12\", \"placeholder\", \"\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"iCDInput1\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [1, \"form-control\"], [\"type\", \"text\", \"formControlName\", \"iCDInput2\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"iCDInput3\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"iCDInput4\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"iCDInput5\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"iCDInput6\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"iCDInput7\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"iCDInput8\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"iCDInput9\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"iCDInput10\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"iCDInput11\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"iCDInput12\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"]],\n    template: function DiagnosisNatureComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0);\n        i0.ɵɵtemplate(1, DiagnosisNatureComponent_section_1_Template, 155, 121, \"section\", 1);\n        i0.ɵɵtemplate(2, DiagnosisNatureComponent_section_2_Template, 92, 24, \"section\", 1);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.icdInfo);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isEditClaim || ctx.claimFormData.isAddClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.NgSelectComponent, i8.NgOptionTemplateDirective, i8.NgLabelTemplateDirective],\n    styles: [\".create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.search-icon-alignment[_ngcontent-%COMP%]{position:relative}.search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%], .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}.form-control[_ngcontent-%COMP%]{max-width:200px!important}.view-history[_ngcontent-%COMP%]{height:25px;padding:2px 3px;background-color:#0d6efd;color:#fff}.view-history[_ngcontent-%COMP%]:hover{background-color:#191970!important}.no-data[_ngcontent-%COMP%]{height:200px;width:800px}\"]\n  });\n  return DiagnosisNatureComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}