{"ast": null, "code": "import { COMMON_METHODS } from './common-static';\nimport * as i0 from \"@angular/core\";\n\nfunction CustomDateFilterComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtext(1, \" Please enter a valid date (4-digit year required) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport let CustomDateFilterComponent = /*#__PURE__*/(() => {\n  class CustomDateFilterComponent {\n    constructor() {\n      this.dateValue = '';\n      this.hasError = false;\n      this.validDate = null;\n    }\n\n    agInit(params) {\n      this.params = params;\n    }\n\n    onDateChange(value) {\n      this.dateValue = value;\n      this.validDate = COMMON_METHODS.validateDateForFilter(value);\n      this.hasError = value && !this.validDate; // Only trigger filter change if date is valid or empty\n\n      if (!value || this.validDate) {\n        this.params.filterChangedCallback();\n      }\n    }\n\n    isFilterActive() {\n      return !!this.validDate;\n    }\n\n    doesFilterPass(params) {\n      if (!this.validDate) return true;\n      const cellValue = COMMON_METHODS.validateDateForFilter(params.data.dosFrom);\n      if (!cellValue) return false;\n      return cellValue.toDateString() === this.validDate.toDateString();\n    }\n\n    getModel() {\n      return this.validDate ? {\n        dateFrom: this.validDate\n      } : null;\n    }\n\n    setModel(model) {\n      if (model && model.dateFrom) {\n        this.validDate = new Date(model.dateFrom);\n        this.dateValue = this.validDate.toISOString().split('T')[0];\n        this.hasError = false;\n      } else {\n        this.dateValue = '';\n        this.validDate = null;\n        this.hasError = false;\n      }\n    }\n\n  }\n\n  CustomDateFilterComponent.ɵfac = function CustomDateFilterComponent_Factory(t) {\n    return new (t || CustomDateFilterComponent)();\n  };\n\n  CustomDateFilterComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CustomDateFilterComponent,\n    selectors: [[\"custom-date-filter\"]],\n    decls: 3,\n    vars: 4,\n    consts: [[\"type\", \"date\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"invalid-feedback d-block\", 4, \"ngIf\"], [1, \"invalid-feedback\", \"d-block\"]],\n    template: function CustomDateFilterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"input\", 0);\n        i0.ɵɵlistener(\"ngModelChange\", function CustomDateFilterComponent_Template_input_ngModelChange_1_listener($event) {\n          return ctx.dateValue = $event;\n        })(\"ngModelChange\", function CustomDateFilterComponent_Template_input_ngModelChange_1_listener($event) {\n          return ctx.onDateChange($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(2, CustomDateFilterComponent_div_2_Template, 2, 0, \"div\", 1);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"is-invalid\", ctx.hasError);\n        i0.ɵɵproperty(\"ngModel\", ctx.dateValue);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasError);\n      }\n    },\n    styles: [\".invalid-feedback[_ngcontent-%COMP%]{font-size:12px;color:#dc3545;margin-top:4px}.is-invalid[_ngcontent-%COMP%]{border-color:#dc3545}\"]\n  });\n  return CustomDateFilterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}