{"ast": null, "code": "import { AckFiles997Component } from './ack-files997.component/ack-files997.component.component';\nimport { AckFiles999Component } from './ack-files999.component/ack-files999.component.component';\nimport { ClaimAckReport277Component } from './claim-ack-report277.component/claim-ack-report277.component.component';\nimport { Received835FilesComponent } from './era835.component/era835.component.component';\nimport { ProfessionalClaimsComponentComponent } from './professional-claims-component/professional-claims-component.component';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/file/file.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/tabs\";\nimport * as i5 from \"./professional-claims-component/professional-claims-component.component\";\nimport * as i6 from \"./ack-files999.component/ack-files999.component.component\";\nimport * as i7 from \"./ack-files997.component/ack-files997.component.component\";\nimport * as i8 from \"./claim-ack-report277.component/claim-ack-report277.component.component\";\nimport * as i9 from \"./era835.component/era835.component.component\";\n\nfunction FilesComponent_mat_tab_21_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"i\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tab_r1.count);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", tab_r1.name, \" \");\n  }\n}\n\nfunction FilesComponent_mat_tab_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-professional-claims-component\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FilesComponent_mat_tab_21_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-ack-files999-component\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FilesComponent_mat_tab_21_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-ack-files997-component\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FilesComponent_mat_tab_21_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-claim-ack-report277-component\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FilesComponent_mat_tab_21_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-era835-component\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FilesComponent_mat_tab_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-tab\", 17);\n    i0.ɵɵtemplate(1, FilesComponent_mat_tab_21_ng_template_1_Template, 5, 2, \"ng-template\", 18);\n    i0.ɵɵtemplate(2, FilesComponent_mat_tab_21_div_2_Template, 2, 0, \"div\", 19);\n    i0.ɵɵtemplate(3, FilesComponent_mat_tab_21_div_3_Template, 2, 0, \"div\", 19);\n    i0.ɵɵtemplate(4, FilesComponent_mat_tab_21_div_4_Template, 2, 0, \"div\", 19);\n    i0.ɵɵtemplate(5, FilesComponent_mat_tab_21_div_5_Template, 2, 0, \"div\", 19);\n    i0.ɵɵtemplate(6, FilesComponent_mat_tab_21_div_6_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.activeTabIndex == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.activeTabIndex == 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.activeTabIndex == 2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.activeTabIndex == 3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.activeTabIndex == 4);\n  }\n}\n\nexport let FilesComponent = /*#__PURE__*/(() => {\n  class FilesComponent {\n    constructor(fileService) {\n      this.fileService = fileService;\n      this.minDate = new Date('1/1/2019');\n      this.today = new Date();\n      this.activeTabIndex = 0;\n      this.searchDates = {\n        fromDate: moment(new Date(new Date().getFullYear(), 0, 1)).format('yyyy-MM-DD'),\n        toDate: moment(new Date()).format('yyyy-MM-DD')\n      };\n      this.tabs = [{\n        tabType: 0,\n        name: 'Professional Claims',\n        count: null,\n        selector: ProfessionalClaimsComponentComponent,\n        countVariable: 'fileCount_837'\n      }, {\n        tabType: 1,\n        name: '999 - Ack Files',\n        count: null,\n        selector: AckFiles999Component,\n        countVariable: 'fileCount_999'\n      }, {\n        tabType: 2,\n        name: '997 - Ack Files',\n        count: null,\n        selector: AckFiles997Component,\n        countVariable: 'fileCount_997'\n      }, {\n        tabType: 3,\n        name: 'Claims Ack Report',\n        count: null,\n        selector: ClaimAckReport277Component,\n        countVariable: ''\n      }, {\n        tabType: 4,\n        name: 'Electronic Remittance Advice',\n        count: null,\n        selector: Received835FilesComponent,\n        countVariable: 'fileCount_835'\n      }];\n    }\n\n    ngOnInit() {\n      this.getFiles();\n    }\n\n    setActiveTab(e) {\n      this.activeTabIndex = e.index;\n    }\n\n    checkIfSearchDisabled() {\n      if (!!this.searchDates.fromDate && this.searchDates.fromDate.length > 0 && !!this.searchDates.toDate && this.searchDates.toDate.length > 0) {\n        let fromDate = new Date(this.searchDates.fromDate);\n        fromDate.setHours(0, 0, 0, 0);\n        let toDate = new Date(this.searchDates.toDate);\n        toDate.setHours(0, 0, 0, 0);\n\n        if (fromDate.getTime() > toDate.getTime()) {\n          return true;\n        } else {\n          return false;\n        }\n      } else {\n        return true;\n      }\n    }\n\n    getToolTipForSearchButton() {\n      let result = '';\n\n      if (!!this.searchDates.fromDate && this.searchDates.fromDate.length > 0 && !!this.searchDates.toDate && this.searchDates.toDate.length > 0) {\n        let fromDate = new Date(this.searchDates.fromDate);\n        fromDate.setHours(0, 0, 0, 0);\n        let toDate = new Date(this.searchDates.toDate);\n        toDate.setHours(0, 0, 0, 0);\n\n        if (fromDate.getTime() > toDate.getTime()) {\n          result = \"'From Date' should be less than or equal to 'To Date'.\";\n        }\n      } else {\n        result = 'Please select From and To Date first';\n      }\n\n      return result;\n    }\n\n    getFiles() {\n      let requestBody = {\n        dateFrom: this.searchDates.fromDate,\n        dateTo: this.searchDates.toDate,\n        uuid: JSON.parse(localStorage.getItem('uuid')),\n        sortingColumn: \"default\"\n      };\n      this.fileService.getFilesByType(requestBody).subscribe(res => {\n        console.log(res);\n      });\n      this.getFileCount(requestBody);\n    }\n\n    getFileCount(requestBody) {\n      this.fileService.getFileCount(requestBody).subscribe(res => {\n        if (res.statusCode == 200 && !!res.content) {\n          let fileCount = res.content;\n\n          for (const item of this.tabs) {\n            if (item.countVariable.length > 0) {\n              item.count = fileCount[item.countVariable];\n            } else {\n              item.count = fileCount['fileCount_277'] + fileCount['fileCount_228'];\n            }\n          }\n        }\n      });\n    }\n\n  }\n\n  FilesComponent.ɵfac = function FilesComponent_Factory(t) {\n    return new (t || FilesComponent)(i0.ɵɵdirectiveInject(i1.FileService));\n  };\n\n  FilesComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FilesComponent,\n    selectors: [[\"app-files\"]],\n    decls: 22,\n    vars: 21,\n    consts: [[1, \"row\", \"MainFlexRow\"], [1, \"toggle-section\", \"col-md-12\"], [1, \"detail-progress-bx\", 2, \"padding\", \"22px\"], [1, \"row\", \"comment-templates\", \"mobile-col\", \"relative\", \"audit-tool\"], [1, \"col-4\", \"p-0\", 2, \"margin-right\", \"2%\"], [\"type\", \"date\", \"id\", \"from\", \"placeholder\", \"From\", \"placeholder\", \"MM-DD-YYYY\", 1, \"form-control-date\", 3, \"ngModel\", \"max\", \"min\", \"ngModelChange\"], [1, \"col-4\", \"p-0\"], [\"type\", \"date\", \"id\", \"from\", \"placeholder\", \"To\", \"placeholder\", \"MM-DD-YYYY\", 1, \"form-control-date\", 3, \"ngModel\", \"max\", \"min\", \"ngModelChange\"], [1, \"col-3\", 2, \"text-align\", \"left\", \"margin-left\", \"2%\"], [\"type\", \"button\", 1, \"submit-button-audit\", 3, \"disabled\", \"title\", \"click\"], [1, \"col-md-12\"], [1, \"row\", \"Full-Form\"], [1, \"row\", \"Form-Header-Row\"], [1, \"col-12\", \"p-0\"], [1, \"customtabs\", \"CustomCss\", \"mt-2\", 3, \"selectedTabChange\"], [1, \"container\"], [\"class\", \"container\", \"aria-label\", \"primary\", 4, \"ngFor\", \"ngForOf\"], [\"aria-label\", \"primary\", 1, \"container\"], [\"mat-tab-label\", \"\"], [4, \"ngIf\"], [1, \"col\", \"CustomCss\"], [1, \"count\"], [1, \"fa\", \"fa-file-o\", \"fileIcon\"]],\n    template: function FilesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"input\", 5);\n        i0.ɵɵlistener(\"ngModelChange\", function FilesComponent_Template_input_ngModelChange_5_listener($event) {\n          return ctx.searchDates.fromDate = $event;\n        });\n        i0.ɵɵpipe(6, \"date\");\n        i0.ɵɵpipe(7, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 6)(9, \"input\", 7);\n        i0.ɵɵlistener(\"ngModelChange\", function FilesComponent_Template_input_ngModelChange_9_listener($event) {\n          return ctx.searchDates.toDate = $event;\n        });\n        i0.ɵɵpipe(10, \"date\");\n        i0.ɵɵpipe(11, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 8)(13, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function FilesComponent_Template_button_click_13_listener() {\n          return ctx.getFiles();\n        });\n        i0.ɵɵtext(14, \"Search\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\", 11)(17, \"div\", 12)(18, \"div\", 13)(19, \"mat-tab-group\", 14);\n        i0.ɵɵlistener(\"selectedTabChange\", function FilesComponent_Template_mat_tab_group_selectedTabChange_19_listener($event) {\n          return ctx.setActiveTab($event);\n        });\n        i0.ɵɵelementContainerStart(20, 15);\n        i0.ɵɵtemplate(21, FilesComponent_mat_tab_21_Template, 7, 5, \"mat-tab\", 16);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()()()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(6, 9, ctx.today, \"yyyy-MM-dd\"));\n        i0.ɵɵpropertyInterpolate(\"min\", i0.ɵɵpipeBind2(7, 12, ctx.minDate, \"yyyy-MM-dd\"));\n        i0.ɵɵproperty(\"ngModel\", ctx.searchDates.fromDate);\n        i0.ɵɵadvance(4);\n        i0.ɵɵpropertyInterpolate(\"max\", i0.ɵɵpipeBind2(10, 15, ctx.today, \"yyyy-MM-dd\"));\n        i0.ɵɵpropertyInterpolate(\"min\", i0.ɵɵpipeBind2(11, 18, ctx.minDate, \"yyyy-MM-dd\"));\n        i0.ɵɵproperty(\"ngModel\", ctx.searchDates.toDate);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", ctx.checkIfSearchDisabled())(\"title\", ctx.getToolTipForSearchButton());\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.MatTabGroup, i4.MatTabLabel, i4.MatTab, i5.ProfessionalClaimsComponentComponent, i6.AckFiles999Component, i7.AckFiles997Component, i8.ClaimAckReport277Component, i9.Received835FilesComponent, i2.DatePipe],\n    styles: [\".MainFlexRow[_ngcontent-%COMP%]{flex-wrap:wrap;margin-top:10px;margin-right:0!important;margin-left:0!important}.Full-Form[_ngcontent-%COMP%]{margin-left:10px;padding:5px;opacity:1;background:#F4F7FC 0% 0% no-repeat padding-box}.Form-Header-Row[_ngcontent-%COMP%]{padding:0;background-color:#fff;align-items:center;box-shadow:0 0 8px #0000001a;border-top-right-radius:5px;border-top-left-radius:5px}.Form-Header[_ngcontent-%COMP%]{font-size:23px;justify-content:start;color:#272d3b;font-family:Poppins-SemiBold}.Form-Body[_ngcontent-%COMP%]{margin-right:5px;box-shadow:0 0 8px #0000001a;background-color:#fff;padding:5px}.FilesHeaderBtn[_ngcontent-%COMP%]{margin-right:5px;height:43px;font-size:11px;background-color:#ddd;padding:0;font-family:Poppins-Bold}.col-10[_ngcontent-%COMP%]{padding:0}.TextEnd[_ngcontent-%COMP%]{text-align:end}.btn[_ngcontent-%COMP%]:active{background-color:#00f!important;color:#fff!important}  .CustomCss .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-labels .mat-tab-label-content{padding:5px}  .CustomCss .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-labels .mat-tab-label-active{color:#fff!important;background-color:#0074bc!important;border:1px solid #0074bc}  .CustomCss .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-label{color:#617751!important;opacity:1;font-family:IBM Plex Sans;font-size:13px;font-weight:700;background-color:#fff!important;border:1px solid grey;border-top-right-radius:4px;border-top-left-radius:4px;margin-left:5px;height:37px!important}.fileIcon[_ngcontent-%COMP%]{margin-right:4px}.count[_ngcontent-%COMP%]{margin-right:5px;border:1px solid;padding:2px;background-color:#789;border-radius:25%;color:#fff;border-color:#789}  .mat-tab-list{background-color:transparent}  .CustomCss .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-labels .mat-tab-label-active .headerTotalCount{color:#1059e3!important;background-color:#fff!important}.toggle-section[_ngcontent-%COMP%]{background:#fff;padding:15px;border:1px solid #ced4da;border-radius:5px;margin-top:0!important;margin-bottom:.5%;width:97%;margin-left:1rem}.detail-progress-bx[_ngcontent-%COMP%]{background:#f8f8f8;padding:15px;border:1px solid #ced4da;border-radius:5px}.submit-button-audit[_ngcontent-%COMP%]{width:200px;color:#fff;background-color:#0074bc;border:1px solid #0074bc;border-radius:.25rem;outline:none;height:2rem}.form-control-date[_ngcontent-%COMP%]{display:block;width:100%;height:2rem;padding:.375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#495057;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;border-radius:.25rem}button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:auto}.form-control-date[_ngcontent-%COMP%]:focus{outline:0;box-shadow:none!important;color:#495057;background-color:#fff;border-color:#80bdff}\"]\n  });\n  return FilesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}