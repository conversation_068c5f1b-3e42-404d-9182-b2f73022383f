.Claims-Count {
    font-size: 20px;
    font-weight: bolder;
    font-family: "Poppins-SemiBold", Arial, Helvetica, sans-serif;
}

.table-top {
    margin: 5px 5px 5px 10px;
    display: flex;
    justify-content: space-between;
}

.table {
    margin-bottom: 0px !important;
    table-layout: auto !important;
    word-wrap: unset;
    overflow: scroll;
}

th {
    padding: 1rem 0.5rem !important;
    font-family: "Poppins-Bold";
    table-layout: auto !important;
    font-size: 12px;
    color: black;
}

td {
    padding: 1rem 0.5rem !important;
    height: 5px;
    padding-top: 5px;
    padding-bottom: 5px;
    font-family: "Poppins";
    width: auto;
    font-size: 12px;
    text-align: left;
}

.Margin-Btwn-Heading-Icon {
    margin-right: 10px;
}

.pagination-control {
    padding-top: 15px;
    margin-right: 20px;
    float: right;
    display: flex;
}

select {
    outline: none;
    margin-left: 15px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    height: 28px;
}

.MarginLeftCheckbox{
    margin-left: 3px;
}

.MarginBottom{
    margin-bottom: 200px;
}

.claims-table {
    padding-top: 7px;
    border: 2px solid white;
    border-radius: 12px;
    // box-shadow: 0px 0px 8px #0000001a;
    background-color: white;
}

.claims-table-wrap {
    flex-wrap: wrap;
    margin-top: 10px;
    padding: 5px;
}


.border {
    border-left-color: transparent !important;
    border-right-color: transparent !important;
}

.AcceptBtn {
    padding: 1% 3% 0;
}

.btn {
    background: #0074bc 0% 0% no-repeat padding-box;
    border-radius: 8px;
    height: 31px;
    opacity: 1;
    color: white;
    padding-top: 4px;
    margin-bottom: 7px;
    font-family: "Poppins-SemiBold", Arial, Helvetica, sans-serif;
}

.SemiBold {
    font-family: "Poppins-SemiBold", Arial, Helvetica, sans-serif;
    text-align: center;
}


.word-break {
    word-break: keep-all;
}

.align-middle {
    vertical-align: middle;
}

input[type="checkbox"] {
    height: 16px;
    width: 16px;
    border-radius: 8px;
}
input[type="text"],
input[type="number"] {
  padding-left: 5px;
  border-radius: 6px;
  border: 1px solid #cccccc !important;
  height: 30px;
  outline: none;
  min-width: 40px;
  width: 75%;
  font-size: 10px;
  font-family: "Montserrat-Bold";
  margin-left: 5px;
}

// styling for input when on focus
input[type="text"]:focus,
input[type="number"]:focus {
  border: 1px solid #61abd4 !important;
}

.Margin-Right {
    margin-right: 5px;
}

.form-check {
    padding-top: 6px;
}

.Bottom-Border-None {
    border-bottom: white;
}

tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

.Tr-Hover-None:hover {
    background-color: white !important;
}

.AcceptBtn {
    vertical-align: middle;
}

::ng-deep .p-datatable-resizable .p-datatable-tbody > tr > td,
.p-datatable-resizable .p-datatable-tfoot > tr > td,
.p-datatable-resizable .p-datatable-thead > tr > th {
  text-overflow: ellipsis !important;
}

::ng-deep .mat-sort-header-container:not(.mat-sort-header-sorted) .mat-sort-header-arrow {
    opacity: 0.54 !important;
    transform: translateY(0px) !important;
}

::ng-deep .custom-frame {
    & .mat-checkbox-background,
    .mat-checkbox-frame {
        border-radius: 70% !important;
    }
}

th.mat-header-cell,
td.mat-cell,
td.mat-footer-cell {
    padding: 5px !important;
}

.IconStyle {
    cursor: pointer;
    position: relative;
    z-index: 1 !important;
}

object {
    position: relative;
    z-index: -1 !important;
}

.Faded{
    opacity: 0.5;
    pointer-events: none;
}

.LockIcon{
    height:15px!important;
}

.BorderLeft{
    border-left: 3px solid #11afbc !important;
}

::ng-deep .mat-checkbox-indeterminate.mat-primary .mat-checkbox-background,
.mat-checkbox-checked.mat-primary .mat-checkbox-background {
    background-color: #0074bc !important;
}

::ng-deep .mat-checkbox-checked .mat-checkbox-checkmark {
    background-color: #0074bc !important;
    border-radius: 50% !important;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-down:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-up-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}

::ng-deep .p-datatable .p-sortable-column .p-sortable-column-icon {
    margin-left: 0rem;
}

// style for checkbox and paginator of primeng
::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
    border-color: #0074bc !important;
    background: #0074bc !important;
}

::ng-deep.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: #0074bc !important;
}

::ng-deep .p-checkbox-box {
    border-radius: 50% !important;
}

::ng-deep .p-paginator {
    justify-content: flex-end !important;
}

.Width4 {
    width: 4%;
}

.Width9 {
    width: 9%;
}

.Width12 {
    width: 12%;
}

.Width15 {
    width: 15%;
}

.Width20 {
    width: 20%;
}

.Width14 {
    width: 14%;
}

.Width10 {
    width: 9%;
}

.Width16 {
    width: 16%;
}

.Width11 {
    width: 11%;
}

.Width24 {
    width: 27%;
}

.PositiveZIndex {
    z-index: 11 !important;
    margin: 1px 6px 0px 0px;
}

.LastTd {
    position: absolute;
    right: 0;
    background-color: white;
    height: 62.5px;
    width: 11%;
}

.LastTh {
  /*border-bottom: 2px solid #e3f2fd;
  text-align: center !important;
  position: absolute;
  right: 0;
  background-color: white;
  height: 62.5px;
  width: 7%;*/
}

.ThBtn {
  font-family: "Poppins-Bold";
  width: 8%;
  color: #5d5d5d !important;
}
