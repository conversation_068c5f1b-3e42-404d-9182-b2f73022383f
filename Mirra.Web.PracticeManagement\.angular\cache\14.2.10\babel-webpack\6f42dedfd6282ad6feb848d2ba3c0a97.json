{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as JSLZString from 'lz-string';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../UserAuthentication/UserAuthentication.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/shared/services/api.service\";\nexport let GetAllICDCodeService = /*#__PURE__*/(() => {\n  class GetAllICDCodeService {\n    constructor(userAuthenticationService, httpClient, apiService) {\n      this.userAuthenticationService = userAuthenticationService;\n      this.httpClient = httpClient;\n      this.apiService = apiService;\n    }\n\n    fetchICDBySearch(request) {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      return this.apiService.postCall(`/mdmdata/geticdsearch`, headerData, request).pipe(map(response => {\n        return response.content;\n      }));\n    }\n\n    fetchAllIcd() {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      let params = {\n        icdVersion: \"ICD10\"\n      };\n      return this.httpClient.get(environment.apiUrl + `/mdmdata/getallicd`, {\n        headers: new HttpHeaders({\n          'Accept': 'multipart/form-data',\n          'serializedHeaderData': JSON.stringify(headerData),\n          'Authorization': JSON.parse(localStorage.getItem('currentUser'))\n        }),\n        params: params\n      }).pipe(map(response => {\n        if (response.statusCode == 200) {\n          if (!!response.content) {\n            localStorage.setItem('allICD', JSLZString.compress(JSON.stringify(response.content)));\n          }\n\n          return response.content;\n        } else {\n          return null;\n        }\n      }));\n    }\n\n    validatorICd(data) {\n      let headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      return this.apiService.postCall(`/mdmdata/ICDValidorNot`, headerData, data).pipe(map(response => {\n        return response.content;\n      }));\n    }\n\n    getICDViewHistory(requestBody) {\n      const headerData = {\n        ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),\n        Username: JSON.parse(localStorage.getItem('email')),\n        IsLogRequired: this.userAuthenticationService.IsLogRequired,\n        IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired\n      };\n      return this.httpClient.post(`${environment.apiUrl}/mdmdata/GetICDCodeHistory`, requestBody, {\n        headers: new HttpHeaders({\n          \"Content-Type\": \"application/json\",\n          'serializedHeaderData': JSON.stringify(headerData)\n        })\n      });\n    }\n\n  }\n\n  GetAllICDCodeService.ɵfac = function GetAllICDCodeService_Factory(t) {\n    return new (t || GetAllICDCodeService)(i0.ɵɵinject(i1.UserAuthenticationService), i0.ɵɵinject(i2.HttpClient), i0.ɵɵinject(i3.HttpService));\n  };\n\n  GetAllICDCodeService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: GetAllICDCodeService,\n    factory: GetAllICDCodeService.ɵfac,\n    providedIn: 'root'\n  });\n  return GetAllICDCodeService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}