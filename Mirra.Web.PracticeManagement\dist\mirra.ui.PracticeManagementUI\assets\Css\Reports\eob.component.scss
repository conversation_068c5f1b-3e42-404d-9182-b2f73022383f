
.Form-Body {
    border: 1px solid #CCCCCC;
    border-radius: 8px;
    opacity: 1;
    box-shadow: 0px 0px 8px #0000001a;
    background-color: white;
    margin: 0px 5px 5px 5px;
    padding: 5px 11px 5px 5px;
}


// basic styling for all tds in table
.TableTd{
    padding: 1rem 0rem!important;
    color:#272D3B;
    font-size: 12px;
    font-family:'Poppins';
}

// background effect on hover on table row
.TableTr:hover{
    background-color:#E3F2FD;
}

// style for checkbox and paginator of primeng
::ng-deep .p-checkbox-box{
    border-radius: 50%!important;
}
::ng-deep .p-paginator {
    justify-content: flex-end!important;
}

/* basic setting for all input type */

input[type="text"]
{
    padding-left: 5px;
    border-radius:6px;
    border:1px solid #CCCCCC!important;
    height: 30px;
    outline:none;
    min-width: 40px;
    color: #000000;
    width: 7.5vw;
    font-size:10px;
    font-family: 'Poppins-Bold';
}

// styling for input when on focus
input[type="text"]:focus
{
    border:1px solid #61abd4!important;
}

// styling for table headers
th{
        padding-right: 0px!important;
        padding-left: 0px!important;
        background-color: white!important;
}


/* for setting second heading box*/

.Form-Header-2 {
    font-size: 18px;
    font-family: 'Poppins-SemiBold';
    background-color: white;
    padding: 10px;
    align-items: center;
}

.Form-Header-2-Heading{
    color: #0074BC;
}

p-table{
    margin-left: 0px!important;
}

.TextRight{
    text-align: end;
    justify-content: flex-end;
}

.MarginRight{
    margin-right: 10px;
}

.MarginLeft{
    margin-left: 10px;
}

.btn{
    height: 30px;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074BC;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
    border: none;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}
.ThBtn{
    font-family: "Poppins-Bold";
    width: 6%;
    color: #5d5d5d !important;

}

::ng-deep .p-datatable .p-sortable-column .p-sortable-column-icon{
    margin-left: 0px!important;
}

.Margin-Left-Td{
    margin-left: 50px;
}

.ellipse{
    background-color: #11AFBC;
    height: 15px;
    width: 15px;
    border-radius: 50%;
}

.ellipseLabel{
    font-family: 'Poppins-SemiBold';
    font-size: 12px;
    color: #272D3B;
    padding: 0 20px 0 5px;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC"!important;
    font-family: "FontAwesome"!important;
  }
  :host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon{
      color: initial!important;
  }
