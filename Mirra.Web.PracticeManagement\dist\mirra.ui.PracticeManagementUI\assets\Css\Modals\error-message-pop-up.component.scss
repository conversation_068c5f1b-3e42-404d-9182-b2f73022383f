.ErrorText{
    height: 50px;
    font-size: 19px;
    font-family: 'Poppins-Medium';
    /* vertical-align: middle; */
    text-align: center;
    margin-top: 5%;
}

.btn {
    border-color: #0074BC;
    box-shadow: 0px 0px 8px #0000001A;
    height: 35px;
    background-color: #0074BC;
    color: white;
    border-radius: 8px;
    font-size: 14px;
    font-family: "Poppins-SemiBold";
    width: max-content;
}

.Close-Icon1{
    cursor: pointer;
    padding: 7px 20px 0px 0px;
text-align: right;
}

.Remove{
    color: #0074BC;
    background-color: white;
    border: 1px solid #0074BC
}


.Remove:hover{
    color: white;
    background-color: #0074bc;
    border: solid 1px #0074bc;
}


.TriangleIcon{
    font-size:30px;
    color: #F08A14;
}

.AlertStyling{
    font-family: 'Poppins-SemiBold';
    font-size: 24px;
    padding-left: 1%;
}

.ErrorText1{
    height: 20px;
    font-size: 19px;
    font-family: 'Poppins-Medium';
    /* vertical-align: middle; */
    text-align: center;
}