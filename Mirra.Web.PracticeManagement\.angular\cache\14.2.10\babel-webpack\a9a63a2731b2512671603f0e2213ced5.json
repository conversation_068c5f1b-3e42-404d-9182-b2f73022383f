{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"src/app/services/rendering-provider/renderng-provider.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"ag-grid-angular\";\nimport * as i5 from \"@angular/material/button\";\nexport let RenderingProviderPopupComponent = /*#__PURE__*/(() => {\n  class RenderingProviderPopupComponent {\n    constructor(dialogRef, renderingProviderService) {\n      this.dialogRef = dialogRef;\n      this.renderingProviderService = renderingProviderService;\n      this.renderingProviderGridOptions = {};\n      this.paginationPageSize = 25;\n      this.renderingProviderCriteria = {};\n      this.renderingProviderResult = [];\n      this.destroy$ = new Subject();\n      this.rowData = [];\n      this.columnDefs = [{\n        headerName: 'Name',\n        width: 180,\n        resizable: true,\n        field: 'name',\n        tooltipField: 'name',\n        sortable: true\n      }, {\n        headerName: 'Address',\n        width: 180,\n        resizable: true,\n        field: 'providerFirstAddress',\n        tooltipField: \"providerFirstAddress\"\n      }, {\n        headerName: 'Tax ID',\n        width: 250,\n        resizable: true,\n        tooltipField: \"taxId\",\n        field: 'taxId'\n      }, {\n        headerName: 'Plan Name',\n        width: 200,\n        resizable: true,\n        field: 'planName',\n        tooltipField: \"planName\"\n      }, {\n        headerName: 'IPA Name',\n        width: 220,\n        resizable: true,\n        field: 'ipaName',\n        tooltipField: \"ipaName\"\n      }];\n      dialogRef.disableClose = true;\n    }\n\n    ngOnDestroy() {\n      this.destroy$.next(true);\n      this.destroy$.unsubscribe();\n    }\n\n    ngOnInit() {\n      this.renderingProviderGridOptions = {\n        rowHeight: 30,\n        getRowStyle: this.changeRowColor,\n        defaultColDef: {\n          lockVisible: true,\n          initialWidth: 100,\n          sortable: true,\n          filter: true,\n          floatingFilter: true,\n          suppressMenu: true,\n          wrapText: true,\n          autoHeight: true,\n          floatingFilterComponentParams: {\n            suppressFilterButton: true\n          }\n        },\n        style: {\n          width: '100%',\n          height: '100%',\n          flex: '1 1 auto'\n        },\n        pagination: {\n          enable: true,\n          size: 25\n        },\n        totalRowsCount: 0\n      };\n    }\n\n    changeRowColor(params) {\n      if (params.node.rowIndex % 2 === 0) {\n        return {\n          'background-color': '#f1f0f0'\n        };\n      } else {\n        return {\n          'background-color': 'white'\n        };\n      }\n    }\n\n    closeAll() {\n      this.dialogRef.close();\n    }\n\n    fetchRenderingProviderResult() {\n      this.renderingProviderCriteria.ipaCode = \",undefined,,IPA00000001,IPA00000006,IPA00000007,IPA00000008,IPA00000009,,IPA00000004,,IPA00000010,IPA00000011,IPA00000005,\";\n      this.renderingProviderCriteria.sortBy = \"ProviderNPI\";\n      this.renderingProviderCriteria.sortOrder = \"ASC\";\n      this.renderingProviderCriteria.uuid = String(JSON.parse(localStorage.getItem(\"uuid\")));\n      this.renderingProviderCriteria.username = JSON.parse(localStorage.getItem(\"email\"));\n      this.renderingProviderCriteria.index = 0;\n      this.renderingProviderCriteria.limit = 100;\n      this.renderingProviderService.fetchRenderingProviderByDOS(this.renderingProviderCriteria).pipe(takeUntil(this.destroy$)).subscribe(res => {\n        this.renderingProviderResult = res;\n        this.renderingProviderGridOptions.totalRowsCount = this.renderingProviderResult.length;\n      });\n    }\n\n    cancel() {\n      this.dialogRef.close();\n    }\n\n    onCellClicked(e) {\n      let arrayData = new Array();\n      arrayData.push(e.data);\n      this.dialogRef.close(arrayData);\n    }\n\n  }\n\n  RenderingProviderPopupComponent.ɵfac = function RenderingProviderPopupComponent_Factory(t) {\n    return new (t || RenderingProviderPopupComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(i2.RenderingProviderService));\n  };\n\n  RenderingProviderPopupComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RenderingProviderPopupComponent,\n    selectors: [[\"app-rendering-provider-popup\"]],\n    decls: 42,\n    vars: 15,\n    consts: [[1, \"dailog-header\"], [\"mat-dialog-title\", \"\"], [\"mat-button\", \"\", \"mat-dialog-title\", \"\", 1, \"dailog-close\", 3, \"click\"], [1, \"btn-flex\"], [1, \"row\"], [1, \"col-md-2\"], [\"for\", \"flexCheckDefault\", 1, \"form-check-label\", \"contract-labels\"], [\"type\", \"text\", \"placeholder\", \"Name\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"NPI\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"Tax ID\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"Plan Name\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"IPA Name\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [1, \"mt-2\"], [\"type\", \"button\", 1, \"btn-primary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn-common\", 3, \"click\"], [1, \"mt-4\"], [1, \"row\", \"grid-deal-search\"], [\"id\", \"memberSearchGrid\", 1, \"ag-theme-alpine\", \"ag-grid-view\", 3, \"rowData\", \"columnDefs\", \"accentedSort\", \"gridOptions\", \"pagination\", \"paginationPageSize\", \"overlayNoRowsTemplate\", \"paginationNumberFormatter\", \"enableCellTextSelection\", \"cellClicked\"]],\n    template: function RenderingProviderPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\")(2, \"h3\", 1);\n        i0.ɵɵtext(3, \"Rendering Providers\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\")(5, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function RenderingProviderPopupComponent_Template_button_click_5_listener() {\n          return ctx.closeAll();\n        });\n        i0.ɵɵtext(6, \"X\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"mat-dialog-content\")(8, \"div\", 3)(9, \"div\", 4)(10, \"div\", 5)(11, \"label\", 6);\n        i0.ɵɵtext(12, \"Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"input\", 7);\n        i0.ɵɵlistener(\"ngModelChange\", function RenderingProviderPopupComponent_Template_input_ngModelChange_13_listener($event) {\n          return ctx.renderingProviderCriteria.firstName = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 5)(15, \"label\", 6);\n        i0.ɵɵtext(16, \"NPI\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"input\", 8);\n        i0.ɵɵlistener(\"ngModelChange\", function RenderingProviderPopupComponent_Template_input_ngModelChange_17_listener($event) {\n          return ctx.renderingProviderCriteria.providerNPI = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 5)(19, \"label\", 6);\n        i0.ɵɵtext(20, \"Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"input\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function RenderingProviderPopupComponent_Template_input_ngModelChange_21_listener($event) {\n          return ctx.renderingProviderCriteria.address1 = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 5)(23, \"label\", 6);\n        i0.ɵɵtext(24, \"Tax ID\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"input\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function RenderingProviderPopupComponent_Template_input_ngModelChange_25_listener($event) {\n          return ctx.renderingProviderCriteria.taxId = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 5)(27, \"label\", 6);\n        i0.ɵɵtext(28, \"Plan Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"input\", 11);\n        i0.ɵɵlistener(\"ngModelChange\", function RenderingProviderPopupComponent_Template_input_ngModelChange_29_listener($event) {\n          return ctx.renderingProviderCriteria.planName = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"div\", 5)(31, \"label\", 6);\n        i0.ɵɵtext(32, \"IPA Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"input\", 12);\n        i0.ɵɵlistener(\"ngModelChange\", function RenderingProviderPopupComponent_Template_input_ngModelChange_33_listener($event) {\n          return ctx.renderingProviderCriteria.ipaName = $event;\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(34, \"div\", 13)(35, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function RenderingProviderPopupComponent_Template_button_click_35_listener() {\n          return ctx.fetchRenderingProviderResult();\n        });\n        i0.ɵɵtext(36, \"Search\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function RenderingProviderPopupComponent_Template_button_click_37_listener() {\n          return ctx.cancel();\n        });\n        i0.ɵɵtext(38, \"Clear\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(39, \"div\", 16)(40, \"div\", 17)(41, \"ag-grid-angular\", 18);\n        i0.ɵɵlistener(\"cellClicked\", function RenderingProviderPopupComponent_Template_ag_grid_angular_cellClicked_41_listener($event) {\n          return ctx.onCellClicked($event);\n        });\n        i0.ɵɵelementEnd()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngModel\", ctx.renderingProviderCriteria.firstName);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.renderingProviderCriteria.providerNPI);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.renderingProviderCriteria.address1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.renderingProviderCriteria.taxId);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.renderingProviderCriteria.planName);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.renderingProviderCriteria.ipaName);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"rowData\", ctx.renderingProviderResult)(\"columnDefs\", ctx.columnDefs)(\"accentedSort\", true)(\"gridOptions\", ctx.renderingProviderGridOptions)(\"pagination\", true)(\"paginationPageSize\", ctx.paginationPageSize)(\"overlayNoRowsTemplate\", ctx.overlayNoRowsTemplate)(\"paginationNumberFormatter\", ctx.renderingProviderGridOptions.pagination.formatter)(\"enableCellTextSelection\", true);\n      }\n    },\n    dependencies: [i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.AgGridAngular, i5.MatButton, i1.MatDialogTitle, i1.MatDialogContent],\n    styles: [\".grid-deal-search[_ngcontent-%COMP%]{width:100%;flex:1 1 auto;--bs-gutter-x: 0rem!important;height:40vh}.btn-flex[_ngcontent-%COMP%]{display:flex;justify-content:space-between}body[_ngcontent-%COMP%]{font-size:13px}\"]\n  });\n  return RenderingProviderPopupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}