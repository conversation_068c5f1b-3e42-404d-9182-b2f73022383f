import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SearchClaimComponent } from './search-claim.component';
import {  AgGridModule } from 'ag-grid-angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { InputDirectivesModule } from 'src/app/shared/directives/input-directives.module';
import { MatDialogModule } from '@angular/material/dialog';
import { MatRadioModule } from '@angular/material/radio';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { ClaimReportServiceService } from 'src/app/services/Dashboard/claim-report-service.service';
import { MatRangeDatePickerModule } from 'src/app/shared/components/form-inputs/mat-range-date-picker/mat-range-date-picker.module';
import { AllPlaceOfServicesService } from 'src/app/services/ClaimForm/all-place-of-services.service';
import { ExportFileComponent } from '../popups/search-claim/export-file/export-file.component';
import { ExportService } from 'src/app/services/export-service/export.service';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ClaimGridCustomComponent } from '../popups/search-claim/claim-grid-custom/claim-grid-custom.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ResubmissionMaxExclaimarkRenderer } from 'src/app/shared/Renderer/search-cliam/ResubmissionMaxExclaimarkRenderer';
import { GlobalSearchClaimIdActionRender } from 'src/app/shared/Renderer/search-cliam/GlobalSearchClaimIdActionRenderer';
import { EditClaimActionRenderer } from 'src/app/shared/Renderer/search-cliam/EditClaimActionRenderer';
import { MasterdataService } from 'src/app/services/Shared/masterdata.service';
import { AcceptedClaimLockerRenderer } from 'src/app/shared/Renderer/search-cliam/acceptedClaimLockerRenderer';
import { SaveReportComponent } from './save-report/save-report.component';
import { CustomDateFilterModule } from '../../shared/components/custom-date-filter/custom-date-filter.module';




@NgModule({
  declarations: [
    SearchClaimComponent,
    ExportFileComponent,
    SaveReportComponent,
    ClaimGridCustomComponent,
    ResubmissionMaxExclaimarkRenderer,
    GlobalSearchClaimIdActionRender,
    EditClaimActionRenderer,
    AcceptedClaimLockerRenderer,

  ],
  imports: [

    CommonModule,
    ReactiveFormsModule,
    MatRadioModule,
    MatTooltipModule,
    AgGridModule,
    NgSelectModule,
    FormsModule,
    InputDirectivesModule,
    MatDialogModule,
    NgMultiSelectDropDownModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatRangeDatePickerModule,
    DragDropModule,
    MatSidenavModule,
    MatCheckboxModule,
    CustomDateFilterModule
  ],
  providers:[ClaimReportServiceService,AllPlaceOfServicesService,ExportService,MasterdataService]
})
export class SearchClaimModule { }
