{"ast": null, "code": "import { animate, state, style, transition, trigger } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/services/subject.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/list\";\n\nfunction MenuListItemComponent_p_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.item.displayName);\n  }\n}\n\nfunction MenuListItemComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵelement(1, \"span\", 5);\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \" expand_more \");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"@indicatorRotate\", ctx_r1.expanded ? \"expanded\" : \"collapsed\");\n  }\n}\n\nfunction MenuListItemComponent_div_5_app_menu_list_item_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-menu-list-item\", 7);\n  }\n\n  if (rf & 2) {\n    const child_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"showDetails\", ctx_r3.showDetails)(\"item\", child_r4)(\"depth\", ctx_r3.depth + 1);\n  }\n}\n\nfunction MenuListItemComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, MenuListItemComponent_div_5_app_menu_list_item_1_Template, 1, 3, \"app-menu-list-item\", 6);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.item.children);\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"padding-left\": a0\n  };\n};\n\nconst _c1 = function (a0) {\n  return {\n    \"expanded\": a0\n  };\n};\n\nexport let MenuListItemComponent = /*#__PURE__*/(() => {\n  class MenuListItemComponent {\n    constructor(subService) {\n      this.subService = subService;\n      this.expanded = false;\n      this.ariaExpanded = this.expanded;\n      this.tabList = [];\n      this.selectedIndex = 0;\n      this.closedTabs = [];\n\n      if (this.depth === undefined) {\n        this.depth = 0;\n      }\n    }\n\n    ngOnInit() {}\n\n    CheckDuplicacy(name) {\n      const component = this.tabList.find(x => x.name === name);\n\n      if (component) {\n        return true;\n      }\n\n      return false;\n    }\n\n    SetActiveTab(arg0) {\n      for (let i = 0; i < this.tabList.length; i++) {\n        if (this.tabList[i].name === arg0) {\n          this.selectedIndex = i;\n        }\n      }\n    }\n\n    onItemSelected(item) {\n      if (!item.children || !item.children.length) {// this.router.navigate([item.route]);\n        // this.navService.closeNav();\n      }\n\n      if (item.children && item.children.length) {\n        this.expanded = !this.expanded;\n      }\n    }\n\n  }\n\n  MenuListItemComponent.ɵfac = function MenuListItemComponent_Factory(t) {\n    return new (t || MenuListItemComponent)(i0.ɵɵdirectiveInject(i1.SubjectService));\n  };\n\n  MenuListItemComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MenuListItemComponent,\n    selectors: [[\"app-menu-list-item\"]],\n    hostVars: 1,\n    hostBindings: function MenuListItemComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-expanded\", ctx.ariaExpanded);\n      }\n    },\n    inputs: {\n      item: \"item\",\n      depth: \"depth\",\n      showDetails: \"showDetails\"\n    },\n    decls: 6,\n    vars: 12,\n    consts: [[1, \"menu-list-item\", 3, \"ngStyle\", \"ngClass\", \"click\"], [\"style\", \"padding-right:50px;\", 4, \"ngIf\"], [\"fxFlex\", \"\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"padding-right\", \"50px\"], [\"fxFlex\", \"\"], [3, \"showDetails\", \"item\", \"depth\", 4, \"ngFor\", \"ngForOf\"], [3, \"showDetails\", \"item\", \"depth\"]],\n    template: function MenuListItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"mat-list-item\", 0);\n        i0.ɵɵlistener(\"click\", function MenuListItemComponent_Template_mat_list_item_click_0_listener() {\n          return ctx.onItemSelected(ctx.item);\n        });\n        i0.ɵɵelementStart(1, \"mat-icon\");\n        i0.ɵɵtext(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, MenuListItemComponent_p_3_Template, 2, 1, \"p\", 1);\n        i0.ɵɵtemplate(4, MenuListItemComponent_span_4_Template, 4, 1, \"span\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, MenuListItemComponent_div_5_Template, 2, 1, \"div\", 3);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(8, _c0, ctx.depth * 12 + \"px\"))(\"ngClass\", i0.ɵɵpureFunction1(10, _c1, ctx.expanded));\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"routeIcon\", ctx.showDetails);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate(ctx.item.iconName);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showDetails);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showDetails && ctx.item.children && ctx.item.children.length);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showDetails && ctx.expanded);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgStyle, i3.MatIcon, i4.MatListItem, MenuListItemComponent],\n    styles: [\"[_nghost-%COMP%]{display:flex;flex-direction:column;outline:none;width:100%}[_nghost-%COMP%]   .mat-list-item.active[_ngcontent-%COMP%]{background-color:#000}[_nghost-%COMP%]:hover > .mat-list-item[_ngcontent-%COMP%]:not(.expanded), [_nghost-%COMP%]:focus > .mat-list-item[_ngcontent-%COMP%]:not(.expanded){background-color:#00f!important}.mat-list-item[_ngcontent-%COMP%]{padding:8px 0;display:flex;width:auto}.mat-list-item[_ngcontent-%COMP%]   .mat-list-item-content[_ngcontent-%COMP%]{padding:0}.mat-list-item[_ngcontent-%COMP%]   .routeIcon[_ngcontent-%COMP%]{margin-right:10px}\"],\n    data: {\n      animation: [trigger('indicatorRotate', [state('collapsed', style({\n        transform: 'rotate(0deg)'\n      })), state('expanded', style({\n        transform: 'rotate(180deg)'\n      })), transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4,0.0,0.2,1)'))])]\n    }\n  });\n  return MenuListItemComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}