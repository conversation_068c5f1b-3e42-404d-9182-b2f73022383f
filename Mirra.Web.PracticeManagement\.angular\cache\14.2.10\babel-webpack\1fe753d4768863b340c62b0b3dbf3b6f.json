{"ast": null, "code": "import { SearchInsuredPopupComponent } from '../../popups/search-insured-popup/search-insured-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/icon\";\n\nfunction CapComponent_tr_871_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 2)(3, \"div\", 107)(4, \"div\", 108)(5, \"input\", 109);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_5_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.dynamicArray[i_r2].from = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 110)(7, \"input\", 111);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.dynamicArray[i_r2].from = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"td\")(9, \"div\", 2)(10, \"div\", 20)(11, \"input\", 111);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_11_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.dynamicArray[i_r2].to = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"td\")(13, \"div\", 2)(14, \"div\", 20)(15, \"input\", 112);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_15_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.dynamicArray[i_r2].poservice = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"td\")(17, \"div\", 2)(18, \"div\", 20)(19, \"select\", 113);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_select_ngModelChange_19_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.dynamicArray[i_r2].emg = $event);\n    });\n    i0.ɵɵelementStart(20, \"option\", 53);\n    i0.ɵɵtext(21, \"Open this select menu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"option\", 54);\n    i0.ɵɵtext(23, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\", 55);\n    i0.ɵɵtext(25, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"option\", 56);\n    i0.ɵɵtext(27, \"Three\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(28, \"td\")(29, \"div\", 2)(30, \"div\", 20)(31, \"input\", 114);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_31_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.dynamicArray[i_r2].cpt = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"td\")(33, \"div\", 2)(34, \"div\", 20)(35, \"input\", 115);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_35_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.dynamicArray[i_r2].modifier = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 20)(37, \"input\", 116);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_37_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.dynamicArray[i_r2].modifier = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 20)(39, \"input\", 117);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_39_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.dynamicArray[i_r2].modifier = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 20)(41, \"input\", 118);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_41_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.dynamicArray[i_r2].modifier = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"td\")(43, \"div\", 2)(44, \"div\", 20)(45, \"select\", 113);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_select_ngModelChange_45_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.dynamicArray[i_r2].dp = $event);\n    });\n    i0.ɵɵelementStart(46, \"option\", 53);\n    i0.ɵɵtext(47, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"option\", 54);\n    i0.ɵɵtext(49, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"option\", 55);\n    i0.ɵɵtext(51, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"option\", 56);\n    i0.ɵɵtext(53, \"Three\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 20)(55, \"select\", 113);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_select_ngModelChange_55_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.dynamicArray[i_r2].dp = $event);\n    });\n    i0.ɵɵelementStart(56, \"option\", 53);\n    i0.ɵɵtext(57, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"option\", 54);\n    i0.ɵɵtext(59, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"option\", 55);\n    i0.ɵɵtext(61, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"option\", 56);\n    i0.ɵɵtext(63, \"Three\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(64, \"div\", 20)(65, \"select\", 113);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_select_ngModelChange_65_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.dynamicArray[i_r2].dp = $event);\n    });\n    i0.ɵɵelementStart(66, \"option\", 53);\n    i0.ɵɵtext(67, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"option\", 54);\n    i0.ɵɵtext(69, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"option\", 55);\n    i0.ɵɵtext(71, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"option\", 56);\n    i0.ɵɵtext(73, \"Three\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(74, \"div\", 20)(75, \"select\", 113);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_select_ngModelChange_75_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.dynamicArray[i_r2].dp = $event);\n    });\n    i0.ɵɵelementStart(76, \"option\", 53);\n    i0.ɵɵtext(77, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"option\", 54);\n    i0.ɵɵtext(79, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"option\", 55);\n    i0.ɵɵtext(81, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"option\", 56);\n    i0.ɵɵtext(83, \"Three\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(84, \"td\")(85, \"div\", 2)(86, \"div\", 20)(87, \"input\", 119);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_87_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.dynamicArray[i_r2].uc = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(88, \"td\")(89, \"div\", 2)(90, \"div\", 20)(91, \"input\", 119);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_91_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.dynamicArray[i_r2].du = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(92, \"td\")(93, \"div\", 2)(94, \"div\", 20)(95, \"select\", 113);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_select_ngModelChange_95_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.dynamicArray[i_r2].um = $event);\n    });\n    i0.ɵɵelementStart(96, \"option\", 53);\n    i0.ɵɵtext(97, \"Open this select menu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"option\", 54);\n    i0.ɵɵtext(99, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"option\", 55);\n    i0.ɵɵtext(101, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"option\", 56);\n    i0.ɵɵtext(103, \"Three\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(104, \"td\")(105, \"div\", 2)(106, \"div\", 20)(107, \"select\", 113);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_select_ngModelChange_107_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.dynamicArray[i_r2].epsdt = $event);\n    });\n    i0.ɵɵelementStart(108, \"option\", 53);\n    i0.ɵɵtext(109, \"Open this select menu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(110, \"option\", 54);\n    i0.ɵɵtext(111, \"One\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(112, \"option\", 55);\n    i0.ɵɵtext(113, \"Two\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"option\", 56);\n    i0.ɵɵtext(115, \"Three\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(116, \"td\")(117, \"div\", 2)(118, \"div\", 20)(119, \"input\", 119);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_119_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.dynamicArray[i_r2].idqual = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(120, \"td\")(121, \"div\", 2)(122, \"div\", 120)(123, \"input\", 119);\n    i0.ɵɵlistener(\"ngModelChange\", function CapComponent_tr_871_Template_input_ngModelChange_123_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.dynamicArray[i_r2].rpid = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(124, \"div\", 121)(125, \"mat-icon\", 122);\n    i0.ɵɵlistener(\"click\", function CapComponent_tr_871_Template_mat_icon_click_125_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const i_r2 = restoredCtx.index;\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.deleteRow(i_r2));\n    });\n    i0.ɵɵtext(126, \"close\");\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const i_r2 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].from);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].from);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].to);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].poservice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].emg);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].cpt);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].modifier);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].modifier);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].modifier);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].modifier);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].dp);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].dp);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].dp);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].dp);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].uc);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].du);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].um);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].epsdt);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].idqual);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.dynamicArray[i_r2].rpid);\n  }\n}\n\nexport let CapComponent = /*#__PURE__*/(() => {\n  class CapComponent {\n    constructor(dialog) {\n      this.dialog = dialog;\n      this.dynamicArray = [];\n      this.newDynamic = {};\n    }\n\n    ngOnInit() {\n      this.newDynamic = {\n        from: \"\",\n        to: \"\",\n        poservice: \"\",\n        emg: \"\",\n        cpt: \"\",\n        modifier: \"\",\n        dp: \"\",\n        uc: \"\",\n        du: \"\",\n        um: \"\",\n        epsdt: \"\",\n        idqual: \"\",\n        rpid: \"\"\n      };\n      this.dynamicArray.push(this.newDynamic);\n    }\n\n    addRow() {\n      this.newDynamic = {\n        from: \"\",\n        to: \"\",\n        poservice: \"\",\n        emg: \"\",\n        cpt: \"\",\n        modifier: \"\",\n        dp: \"\",\n        uc: \"\",\n        du: \"\",\n        um: \"\",\n        epsdt: \"\",\n        idqual: \"\",\n        rpid: \"\"\n      };\n      this.dynamicArray.push(this.newDynamic);\n      console.log(this.dynamicArray);\n      return true;\n    }\n\n    deleteRow(index) {\n      if (this.dynamicArray.length == 1) {\n        return false;\n      } else {\n        this.dynamicArray.splice(index, 1);\n        return true;\n      }\n    }\n\n    searchInsuredId() {\n      this.dialog.open(SearchInsuredPopupComponent, {\n        height: '70%',\n        width: '80%',\n        panelClass: 'custom-dialog-containers'\n      });\n    }\n\n  }\n\n  CapComponent.ɵfac = function CapComponent_Factory(t) {\n    return new (t || CapComponent)(i0.ɵɵdirectiveInject(i1.MatDialog));\n  };\n\n  CapComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CapComponent,\n    selectors: [[\"app-cap\"]],\n    decls: 1238,\n    vars: 1,\n    consts: [[1, \"row\"], [1, \"col-md\", \"form-border\"], [1, \"row\", \"mt-2\"], [1, \"col-md\", \"claim-title\"], [1, \"col-md\"], [\"for\", \"flexCheckDefault\", 1, \"form-check-label\", \"contract-labels\"], [\"type\", \"text\", \"placeholder\", \"Valor Health Plan\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Payer ID\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Address Line - 1\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Address Line - 2\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"City\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"State\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Payer Zip\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-md-8\", \"form-border\"], [1, \"col-md-3\"], [1, \"form-title\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"radio\", \"name\", \"inlineRadioOptions\", \"id\", \"inlineRadio1\", \"value\", \"option1\", 1, \"form-check-input\"], [\"for\", \"inlineRadio1\", 1, \"form-check-label\"], [1, \"col-md-4\", \"form-border\"], [1, \"col\"], [1, \"search-icon-alignment\"], [\"type\", \"text\", \"placeholder\", \"Insure\\u2019d ID\", 1, \"form-control\", \"form-control-sm\"], [1, \"search-icons\", 3, \"click\"], [1, \"2\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Last Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"First Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Middle Name\", 1, \"form-control\", \"form-control-sm\"], [1, \"3\", \"bd\"], [\"type\", \"date\", \"name\", \"birthday\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"radio\", \"name\", \"inlineRadioOptions\", \"id\", \"inlineRadio2\", \"value\", \"option2\", 1, \"form-check-input\"], [\"for\", \"inlineRadio2\", 1, \"form-check-label\"], [1, \"5\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Stae\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Zip\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Telephone\", 1, \"form-control\", \"form-control-sm\"], [1, \"6\", \"bd\"], [1, \"8\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\"], [1, \"9\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Other Insured\\u2019s Policy Or Group Number\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-4\"], [1, \"col-8\"], [\"type\", \"text\", \"placeholder\", \"Reserved For NUCC Use\", 1, \"form-control\", \"form-control-sm\"], [1, \"10\", \"bd\"], [1, \"col-md-10\"], [1, \"col-md-2\"], [\"src\", \"../assets/icons/search-icon.svg\"], [\"type\", \"date\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\"], [1, \"\"], [1, \"14\", \"bd\"], [\"aria-label\", \"Default select example\", 1, \"form-select\", \"form-select-sm\", \"form-select\", \"form-select-sm-sm\"], [\"selected\", \"\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [1, \"15\", \"bd\"], [\"aria-label\", \"Default select example\", 1, \"form-select\", \"form-select-sm\"], [1, \"17\", \"bd\"], [1, \"bd\"], [1, \"col-1\"], [1, \"19\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Additional Claim Information\", 1, \"form-control\", \"form-control-sm\"], [1, \"21\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Resubmission Code\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Reference Number\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Prior Authorization Number\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup Address 1\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup Address 2\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup City\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Ambulance Pickup State\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Zip Code\", 1, \"form-control\", \"form-control-sm\"], [1, \"card\"], [1, \"24\"], [1, \"table-responsive\"], [1, \"table\", \"table-bordered\"], [\"colspan\", \"2\"], [\"scope\", \"col\"], [4, \"ngFor\", \"ngForOf\"], [\"action-flex\", \"\"], [\"disabled\", \"\", 1, \"btn-common-danger\"], [1, \"btn-primary\", 3, \"click\"], [\"type\", \"text\", \"placeholder\", \"Search\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Patient Account Number\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Total Charge\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Amount Paid\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Reserved For NUCC\", 1, \"form-control\", \"form-control-sm\"], [1, \"31\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\"], [1, \"row\", \"mt-2\", \"mt-3\"], [\"type\", \"text\", \"placeholder\", \"NPI\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Provider ID\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"CLIA\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Mammography Certificate\", 1, \"form-control\", \"form-control-sm\"], [1, \"32\", \"bd\"], [\"type\", \"text\", \"placeholder\", \"Facility Name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Address Line 1\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Address Line 2\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Facility ID\", 1, \"form-control\", \"form-control-sm\"], [1, \"33\"], [\"type\", \"text\", \"placeholder\", \"ZIP Code\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Taxonomy Code\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Provider Specialty\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Provider NPI\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Provider PIN\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Qual\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-2\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"value\", \"\", \"id\", \"flexCheckDefault\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-10\"], [\"type\", \"date\", \"name\", \"birthday\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"POS\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"aria-label\", \"Default select example\", 1, \"form-select\", \"form-select-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"CPT\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"A\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"B\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"C\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"D\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-md-9\", \"p-1\"], [1, \"col-md-3\", \"p-1\"], [2, \"color\", \"red\", 3, \"click\"]],\n    template: function CapComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵtext(4, \" Current Claim Status: \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"div\", 2)(6, \"div\", 3);\n        i0.ɵɵtext(7, \" Created on \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 2)(9, \"div\", 3);\n        i0.ɵɵtext(10, \" Created By \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 2)(12, \"div\", 4);\n        i0.ɵɵelement(13, \"label\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(14, \"div\", 1)(15, \"div\", 2)(16, \"div\", 3);\n        i0.ɵɵtext(17, \" Held On \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 2)(19, \"div\", 3);\n        i0.ɵɵtext(20, \" Held By \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(21, \"div\", 2)(22, \"div\", 3);\n        i0.ɵɵtext(23, \" Reason for holding \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"div\", 2)(25, \"div\", 3);\n        i0.ɵɵtext(26, \" Description \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(27, \"div\", 1)(28, \"div\", 2)(29, \"div\", 4)(30, \"label\", 5);\n        i0.ɵɵtext(31, \"Payer Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(32, \"input\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"div\", 4)(34, \"label\", 5);\n        i0.ɵɵtext(35, \"Payer ID\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(36, \"input\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(37, \"div\", 2)(38, \"div\", 4)(39, \"label\", 5);\n        i0.ɵɵtext(40, \"1st Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(41, \"input\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"div\", 2)(43, \"div\", 4)(44, \"label\", 5);\n        i0.ɵɵtext(45, \"2nd Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(46, \"input\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(47, \"div\", 2)(48, \"div\", 4)(49, \"label\", 5);\n        i0.ɵɵtext(50, \"City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(51, \"div\", 4)(52, \"label\", 5);\n        i0.ɵɵtext(53, \"State\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(54, \"div\", 4)(55, \"label\", 5);\n        i0.ɵɵtext(56, \"Payer Zip\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(57, \"div\", 0)(58, \"div\", 4);\n        i0.ɵɵelement(59, \"input\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"div\", 4);\n        i0.ɵɵelement(61, \"input\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"div\", 4);\n        i0.ɵɵelement(63, \"input\", 12);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(64, \"div\", 0)(65, \"div\", 13)(66, \"div\", 0)(67, \"div\", 14)(68, \"p\", 15);\n        i0.ɵɵtext(69, \"1. Medicare\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"div\", 16);\n        i0.ɵɵelement(71, \"input\", 17);\n        i0.ɵɵelementStart(72, \"label\", 18);\n        i0.ɵɵtext(73, \"Medicare\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(74, \"div\", 14)(75, \"p\", 15);\n        i0.ɵɵtext(76, \"Medicaid\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(77, \"div\", 16);\n        i0.ɵɵelement(78, \"input\", 17);\n        i0.ɵɵelementStart(79, \"label\", 18);\n        i0.ɵɵtext(80, \"Medicaid\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(81, \"div\", 14)(82, \"p\", 15);\n        i0.ɵɵtext(83, \"Tricare\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(84, \"div\", 16);\n        i0.ɵɵelement(85, \"input\", 17);\n        i0.ɵɵelementStart(86, \"label\", 18);\n        i0.ɵɵtext(87, \"Tricare\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(88, \"div\", 14)(89, \"p\", 15);\n        i0.ɵɵtext(90, \"CHAMPVA\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(91, \"div\", 16);\n        i0.ɵɵelement(92, \"input\", 17);\n        i0.ɵɵelementStart(93, \"label\", 18);\n        i0.ɵɵtext(94, \"CHAMPVA\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(95, \"div\", 14)(96, \"p\", 15);\n        i0.ɵɵtext(97, \"Group Health Plan\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(98, \"div\", 16);\n        i0.ɵɵelement(99, \"input\", 17);\n        i0.ɵɵelementStart(100, \"label\", 18);\n        i0.ɵɵtext(101, \"Group Health Plan\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(102, \"div\", 14)(103, \"p\", 15);\n        i0.ɵɵtext(104, \"FECA BLK LUNG\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(105, \"div\", 16);\n        i0.ɵɵelement(106, \"input\", 17);\n        i0.ɵɵelementStart(107, \"label\", 18);\n        i0.ɵɵtext(108, \"FECA BLK LUNG\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(109, \"div\", 14)(110, \"p\", 15);\n        i0.ɵɵtext(111, \"Others\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(112, \"div\", 16);\n        i0.ɵɵelement(113, \"input\", 17);\n        i0.ɵɵelementStart(114, \"label\", 18);\n        i0.ɵɵtext(115, \"Others\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(116, \"div\", 19)(117, \"div\", 20)(118, \"p\", 15);\n        i0.ɵɵtext(119, \"1A. Insured\\u2019s I.D. Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(120, \"div\", 21);\n        i0.ɵɵelement(121, \"input\", 22);\n        i0.ɵɵelementStart(122, \"mat-icon\", 23);\n        i0.ɵɵlistener(\"click\", function CapComponent_Template_mat_icon_click_122_listener() {\n          return ctx.searchInsuredId();\n        });\n        i0.ɵɵtext(123, \"search\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(124, \"div\", 0)(125, \"div\", 1)(126, \"section\", 24)(127, \"div\", 2)(128, \"div\", 20)(129, \"p\", 15);\n        i0.ɵɵtext(130, \"2. Patient Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(131, \"div\", 2)(132, \"div\", 20)(133, \"label\", 5);\n        i0.ɵɵtext(134, \"Last Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(135, \"div\", 20)(136, \"label\", 5);\n        i0.ɵɵtext(137, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(138, \"div\", 20)(139, \"label\", 5);\n        i0.ɵɵtext(140, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(141, \"div\", 0)(142, \"div\", 20);\n        i0.ɵɵelement(143, \"input\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(144, \"div\", 20);\n        i0.ɵɵelement(145, \"input\", 26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(146, \"div\", 20);\n        i0.ɵɵelement(147, \"input\", 27);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(148, \"div\", 1)(149, \"section\", 28)(150, \"div\", 2)(151, \"div\", 20)(152, \"p\", 15);\n        i0.ɵɵtext(153, \"3. Patient Birth Date\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(154, \"div\", 2)(155, \"div\", 20)(156, \"label\", 5);\n        i0.ɵɵtext(157, \"Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(158, \"div\", 20)(159, \"label\", 5);\n        i0.ɵɵtext(160, \"Sex\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(161, \"div\", 0)(162, \"div\", 20);\n        i0.ɵɵelement(163, \"input\", 29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(164, \"div\", 20)(165, \"div\", 16);\n        i0.ɵɵelement(166, \"input\", 17);\n        i0.ɵɵelementStart(167, \"label\", 18);\n        i0.ɵɵtext(168, \"Male\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(169, \"div\", 16);\n        i0.ɵɵelement(170, \"input\", 30);\n        i0.ɵɵelementStart(171, \"label\", 31);\n        i0.ɵɵtext(172, \"Female\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(173, \"div\", 16);\n        i0.ɵɵelement(174, \"input\", 30);\n        i0.ɵɵelementStart(175, \"label\", 31);\n        i0.ɵɵtext(176, \"Other\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(177, \"div\", 1)(178, \"div\", 2)(179, \"p\", 15);\n        i0.ɵɵtext(180, \"4. Insured\\u2019s Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(181, \"div\", 2)(182, \"div\", 20)(183, \"label\", 5);\n        i0.ɵɵtext(184, \"Last Name \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(185, \"div\", 20)(186, \"label\", 5);\n        i0.ɵɵtext(187, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(188, \"div\", 20)(189, \"label\", 5);\n        i0.ɵɵtext(190, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(191, \"div\", 0)(192, \"div\", 20);\n        i0.ɵɵelement(193, \"input\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(194, \"div\", 20);\n        i0.ɵɵelement(195, \"input\", 26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(196, \"div\", 20);\n        i0.ɵɵelement(197, \"input\", 27);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(198, \"div\", 0)(199, \"div\", 1)(200, \"section\", 32)(201, \"div\", 2)(202, \"p\", 15);\n        i0.ɵɵtext(203, \"5.Patient Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(204, \"div\", 2)(205, \"div\", 20)(206, \"label\", 5);\n        i0.ɵɵtext(207, \"Address Line - 1 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(208, \"div\", 20)(209, \"label\", 5);\n        i0.ɵɵtext(210, \"Address Line - 2\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(211, \"div\", 20)(212, \"label\", 5);\n        i0.ɵɵtext(213, \"City\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(214, \"div\", 0)(215, \"div\", 20);\n        i0.ɵɵelement(216, \"input\", 33);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(217, \"div\", 20);\n        i0.ɵɵelement(218, \"input\", 33);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(219, \"div\", 20);\n        i0.ɵɵelement(220, \"input\", 33);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(221, \"div\", 2)(222, \"div\", 20)(223, \"label\", 5);\n        i0.ɵɵtext(224, \"State \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(225, \"div\", 20)(226, \"label\", 5);\n        i0.ɵɵtext(227, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(228, \"div\", 20)(229, \"label\", 5);\n        i0.ɵɵtext(230, \"Telephone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(231, \"div\", 0)(232, \"div\", 20);\n        i0.ɵɵelement(233, \"input\", 34);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(234, \"div\", 20);\n        i0.ɵɵelement(235, \"input\", 35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(236, \"div\", 20);\n        i0.ɵɵelement(237, \"input\", 36);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(238, \"div\", 1)(239, \"section\", 37)(240, \"div\", 2)(241, \"div\", 20)(242, \"p\", 15);\n        i0.ɵɵtext(243, \"6. Patient Relationship To Insured\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(244, \"div\", 2)(245, \"div\", 20)(246, \"div\", 16);\n        i0.ɵɵelement(247, \"input\", 17);\n        i0.ɵɵelementStart(248, \"label\", 18);\n        i0.ɵɵtext(249, \"Self\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(250, \"div\", 16);\n        i0.ɵɵelement(251, \"input\", 30);\n        i0.ɵɵelementStart(252, \"label\", 31);\n        i0.ɵɵtext(253, \"Spouse\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(254, \"div\", 16);\n        i0.ɵɵelement(255, \"input\", 30);\n        i0.ɵɵelementStart(256, \"label\", 31);\n        i0.ɵɵtext(257, \"Child\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(258, \"div\", 16);\n        i0.ɵɵelement(259, \"input\", 30);\n        i0.ɵɵelementStart(260, \"label\", 31);\n        i0.ɵɵtext(261, \"Other\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(262, \"section\", 38)(263, \"div\", 2)(264, \"div\", 20)(265, \"p\", 15);\n        i0.ɵɵtext(266, \"8. Reserved For NUCC Use\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(267, \"div\", 2)(268, \"div\", 20);\n        i0.ɵɵelement(269, \"input\", 39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(270, \"div\", 2)(271, \"div\", 20);\n        i0.ɵɵelement(272, \"input\", 39);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(273, \"div\", 1)(274, \"div\", 2)(275, \"p\", 15);\n        i0.ɵɵtext(276, \"7.Insured\\u2019s Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(277, \"div\", 2)(278, \"div\", 20)(279, \"label\", 5);\n        i0.ɵɵtext(280, \"Address Line - 1 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(281, \"div\", 20)(282, \"label\", 5);\n        i0.ɵɵtext(283, \"Address Line - 2\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(284, \"div\", 20)(285, \"label\", 5);\n        i0.ɵɵtext(286, \"City\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(287, \"div\", 0)(288, \"div\", 20);\n        i0.ɵɵelement(289, \"input\", 33);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(290, \"div\", 20);\n        i0.ɵɵelement(291, \"input\", 33);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(292, \"div\", 20);\n        i0.ɵɵelement(293, \"input\", 33);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(294, \"div\", 2)(295, \"div\", 20)(296, \"label\", 5);\n        i0.ɵɵtext(297, \"State \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(298, \"div\", 20)(299, \"label\", 5);\n        i0.ɵɵtext(300, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(301, \"div\", 20)(302, \"label\", 5);\n        i0.ɵɵtext(303, \"Telephone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(304, \"div\", 0)(305, \"div\", 20);\n        i0.ɵɵelement(306, \"input\", 34);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(307, \"div\", 20);\n        i0.ɵɵelement(308, \"input\", 35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(309, \"div\", 20);\n        i0.ɵɵelement(310, \"input\", 36);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(311, \"div\", 0)(312, \"div\", 1)(313, \"section\", 40)(314, \"div\", 2)(315, \"div\", 20)(316, \"p\", 15);\n        i0.ɵɵtext(317, \"9. Other Insured\\u2019s Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(318, \"div\", 2)(319, \"div\", 20)(320, \"label\", 5);\n        i0.ɵɵtext(321, \"Last Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(322, \"div\", 20)(323, \"label\", 5);\n        i0.ɵɵtext(324, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(325, \"div\", 20)(326, \"label\", 5);\n        i0.ɵɵtext(327, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(328, \"div\", 0)(329, \"div\", 20);\n        i0.ɵɵelement(330, \"input\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(331, \"div\", 20);\n        i0.ɵɵelement(332, \"input\", 26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(333, \"div\", 20);\n        i0.ɵɵelement(334, \"input\", 27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(335, \"div\", 2)(336, \"div\", 20)(337, \"label\", 5);\n        i0.ɵɵtext(338, \"A. Other Insured\\u2019s Policy Or Group Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(339, \"input\", 41);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(340, \"div\", 2)(341, \"div\", 42)(342, \"label\", 5);\n        i0.ɵɵtext(343, \"B. Reserved For NUCC Use\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(344, \"div\", 43)(345, \"label\", 5);\n        i0.ɵɵtext(346, \"C. Reserved For NUCC Use\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(347, \"div\", 0)(348, \"div\", 42);\n        i0.ɵɵelement(349, \"input\", 44);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(350, \"div\", 43);\n        i0.ɵɵelement(351, \"input\", 44);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(352, \"div\", 2)(353, \"div\", 20)(354, \"label\", 5);\n        i0.ɵɵtext(355, \"A. Other Insured\\u2019s Policy Or Group Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(356, \"input\", 41);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(357, \"div\", 1)(358, \"section\", 45)(359, \"div\", 2)(360, \"div\", 20)(361, \"p\", 15);\n        i0.ɵɵtext(362, \"10. Is Patient\\u2019s Condition Related To\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(363, \"div\", 2)(364, \"div\", 20)(365, \"label\", 5);\n        i0.ɵɵtext(366, \"10.A. Employment? (Current Or Previous)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(367, \"div\", 0)(368, \"div\", 20)(369, \"div\", 16);\n        i0.ɵɵelement(370, \"input\", 17);\n        i0.ɵɵelementStart(371, \"label\", 18);\n        i0.ɵɵtext(372, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(373, \"div\", 16);\n        i0.ɵɵelement(374, \"input\", 30);\n        i0.ɵɵelementStart(375, \"label\", 31);\n        i0.ɵɵtext(376, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(377, \"div\", 2)(378, \"div\", 20)(379, \"label\", 5);\n        i0.ɵɵtext(380, \"10.B. Auto Accident?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(381, \"div\", 0)(382, \"div\", 20)(383, \"div\", 16);\n        i0.ɵɵelement(384, \"input\", 17);\n        i0.ɵɵelementStart(385, \"label\", 18);\n        i0.ɵɵtext(386, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(387, \"div\", 16);\n        i0.ɵɵelement(388, \"input\", 30);\n        i0.ɵɵelementStart(389, \"label\", 31);\n        i0.ɵɵtext(390, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(391, \"div\", 2)(392, \"div\", 20)(393, \"label\", 5);\n        i0.ɵɵtext(394, \"10.C. Other Accidents?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(395, \"div\", 0)(396, \"div\", 20)(397, \"div\", 16);\n        i0.ɵɵelement(398, \"input\", 17);\n        i0.ɵɵelementStart(399, \"label\", 18);\n        i0.ɵɵtext(400, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(401, \"div\", 16);\n        i0.ɵɵelement(402, \"input\", 30);\n        i0.ɵɵelementStart(403, \"label\", 31);\n        i0.ɵɵtext(404, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(405, \"div\", 2)(406, \"div\", 20)(407, \"label\", 5);\n        i0.ɵɵtext(408, \"10.D Claims Codes(Designed By NUCC)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(409, \"div\", 2)(410, \"div\", 20)(411, \"div\", 16);\n        i0.ɵɵelement(412, \"input\", 17);\n        i0.ɵɵelementStart(413, \"label\", 18);\n        i0.ɵɵtext(414, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(415, \"div\", 16);\n        i0.ɵɵelement(416, \"input\", 30);\n        i0.ɵɵelementStart(417, \"label\", 31);\n        i0.ɵɵtext(418, \"No\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(419, \"div\", 1)(420, \"div\", 2)(421, \"div\", 46)(422, \"p\", 15);\n        i0.ɵɵtext(423, \"11. Insured\\u2019s Policy Group Or FECA Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(424, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(425, \"div\", 47);\n        i0.ɵɵelement(426, \"p\", 15)(427, \"img\", 48);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(428, \"div\", 2)(429, \"div\", 4)(430, \"label\", 5);\n        i0.ɵɵtext(431, \" A. Insured\\u2019s Date Of Birth\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(432, \"div\", 4)(433, \"label\", 5);\n        i0.ɵɵtext(434, \" Sex\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(435, \"div\", 0)(436, \"div\", 4);\n        i0.ɵɵelement(437, \"input\", 49);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(438, \"div\", 4)(439, \"div\", 16);\n        i0.ɵɵelement(440, \"input\", 17);\n        i0.ɵɵelementStart(441, \"label\", 18);\n        i0.ɵɵtext(442, \"Male\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(443, \"div\", 16);\n        i0.ɵɵelement(444, \"input\", 30);\n        i0.ɵɵelementStart(445, \"label\", 31);\n        i0.ɵɵtext(446, \"Female\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(447, \"div\", 16);\n        i0.ɵɵelement(448, \"input\", 30);\n        i0.ɵɵelementStart(449, \"label\", 31);\n        i0.ɵɵtext(450, \"Others\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(451, \"div\", 2)(452, \"div\", 4)(453, \"label\", 5);\n        i0.ɵɵtext(454, \" B.Other Claim ID (Designated By NUCC)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(455, \"input\", 39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(456, \"div\", 2)(457, \"div\", 4)(458, \"label\", 5);\n        i0.ɵɵtext(459, \" C. Insurance Plan Name Or Program Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(460, \"input\", 39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(461, \"div\", 2)(462, \"div\", 4)(463, \"label\", 5);\n        i0.ɵɵtext(464, \" D. Is There Another Health Benefit Plan\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(465, \"div\", 4);\n        i0.ɵɵelement(466, \"label\", 5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(467, \"div\", 2)(468, \"div\", 4)(469, \"div\", 16);\n        i0.ɵɵelement(470, \"input\", 17);\n        i0.ɵɵelementStart(471, \"label\", 18);\n        i0.ɵɵtext(472, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(473, \"div\", 16);\n        i0.ɵɵelement(474, \"input\", 30);\n        i0.ɵɵelementStart(475, \"label\", 31);\n        i0.ɵɵtext(476, \"No\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(477, \"div\", 4);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(478, \"div\", 0)(479, \"div\", 1)(480, \"section\", 50)(481, \"div\", 2)(482, \"p\", 15);\n        i0.ɵɵtext(483, \"12. Patients Or Authorized Persons Signature\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(484, \"div\", 0)(485, \"div\", 20)(486, \"div\", 16);\n        i0.ɵɵelement(487, \"input\", 17);\n        i0.ɵɵelementStart(488, \"label\", 18);\n        i0.ɵɵtext(489, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(490, \"div\", 16);\n        i0.ɵɵelement(491, \"input\", 17);\n        i0.ɵɵelementStart(492, \"label\", 18);\n        i0.ɵɵtext(493, \"No\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(494, \"div\", 20)(495, \"label\", 5);\n        i0.ɵɵtext(496, \" Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(497, \"input\", 29);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(498, \"div\", 1);\n        i0.ɵɵelementStart(499, \"div\", 1)(500, \"div\", 2)(501, \"p\", 15);\n        i0.ɵɵtext(502, \"13. Insured Or Authorized Persons Signature\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(503, \"div\", 0)(504, \"div\", 20)(505, \"div\", 16);\n        i0.ɵɵelement(506, \"input\", 17);\n        i0.ɵɵelementStart(507, \"label\", 18);\n        i0.ɵɵtext(508, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(509, \"div\", 16);\n        i0.ɵɵelement(510, \"input\", 30);\n        i0.ɵɵelementStart(511, \"label\", 31);\n        i0.ɵɵtext(512, \"No\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(513, \"div\", 0)(514, \"div\", 1)(515, \"section\", 51)(516, \"div\", 2)(517, \"p\", 15);\n        i0.ɵɵtext(518, \"14. Date Of Current Illness,Injury Or Pregnancy (LMP)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(519, \"div\", 0)(520, \"div\", 20)(521, \"label\", 5);\n        i0.ɵɵtext(522, \" Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(523, \"input\", 29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(524, \"div\", 20)(525, \"label\", 5);\n        i0.ɵɵtext(526, \" Qualifier\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(527, \"select\", 52)(528, \"option\", 53);\n        i0.ɵɵtext(529, \"Open this select menu\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(530, \"option\", 54);\n        i0.ɵɵtext(531, \"One\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(532, \"option\", 55);\n        i0.ɵɵtext(533, \"Two\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(534, \"option\", 56);\n        i0.ɵɵtext(535, \"Three\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(536, \"div\", 1)(537, \"section\", 57)(538, \"div\", 2)(539, \"div\", 20)(540, \"p\", 15);\n        i0.ɵɵtext(541, \"15. Other Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(542, \"div\", 0)(543, \"div\", 20)(544, \"label\", 31);\n        i0.ɵɵtext(545, \"Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(546, \"input\", 29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(547, \"div\", 20)(548, \"label\", 31);\n        i0.ɵɵtext(549, \"Qualifier\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(550, \"select\", 58)(551, \"option\", 53);\n        i0.ɵɵtext(552, \"Open this select menu\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(553, \"option\", 54);\n        i0.ɵɵtext(554, \"One\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(555, \"option\", 55);\n        i0.ɵɵtext(556, \"Two\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(557, \"option\", 56);\n        i0.ɵɵtext(558, \"Three\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(559, \"div\", 1)(560, \"div\", 2)(561, \"p\", 15);\n        i0.ɵɵtext(562, \"16. Patient Unable To Work In Current Occupation\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(563, \"div\", 0)(564, \"div\", 20)(565, \"label\", 5);\n        i0.ɵɵtext(566, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(567, \"input\", 29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(568, \"div\", 20)(569, \"label\", 5);\n        i0.ɵɵtext(570, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(571, \"input\", 29);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(572, \"div\", 0)(573, \"div\", 1)(574, \"section\", 59)(575, \"div\", 2)(576, \"div\", 20)(577, \"p\", 15);\n        i0.ɵɵtext(578, \"17. Name Of The Referring Provider Or Other Source\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(579, \"div\", 2)(580, \"div\", 20)(581, \"label\", 5);\n        i0.ɵɵtext(582, \"Last Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(583, \"div\", 20)(584, \"label\", 5);\n        i0.ɵɵtext(585, \"First Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(586, \"div\", 20)(587, \"label\", 5);\n        i0.ɵɵtext(588, \"Middle Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(589, \"div\", 0)(590, \"div\", 20);\n        i0.ɵɵelement(591, \"input\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(592, \"div\", 20);\n        i0.ɵɵelement(593, \"input\", 26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(594, \"div\", 20);\n        i0.ɵɵelement(595, \"input\", 27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(596, \"div\", 2)(597, \"div\", 20);\n        i0.ɵɵelement(598, \"label\", 5);\n        i0.ɵɵelementStart(599, \"select\", 58)(600, \"option\", 53);\n        i0.ɵɵtext(601, \"Open this select menu\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(602, \"option\", 54);\n        i0.ɵɵtext(603, \"One\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(604, \"option\", 55);\n        i0.ɵɵtext(605, \"Two\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(606, \"option\", 56);\n        i0.ɵɵtext(607, \"Three\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(608, \"div\", 1)(609, \"section\", 60)(610, \"div\", 2)(611, \"div\", 61)(612, \"p\", 15);\n        i0.ɵɵtext(613, \"17A.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(614, \"div\", 20)(615, \"label\", 5);\n        i0.ɵɵtext(616, \"Payer Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(617, \"div\", 20)(618, \"label\", 5);\n        i0.ɵɵtext(619, \"Payer Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(620, \"div\", 0);\n        i0.ɵɵelement(621, \"div\", 61);\n        i0.ɵɵelementStart(622, \"div\", 20)(623, \"select\", 58)(624, \"option\", 53);\n        i0.ɵɵtext(625, \"Open this select menu\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(626, \"option\", 54);\n        i0.ɵɵtext(627, \"One\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(628, \"option\", 55);\n        i0.ɵɵtext(629, \"Two\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(630, \"option\", 56);\n        i0.ɵɵtext(631, \"Three\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(632, \"div\", 20);\n        i0.ɵɵelement(633, \"input\", 29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(634, \"div\", 2)(635, \"div\", 61)(636, \"p\", 15);\n        i0.ɵɵtext(637, \"17B.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(638, \"div\", 20)(639, \"label\", 5);\n        i0.ɵɵtext(640, \"Payer Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(641, \"div\", 20)(642, \"label\", 5);\n        i0.ɵɵtext(643, \"Payer Name\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(644, \"div\", 0);\n        i0.ɵɵelement(645, \"div\", 61);\n        i0.ɵɵelementStart(646, \"div\", 20);\n        i0.ɵɵelement(647, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(648, \"div\", 20);\n        i0.ɵɵelement(649, \"input\", 39);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(650, \"div\", 1)(651, \"div\", 2)(652, \"p\", 15);\n        i0.ɵɵtext(653, \"18. Hospitalization Dates Related To Current Services\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(654, \"div\", 0)(655, \"div\", 20)(656, \"label\", 5);\n        i0.ɵɵtext(657, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(658, \"input\", 29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(659, \"div\", 20)(660, \"label\", 5);\n        i0.ɵɵtext(661, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(662, \"input\", 29);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(663, \"div\", 0)(664, \"div\", 13)(665, \"section\", 62)(666, \"div\", 2)(667, \"div\", 20)(668, \"p\", 15);\n        i0.ɵɵtext(669, \"19. Additional Claim Information (Designated By NUCC)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(670, \"div\", 0)(671, \"div\", 20);\n        i0.ɵɵelement(672, \"input\", 63);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(673, \"div\", 19)(674, \"div\", 2)(675, \"p\", 15);\n        i0.ɵɵtext(676, \"20. Outside Lab\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(677, \"div\", 0)(678, \"div\", 20)(679, \"div\", 16);\n        i0.ɵɵelement(680, \"input\", 17);\n        i0.ɵɵelementStart(681, \"label\", 18);\n        i0.ɵɵtext(682, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(683, \"div\", 16);\n        i0.ɵɵelement(684, \"input\", 30);\n        i0.ɵɵelementStart(685, \"label\", 31);\n        i0.ɵɵtext(686, \"No\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(687, \"div\", 0)(688, \"div\", 13)(689, \"section\", 64)(690, \"div\", 2)(691, \"div\", 20)(692, \"p\", 15);\n        i0.ɵɵtext(693, \"21. Diagnosis Or Nature Of Illness Or Injury. (Relate A-L To Service Line Below (24E)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(694, \"div\", 0)(695, \"div\", 20)(696, \"label\", 5);\n        i0.ɵɵtext(697, \"A(1)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(698, \"div\", 20)(699, \"label\", 5);\n        i0.ɵɵtext(700, \"B(2)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(701, \"div\", 20)(702, \"label\", 5);\n        i0.ɵɵtext(703, \"C(3)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(704, \"div\", 20)(705, \"label\", 5);\n        i0.ɵɵtext(706, \"D(4)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(707, \"div\", 0)(708, \"div\", 20);\n        i0.ɵɵelement(709, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(710, \"div\", 20);\n        i0.ɵɵelement(711, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(712, \"div\", 20);\n        i0.ɵɵelement(713, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(714, \"div\", 20);\n        i0.ɵɵelement(715, \"input\", 39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(716, \"div\", 2)(717, \"div\", 20)(718, \"label\", 5);\n        i0.ɵɵtext(719, \"E(5)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(720, \"div\", 20)(721, \"label\", 5);\n        i0.ɵɵtext(722, \"F(6)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(723, \"div\", 20)(724, \"label\", 5);\n        i0.ɵɵtext(725, \"G(7)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(726, \"div\", 20)(727, \"label\", 5);\n        i0.ɵɵtext(728, \"H(8)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(729, \"div\", 0)(730, \"div\", 20);\n        i0.ɵɵelement(731, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(732, \"div\", 20);\n        i0.ɵɵelement(733, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(734, \"div\", 20);\n        i0.ɵɵelement(735, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(736, \"div\", 20);\n        i0.ɵɵelement(737, \"input\", 39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(738, \"div\", 2)(739, \"div\", 20)(740, \"label\", 5);\n        i0.ɵɵtext(741, \"I(9)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(742, \"div\", 20)(743, \"label\", 5);\n        i0.ɵɵtext(744, \"J(10)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(745, \"div\", 20)(746, \"label\", 5);\n        i0.ɵɵtext(747, \"K(11)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(748, \"div\", 20)(749, \"label\", 5);\n        i0.ɵɵtext(750, \"L(12)\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(751, \"div\", 0)(752, \"div\", 20);\n        i0.ɵɵelement(753, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(754, \"div\", 20);\n        i0.ɵɵelement(755, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(756, \"div\", 20);\n        i0.ɵɵelement(757, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(758, \"div\", 20);\n        i0.ɵɵelement(759, \"input\", 39);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(760, \"div\", 19)(761, \"div\", 2)(762, \"p\", 15);\n        i0.ɵɵtext(763, \"22. Resubmission Code\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(764, \"div\", 0)(765, \"div\", 4)(766, \"label\", 5);\n        i0.ɵɵtext(767, \"Resubmission Code\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(768, \"div\", 4)(769, \"label\", 5);\n        i0.ɵɵtext(770, \"Reference Number\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(771, \"div\", 2)(772, \"div\", 4);\n        i0.ɵɵelement(773, \"input\", 65);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(774, \"div\", 4);\n        i0.ɵɵelement(775, \"input\", 66);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(776, \"div\", 2)(777, \"p\", 15);\n        i0.ɵɵtext(778, \"23. Prior Authorization Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(779, \"div\", 2)(780, \"div\", 4)(781, \"label\", 5);\n        i0.ɵɵtext(782, \"Prior Authorization Number\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(783, \"div\", 0)(784, \"div\", 4);\n        i0.ɵɵelement(785, \"input\", 67);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(786, \"div\", 2)(787, \"div\", 4)(788, \"label\", 5);\n        i0.ɵɵtext(789, \"Ambulance Pickup Address 1\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(790, \"div\", 4)(791, \"label\", 5);\n        i0.ɵɵtext(792, \"Ambulance Pickup Address 2\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(793, \"div\", 0)(794, \"div\", 4);\n        i0.ɵɵelement(795, \"input\", 68);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(796, \"div\", 4);\n        i0.ɵɵelement(797, \"input\", 69);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(798, \"div\", 2)(799, \"div\", 4)(800, \"label\", 5);\n        i0.ɵɵtext(801, \"Ambulance Pickup City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(802, \"div\", 4)(803, \"label\", 5);\n        i0.ɵɵtext(804, \"Ambulance Pickup State\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(805, \"div\", 0)(806, \"div\", 4);\n        i0.ɵɵelement(807, \"input\", 70);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(808, \"div\", 4);\n        i0.ɵɵelement(809, \"input\", 71);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(810, \"div\", 2)(811, \"div\", 4)(812, \"label\", 5);\n        i0.ɵɵtext(813, \"Ambulance Pickup Zip Code\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(814, \"div\", 0)(815, \"div\", 4);\n        i0.ɵɵelement(816, \"input\", 72);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(817, \"div\", 73)(818, \"div\", 0)(819, \"div\", 1)(820, \"div\", 0)(821, \"p\", 15);\n        i0.ɵɵtext(822, \"24. Service Line\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(823, \"div\", 2)(824, \"div\", 4)(825, \"section\", 74)(826, \"div\", 75)(827, \"table\", 76)(828, \"thead\")(829, \"tr\")(830, \"th\", 77);\n        i0.ɵɵtext(831, \"A.) Date(S) of Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(832, \"th\", 78);\n        i0.ɵɵtext(833, \"B.) Place of Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(834, \"th\", 78);\n        i0.ɵɵtext(835, \"C. EMG\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(836, \"th\", 77);\n        i0.ɵɵtext(837, \"D.) Procedures, Services or Supplies\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(838, \"th\", 78);\n        i0.ɵɵtext(839, \"E.) Diagnosis Pointer\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(840, \"th\", 78);\n        i0.ɵɵtext(841, \"F.) Unit Charge\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(842, \"th\", 78);\n        i0.ɵɵtext(843, \"G.) Days & Unit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(844, \"th\", 78);\n        i0.ɵɵtext(845, \"Units or Minutes\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(846, \"th\", 78);\n        i0.ɵɵtext(847, \"H.) EPSDT\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(848, \"th\", 78);\n        i0.ɵɵtext(849, \"I.) ID Qual\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(850, \"th\", 78);\n        i0.ɵɵtext(851, \"J.) Rendering Provider ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(852, \"tr\")(853, \"th\", 78);\n        i0.ɵɵtext(854, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(855, \"th\", 78);\n        i0.ɵɵtext(856, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(857, \"th\", 78)(858, \"th\", 78);\n        i0.ɵɵelementStart(859, \"th\", 78);\n        i0.ɵɵtext(860, \"CPT/HCPCS\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(861, \"th\", 78);\n        i0.ɵɵtext(862, \"Modifier\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(863, \"th\", 78)(864, \"th\", 78)(865, \"th\", 78)(866, \"th\", 78)(867, \"th\", 78)(868, \"th\", 78)(869, \"th\", 78);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(870, \"tbody\");\n        i0.ɵɵtemplate(871, CapComponent_tr_871_Template, 127, 20, \"tr\", 79);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(872, \"div\", 80)(873, \"div\")(874, \"button\", 81);\n        i0.ɵɵtext(875, \"Delete\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(876, \"div\")(877, \"button\", 82);\n        i0.ɵɵlistener(\"click\", function CapComponent_Template_button_click_877_listener() {\n          return ctx.addRow();\n        });\n        i0.ɵɵtext(878, \"Add\");\n        i0.ɵɵelementEnd()()()()()()()()()();\n        i0.ɵɵelementStart(879, \"div\", 0)(880, \"div\", 1)(881, \"div\", 2)(882, \"div\", 4)(883, \"p\", 15);\n        i0.ɵɵtext(884, \"25. Federal Tax Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(885, \"div\", 4)(886, \"div\", 16);\n        i0.ɵɵelement(887, \"input\", 17);\n        i0.ɵɵelementStart(888, \"label\", 18);\n        i0.ɵɵtext(889, \"SSN\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(890, \"div\", 16);\n        i0.ɵɵelement(891, \"input\", 30);\n        i0.ɵɵelementStart(892, \"label\", 31);\n        i0.ɵɵtext(893, \"EIN\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(894, \"div\", 0)(895, \"div\", 4);\n        i0.ɵɵelement(896, \"input\", 83);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(897, \"div\", 1)(898, \"div\", 2)(899, \"div\", 4)(900, \"p\", 15);\n        i0.ɵɵtext(901, \"26. Patient Account Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(902, \"div\", 4)(903, \"p\", 15);\n        i0.ɵɵtext(904, \"27. Acceptance Assignment?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(905, \"div\", 0)(906, \"div\", 4);\n        i0.ɵɵelement(907, \"input\", 84);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(908, \"div\", 4)(909, \"div\", 16);\n        i0.ɵɵelement(910, \"input\", 17);\n        i0.ɵɵelementStart(911, \"label\", 18);\n        i0.ɵɵtext(912, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(913, \"div\", 16);\n        i0.ɵɵelement(914, \"input\", 30);\n        i0.ɵɵelementStart(915, \"label\", 31);\n        i0.ɵɵtext(916, \"No\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(917, \"div\", 1)(918, \"div\", 2)(919, \"div\", 4)(920, \"p\", 15);\n        i0.ɵɵtext(921, \"28. Total Charge($)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(922, \"div\", 4)(923, \"p\", 15);\n        i0.ɵɵtext(924, \"29. Amount Paid\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(925, \"div\", 4)(926, \"p\", 15);\n        i0.ɵɵtext(927, \"30. Reserved For NUCC\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(928, \"div\", 0)(929, \"div\", 4);\n        i0.ɵɵelement(930, \"input\", 85);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(931, \"div\", 4);\n        i0.ɵɵelement(932, \"input\", 86);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(933, \"div\", 4);\n        i0.ɵɵelement(934, \"input\", 87);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(935, \"div\", 0)(936, \"div\", 1)(937, \"section\", 88)(938, \"div\", 2)(939, \"div\", 20);\n        i0.ɵɵelement(940, \"p\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(941, \"div\", 20)(942, \"div\", 16);\n        i0.ɵɵelement(943, \"input\", 17);\n        i0.ɵɵelementStart(944, \"label\", 18);\n        i0.ɵɵtext(945, \"Yes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(946, \"div\", 16);\n        i0.ɵɵelement(947, \"input\", 17);\n        i0.ɵɵelementStart(948, \"label\", 18);\n        i0.ɵɵtext(949, \"No\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(950, \"div\", 2)(951, \"div\", 20)(952, \"label\", 5);\n        i0.ɵɵtext(953, \"Provider Signature Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(954, \"div\", 20);\n        i0.ɵɵelement(955, \"input\", 89);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(956, \"div\", 2)(957, \"div\", 20)(958, \"label\", 5);\n        i0.ɵɵtext(959, \"Date Of Initial Treatment\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(960, \"div\", 20);\n        i0.ɵɵelement(961, \"input\", 89);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(962, \"div\", 2)(963, \"div\", 20)(964, \"label\", 5);\n        i0.ɵɵtext(965, \"Latest Visit OrConsultation Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(966, \"div\", 20);\n        i0.ɵɵelement(967, \"input\", 89);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(968, \"div\", 2)(969, \"div\", 20)(970, \"label\", 5);\n        i0.ɵɵtext(971, \"Supervising Physician\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(972, \"div\", 20);\n        i0.ɵɵelement(973, \"input\", 25);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(974, \"div\", 90)(975, \"div\", 20);\n        i0.ɵɵelement(976, \"label\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(977, \"div\", 20);\n        i0.ɵɵelement(978, \"input\", 26);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(979, \"div\", 90)(980, \"div\", 20);\n        i0.ɵɵelement(981, \"label\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(982, \"div\", 20);\n        i0.ɵɵelement(983, \"input\", 27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(984, \"div\", 90)(985, \"div\", 20)(986, \"label\", 5);\n        i0.ɵɵtext(987, \"Supervising Physician NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(988, \"div\", 20);\n        i0.ɵɵelement(989, \"input\", 91);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(990, \"div\", 90)(991, \"div\", 20)(992, \"label\", 5);\n        i0.ɵɵtext(993, \"Ordering Physician\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(994, \"div\", 20);\n        i0.ɵɵelement(995, \"input\", 25);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(996, \"div\", 90)(997, \"div\", 20);\n        i0.ɵɵelement(998, \"label\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(999, \"div\", 20);\n        i0.ɵɵelement(1000, \"input\", 26);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1001, \"div\", 90)(1002, \"div\", 20);\n        i0.ɵɵelement(1003, \"label\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1004, \"div\", 20);\n        i0.ɵɵelement(1005, \"input\", 27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1006, \"div\", 90)(1007, \"div\", 20)(1008, \"label\", 5);\n        i0.ɵɵtext(1009, \"Ordering Physician NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1010, \"div\", 20);\n        i0.ɵɵelement(1011, \"input\", 27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1012, \"div\", 90)(1013, \"div\", 20)(1014, \"label\", 5);\n        i0.ɵɵtext(1015, \"Ordering Physician ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1016, \"div\", 20);\n        i0.ɵɵelement(1017, \"input\", 91);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1018, \"div\", 90)(1019, \"div\", 20)(1020, \"label\", 5);\n        i0.ɵɵtext(1021, \"Accident Date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1022, \"div\", 20);\n        i0.ɵɵelement(1023, \"input\", 92);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1024, \"div\", 90)(1025, \"div\", 20)(1026, \"label\", 5);\n        i0.ɵɵtext(1027, \"CLIA\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1028, \"div\", 20);\n        i0.ɵɵelement(1029, \"input\", 93);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1030, \"div\", 90)(1031, \"div\", 20)(1032, \"label\", 5);\n        i0.ɵɵtext(1033, \"Mammography Certificate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1034, \"div\", 20);\n        i0.ɵɵelement(1035, \"input\", 94);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1036, \"div\", 1)(1037, \"section\", 95)(1038, \"div\", 2)(1039, \"div\", 20)(1040, \"p\", 15);\n        i0.ɵɵtext(1041, \" 32. Service Facility Location And Information\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1042, \"div\", 90)(1043, \"div\", 20)(1044, \"label\", 5);\n        i0.ɵɵtext(1045, \"Facility Name\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1046, \"div\", 20);\n        i0.ɵɵelement(1047, \"input\", 96);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1048, \"div\", 90)(1049, \"div\", 20)(1050, \"label\", 5);\n        i0.ɵɵtext(1051, \"Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1052, \"div\", 20);\n        i0.ɵɵelement(1053, \"input\", 97);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1054, \"div\", 90)(1055, \"div\", 20);\n        i0.ɵɵelement(1056, \"label\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1057, \"div\", 20);\n        i0.ɵɵelement(1058, \"input\", 98);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1059, \"div\", 90)(1060, \"div\", 20)(1061, \"label\", 5);\n        i0.ɵɵtext(1062, \"City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1063, \"div\", 20);\n        i0.ɵɵelement(1064, \"input\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1065, \"div\", 90)(1066, \"div\", 20)(1067, \"label\", 5);\n        i0.ɵɵtext(1068, \"State\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1069, \"div\", 20);\n        i0.ɵɵelement(1070, \"input\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1071, \"div\", 90)(1072, \"div\", 20)(1073, \"label\", 5);\n        i0.ɵɵtext(1074, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1075, \"div\", 20);\n        i0.ɵɵelement(1076, \"input\", 72);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1077, \"div\", 90)(1078, \"div\", 20)(1079, \"label\", 5);\n        i0.ɵɵtext(1080, \"a. NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1081, \"div\", 20);\n        i0.ɵɵelement(1082, \"input\", 83);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1083, \"div\", 90)(1084, \"div\", 20)(1085, \"label\", 5);\n        i0.ɵɵtext(1086, \"b. Facility ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1087, \"div\", 20);\n        i0.ɵɵelement(1088, \"input\", 99);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1089, \"div\", 90)(1090, \"div\", 20)(1091, \"label\", 5);\n        i0.ɵɵtext(1092, \"Ambulance Drop off Address 1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(1093, \"input\", 26);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1094, \"div\", 90)(1095, \"div\", 20)(1096, \"label\", 5);\n        i0.ɵɵtext(1097, \" Ambulance Drop off Address 1\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1098, \"div\", 20)(1099, \"label\", 5);\n        i0.ɵɵtext(1100, \"Ambulance Drop off Address 2\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1101, \"div\", 90)(1102, \"div\", 20);\n        i0.ɵɵelement(1103, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1104, \"div\", 20);\n        i0.ɵɵelement(1105, \"input\", 39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1106, \"div\", 90)(1107, \"div\", 20)(1108, \"label\", 5);\n        i0.ɵɵtext(1109, \"Ambulance Drop off City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1110, \"div\", 20)(1111, \"label\", 5);\n        i0.ɵɵtext(1112, \"Ambulance Drop off State\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1113, \"div\", 90)(1114, \"div\", 20);\n        i0.ɵɵelement(1115, \"input\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1116, \"div\", 20);\n        i0.ɵɵelement(1117, \"input\", 39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1118, \"div\", 90)(1119, \"div\", 20)(1120, \"label\", 5);\n        i0.ɵɵtext(1121, \"Ambulance Drop off Zip Code\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(1122, \"input\", 39);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1123, \"div\", 1)(1124, \"section\", 100)(1125, \"div\", 2)(1126, \"div\", 20)(1127, \"p\", 15);\n        i0.ɵɵtext(1128, \" 33. Billing Provider Info And Phone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1129, \"div\", 90)(1130, \"div\", 20)(1131, \"label\", 5);\n        i0.ɵɵtext(1132, \" Billing Provider\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1133, \"div\", 20);\n        i0.ɵɵelement(1134, \"input\", 25);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1135, \"div\", 90)(1136, \"div\", 20);\n        i0.ɵɵelement(1137, \"label\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1138, \"div\", 20);\n        i0.ɵɵelement(1139, \"input\", 26);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1140, \"div\", 90)(1141, \"div\", 20);\n        i0.ɵɵelement(1142, \"label\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1143, \"div\", 20);\n        i0.ɵɵelement(1144, \"input\", 27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1145, \"div\", 90)(1146, \"div\", 20)(1147, \"label\", 5);\n        i0.ɵɵtext(1148, \"Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1149, \"div\", 20);\n        i0.ɵɵelement(1150, \"input\", 97);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1151, \"div\", 90)(1152, \"div\", 20);\n        i0.ɵɵelement(1153, \"label\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1154, \"div\", 20);\n        i0.ɵɵelement(1155, \"input\", 98);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1156, \"div\", 90)(1157, \"div\", 20)(1158, \"label\", 5);\n        i0.ɵɵtext(1159, \"City\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1160, \"div\", 20);\n        i0.ɵɵelement(1161, \"input\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1162, \"div\", 90)(1163, \"div\", 20)(1164, \"label\", 5);\n        i0.ɵɵtext(1165, \" State\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1166, \"div\", 20);\n        i0.ɵɵelement(1167, \"input\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1168, \"div\", 90)(1169, \"div\", 20)(1170, \"label\", 5);\n        i0.ɵɵtext(1171, \"Zip Code\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1172, \"div\", 20);\n        i0.ɵɵelement(1173, \"input\", 101);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1174, \"div\", 90)(1175, \"div\", 20)(1176, \"label\", 5);\n        i0.ɵɵtext(1177, \"Telephone\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1178, \"div\", 20);\n        i0.ɵɵelement(1179, \"input\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1180, \"div\", 90)(1181, \"div\", 20)(1182, \"label\", 5);\n        i0.ɵɵtext(1183, \"Specialty/Taxonomy\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1184, \"div\", 20);\n        i0.ɵɵelement(1185, \"input\", 102);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1186, \"div\", 90)(1187, \"div\", 20)(1188, \"label\", 5);\n        i0.ɵɵtext(1189, \" Rendering Provider (Last,First,MI)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1190, \"div\", 20);\n        i0.ɵɵelement(1191, \"input\", 25);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1192, \"div\", 90)(1193, \"div\", 20);\n        i0.ɵɵelement(1194, \"label\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1195, \"div\", 20);\n        i0.ɵɵelement(1196, \"input\", 26);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1197, \"div\", 90)(1198, \"div\", 20);\n        i0.ɵɵelement(1199, \"label\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1200, \"div\", 20);\n        i0.ɵɵelement(1201, \"input\", 27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1202, \"div\", 90)(1203, \"div\", 20)(1204, \"label\", 5);\n        i0.ɵɵtext(1205, \"Provider Specialty\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1206, \"div\", 20);\n        i0.ɵɵelement(1207, \"input\", 103);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1208, \"div\", 90)(1209, \"div\", 20)(1210, \"label\", 5);\n        i0.ɵɵtext(1211, \" Provider NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1212, \"div\", 20);\n        i0.ɵɵelement(1213, \"input\", 104);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1214, \"div\", 90)(1215, \"div\", 20)(1216, \"label\", 5);\n        i0.ɵɵtext(1217, \" Provider PIN\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1218, \"div\", 20);\n        i0.ɵɵelement(1219, \"input\", 105);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1220, \"div\", 90)(1221, \"div\", 20)(1222, \"label\", 5);\n        i0.ɵɵtext(1223, \" A. Billing/Group NPI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1224, \"div\", 20);\n        i0.ɵɵelement(1225, \"input\", 39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1226, \"div\", 90)(1227, \"div\", 20)(1228, \"label\", 5);\n        i0.ɵɵtext(1229, \" Provider ID\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1230, \"div\", 20);\n        i0.ɵɵelement(1231, \"input\", 92);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1232, \"div\", 90)(1233, \"div\", 20)(1234, \"label\", 5);\n        i0.ɵɵtext(1235, \" Id Qual\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1236, \"div\", 20);\n        i0.ɵɵelement(1237, \"input\", 106);\n        i0.ɵɵelementEnd()()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(871);\n        i0.ɵɵproperty(\"ngForOf\", ctx.dynamicArray);\n      }\n    },\n    dependencies: [i2.NgForOf, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel, i4.MatIcon],\n    styles: [\".create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;line-height:24px;color:#3b475a}.form-border[_ngcontent-%COMP%]{border:1px solid #bdbdbd;padding:12px}.claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:24px}.claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}\"]\n  });\n  return CapComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}