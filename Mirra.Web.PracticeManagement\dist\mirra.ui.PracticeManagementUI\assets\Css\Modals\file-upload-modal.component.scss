select {
    width: 100%;
    font-size: 12px;
    height: 35px;
    border-radius: 8px;
    border:1px solid #D5D7E3;
    width: 135px;
    color: #565656;
    outline: none;
    cursor: pointer;
}

.btn {
    border: none;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
}

.reset{
    color: #0074BC;
    background-color: white;
    border: 1px solid #0074BC
}


.ErrorText{
    font-size: 23px;
    font-family: 'Poppins-SemiBold';
    text-align: center;
}


.form-control{
    box-shadow: none
}

.Close-Icon1{
    text-align: right;
    margin-top: 1%;
}

.FileSelectLabel{
    font-family:'Poppins-SemiBold';
}

.RedColor{
    color:red
}
.Modal-Close-Image{
 cursor: pointer;
}

.custom-file-input::-webkit-file-upload-button {
    visibility: hidden;
}
.custom-file-input::before {
    font-family: "Poppins-SemiBold";
    font-weight: 900;
    // content: "\f093     Choose file";
    content: "Choose file";
    display: inline-block;
    cursor: pointer;
    padding: 5px 12px;
    border: none;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: white;
    color: #0074bc;
    border: solid 1px #0074bc;
    font-size: 14px;
}
.custom-file-input:hover::before ,.reset:hover{
    color: white;
    border-color: #0074bc;
    background-color: #0074bc;
}
.custom-file-input:active::before {
    background: -webkit-linear-gradient(top, #e3e3e3, #f9f9f9);
}

.submit:hover{
    background-color: white;
    color: #0074bc;
    border: solid 1px #0074bc;
}

.Poppins{
    font-family: 'Poppins';
}

input[type="file"] {
    width: 130px;
}
.DeleteIcon{
    cursor: pointer;
    z-index: 100;
    position: relative;
}

.d-inherit{
    display: inherit!important;
}

object{
    z-index: -1;
    position: relative;
}

/* style icon */

.inner-addon .EyeIcon {
    position: absolute;
    padding: 10px;
}


/* align icon */

.left-addon .EyeIcon {
    left: 0px;
}

.right-addon .EyeIcon {
    pointer-events: none;
    margin-left: 109px;
}

.EyeIcon{
    cursor: pointer;
    z-index: 100;
    position: relative;
}

.Padding-Right{
    padding-right: 25px;
}

.ModalWidth{
    width: 770px;
}

