.Form-Body {
    border: 1px solid #CCCCCC;
    border-radius: 8px;
    opacity: 1;
    box-shadow: 0px 0px 8px #0000001a;
    background-color: white;
    margin: 0px 5px 5px 5px;
    padding: 5px 11px 5px 0px;

    // box-shadow: 0px 0px 8px #0000001a;
    // background-color: white;
    // padding: 5px 7px 5px 5px;
    // min-height: 400px;
}

// basic styling for all tds in table
.TableTd {
    color: #272d3b;
    font-size: 12px;
    font-family: "Poppins";
}

// background effect on hover on table row
.TableTr:hover {
    background-color: #e3f2fd;
}

// style for checkbox and paginator of primeng
::ng-deep .p-checkbox-box {
    border-radius: 50% !important;
}
::ng-deep .p-paginator {
    margin-right: 1%;
    justify-content: flex-end !important;
}

/* basic setting for all input type */

input[type="text"],
input[type="number"] {
    padding-left: 5px;
    border-radius: 6px;
    border: 1px solid #cccccc !important;
    height: 30px;
    outline: none;
    min-width: 40px;
    width: 75%;
    font-size: 10px;
    font-family: "Montserrat-Bold";
    margin-left: 5px;
}

// styling for input when on focus
input[type="text"]:focus,
input[type="number"]:focus {
    border: 1px solid #61abd4 !important;
}

// styling for table headers
th {
    padding-right: 0px !important;
    padding-left: 0px !important;
    background-color: white !important;
}

/* for setting second heading box*/

.Form-Header-2 {
    font-family: "Poppins";
    background-color: white;
    padding: 10px;
    align-items: center;
}

.Form-Header-2-Heading {
    color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 18px;
}


.TextRight {
    text-align: end;
}

.MarginRight {
    margin-right: 10px;
}

.MarginLeft {
    margin-left: 20px;
}

.btn {
    border: none;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
}

.btn:hover {
    background-color: #005488;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-down:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-up-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}


:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}

.ThBtn {
    font-family: "Poppins-Bold";
    width: 8%;
    color: #5d5d5d !important;
}

::ng-deep .p-datatable-resizable .p-datatable-tbody > tr > td,
.p-datatable-resizable .p-datatable-tfoot > tr > td,
.p-datatable-resizable .p-datatable-thead > tr > th {
    text-overflow: ellipsis !important;
}

.IconStyle {
    cursor: pointer;
    position: relative;
    z-index: 1 !important;
}

object {
    position: relative;
    z-index: -1 !important;
}

.ClaimListFilesInput {
    width: 76% !important;
}

.ThBtn1 {
    width: 6%;
}
input.ng-invalid.ng-touched {
    border: 1px solid red !important;
}

select.ng-invalid.ng-touched {
    border: 1px solid red !important;
}

::ng-deep .p-datatable .p-sortable-column .p-sortable-column-icon {
    margin-left: 0rem;
}

.CheckboxLabel {
    font-family: "Poppins";
}

.AgeMarginLeft {
    margin-left: 50%;
}

.ageOfDOS,
.ageOfDOC,
.amount {
    padding-left: 55%;
}
.controlNumber {
    padding-left: 29%;
}

.custom-file-input::-webkit-file-upload-button {
    visibility: hidden;
}
.custom-file-input::before {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f093     File Upload";
    display: inline-block;
    cursor: pointer;
    padding: 5px 12px;
    background: #0074bc;
    border: none;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    // font-family: 'Poppins-SemiBold';
    font-size: 14px;
}
.custom-file-input:hover::before {
    border-color: black;
}
.custom-file-input:active::before {
    background: -webkit-linear-gradient(top, #e3e3e3, #f9f9f9);
}

input[type="file"] {
    width: 130px;
}

.LastTd{
    text-align: center!important;
    position: absolute;
    right: 0;
    background-color: white;
    height: 53px;
    width: 7%;
  }

.LastTh{
    border-bottom: 2px solid #e3f2fd;
    text-align: center!important;
    position: absolute;
    right: 0;
    background-color: white;
    height: 62.5px;
    width: 7%;
  }

.sender{
    width: 15%;
}

.IconsPadding{
    padding-top: 1%!important;
}

.ageInDays{
    width: 9%;
}

.fileName {
    width: 21%;
}

.dateOfCreation ,.controlNumber ,.receiver{
    width: 12%;
}

.receivedDate {
    width: 9%;

}

.status{
    width: 20%;
}

#Status{
    width: 50%;
}

.hover{
    cursor: pointer;
}

.imageView{
    text-align: center!important;
    border-bottom: 2px solid #e3f2fd;
    right: 0;
    background-color: white;
    height: 53px;
    width: 7%;
  }

  
.radioClass {
    height: 16px;
    width: 16px;
}

.radiopClass {
    height: 16px;
    width: 16px;
}

.originalIcon{
    height: 16px; 
    width: 16px;
    margin-right: 5px;
}


.customIcon{
    height: 16px;
    width: 16px; 
    margin-right: 5px;
    margin-top:3px
}


input[type=checkbox] {
    /* Hide original inputs */
    opacity: 0.001;
    visibility: visible;
    position: absolute;
    z-index: 1000;
}

input[type=checkbox]+label+p {
    height: 16px;
    width: 16px;
}

input[type=checkbox]+label>p {
    transition: 100ms all;
    height: 16px;
    width: 16px;
}

input[type=checkbox]:checked+label>p {
    z-index: -1000;
    background-repeat: no-repeat;
    border-style: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16.17' height='16.505' viewBox='0 0 14.17 13.505'%3E%3Cg id='Checkbox' transform='translate(0 0.001)'%3E%3Crect id='Rectangle' width='14.17' height='13.505' rx='3' transform='translate(0 -0.001)' fill='%230074bc'/%3E%3Cpath id='check' d='M10.97,4.97a.75.75,0,1,1,1.071,1.05L8.049,11.01a.75.75,0,0,1-1.08.02L4.324,8.384a.75.75,0,1,1,1.06-1.06L7.478,9.417l3.473-4.425.02-.022Z' transform='translate(-1.085 -0.745)' fill='%23fff' fill-rule='evenodd'/%3E%3C/g%3E%3C/svg%3E%0A");
}

input[type=checkbox]+label>p {
    width: 16px;
    height: 16px;
    border: #999;
    border-width: 1px;
    border-style: solid;
    border-radius: 5px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16.17' height='16.505' viewBox='0 0 8.17 6.505'%3E%3Cpath id='check' d='M10.97,4.97a.75.75,0,1,1,1.071,1.05L8.049,11.01a.75.75,0,0,1-1.08.02L4.324,8.384a.75.75,0,1,1,1.06-1.06L7.478,9.417l3.473-4.425.02-.022Z' transform='translate(-4.085 -4.745)' fill='%23f4f7fc'/%3E%3C/svg%3E%0A");
}

::ng-deep .p-dropdown .p-dropdown-panel {
    top: -196px!important;
}