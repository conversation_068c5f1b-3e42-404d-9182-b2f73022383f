{"ast": null, "code": "import { FormArray, FormControl } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/tooltip\";\n\nfunction IcdViewHistoryComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"h5\", 18);\n    i0.ɵɵtext(2, \"Diagnosis codes reached the limit of 12.\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"fa fa-2x fa-check-circle-o CPTselectspan\": a0\n  };\n};\n\nfunction IcdViewHistoryComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function IcdViewHistoryComponent_div_15_div_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const i_r5 = restoredCtx.index;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.selectIcd(i_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 22);\n    i0.ɵɵelement(2, \"span\", 23);\n    i0.ɵɵelementStart(3, \"span\", 24)(4, \"b\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const formItem_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, formItem_r4.value.isSelected));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", formItem_r4.value.displayCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(formItem_r4.value.displayCode);\n  }\n}\n\nfunction IcdViewHistoryComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, IcdViewHistoryComponent_div_15_div_1_Template, 6, 5, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.icdFormArray.controls);\n  }\n}\n\nfunction IcdViewHistoryComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1, \" No Active Diagnosis Codes found for the selected Member. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport let IcdViewHistoryComponent = /*#__PURE__*/(() => {\n  class IcdViewHistoryComponent {\n    constructor(formBuilder, dialogRef, data) {\n      this.formBuilder = formBuilder;\n      this.dialogRef = dialogRef;\n      this.data = data;\n      this.icdList = [];\n      this.isSelectICDLimitExceed = false;\n      this.icdCodesNonHistoryCount = 0;\n      dialogRef.disableClose = true;\n      this.createFormGroup();\n    }\n\n    ngOnInit() {\n      if (!!this.data) {\n        this.icdList = this.data.icdList || [];\n\n        if (!!this.data.icdVersion) {\n          this.icdVersion = this.data.icdVersion;\n        }\n\n        this.icdCodesNonHistoryCount = this.data.icdCodesNonHistoryCount;\n      }\n\n      this.icdFormGroup();\n    }\n\n    createFormGroup() {\n      this.formGroup = this.formBuilder.group({\n        icds: new FormArray([])\n      });\n      return this.formGroup;\n    }\n\n    icdFormGroup() {\n      this.icdList.forEach(icdData => {\n        let formGroup = this.formBuilder.group({\n          icd: new FormControl(icdData.icd),\n          isSelected: new FormControl(icdData.isSelected),\n          displayCode: new FormControl(icdData.displayCode),\n          displayCode_v1: new FormControl(icdData.displayCode_v1),\n          shortDescription: new FormControl(icdData.shortDescription),\n          add_date: new FormControl(icdData.add_date),\n          term_date: new FormControl(icdData.term_date)\n        });\n        this.icdFormArray.push(formGroup);\n      });\n    }\n\n    get icdFormArray() {\n      return this.formGroup.controls[\"icds\"];\n    }\n\n    selectIcd(index) {\n      let icdSelectedCount = this.selectedICDCodeCount();\n\n      if (icdSelectedCount < 12) {\n        this.icdFormArray.controls[index].get('isSelected').patchValue(!this.icdFormArray.controls[index].get('isSelected').value);\n        icdSelectedCount = this.selectedICDCodeCount();\n        this.isSelectICDLimitExceed = false;\n      } else {\n        if (this.icdFormArray.controls[index].get('isSelected').value) {\n          this.icdFormArray.controls[index].get('isSelected').patchValue(!this.icdFormArray.controls[index].get('isSelected').value);\n          icdSelectedCount = this.selectedICDCodeCount();\n          this.isSelectICDLimitExceed = false;\n        } else {\n          this.isSelectICDLimitExceed = true;\n        }\n      }\n    }\n\n    selectedICDCodeCount() {\n      return this.icdFormArray.value.filter(item => item.isSelected == true).length + this.icdCodesNonHistoryCount;\n    }\n\n    selectedCPT() {\n      const selectedICDs = this.icdFormArray.value.filter(item => item.isSelected == true);\n      this.dialogRef.close({\n        selectedICDs: selectedICDs\n      });\n    }\n\n    close() {\n      this.dialogRef.close();\n    }\n\n  }\n\n  IcdViewHistoryComponent.ɵfac = function IcdViewHistoryComponent_Factory(t) {\n    return new (t || IcdViewHistoryComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n  };\n\n  IcdViewHistoryComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IcdViewHistoryComponent,\n    selectors: [[\"app-icd-view-history\"]],\n    decls: 26,\n    vars: 4,\n    consts: [[1, \"row\", \"p-1\"], [1, \"dailog-header\", 2, \"padding-bottom\", \"0\"], [\"mat-dialog-title\", \"\", 2, \"margin-bottom\", \"0.5rem !important\"], [\"mat-button\", \"\", \"mat-dialog-title\", \"\", 1, \"dailog-close\", 3, \"click\"], [1, \"fa\", \"fa-times\"], [1, \"dailog-header\", 2, \"padding-bottom\", \"0\", \"border-bottom\", \"none\", \"padding-bottom\", \"0px\"], [2, \"margin-bottom\", \"0.5rem !important\"], [\"class\", \"limt-error\", 4, \"ngIf\"], [1, \"m-0\", \"dialog-inline-height\"], [\"class\", \"col-md-12  row \", \"style\", \"padding-left:3px;\", 4, \"ngIf\"], [\"class\", \"description\", 4, \"ngIf\"], [1, \"bottom-actions\"], [1, \"cancel\"], [\"mat-dialog-close\", \"\", 1, \"btn-common\", \"btn-height\"], [1, \"save\"], [\"type\", \"submit\", 1, \"btn-primary\", \"primary-btn\", \"btn-height\", 3, \"click\"], [1, \"material-icons\", \"icon-margin\", \"icon-height\"], [1, \"limt-error\"], [2, \"color\", \"red\"], [1, \"col-md-12\", \"row\", 2, \"padding-left\", \"3px\"], [\"class\", \"col-lg-3 icdCodesborderStyle border_on_panel\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-3\", \"icdCodesborderStyle\", \"border_on_panel\", 3, \"click\"], [1, \"popover-ellipsis\", \"description-ellipsis\"], [1, \"cpt-span\", 3, \"ngClass\"], [\"matTooltipPosition\", \"above\", 1, \"font-description\", 3, \"matTooltip\"], [1, \"description\"]],\n    template: function IcdViewHistoryComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\")(3, \"h3\", 2);\n        i0.ɵɵtext(4, \"Active Diagnosis Codes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function IcdViewHistoryComponent_Template_button_click_5_listener() {\n          return ctx.close();\n        });\n        i0.ɵɵelement(6, \"i\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\")(9, \"h6\", 6);\n        i0.ɵɵtext(10, \"ICD Indicator: \");\n        i0.ɵɵelementStart(11, \"b\");\n        i0.ɵɵtext(12);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(13, IcdViewHistoryComponent_div_13_Template, 3, 0, \"div\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"mat-dialog-content\", 8);\n        i0.ɵɵtemplate(15, IcdViewHistoryComponent_div_15_Template, 2, 1, \"div\", 9);\n        i0.ɵɵtemplate(16, IcdViewHistoryComponent_div_16_Template, 2, 0, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"mat-dialog-actions\", 11)(18, \"div\", 12)(19, \"button\", 13);\n        i0.ɵɵtext(20, \"Cancel\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(21, \"div\", 14)(22, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function IcdViewHistoryComponent_Template_button_click_22_listener() {\n          return ctx.selectedCPT();\n        });\n        i0.ɵɵelementStart(23, \"i\", 16);\n        i0.ɵɵtext(24, \"assignment_turned_in\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(25, \"Proceed\");\n        i0.ɵɵelementEnd()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate(ctx.icdVersion);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isSelectICDLimitExceed);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.icdFormArray.value.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.icdFormArray.value.length == 0);\n      }\n    },\n    dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.MatTooltip, i2.MatDialogClose, i2.MatDialogTitle, i2.MatDialogContent, i2.MatDialogActions],\n    styles: [\".CPTWidth[_ngcontent-%COMP%]{width:130px}.icdCodesborderStyle[_ngcontent-%COMP%]{border:2px solid #ccc!important;border-radius:10px!important;padding:20px!important;margin:5px!important;width:253px!important;width:fi!important;height:68px!important}.CPTselectspan[_ngcontent-%COMP%]{color:green}.border_on_panel[_ngcontent-%COMP%]{box-shadow:0 1px 2px #0000000d;border:1px solid #3d5c9f!important;border-radius:1px}.font-description[_ngcontent-%COMP%]{font-size:10px}.description-ellipsis[_ngcontent-%COMP%]{text-overflow:ellipsis;overflow:hidden!important;white-space:nowrap}.icdCodesborderStyle[_ngcontent-%COMP%]:hover{cursor:pointer}.btn-height[_ngcontent-%COMP%]{height:25px!important}.bottom-actions[_ngcontent-%COMP%]{align-items:baseline;padding-left:1px!important}sub-heading[_ngcontent-%COMP%]{margin-bottom:5px}.dialog-inline-height[_ngcontent-%COMP%]{max-height:68vh}.limt-error[_ngcontent-%COMP%]{margin-right:348px}.description[_ngcontent-%COMP%]{font-size:15px}\"]\n  });\n  return IcdViewHistoryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}