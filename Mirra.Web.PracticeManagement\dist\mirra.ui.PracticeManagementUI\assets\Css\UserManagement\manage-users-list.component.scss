.MainFlexRow{
    flex-wrap: wrap; margin-top: 10px; padding:5px; 
}
/* applies on full form */

.Full-Form {
    margin-left: 5px;
    font-weight: bolder;
    padding: 5px;
    /* box-shadow: 0px 0px 8px #0000001A; */
    /* border-radius: 8px; */
    opacity: 1;
    background: #F4F7FC 0% 0% no-repeat padding-box;
}


/* for setting first heading box*/

.Form-Header-Row {
    /* position: sticky;
            top: 0px; */
    background-color: white;
    // height: 50px;
    align-items: center;
    box-shadow: 0px 0px 8px #0000001A;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
}


/* for setting heading of first header */

.Form-Header {
    font-size: 23px;
    justify-content: start;
    color: #272D3B;
    font-family: 'Poppins-SemiBold';
}

.Form-Header-1 {
    font-size: 18px;
    font-family: 'Poppins-SemiBold';
    background-color: white;
    padding: 10px;
    padding-left:0;
    align-items: center;
}

.Blue-Heading{
    color:#0074BC;
}

.Grey-Heading{
    color:#646464;
    font-size: 14px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}
.ThBtn{
    font-family: "Poppins-Bold";
    width: 9%;
    color: black !important;
}


.ThBtnIpa{
    font-family: "Poppins-Bold";
    color: black !important;
    width: 6%;
}

.firstName,.lastName{
    width: 15%;
}

.TextOverFlowNoEllipse{
    text-overflow: initial!important;
    padding-right : 0px!important;
    
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC"!important;
    font-family: "FontAwesome"!important;
  }
  :host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon{
      color: initial!important;
  }

  object{
    pointer-events: none;
    position: relative;
}
.ParentOfObject{
    cursor: pointer;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-down:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-up-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}


:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}


.Form-Body {
    box-shadow: 0px 0px 8px #0000001A;
    margin: 0 0.1%;
    padding: 5px;
    border: 1px solid #CCCCCC;
    border-radius: 8px;
    opacity: 1;
    background-color: white;
}


// basic styling for all tds in table
.TableTd{
    text-overflow: ellipsis;
    padding: 1rem 0.5rem!important;
    color:#272D3B;
    font-size: 12px;
    font-family:'Poppins';
}

// background effect on hover on table row
.TableTr:hover{
    background-color:#e3f2fd;
}

.subscriberID{
    padding:15px 0 15px 12px;
}
// style for checkbox and paginator of primeng
::ng-deep .p-checkbox-box{
    border-radius: 50%!important;
}
::ng-deep .p-paginator {
    justify-content: flex-end!important;
}

/* basic setting for all input type */

input[type="text"]
{
    padding-left: 5px;
    border-radius:6px;
    border:1px solid #CCCCCC!important;
    height: 30px;
    color: #000000;
    outline:none;
    min-width: 40px;
    width: 75%;
    font-size:10px;
    font-family: 'Poppins-Bold';
}

// styling for input when on focus
input[type="text"]:focus,input[type="number"]:focus
{
    border:1px solid #61abd4!important;
}

// styling for table headers
th{
        padding-right: 0px!important;
        padding-left: 0px!important;
        background-color: white!important;
}


.MarginLeft{
    margin-left: 8px;
}

.TextCenter{
    text-align: center!important;
}

.btn{
    height: 35px;
    color: white;
    box-shadow: 0px 0px 8px #0000001A;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074BC;
    font-family: 'Poppins-SemiBold';
    font-size: 14px;
}

.roleCode,.uuid,.roleName{
    width: 15%;
}

 
::ng-deep .p-datatable .p-sortable-column .p-sortable-column-icon {
    margin-left: 0rem;
}

// style for checkbox and paginator of primeng
::ng-deep .p-checkbox .p-checkbox-box.p-highlight{
    border-color: #0074BC!important;
    background: #0074BC!important;

}

::ng-deep.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: #0074BC!important;
}


.Width7{
    width: 7%;
}

::ng-deep .p-dropdown .p-dropdown-panel {
    top: -196px!important;
}


.LastTd{
    text-align: center!important;
    position: absolute;
    right: 0;
    background-color: white;
    height: 53px;
    width: 9%;
  }

.LastTh{
    border-bottom: 2px solid #e3f2fd;
    text-align: center!important;
    position: absolute;
    right: 0;
    background-color: white;
    height: 62.5px;
    width: 9%;
  }

  .IconsPadding{
    padding-top: 1%!important;
}

.userName{
    width: 32%;
}


.status{
    width: 20%;
}

#Status{
    width: 40%;
}

.ipaMapped {
    width: 63%;
}

.description {
    width: 43%;
}