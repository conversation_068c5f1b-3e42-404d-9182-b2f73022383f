{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { COMMON_METHODS } from './common-static';\nlet CustomDateFilterComponent = class CustomDateFilterComponent {\n  constructor() {\n    this.dateValue = '';\n    this.hasError = false;\n    this.validDate = null;\n  }\n\n  agInit(params) {\n    this.params = params;\n  }\n\n  onDateChange(event) {\n    const target = event.target;\n    const value = target.value;\n    this.dateValue = value;\n    this.validDate = COMMON_METHODS.validateDateForFilter(value);\n    this.hasError = value && !this.validDate; // Only trigger filter change if date is valid or empty\n\n    if (!value || this.validDate) {\n      this.params.filterChangedCallback();\n    }\n  }\n\n  isFilterActive() {\n    return !!this.validDate;\n  }\n\n  doesFilterPass(params) {\n    if (!this.validDate) return true;\n    const cellValue = COMMON_METHODS.validateDateForFilter(params.data.dosFrom);\n    if (!cellValue) return false;\n    return cellValue.toDateString() === this.validDate.toDateString();\n  }\n\n  getModel() {\n    return this.validDate ? {\n      dateFrom: this.validDate\n    } : null;\n  }\n\n  setModel(model) {\n    if (model && model.dateFrom) {\n      this.validDate = new Date(model.dateFrom);\n      this.dateValue = this.validDate.toISOString().split('T')[0];\n      this.hasError = false;\n    } else {\n      this.dateValue = '';\n      this.validDate = null;\n      this.hasError = false;\n    }\n  }\n\n};\nCustomDateFilterComponent = __decorate([Component({\n  selector: 'custom-date-filter',\n  template: `\n    <div>\n      <input\n        type=\"date\"\n        [value]=\"dateValue\"\n        (input)=\"onDateChange($event)\"\n        class=\"form-control\"\n        [class.is-invalid]=\"hasError\"\n      />\n      <div *ngIf=\"hasError\" class=\"invalid-feedback d-block\">\n        Please enter a valid date (4-digit year required)\n      </div>\n    </div>\n  `,\n  styles: [`\n    .invalid-feedback {\n      font-size: 12px;\n      color: #dc3545;\n      margin-top: 4px;\n    }\n    .is-invalid {\n      border-color: #dc3545;\n    }\n  `]\n})], CustomDateFilterComponent);\nexport { CustomDateFilterComponent };", "map": null, "metadata": {}, "sourceType": "module"}