.Form-Body {
    background-color: white;
    // padding: 5px;
    padding: 5px 7px 5px 5px;
    min-height: 400px;
}

.body {
    background-color: white;
    border-radius: 8px;
    margin-top: 10px;
    box-shadow: 0px 0px 8px #0000001a;
    border-top-right-radius: 5px;
}

// basic styling for all tds in table
.TableTd {
    color: #272d3b;
    font-size: 12px;
    font-family: "Poppins";
}

// background effect on hover on table row
.TableTr:hover {
    background-color: #e3f2fd;
}

// style for checkbox and paginator of primeng
::ng-deep .p-checkbox-box {
    border-radius: 50% !important;
}
::ng-deep .p-paginator {
    margin-right: 1%;
    justify-content: flex-end !important;
}

/* basic setting for all input type */

input[type="text"],
input[type="number"] {
    border-radius: 6px;
    border: 1px solid #cccccc !important;
    height: 30px;
    outline: none;
    min-width: 40px;
    margin-left: 5px;
    color: #757575;
    font-size: 12px;
    font-family: "Poppins";
    font-weight: bold;
    width: 160px;
    padding-left: 5px !important;
}

// styling for input when on focus
input[type="text"]:focus,
input[type="number"]:focus {
    border: 1px solid #61abd4 !important;
}

input[type="checkbox"] {
    cursor: pointer;
}

// styling for table headers
th {
    padding-right: 0px !important;
    padding-left: 0px !important;
    background-color: white !important;
    border-radius: 8px;
}

/* for setting second heading box*/

.Form-Header-2 {
    font-family: "Poppins";
    background-color: white;
    padding: 10px;
    align-items: center;
    border-radius: 40px;
}

.Form-Header-2-Heading {
    color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 18px;
}

p-table {
    margin-left: 16px !important;
    border: 1px solid #cccccc;
    border-radius: 8px;
    width: 97% !important;
}

.TextRight {
    text-align: end;
}

.MarginRight {
    margin-right: 10px;
}

.MarginLeft {
    margin-left: 20px;
}

.btn {
    border: none;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
}

.btn:hover {
    background-color: #005488;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-down:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}
::ng-deep .pi-sort-amount-up-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}

::ng-deep .p-datatable .p-paginator-bottom {
    border-radius: 8px;
}

.ThBtn {
    font-family: "Poppins-Bold";
    width: 8%;
    color: #5d5d5d !important;
}

::ng-deep .p-datatable-resizable .p-datatable-tbody > tr > td,
.p-datatable-resizable .p-datatable-tfoot > tr > td,
.p-datatable-resizable .p-datatable-thead > tr > th {
    text-overflow: ellipsis !important;
}

.IconStyle {
    cursor: pointer;
    position: relative;
    z-index: 1 !important;
}

object {
    position: relative;
    z-index: -1 !important;
}

::ng-deep .p-datatable .p-sortable-column .p-sortable-column-icon {
    margin-left: 0rem;
}

.view {
    margin-left: 2%;
}

.heading {
    margin-left: 18px;
}

.col {
    font-family: "Poppins";
}

.inputFields {
    margin-left: 0px !important;
}

.heading {
    color: #5d5d5d;
    font-family: "Poppins-SemiBold";
    font-size: 18px;
}

.inputView {
    margin-right: -6%;
}

.table {
    margin-left: 5%;
    border: 1px solid #cccccc;
    width: 40%;
    border-radius: 7px;
    border-collapse: inherit;
    border-spacing: 0px;
    font-family: "Poppins-SemiBold";
    min-height: 250px;
    padding: 0px;
}

.th {
    padding-left: 5% !important;
    background-color: #e5e5e5 !important;
    height: 60px;
    font-size: 16px;
    border-radius: 0px !important;
}

.td {
    padding-left: 4%;
}

.plusButton {
    width: 93%;
    text-align: end;
    border-bottom: 0px;
    margin-bottom: 5px;
}

.closeBtn {
    margin-right: 5px;
}

.deactivateBtn {
    margin-right: -2%;
}

.addBtn {
    border: none;
    color: white;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    padding-left: 3%;
    padding-right: 3%;
}

.inputBgColor {
    background-color: white;
}

.updateBtn {
    margin-right: 2%;
}

.btnTd {
    padding-right: 30px !important;
}

.btnRow {
    text-align: end;
    height: 24%;
}

input.ng-invalid.ng-touched {
    border: 1px solid red !important;
}

.inputBorder {
    border: 1px solid red !important;
}

.backBtn {
    border-color: #0074bc !important;
    background-color: white;
    margin-right: 3%;
    color: #0074bc;
    border: 1px solid;
    font-size: 14px;
}

.backBtn:hover {
    border-color: #0074bc !important;
    background-color: white;
}

.deactivateBtn {
    margin-right: 3%;
}

 

 

.providerGroupName {
    width: 17%;
}

.providerGroupID {
    width: 17%;
}

.providerGroupDesc {
    width: 17%;
}

.providerCount {
    width: 17%;
}

.updatedBy {
    width: 20%;
}

.updatedOn {
    width: 18%;
}

.ExtraCol {
    width: 12%;
}

.LastTd {
    position: absolute;
    right: 0;
    background-color: white;
    height: 53px;
    width: 11%;
}

.LastTh {
    position: absolute;
    right: 0;
    background-color: white;
    height: 62.5px;
    width: 11%;
}

.IconsPadding {
    padding-top: 1% !important;
}

.marginBottom {
    margin-bottom: 200px;
}
