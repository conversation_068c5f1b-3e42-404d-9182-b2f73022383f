{"ast": null, "code": "import { FormControl, Validators } from '@angular/forms';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport { zipCodeValidator } from 'src/app/shared/functions/customFormValidators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ng-select/ng-select\";\nimport * as i4 from \"../../../../../shared/directives/numbers-only.directive\";\nimport * as i5 from \"../../../../../shared/directives/address-related-characters.directive\";\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction InsureAddressComponent_input_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 19);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r0.isFormReadOnly)(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, (ctx_r0.f.n301SubscriberAddr1 == null ? null : ctx_r0.f.n301SubscriberAddr1.invalid) && (ctx_r0.f.n301SubscriberAddr1 == null ? null : ctx_r0.f.n301SubscriberAddr1.errors)));\n  }\n}\n\nfunction InsureAddressComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" The 1st Address field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction InsureAddressComponent_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r2.f.n301SubscriberAddr1.value && ctx_r2.f.n301SubscriberAddr1.value.length > 0 ? ctx_r2.f.n301SubscriberAddr1.value : \"-\");\n  }\n}\n\nfunction InsureAddressComponent_input_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 22);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r3.isFormReadOnly);\n  }\n}\n\nfunction InsureAddressComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r4.f.n302SubscriberAddr2.value && ctx_r4.f.n302SubscriberAddr2.value.length > 0 ? ctx_r4.f.n302SubscriberAddr2.value : \"-\");\n  }\n}\n\nfunction InsureAddressComponent_input_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 23);\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r5.isFormReadOnly)(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, (ctx_r5.f.n401SubscriberCity == null ? null : ctx_r5.f.n401SubscriberCity.invalid) && (ctx_r5.f.n401SubscriberCity == null ? null : ctx_r5.f.n401SubscriberCity.errors)));\n  }\n}\n\nfunction InsureAddressComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" The City field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction InsureAddressComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r7.f.n401SubscriberCity.value && ctx_r7.f.n401SubscriberCity.value.length > 0 ? ctx_r7.f.n401SubscriberCity.value : \"-\");\n  }\n}\n\nfunction InsureAddressComponent_ng_select_38_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"uppercase\");\n    i0.ɵɵpipe(2, \"uppercase\");\n  }\n\n  if (rf & 2) {\n    const item_r20 = ctx.item;\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(1, 2, item_r20.stateCode), \"-\", i0.ɵɵpipeBind1(2, 4, item_r20.stateName), \" \");\n  }\n}\n\nfunction InsureAddressComponent_ng_select_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-select\", 24, 25);\n    i0.ɵɵtemplate(2, InsureAddressComponent_ng_select_38_ng_template_2_Template, 3, 6, \"ng-template\", 26);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r8.isFormReadOnly)(\"items\", ctx_r8.allStateData)(\"searchFn\", ctx_r8.StateSearch)(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, (ctx_r8.f.n402SubscriberState == null ? null : ctx_r8.f.n402SubscriberState.invalid) && (ctx_r8.f.n402SubscriberState.dirty || ctx_r8.f.n402SubscriberState.touched) && (ctx_r8.f.n402SubscriberState == null ? null : ctx_r8.f.n402SubscriberState.errors == null ? null : ctx_r8.f.n402SubscriberState.errors.required)));\n  }\n}\n\nfunction InsureAddressComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" The State field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction InsureAddressComponent_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r10.f.n402SubscriberState.value && ctx_r10.f.n402SubscriberState.value.length > 0 ? ctx_r10.f.n402SubscriberState.value : \"-\");\n  }\n}\n\nfunction InsureAddressComponent_input_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 27);\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r11.isFormReadOnly)(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, (ctx_r11.f.n403SubscriberZip == null ? null : ctx_r11.f.n403SubscriberZip.invalid) && (ctx_r11.f.n403SubscriberZip == null ? null : ctx_r11.f.n403SubscriberZip.errors)));\n  }\n}\n\nfunction InsureAddressComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" The Zip field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction InsureAddressComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" Invalid Zip Code. Please enter only 9 digit number. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction InsureAddressComponent_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r14.f.n403SubscriberZip.value && ctx_r14.f.n403SubscriberZip.value.length > 0 ? ctx_r14.f.n403SubscriberZip.value : \"-\");\n  }\n}\n\nfunction InsureAddressComponent_input_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 28);\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r15.isFormReadOnly)(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, (ctx_r15.f.per04SubscriberPhoneNo == null ? null : ctx_r15.f.per04SubscriberPhoneNo.invalid) && (ctx_r15.f.per04SubscriberPhoneNo == null ? null : ctx_r15.f.per04SubscriberPhoneNo.errors)));\n  }\n}\n\nfunction InsureAddressComponent_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r16.f.per04SubscriberPhoneNo.value && ctx_r16.f.per04SubscriberPhoneNo.value.length > 0 ? ctx_r16.f.per04SubscriberPhoneNo.value : \"-\");\n  }\n}\n\nfunction InsureAddressComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" Invalid Telephone. Please enter only 10 digit number. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport let InsureAddressComponent = /*#__PURE__*/(() => {\n  class InsureAddressComponent {\n    constructor(patientAddressForm) {\n      this.patientAddressForm = patientAddressForm;\n      this.isFormReadOnly = true;\n    }\n\n    get f() {\n      return this.patientAddress.controls;\n    }\n\n    ngOnInit() {\n      this.patchValue();\n    }\n\n    createForm() {\n      this.patientAddress = this.patientAddressForm.group({\n        n301SubscriberAddr1: new FormControl('', Validators.required),\n        n302SubscriberAddr2: new FormControl(''),\n        n401SubscriberCity: new FormControl('', Validators.required),\n        n402SubscriberState: new FormControl('', [Validators.required]),\n        n403SubscriberZip: new FormControl('', [Validators.required, zipCodeValidator]),\n        per04SubscriberPhoneNo: new FormControl('')\n      });\n      return this.patientAddress;\n    }\n\n    patchValue() {\n      if (this.claimFormData.isAddClaim) {\n        this.patientAddress.patchValue({\n          n301SubscriberAddr1: this.insuredPerson?.addressLine1,\n          n302SubscriberAddr2: this.insuredPerson?.addressLine2,\n          n401SubscriberCity: this.insuredPerson?.city,\n          n402SubscriberState: this.insuredPerson?.stateSubdivisionCode,\n          n403SubscriberZip: this.insuredPerson?.zipCode,\n          per04SubscriberPhoneNo: this.insuredPerson?.number\n        });\n        submitValidateAllFields.validateDisableControl(this.patientAddress, [\"\"]);\n      } else if (this.claimFormData.isEditClaim || this.claimFormData.isViewClaim) {\n        this.patientAddress.patchValue({\n          n301SubscriberAddr1: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.n301SubscriberAddr1,\n          n302SubscriberAddr2: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.n302SubscriberAddr2,\n          n401SubscriberCity: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.n401SubscriberCity,\n          n402SubscriberState: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.n402SubscriberState,\n          n403SubscriberZip: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.n403SubscriberZip,\n          per04SubscriberPhoneNo: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.per04SubscriberPhoneNo\n        });\n\n        if (this.claimFormData.isViewClaim) {\n          this.patientAddress.disable();\n        } else {\n          this.patientAddress.markAllAsTouched(); // submitValidateAllFields.validateDisableControl(this.patientAddress,['']);\n\n          this.patientAddress.updateValueAndValidity();\n        }\n      }\n\n      if (!!this.patientAddress.controls.n403SubscriberZip.value) {\n        this.patientAddress.controls.n403SubscriberZip.setValue(this.patientAddress.controls.n403SubscriberZip.value.trim().replace(/[^0-9]/g, ''));\n      }\n\n      if (!!this.patientAddress.controls.per04SubscriberPhoneNo.value) {\n        this.patientAddress.controls.per04SubscriberPhoneNo.setValue(this.patientAddress.controls.per04SubscriberPhoneNo.value.trim().replace(/[^a-zA-Z0-9]/g, ''));\n      }\n\n      if (!!this.patientAddress.controls.n301SubscriberAddr1.value) {\n        this.patientAddress.controls.n301SubscriberAddr1.setValue(this.patientAddress.controls.n301SubscriberAddr1.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));\n      }\n\n      if (!!this.patientAddress.controls.n302SubscriberAddr2.value) {\n        this.patientAddress.controls.n302SubscriberAddr2.setValue(this.patientAddress.controls.n302SubscriberAddr2.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));\n      }\n\n      if (!!this.patientAddress.controls.n401SubscriberCity.value) {\n        this.patientAddress.controls.n401SubscriberCity.setValue(this.patientAddress.controls.n401SubscriberCity.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));\n      }\n    }\n\n    validateForm() {\n      if (this.patientAddress.invalid) {\n        submitValidateAllFields.validateAllFields(this.patientAddress);\n        return false;\n      }\n\n      return true;\n    }\n\n    StateSearch(term, item) {\n      term = term.toLocaleLowerCase();\n      return item.stateCode.toLocaleLowerCase().indexOf(term) > -1 || item.stateName.toLocaleLowerCase().indexOf(term) > -1;\n    }\n\n  }\n\n  InsureAddressComponent.ɵfac = function InsureAddressComponent_Factory(t) {\n    return new (t || InsureAddressComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n  };\n\n  InsureAddressComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: InsureAddressComponent,\n    selectors: [[\"app-insure-address\"]],\n    inputs: {\n      insuredPerson: \"insuredPerson\",\n      allStateData: \"allStateData\",\n      claimFormData: \"claimFormData\"\n    },\n    decls: 50,\n    vars: 19,\n    consts: [[3, \"formGroup\"], [1, \"row\", \"mt-2\"], [1, \"form-title\"], [1, \"col-4\"], [\"for\", \"InsuredStreetNumber\", 1, \"create-claims-labels\"], [\"for\", \"InsuredAddressLine2\", 1, \"create-claims-labels\"], [\"for\", \"InsuredCity\", 1, \"create-claims-labels\"], [1, \"row\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"n301SubscriberAddr1\", \"addressRelatedCharacters\", \"\", \"placeholder\", \"Address\", 3, \"readonly\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"form-control\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"n302SubscriberAddr2\", \"addressRelatedCharacters\", \"\", \"placeholder\", \"Address\", 3, \"readonly\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"n401SubscriberCity\", \"addressRelatedCharacters\", \"\", \"placeholder\", \"City\", 3, \"readonly\", \"ngClass\", 4, \"ngIf\"], [\"for\", \"InsuredState\", 1, \"create-claims-labels\"], [\"for\", \"InsuredZip\", 1, \"create-claims-labels\"], [\"for\", \"InsuredTelephone\", 1, \"create-claims-labels\"], [\"class\", \"form-control form-control-sm\", \"placeholder\", \"State\", \"bindLabel\", \"stateName\", \"bindValue\", \"stateCode\", \"formControlName\", \"n402SubscriberState\", 3, \"readonly\", \"items\", \"searchFn\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"n403SubscriberZip\", \"numbersOnly\", \"\", \"placeholder\", \"Zip\", \"maxlength\", \"9\", 3, \"readonly\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"formControlName\", \"per04SubscriberPhoneNo\", \"numbersOnly\", \"\", \"placeholder\", \"Telephone\", \"minlength\", \"10\", \"maxlength\", \"10\", 3, \"readonly\", \"ngClass\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"n301SubscriberAddr1\", \"addressRelatedCharacters\", \"\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"ngClass\"], [1, \"invalid-feedback\"], [1, \"form-control\"], [\"type\", \"text\", \"formControlName\", \"n302SubscriberAddr2\", \"addressRelatedCharacters\", \"\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"type\", \"text\", \"formControlName\", \"n401SubscriberCity\", \"addressRelatedCharacters\", \"\", \"placeholder\", \"City\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"ngClass\"], [\"placeholder\", \"State\", \"bindLabel\", \"stateName\", \"bindValue\", \"stateCode\", \"formControlName\", \"n402SubscriberState\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"items\", \"searchFn\", \"ngClass\"], [\"assignto\", \"\"], [\"ng-option-tmp\", \"\", \"ng-label-tmp\", \"\"], [\"type\", \"text\", \"formControlName\", \"n403SubscriberZip\", \"numbersOnly\", \"\", \"placeholder\", \"Zip\", \"maxlength\", \"9\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"per04SubscriberPhoneNo\", \"numbersOnly\", \"\", \"placeholder\", \"Telephone\", \"minlength\", \"10\", \"maxlength\", \"10\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"ngClass\"]],\n    template: function InsureAddressComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"p\", 2);\n        i0.ɵɵtext(3, \"7. Insured's Address\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 1)(5, \"div\", 3)(6, \"label\", 4);\n        i0.ɵɵtext(7, \"Address Line - 1 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 3)(9, \"label\", 5);\n        i0.ɵɵtext(10, \"Address Line - 2\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 3)(12, \"label\", 6);\n        i0.ɵɵtext(13, \"City\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(14, \"div\", 7)(15, \"div\", 3);\n        i0.ɵɵtemplate(16, InsureAddressComponent_input_16_Template, 1, 4, \"input\", 8);\n        i0.ɵɵtemplate(17, InsureAddressComponent_div_17_Template, 2, 0, \"div\", 9);\n        i0.ɵɵtemplate(18, InsureAddressComponent_span_18_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 3);\n        i0.ɵɵtemplate(20, InsureAddressComponent_input_20_Template, 1, 1, \"input\", 11);\n        i0.ɵɵtemplate(21, InsureAddressComponent_span_21_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 3);\n        i0.ɵɵtemplate(23, InsureAddressComponent_input_23_Template, 1, 4, \"input\", 12);\n        i0.ɵɵtemplate(24, InsureAddressComponent_div_24_Template, 2, 0, \"div\", 9);\n        i0.ɵɵtemplate(25, InsureAddressComponent_span_25_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 1)(27, \"div\", 3)(28, \"label\", 13);\n        i0.ɵɵtext(29, \"State \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"div\", 3)(31, \"label\", 14);\n        i0.ɵɵtext(32, \"Zip\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"div\", 3)(34, \"label\", 15);\n        i0.ɵɵtext(35, \"Telephone\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(36, \"div\", 7)(37, \"div\", 3);\n        i0.ɵɵtemplate(38, InsureAddressComponent_ng_select_38_Template, 3, 6, \"ng-select\", 16);\n        i0.ɵɵtemplate(39, InsureAddressComponent_div_39_Template, 2, 0, \"div\", 9);\n        i0.ɵɵtemplate(40, InsureAddressComponent_span_40_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"div\", 3);\n        i0.ɵɵtemplate(42, InsureAddressComponent_input_42_Template, 1, 4, \"input\", 17);\n        i0.ɵɵtemplate(43, InsureAddressComponent_div_43_Template, 2, 0, \"div\", 9);\n        i0.ɵɵtemplate(44, InsureAddressComponent_div_44_Template, 2, 0, \"div\", 9);\n        i0.ɵɵtemplate(45, InsureAddressComponent_span_45_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(46, \"div\", 3);\n        i0.ɵɵtemplate(47, InsureAddressComponent_input_47_Template, 1, 4, \"input\", 18);\n        i0.ɵɵtemplate(48, InsureAddressComponent_span_48_Template, 2, 1, \"span\", 10);\n        i0.ɵɵtemplate(49, InsureAddressComponent_div_49_Template, 2, 0, \"div\", 9);\n        i0.ɵɵelementEnd()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.patientAddress);\n        i0.ɵɵadvance(16);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.n301SubscriberAddr1 == null ? null : ctx.f.n301SubscriberAddr1.invalid) && ctx.f.n301SubscriberAddr1.errors.required);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.n401SubscriberCity == null ? null : ctx.f.n401SubscriberCity.invalid) && ctx.f.n401SubscriberCity.errors.required);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.n402SubscriberState == null ? null : ctx.f.n402SubscriberState.invalid) && ctx.f.n402SubscriberState.errors.required);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.n403SubscriberZip == null ? null : ctx.f.n403SubscriberZip.invalid) && ctx.f.n403SubscriberZip.errors.required);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.n403SubscriberZip == null ? null : ctx.f.n403SubscriberZip.invalid) && !ctx.f.n403SubscriberZip.errors.required && ctx.f.n403SubscriberZip.errors.isInvalid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.per04SubscriberPhoneNo == null ? null : ctx.f.per04SubscriberPhoneNo.invalid) && ctx.f.per04SubscriberPhoneNo.errors);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinLengthValidator, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i3.NgSelectComponent, i3.NgOptionTemplateDirective, i3.NgLabelTemplateDirective, i4.OnlyNumberDirective, i5.AddressRelatedCharactersDirective, i2.UpperCasePipe],\n    styles: [\".create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.search-icon-alignment[_ngcontent-%COMP%]{position:relative}.search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%], .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}input[readonly][_ngcontent-%COMP%]{background-color:#e9ecef;opacity:1;border:1px solid #ced4da}\"]\n  });\n  return InsureAddressComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}