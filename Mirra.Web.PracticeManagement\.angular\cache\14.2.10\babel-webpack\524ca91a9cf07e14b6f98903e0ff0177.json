{"ast": null, "code": "import { Tabs } from \"./nav-constant\";\nimport Swal from \"sweetalert2\";\nexport class ssnMask {\n  static ssnMaskApply(value) {\n    return `${'X'.repeat(3)}${'-'}${'X'.repeat(2)}${'-'}${value.substr(value.length - 5, value.length)}`;\n  }\n\n}\nexport var CLAIM_STATUS = /*#__PURE__*/(() => {\n  (function (CLAIM_STATUS) {\n    CLAIM_STATUS[\"INIT\"] = \"INIT\";\n    CLAIM_STATUS[\"RECEIVED\"] = \"RECEIVED\";\n    CLAIM_STATUS[\"RECEIVEDPAYER\"] = \"RECEIVEDPAYER\";\n    CLAIM_STATUS[\"PAYMENT\"] = \"PAYMENT\";\n  })(CLAIM_STATUS || (CLAIM_STATUS = {}));\n\n  return CLAIM_STATUS;\n})();\nexport var CLAIM_TYPE = /*#__PURE__*/(() => {\n  (function (CLAIM_TYPE) {\n    CLAIM_TYPE[\"open\"] = \"ON\";\n    CLAIM_TYPE[\"onHold\"] = \"OH\";\n    CLAIM_TYPE[\"accepted\"] = \"AC\";\n    CLAIM_TYPE[\"dispatched\"] = \"DP\";\n    CLAIM_TYPE[\"unackByCH\"] = \"UACH\";\n    CLAIM_TYPE[\"rejectedByCH\"] = \"RCH\";\n    CLAIM_TYPE[\"acknolwedgedByCH\"] = \"ANCH\";\n    CLAIM_TYPE[\"unAckByPayer\"] = \"UAP\";\n    CLAIM_TYPE[\"rejectedByPayer\"] = \"RP\";\n    CLAIM_TYPE[\"acknolwedgedByPayer\"] = \"AP\";\n    CLAIM_TYPE[\"acceptedByPayer\"] = \"ABP\";\n    CLAIM_TYPE[\"pending\"] = \"Pend\";\n    CLAIM_TYPE[\"eobReceived\"] = \"EOB\";\n    CLAIM_TYPE[\"deniedByPayer\"] = \"DBP\";\n    CLAIM_TYPE[\"ntr\"] = \"NTR\";\n    CLAIM_TYPE[\"acceptedByCH\"] = \"ABCH\";\n    CLAIM_TYPE[\"acceptedBucket\"] = \"accepted\";\n  })(CLAIM_TYPE || (CLAIM_TYPE = {}));\n\n  return CLAIM_TYPE;\n})();\nexport var COMMON_VALUES = /*#__PURE__*/(() => {\n  (function (COMMON_VALUES) {\n    COMMON_VALUES[\"all\"] = \"All\";\n    COMMON_VALUES[\"resubmittedClaims\"] = \"Resubmitted Claims\";\n    COMMON_VALUES[\"deactivatedClaims\"] = \"Deactivated Claims\";\n    COMMON_VALUES[\"includeAllClaims\"] = \"INCLUDE ALL CLAIMS\";\n    COMMON_VALUES[\"includeLatestClaims\"] = \"INCLUDE LATEST CLAIMS\";\n    COMMON_VALUES[COMMON_VALUES[\"maximumResubmissionCount\"] = 50] = \"maximumResubmissionCount\";\n    COMMON_VALUES[\"fileType\"] = \"005010X231A1\";\n    COMMON_VALUES[COMMON_VALUES[\"_999\"] = 999] = \"_999\";\n    COMMON_VALUES[COMMON_VALUES[\"_277\"] = 277] = \"_277\";\n    COMMON_VALUES[\"GATEWAY\"] = \"GATEWAY\";\n    COMMON_VALUES[COMMON_VALUES[\"Resubmit_File_PatientControlNumberLimit\"] = 10] = \"Resubmit_File_PatientControlNumberLimit\";\n    COMMON_VALUES[\"inactivelogin\"] = \"inactivelogin\";\n    COMMON_VALUES[\"invalid\"] = \"invalid\";\n    COMMON_VALUES[\"patientInsured\"] = \"Patient & Insured\";\n  })(COMMON_VALUES || (COMMON_VALUES = {}));\n\n  return COMMON_VALUES;\n})();\nexport class YearsValuesByCurentYerar {\n  static getYears() {\n    let years = [];\n    let selectedYear = new Date().getFullYear();\n\n    for (let i = 0; i < 3; i++) {\n      let year = selectedYear - i;\n      years.push({\n        id: i,\n        year: year\n      });\n      years = years.sort();\n    }\n\n    years.push({\n      id: years.length,\n      year: 'All'\n    });\n    return years;\n  }\n\n  static getMonthsNames() {\n    const months = [{\n      id: 0,\n      name: \"All\",\n      code: \"All\"\n    }, {\n      id: 1,\n      name: \"January\",\n      code: \"Jan\"\n    }, {\n      id: 2,\n      name: \"February\",\n      code: \"Feb\"\n    }, {\n      id: 3,\n      name: \"March\",\n      code: \"Mar\"\n    }, {\n      id: 4,\n      name: \"April\",\n      code: \"Apr\"\n    }, {\n      id: 5,\n      name: \"May\",\n      code: \"May\"\n    }, {\n      id: 6,\n      name: \"June\",\n      code: \"Jun\"\n    }, {\n      id: 7,\n      name: \"July\",\n      code: \"Jul\"\n    }, {\n      id: 8,\n      name: \"August\",\n      code: \"Aug\"\n    }, {\n      id: 9,\n      name: \"September\",\n      code: \"Sep\"\n    }, {\n      id: 10,\n      name: \"October\",\n      code: \"Oct\"\n    }, {\n      id: 11,\n      name: \"November\",\n      code: \"Nov\"\n    }, {\n      id: 12,\n      name: \"December\",\n      code: \"Dec\"\n    }];\n    return months;\n  }\n\n}\nexport class ClaimsActions {\n  static claimsTypeDashboardFilter() {\n    const claimTypes = [{\n      id: 1,\n      name: 'INCLUDE ALL CLAIMS'\n    }, {\n      id: 2,\n      name: 'INCLUDE LATEST CLAIMS'\n    }];\n    return claimTypes;\n  }\n\n}\nexport class COMMON_METHODS {\n  static currencyFormatter(currency) {\n    let resp = null;\n\n    if (typeof currency === 'number' && !isNaN(currency)) {\n      const currencyVallue = currency ? Number(currency) : 0;\n      var sansDec = currencyVallue?.toFixed(2);\n      var formatted = sansDec.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n      resp = '$' + `${formatted}`;\n    }\n\n    return resp;\n  }\n\n  static removeDollarSign(amountString) {\n    const dollarRegex = /\\$/;\n\n    if (!!amountString && dollarRegex.test(amountString)) {\n      return amountString.replace(/\\$/g, '');\n    }\n\n    return amountString;\n  }\n\n  static getClaimsStatusArray() {\n    return [Tabs.open, Tabs.onHold, Tabs.resubmission, Tabs.dispatched, Tabs.accepted, Tabs.unAckByCH, Tabs.ackByCH, Tabs.rejectedByCH, Tabs.acceptedByCH, Tabs.unAckByPayer, Tabs.ackByPayer, Tabs.rejectedByPayer, Tabs.acceptedByPayer, Tabs.pending, Tabs.eobReceived, Tabs.deniedByPayer];\n  }\n\n  static getCountries() {\n    const countries = [{\n      id: 1,\n      countryCode: \"US\",\n      countryName: \"United States\"\n    }];\n    return countries;\n  }\n\n  static hasAlphabet(input) {\n    return /[a-zA-Z]/.test(input);\n  }\n\n  static truncateTextFormatter(params, length = 15) {\n    const value = params.value; // Ensure value is a string and not null/undefined\n\n    if (value && typeof value === 'string' && value.length > length) {\n      return value.substring(0, length) + '...';\n    }\n\n    return value;\n  }\n\n  static validateDateForFilter(dateValue) {\n    if (!dateValue) return null;\n    const date = new Date(dateValue); // Comprehensive date validation\n\n    if (isNaN(date.getTime())) return null; // Invalid date\n\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1; // getMonth() returns 0-11\n\n    const day = date.getDate(); // Validate year (must be exactly 4 digits and reasonable range)\n\n    if (year < 2010 || year > new Date().getFullYear()) {\n      return null;\n    } // Validate month (1-12)\n\n\n    if (month < 1 || month > 12) return null; // Validate day (1-31, but also check for valid day in month)\n\n    if (day < 1 || day > 31) return null; // Additional check: ensure the date is actually valid for the month/year\n\n    const reconstructedDate = new Date(year, month - 1, day);\n\n    if (reconstructedDate.getFullYear() !== year || reconstructedDate.getMonth() !== month - 1 || reconstructedDate.getDate() !== day) {\n      return null;\n    }\n\n    console.log('Valid date returned:', date);\n    return date;\n  }\n\n}\nexport var VALIDATION_PATTERNS = /*#__PURE__*/(() => {\n  (function (VALIDATION_PATTERNS) {\n    VALIDATION_PATTERNS[\"emailPattern\"] = \"^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\.[A-Za-z]{2,4}$\";\n    VALIDATION_PATTERNS[\"emailMaxLength\"] = \"60\";\n  })(VALIDATION_PATTERNS || (VALIDATION_PATTERNS = {}));\n\n  return VALIDATION_PATTERNS;\n})();\nexport class FromDateToDateCompareValidations {\n  //scenario :2\n  //====================\n  // startDate allows\n  //   Today,s Date\n  // Back Dated\n  // future dated aloowed\n  static startDateNoMaxLimitChangeEvent(form, startDateValue, startDateControl, endDateControl) {\n    let endMinDate;\n    endMinDate = new Date(startDateValue);\n    let startDate = new Date(startDateValue);\n    let endDateValue = form.get(endDateControl).value;\n\n    if (startDateValue && endDateValue && startDate) {\n      let endDate = new Date(endDateValue);\n\n      if (startDate > endDate) {\n        form.controls[startDateControl].setErrors({\n          'startDateGreaterError': true\n        });\n      } else {\n        form.controls[startDateControl].setErrors(null);\n        form.controls[endDateControl].setErrors(null);\n      }\n    }\n\n    return endMinDate;\n  } // End Date\n  //Today,s Date\n  // Back Dated\n  // future dated\n  // startDate not future Date.\n\n\n  static endDateNoStartDateLimitChangeEvent(form, endDateControl, startDateControl, endDateValue) {\n    let endDate = new Date(endDateValue);\n    let startDate = new Date(form.get(startDateControl).value);\n\n    if (endDateValue && endDate && startDate) {\n      if (startDate <= endDate) {\n        form.controls[startDateControl].setErrors(null);\n      } else if (startDate > endDate) {\n        form.controls[endDateControl].setErrors({\n          'endDateGreaterError': true\n        });\n      }\n    }\n  }\n\n}\nexport var DateValidationErrors = /*#__PURE__*/(() => {\n  (function (DateValidationErrors) {\n    DateValidationErrors[\"minEndDateErrorMsg\"] = \" Effective To Date must be greater than Effective From Date. \";\n    DateValidationErrors[\"maxStartDateErrorMsg\"] = \"Start Date should not be greater than current date.\";\n    DateValidationErrors[\"maxEndDateErrorMsg\"] = \"End Date should not be greater than current date.\";\n    DateValidationErrors[\"startDateGreaterErrorMsg\"] = \"Effective From Date should not be greater than Effective To Date.\";\n    DateValidationErrors[\"invalidDateErrorMsg\"] = \"Please select valid date.\";\n  })(DateValidationErrors || (DateValidationErrors = {}));\n\n  return DateValidationErrors;\n})();\nexport var PREVILEGES = /*#__PURE__*/(() => {\n  (function (PREVILEGES) {\n    PREVILEGES[\"CLAIMS_BillingManagement_CreateClaim\"] = \"CLAIMS_BillingManagement_CreateClaim\";\n    PREVILEGES[\"CLAIMS_BillingManagement_AddFacility\"] = \"CLAIMS_BillingManagement_AddFacility\";\n    PREVILEGES[\"CLAIMS_BillingManagement_AddProvider\"] = \"CLAIMS_BillingManagement_AddProvider\";\n    PREVILEGES[\"CLAIMS_BillingManagement_AddMember\"] = \"CLAIMS_BillingManagement_AddMember\";\n    PREVILEGES[\"MemberHouse_MemberManagement_Claims_EditMember\"] = \"MemberHouse_MemberManagement_Claims_EditMember\";\n    PREVILEGES[\"MemberHouse_MemberManagement_Claims_ViewMember\"] = \"MemberHouse_MemberManagement_Claims_ViewMember\";\n    PREVILEGES[\"CLAIMS_BillingManagement_DeactivateaClaim\"] = \"CLAIMS_BillingManagement_DeactivateaClaim\";\n    PREVILEGES[\"CLAIMS_BillingManagement_DownloadEDIFile\"] = \"CLAIMS_BillingManagement_DownloadEDIFile\";\n    PREVILEGES[\"CLAIMS_BillingManagement_GenerateEDI837File\"] = \"CLAIMS_BillingManagement_GenerateEDI837File\";\n    PREVILEGES[\"CLAIMS_BillingManagement_GetFileListIrespectiveOfFileType\"] = \"CLAIMS_BillingManagement_GetFileListIrespectiveOfFileType\";\n    PREVILEGES[\"CLAIMS_BillingManagement_ReadlistofEOBfile\"] = \"CLAIMS_BillingManagement_ReadlistofEOBfile\";\n    PREVILEGES[\"CLAIMS_BillingManagement_ViewTrackingDashBoard\"] = \"CLAIMS_BillingManagement_ViewTrackingDashBoard\";\n    PREVILEGES[\"CLAIMS_BillingManagement_ViewAdmin\"] = \"CLAIMS_BillingManagement_ViewAdmin\";\n    PREVILEGES[\"CLAIMS_BillingManagement_SearchProvider\"] = \"CLAIMS_BillingManagement_SearchProvider\";\n    PREVILEGES[\"MemberHouse_MemberManagement_Claims_SearchMember\"] = \"MemberHouse_MemberManagement_Claims_SearchMember\";\n    PREVILEGES[\"CLAIMS_BillingManagement_AcceptAClaim\"] = \"CLAIMS_BillingManagement_AcceptAClaim\";\n    PREVILEGES[\"CLAIMS_BillingManagement_ActivateaClaim\"] = \"CLAIMS_BillingManagement_ActivateaClaim\";\n    PREVILEGES[\"UserManagement_ViewAllUsers\"] = \"UserManagement_ViewAllUsers\";\n    PREVILEGES[\"UserManagement_ViewAllRoles\"] = \"UserManagement_ViewAllRoles\";\n    PREVILEGES[\"UserManagement_ViewAllUsersIPAMapping\"] = \"UserManagement_ViewAllUsersIPAMapping\";\n    PREVILEGES[\"UserManagement_AddRole\"] = \"UserManagement_AddRole\";\n    PREVILEGES[\"UserManagement_EditRole\"] = \"UserManagement_EditRole\";\n    PREVILEGES[\"UserManagement_ViewRole\"] = \"UserManagement_ViewRole\";\n    PREVILEGES[\"UserManagement_AddUser\"] = \"UserManagement_AddUser\";\n    PREVILEGES[\"UserManagement_EditUser\"] = \"UserManagement_EditUser\";\n    PREVILEGES[\"UserManagement_ViewUser\"] = \"UserManagement_ViewUser\";\n    PREVILEGES[\"UserManagement_AddUserIPA\"] = \"UserManagement_AddUserIPA\";\n    PREVILEGES[\"UserManagement_EditUserIPA\"] = \"UserManagement_EditUserIPA\";\n    PREVILEGES[\"ProviderVilla_ProviderBridge_EditProvider\"] = \"ProviderVilla_ProviderBridge_EditProvider\";\n    PREVILEGES[\"CLAIMS_BillingManagement_MoveAllOpenToAccepted_Biller\"] = \"CLAIMS_BillingManagement_MoveAllOpenToAccepted_Biller\";\n    PREVILEGES[\"CLAIMS_BillingManagement_MoveAllOpenToAccepted\"] = \"CLAIMS_BillingManagement_MoveAllOpenToAccepted\";\n    PREVILEGES[\"CLAIMS_BillingManagement_MoveSelectedOpenToAccepted\"] = \"CLAIMS_BillingManagement_MoveSelectedOpenToAccepted\";\n    PREVILEGES[\"CLAIMS_BillingManagement_OnHoldAClaim\"] = \"CLAIMS_BillingManagement_OnHoldAClaim\";\n    PREVILEGES[\"CLAIMS_BillingManagement_Resubmission\"] = \"CLAIMS_BillingManagement_Resubmission\";\n    PREVILEGES[\"CLAIMS_BillingManagement_UpdateaClaim\"] = \"CLAIMS_BillingManagement_UpdateaClaim\";\n    PREVILEGES[\"CLAIMS_BillingManagement_UploadFiles\"] = \"CLAIMS_BillingManagement_UploadFiles\";\n    PREVILEGES[\"CLAIMS_BillingManagement_ViewReportsLibrary\"] = \"CLAIMS_BillingManagement_ViewReportsLibrary\";\n    PREVILEGES[\"CLAIMS_BillingManagement_ExportEOBreportToExcel\"] = \"CLAIMS_BillingManagement_ExportEOBreportToExcel\";\n  })(PREVILEGES || (PREVILEGES = {}));\n\n  return PREVILEGES;\n})();\nexport var ClaimFormType = /*#__PURE__*/(() => {\n  (function (ClaimFormType) {\n    ClaimFormType[ClaimFormType[\"addClaim\"] = 1] = \"addClaim\";\n    ClaimFormType[ClaimFormType[\"viewClaim\"] = 2] = \"viewClaim\";\n    ClaimFormType[ClaimFormType[\"editClaim\"] = 3] = \"editClaim\";\n    ClaimFormType[ClaimFormType[\"loadClaim\"] = 4] = \"loadClaim\";\n    ClaimFormType[ClaimFormType[\"memberClaim\"] = 5] = \"memberClaim\";\n  })(ClaimFormType || (ClaimFormType = {}));\n\n  return ClaimFormType;\n})();\nexport class Insurance {\n  static InsuranceTypes() {\n    const insuranceTypes = [{\n      id: 1,\n      name: 'MEDICARE',\n      value: 'MB'\n    }, {\n      id: 2,\n      name: 'MEDICAID',\n      value: 'MC'\n    }, {\n      id: 3,\n      name: 'TRICARE',\n      value: 'TR'\n    }, {\n      id: 4,\n      name: 'CHAMPVA',\n      value: 'CH'\n    }, {\n      id: 5,\n      name: 'GROUP HEALTH PLAN',\n      value: 'GR'\n    }, {\n      id: 6,\n      name: 'FECA BLK LUNG',\n      value: 'FE'\n    }, {\n      id: 7,\n      name: 'OTHERS',\n      value: 'CI'\n    }];\n    return insuranceTypes;\n  }\n\n}\nexport var ValidationMsgs = /*#__PURE__*/(() => {\n  (function (ValidationMsgs) {\n    ValidationMsgs[\"no_data_available\"] = \"<span  class=\\\"uppar-case\\\"> No Data Available.</span>\";\n    ValidationMsgs[\"overlayLoadingTemplate\"] = \"<span class=\\\"ag-overlay-loading-center uppar-case\\\" >Please wait while your rows are loading.</span>\";\n    ValidationMsgs[\"overlayTemplate\"] = \"<span class=\\\"ag-overlay-loading-center\\\">Please wait while your rows are loading.</span>\";\n    ValidationMsgs[\"success\"] = \"Success\";\n  })(ValidationMsgs || (ValidationMsgs = {}));\n\n  return ValidationMsgs;\n})();\nexport class SwalFire {\n  static confirmCancelSwlAlert(msg) {\n    return Swal.fire({\n      title: 'Confirm !',\n      html: msg,\n      //icon: 'success',\n      showDenyButton: true,\n      //showCancelButton: true,\n      confirmButtonColor: 'rgb(54, 69, 116)',\n      denyButtonColor: 'rgb(54, 69, 116)',\n      confirmButtonText: 'Confirm',\n      denyButtonText: 'Cancel',\n      allowOutsideClick: false,\n      showCloseButton: true\n    });\n  }\n\n}\nexport var DebounceTime = /*#__PURE__*/(() => {\n  (function (DebounceTime) {\n    DebounceTime[DebounceTime[\"citiesDebounceTime\"] = 800] = \"citiesDebounceTime\";\n  })(DebounceTime || (DebounceTime = {}));\n\n  return DebounceTime;\n})();", "map": null, "metadata": {}, "sourceType": "module"}