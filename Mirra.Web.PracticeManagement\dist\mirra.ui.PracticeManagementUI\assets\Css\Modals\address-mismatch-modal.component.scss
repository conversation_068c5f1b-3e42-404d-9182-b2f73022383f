.SemiBold{
    font-family:'Poppins-SemiBold';
}

.PleaseMapLine{
    font-size: 14px;
}

.MainHeading{
    text-align: center;
    font-size: 25px;
}

.Heading{
    font-family: 'Poppins-SemiBold';
    font-size: 17px;
}

.Error{
    color: red;
    font-family: 'Poppins-SemiBold';
}

.btn{
    height: 35px;
    color: white;
    box-shadow: 0px 0px 8px #0000001A;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074BC;
    font-family: 'Poppins-SemiBold';
    font-size: 14px;
}

.reset{
    background-color: white;
    color: #0074bc;
    border: solid 1px #0074bc;
}

input {
    background-color: white;
    outline: none;
    border: 1px solid #D9DADE;
    font-family: 'Poppins';
    font-size: 12px;
    width: 132px;
    border-radius: 8px;
    height: 34px;
}

.MarginRight{
    margin-right: 7px;
}

.Modal-Close-Image{
    cursor: pointer;
}

.Grey-Heading{
    color:#646464;

}

.NestedHeading{
    font-size: 21px;
    font-family: 'Poppins-Bold';
}

.Value{
    font-family: 'Poppins-Medium';
}

.BoxStyling{
    background: #F8F8F8 0% 0% no-repeat padding-box;
    border: 1px solid #CCCCCC;
    border-radius: 6px;
    opacity: 1;
    margin: 12px 5px;
    padding: 8px 0;
    font-family: "Poppins-SemiBold";
}

.OuterBox{
    margin-right: 20px;
}

.CloseIcon{
    margin-right: -30px;
}