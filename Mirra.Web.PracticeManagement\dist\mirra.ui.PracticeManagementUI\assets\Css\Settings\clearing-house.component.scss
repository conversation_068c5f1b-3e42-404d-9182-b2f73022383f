.Form-Body {
    //box-shadow: 0px 0px 8px #0000001A;
    background-color: white;
    // padding: 5px;
    padding: 5px 7px 5px 5px;
    min-height: 400px;
}

.body {
    background-color: white;
    border-radius: 8px;
    margin-top: 10px;
    box-shadow: 0px 0px 8px #0000001a;
}

// basic styling for all tds in table
.TableTd {
    color: #272d3b;
    font-size: 12px;
    font-family: "Poppins";
}

// background effect on hover on table row
.TableTr:hover {
    background-color: #e3f2fd !important;
}

// style for checkbox and paginator of primeng
::ng-deep .p-checkbox-box {
    border-radius: 50% !important;
}
::ng-deep .p-paginator {
    margin-right: 1%;
    justify-content: flex-end !important;
}

/* basic setting for all input type */

input[type="text"],
input[type="number"] {
    border-radius: 6px;
    border: 1px solid #cccccc !important;
    height: 30px;
    outline: none;
    min-width: 40px;
    margin-left: 5px;
    // width: 200px;
    width: 65%;
    color: #757575;
    font-size: 12px;
    font-family: "Poppins";
    font-weight: bold;
    padding-left: 5px !important;
}

// styling for input when on focus
input[type="text"]:focus,
input[type="number"]:focus {
    border: 1px solid #61abd4 !important;
}

// styling for table headers
th {
    padding-right: 0px !important;
    padding-left: 0px !important;
    background-color: white !important;
    border-radius: 8px;
}

/* for setting second heading box*/

.Form-Header-2 {
    font-family: "Poppins";
    background-color: white;
    padding: 10px;
    align-items: center;
    border-radius: 40px;
}

.Form-Header-2-Heading {
    color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 18px;
}

.p-table {
    margin-left: 16px !important;
    border: 1px solid #cccccc;
    border-radius: 8px;
    width: 97% !important;
}

.TextRight {
    text-align: end;
}

.MarginRight {
    margin-right: 10px;
}

.MarginLeft {
    margin-left: 20px;
}

.btn {
    border: none;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
}

.btn:hover {
    background-color: #005488;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-down:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}
::ng-deep .pi-sort-amount-up-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}

.ThBtn {
    font-family: "Poppins-Bold";
    width: 8%;
    color: #5d5d5d !important;
}

::ng-deep .p-datatable-resizable .p-datatable-tbody > tr > td,
.p-datatable-resizable .p-datatable-tfoot > tr > td,
.p-datatable-resizable .p-datatable-thead > tr > th {
    text-overflow: ellipsis !important;
}

::ng-deep .p-datatable .p-paginator-bottom {
    border-radius: 8px;
}

.IconStyle {
    cursor: pointer;
    position: relative;
    z-index: 1 !important;
}

object {
    position: relative;
    z-index: -1 !important;
}

::ng-deep .p-datatable .p-sortable-column .p-sortable-column-icon {
    margin-left: 0rem;
}

.view {
    margin-left: 2%;
    font-family: "Poppins";
}

.col {
    font-family: "Poppins";
}

.inputFields {
    margin-left: 0px !important;
}

.heading {
    color: #5d5d5d;
    font-family: "Poppins-SemiBold";
    font-size: 18px;
    margin-left: 18px;
}

.table {
    //box-shadow: 0px 0px 8px #0000001A;
    background-color: white;
    // padding: 5px;
    padding: 5px 7px 5px 5px;
    min-height: 400px;
    margin-left: 10px;
}

.inputBgColor {
    background-color: white;
}

.updateBtn {
    margin-right: 2%;
}

.ptablediv {
    margin-left: 16px !important;
    border: 1px solid #cccccc;
    border-radius: 8px;
    width: 97% !important;
    min-height: 300px;
}

.ptableTd {
    border: none !important;
    border-radius: 6px;
    padding: 5px !important;
}
.ptableTh {
    border: none !important;
    padding: 0 !important;
}

.payerHeading {
    margin-left: 35%;
}

.ptableInput {
    border: 0.5px solid #707070 !important;
    width: 100% !important;
    height: 40px !important;
    padding: 0 !important;
    margin: 0 !important;
    border-top: 0% !important;
    border-left: 0% !important;
    border-right: 0% !important;
    border-radius: 8px !important;
}

.backBtn {
    border-color: #0074bc !important;
    background-color: white;
    margin-right: 3%;
    color: #0074bc;
    border: 1px solid;
    font-size: 14px;
}

.backBtn:hover {
    border-color: #0074bc !important;
    background-color: white;
}

.tableBtn {
    width: 80%;
    margin-left: 22%;
    margin-top: 22%;
}

.custom-select {
    // margin-left: 11px;
    // width: 205px;
    border-radius: 6px;
    border: 1px solid #cccccc !important;
    color: #757575;
    outline: none;
    font-size: 12px;
    height: 30px;
    font-weight: bold;
    padding-left: 5px !important;
    // margin-right: 62px;
}

 

 

.SelectedRow {
    background-color: #e3f2fd;
}

::ng-deep .p-datatable .p-datatable-tbody > tr.p-highlight {
    background-color: white;
}

.clearingHouseIdentififcationNumber {
    width: 30%;
}

.clearingHouseName {
    width: 30%;
}

.clearingHouseId {
    width: 11%;
}

.payersCount {
    width: 30%;
}

.LastTd {
    position: absolute;
    right: 0;
    background-color: white;
    height: 53px;
    width: 11%;
}

.LastTh {
    border-bottom: 2px solid #e3f2fd;
    position: absolute;
    right: 0;
    background-color: white;
    height: 62.5px;
    width: 11%;
}

.IconsPadding {
    padding-top: 1% !important;
}

.widthCustom {
    width: 100%;
}

.WidthManual {
    width: 65% !important;
}

.marginBottom {
    margin-bottom: 200px;
}
