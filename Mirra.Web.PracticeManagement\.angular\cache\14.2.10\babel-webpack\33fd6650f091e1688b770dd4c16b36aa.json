{"ast": null, "code": "import { ChangeDetectorRef } from '@angular/core';\nimport { FacilityResult } from 'src/app/classmodels/ResponseModel/Facility/FacilityResult';\nimport { ReferringProviderResult } from 'src/app/classmodels/ResponseModel/ReferringProvider/ReferringProviderResult';\nimport { FacilityInfo } from 'src/app/classmodels/ResponseModel/RenderingProvider/Facility';\nimport { ProviderInfo } from 'src/app/classmodels/ResponseModel/RenderingProvider/Provider';\nimport { ServiceLine } from 'src/app/models/ClaimForm/ServiceLineSelectionModel';\nimport { ClaimInfoModel, ProviderSelectedDataModel, ReferringData } from 'src/app/models/Providers/ClaimInfoModel';\nimport { ClaimCreationType, LocalStorageKey } from 'src/app/shared/constant/constatnt';\nimport { first } from 'rxjs';\nimport * as JSLZString from 'lz-string';\nimport { ClaimErrorComponent } from 'src/app/components/popups/claim-error/claim-error.component';\nimport { ServiceLineComponent } from '../search/service-line/service-line.component';\nimport { ClaimFormType } from 'src/app/common/common-static';\nimport { DosSelectionDateComponent } from 'src/app/modals/dos-selection-date/dos-selection-date.component';\nimport { CreateClaimModel } from 'src/app/models/ClaimForm/claim.create.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/Member/member.service\";\nimport * as i2 from \"src/app/services/ClaimForm/all-states-by-searchstring.service\";\nimport * as i3 from \"src/app/shared/services/subject.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/services/ClaimForm/claim.service\";\nimport * as i6 from \"src/app/services/ProviderManagement/provider-management.service\";\nimport * as i7 from \"src/app/services/rendering-provider/renderng-provider.service\";\nimport * as i8 from \"src/app/services/ClaimForm/all-place-of-services.service\";\nimport * as i9 from \"src/app/services/ClaimForm/qualifier-data-by-type.service\";\nimport * as i10 from \"src/app/services/ClaimForm/all-resubmission-code.service\";\nimport * as i11 from \"ngx-spinner\";\nimport * as i12 from \"src/app/services/ClaimForm/get-all-cptcode.service\";\nimport * as i13 from \"src/app/services/ClaimForm/get-all-icdcode.service\";\nimport * as i14 from \"src/app/services/cache-service/cache.service\";\n\nfunction CreateClaimSelectionComponent_div_0_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\")(1, \"b\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.getFullMemberName(ctx_r2.claimInfoModel == null ? null : ctx_r2.claimInfoModel.profileMember), \")\");\n  }\n}\n\nconst _c0 = function (a0, a1) {\n  return {\n    \"fa-angle-up\": a0,\n    \"fa-angle-down\": a1\n  };\n};\n\nfunction CreateClaimSelectionComponent_div_0_i_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 15);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c0, ctx_r3.panelMemberExpand, !ctx_r3.panelMemberExpand));\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_0_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"app-search-member\", 17);\n    i0.ɵɵlistener(\"selectedMember\", function CreateClaimSelectionComponent_div_0_div_11_Template_app_search_member_selectedMember_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.setMemberDetails($event));\n    })(\"selectedSearchMemberItems\", function CreateClaimSelectionComponent_div_0_div_11_Template_app_search_member_selectedSearchMemberItems_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.selectedSearchMemberItems($event));\n    })(\"ipaCodes\", function CreateClaimSelectionComponent_div_0_div_11_Template_app_search_member_ipaCodes_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.getIpaCodes($event));\n    })(\"resetPcp\", function CreateClaimSelectionComponent_div_0_div_11_Template_app_search_member_resetPcp_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.resetPcpInfo($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"claimFormData\", ctx_r4.claimInfoModel);\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_0_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1, \"(Select Billing Provider)\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_0_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1, \"(Select Rendering Provider)\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_0_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1, \"(Select Facility)\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_0_i_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 15);\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c0, ctx_r8.panelProviderExpand, !ctx_r8.panelProviderExpand));\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"app-search-provider\", 19);\n    i0.ɵɵlistener(\"selectedBillingProvider\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_selectedBillingProvider_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.getBillingProvider($event));\n    })(\"searchBillingResult\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_searchBillingResult_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.getBillingResult($event));\n    })(\"selectedReferringProvider\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_selectedReferringProvider_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.getReferringProvider($event));\n    })(\"searchReferringResult\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_searchReferringResult_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.getReferringResult($event));\n    })(\"removeReferringProvider\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_removeReferringProvider_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.removeReferringProvider($event));\n    })(\"selectedRenderringProvider\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_selectedRenderringProvider_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.getRenderringProvider($event));\n    })(\"searchRenderringResult\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_searchRenderringResult_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.getRenderringResult($event));\n    })(\"selectedSupervising\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_selectedSupervising_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.getSupervising($event));\n    })(\"searchSupervisingResult\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_searchSupervisingResult_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.getSupervisingResult($event));\n    })(\"removeSupervisingProvider\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_removeSupervisingProvider_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.removeSupervisingProvider($event));\n    })(\"selectedFacility\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_selectedFacility_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.setFacilityDetails($event));\n    })(\"setFacilityDetails\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_setFacilityDetails_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.getFacility($event));\n    })(\"searchFacilityResult\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_searchFacilityResult_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.getFacilityResult($event));\n    })(\"providerSelected\", function CreateClaimSelectionComponent_div_0_div_24_Template_app_search_provider_providerSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.getproviderInfoData($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ipaCodes\", ctx_r9.ipaCodeItems)(\"claimFormData\", ctx_r9.claimInfoModel);\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_0_i_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 15);\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c0, ctx_r10.panelServiceExpand, !ctx_r10.panelServiceExpand));\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_0_div_32_ng_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r33 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r33);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r33.text, \" \");\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_0_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 20)(2, \"div\", 21)(3, \"label\", 22);\n    i0.ɵɵtext(4, \" Place of Service \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ng-select\", 23);\n    i0.ɵɵlistener(\"ngModelChange\", function CreateClaimSelectionComponent_div_0_div_32_Template_ng_select_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.locationOfService = $event);\n    });\n    i0.ɵɵtemplate(6, CreateClaimSelectionComponent_div_0_div_32_ng_option_6_Template, 2, 2, \"ng-option\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"app-service-line\", 25);\n    i0.ɵɵlistener(\"selectedServicelines\", function CreateClaimSelectionComponent_div_0_div_32_Template_app_service_line_selectedServicelines_7_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.selectedServicelines($event));\n    })(\"setPreview\", function CreateClaimSelectionComponent_div_0_div_32_Template_app_service_line_setPreview_7_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.setPreview($event));\n    })(\"setServiceLine\", function CreateClaimSelectionComponent_div_0_div_32_Template_app_service_line_setServiceLine_7_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.setServiceLine($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"ngModel\", ctx_r11.locationOfService);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.allPlaceOfServices);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"cptCodes\", ctx_r11.claimInfoModel.cPTCodes)(\"claimFormData\", ctx_r11.claimInfoModel)(\"selectedMember\", ctx_r11.claimInfoModel.profileMember);\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"label\", 3);\n    i0.ɵɵtext(2, \"Create Claim (CMS 1500) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function CreateClaimSelectionComponent_div_0_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.openMember());\n    });\n    i0.ɵɵelementStart(5, \"div\", 6)(6, \"h4\", 7);\n    i0.ɵɵtext(7, \" Search Member \");\n    i0.ɵɵtemplate(8, CreateClaimSelectionComponent_div_0_span_8_Template, 3, 1, \"span\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 9);\n    i0.ɵɵtemplate(10, CreateClaimSelectionComponent_div_0_i_10_Template, 1, 4, \"i\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, CreateClaimSelectionComponent_div_0_div_11_Template, 2, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 4)(13, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function CreateClaimSelectionComponent_div_0_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.openProvider());\n    });\n    i0.ɵɵelementStart(14, \"div\", 6)(15, \"h4\", 7);\n    i0.ɵɵtext(16, \" Search Provider \\u00A0\\u00A0\\u00A0 \");\n    i0.ɵɵtemplate(17, CreateClaimSelectionComponent_div_0_span_17_Template, 2, 0, \"span\", 12);\n    i0.ɵɵtext(18, \"\\u00A0\\u00A0 \");\n    i0.ɵɵtemplate(19, CreateClaimSelectionComponent_div_0_span_19_Template, 2, 0, \"span\", 13);\n    i0.ɵɵtext(20, \"\\u00A0\\u00A0 \");\n    i0.ɵɵtemplate(21, CreateClaimSelectionComponent_div_0_span_21_Template, 2, 0, \"span\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 9);\n    i0.ɵɵtemplate(23, CreateClaimSelectionComponent_div_0_i_23_Template, 1, 4, \"i\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, CreateClaimSelectionComponent_div_0_div_24_Template, 2, 2, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 14)(26, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function CreateClaimSelectionComponent_div_0_Template_div_click_26_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.openService());\n    });\n    i0.ɵɵelementStart(27, \"div\", 6)(28, \"h4\", 7);\n    i0.ɵɵtext(29, \" Service Lines \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 9);\n    i0.ɵɵtemplate(31, CreateClaimSelectionComponent_div_0_i_31_Template, 1, 4, \"i\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, CreateClaimSelectionComponent_div_0_div_32_Template, 8, 6, \"div\", 11);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.claimInfoModel == null ? null : ctx_r0.claimInfoModel.profileMember);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.panelMemberExpand);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.panelMemberExpand);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showErrorBilling() && ctx_r0.isSelectedMember);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showErrorRenderring() && ctx_r0.isSelectedMember);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showErrorFacility() && ctx_r0.isSelectedMember);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.claimInfoModel == null ? null : ctx_r0.claimInfoModel.profileMember) && !ctx_r0.panelProviderExpand);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.panelProviderExpand);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.claimInfoModel == null ? null : ctx_r0.claimInfoModel.provider == null ? null : ctx_r0.claimInfoModel.provider.facility) && !ctx_r0.checkIfFacilityAvailable() && (ctx_r0.claimInfoModel == null ? null : ctx_r0.claimInfoModel.provider == null ? null : ctx_r0.claimInfoModel.provider.billingProvider) && (ctx_r0.claimInfoModel == null ? null : ctx_r0.claimInfoModel.provider == null ? null : ctx_r0.claimInfoModel.provider.renderingProvider) && !ctx_r0.panelServiceExpand);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.panelServiceExpand);\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function CreateClaimSelectionComponent_div_1_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.openService());\n    });\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵtext(2, \"Back\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimSelectionComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, CreateClaimSelectionComponent_div_1_button_1_Template, 3, 0, \"button\", 28);\n    i0.ɵɵelementStart(2, \"app-create-claim\", 29);\n    i0.ɵɵlistener(\"closeClaim\", function CreateClaimSelectionComponent_div_1_Template_app_create_claim_closeClaim_2_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.claimRefill($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimType === ctx_r1.addClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"claimFormData\", ctx_r1.claimInfoModel)(\"allPlaceOfServices\", ctx_r1.allPlaceOfServices)(\"claimData\", ctx_r1.claimData);\n  }\n}\n\nexport let CreateClaimSelectionComponent = /*#__PURE__*/(() => {\n  class CreateClaimSelectionComponent {\n    constructor(memberService, cdr, stateService, subjectService, dialog, claimService, providerMgmtService, renderingProviderService, placeOfService, providerManagementService, qualifierDataByTypeService, resubmissionCodeService, spinnerService, cptservice, ICDService, cacheService) {\n      this.memberService = memberService;\n      this.cdr = cdr;\n      this.stateService = stateService;\n      this.subjectService = subjectService;\n      this.dialog = dialog;\n      this.claimService = claimService;\n      this.providerMgmtService = providerMgmtService;\n      this.renderingProviderService = renderingProviderService;\n      this.placeOfService = placeOfService;\n      this.providerManagementService = providerManagementService;\n      this.qualifierDataByTypeService = qualifierDataByTypeService;\n      this.resubmissionCodeService = resubmissionCodeService;\n      this.spinnerService = spinnerService;\n      this.cptservice = cptservice;\n      this.ICDService = ICDService;\n      this.cacheService = cacheService;\n      this.panelMemberExpand = false;\n      this.panelProviderExpand = false;\n      this.panelServiceExpand = false;\n      this.payerDetail = {};\n      this.enableServicepanel = false;\n      this.isProviderSelectData = false;\n      this.isSelectedMember = false;\n      this.claimType = ClaimFormType.loadClaim;\n      this.addClaim = ClaimFormType.addClaim;\n      this.editClaim = ClaimFormType.editClaim;\n      this.viewClaim = ClaimFormType.viewClaim;\n      this.memberClaim = ClaimFormType.memberClaim;\n      this.claimInfoModel = {\n        renderingProviderInfo: null,\n        supervisingProviderInfo: new ReferringProviderResult(),\n        facilityProviderInfo: new FacilityResult(),\n        serviceLineInfo: new ServiceLine(),\n        billingProviderInfo: null,\n        facilityInfo: new FacilityInfo(),\n        allStatesBySearchstring: [],\n        qualifierDataByType: [],\n        ndcQtyQualifierDataByType: [],\n        ndcQualifierDataByType: [],\n        resubmissionCode: [],\n        taxonomyCode: [],\n        placeOfServiceCode: \"\",\n        provider: new ProviderInfo(),\n        isViewClaim: false,\n        isEditClaim: false,\n        isAddClaim: false,\n        isParentClaim: false,\n        useFacilityNameAndNPI: false,\n        icdCodes: [],\n        cPTCodes: [],\n        claimViewModel: new CreateClaimModel()\n      };\n      this.isViewClaim = false;\n      this.isEditClaim = false;\n      this.claimId = null;\n      this.isExistMemberAvaiable = false;\n      this.isProviderFrom = false;\n      this.allPlaceOfServices = [];\n      this.isFromMember = false;\n      this.previewVisible = false;\n      this.claimCall = true;\n      this.locationOfService = \"11\";\n      this.claimInfoModel.claimViewModel.notes = [];\n      this.fillQual();\n      this.fillTaxnonmyCode();\n      this.subjectService.getMemberInfo().pipe(first()).subscribe(res => {\n        if (res) {\n          this.subjectService.resetMemberInfo();\n          this.claimInfoModel.profileMember = res;\n          this.panelMemberExpand = true;\n          this.panelProviderExpand = false;\n          this.isFromMember = true;\n          this.claimType = this.memberClaim;\n          this.dosSelection();\n        }\n      });\n      this.subjectService.getViewClaim().pipe(first()).subscribe(res => {\n        if (res) {\n          this.claimId = res;\n          this.isViewClaim = true;\n          this.fillResubmission();\n          this.claimType = ClaimFormType.viewClaim; //this.fillQual();\n\n          this.getViewClaimData();\n          this.subjectService.resetViewClaim();\n        }\n      });\n      this.subjectService.getEditClaim().pipe(first()).subscribe(res => {\n        if (res) {\n          this.claimId = res;\n          this.isEditClaim = true;\n          this.fillState();\n          this.fillResubmission();\n          this.claimType = ClaimFormType.editClaim; //this.fillQual();\n\n          this.getEditClaimData();\n          this.subjectService.resetEditClaim();\n        }\n      });\n      this.subjectService.getaAddClaim().pipe(first()).subscribe(res => {\n        if (res && !this.claimId) {\n          this.claimType = ClaimFormType.addClaim;\n          this.claimInfoModel.isAddClaim = true;\n          this.claimInfoModel.isEditClaim = false;\n          this.claimInfoModel.isViewClaim = false;\n          this.panelMemberExpand = true;\n          this.placeOfServiceData();\n        }\n      });\n    }\n\n    ngOnInit() {\n      this.providerData = {\n        isUseFacilityNameAndNPI: false,\n        displayBillingName: '',\n        displayFacilityName: '',\n        displayReferrringName: '',\n        displayRenderringName: '',\n        displaySupervisingName: '',\n        billingData: null,\n        facilityData: null,\n        renderringData: null\n      };\n\n      if (!this.isEditClaim && !this.isViewClaim) {\n        this.fetchCPT1();\n        this.fetchIcd();\n        this.fillQual();\n      }\n    }\n\n    ngAfterViewInit() {\n      this.cdr.detectChanges();\n    }\n\n    fetchCPT1() {\n      let cpt = localStorage.getItem(LocalStorageKey.allCPT);\n\n      if (!!cpt) {\n        this.cptCodes = JSON.parse(JSLZString.decompress(cpt));\n        ;\n      } else {\n        this.cptservice.fetchAllCpt().subscribe(res => {\n          if (!!res) {\n            this.cptCodes = res;\n          }\n        });\n      }\n    }\n\n    fetchIcd() {// let icd = localStorage.getItem('allICD');\n      // if (!!icd) {\n      //   //this.icdCodes = JSON.parse(JSLZString.decompress(icd));;\n      // } else {\n      //   this.ICDService.fetchAllIcd().subscribe((res) => {\n      //     if (!!res) {\n      //       //  this.icdCodes = res;\n      //     }\n      //   })\n      // }\n    }\n\n    fillState() {\n      let state = localStorage.getItem('allStates');\n\n      if (!!state) {\n        this.claimInfoModel.allStatesBySearchstring = JSON.parse(JSLZString.decompress(state));\n        ;\n        this.callClaimSub();\n      } else {\n        this.stateService.fetchchAllStatesBySearchstring().subscribe(res => {\n          this.claimInfoModel.allStatesBySearchstring = res;\n          this.callClaimSub();\n        });\n      }\n    }\n\n    fillQual() {\n      if (!!this.cacheService.localStorageGetItem(LocalStorageKey.getQualList)) {\n        this.claimInfoModel.qualifierDataByType = this.cacheService.localStorageGetItem(LocalStorageKey.getQualList);\n      } else {\n        this.qualifierDataByTypeService.fetchchQualifierDataByType('').subscribe(res => {\n          if (res && res.length > 0) {\n            this.claimInfoModel.qualifierDataByType = res;\n            this.cacheService.localStorageSetItem(LocalStorageKey.getQualList, res);\n          }\n        });\n      }\n\n      this.claimInfoModel.ndcQualifierDataByType = this.claimInfoModel.qualifierDataByType.filter(t => t.dateQualifierTypeCode.trim() === \"NDCQual\");\n      this.claimInfoModel.ndcQtyQualifierDataByType = this.claimInfoModel.qualifierDataByType.filter(t => t.dateQualifierTypeCode.trim() === \"NDCQtyQual\");\n      this.callClaimSub();\n    }\n\n    fillResubmission() {\n      if (!!this.cacheService.localStorageGetItem(LocalStorageKey.resubmissionCode)) {\n        this.claimInfoModel.resubmissionCode = this.cacheService.localStorageGetItem(LocalStorageKey.resubmissionCode);\n      } else {\n        this.resubmissionCodeService.fetchchAllResubmissionCode().subscribe(res => {\n          if (res && res.length > 0) {\n            this.claimInfoModel.resubmissionCode = res;\n            this.cacheService.localStorageSetItem(LocalStorageKey.resubmissionCode, res);\n          }\n        });\n      }\n\n      this.callClaimSub();\n    }\n\n    callClaimSub() {\n      if (this.isViewClaim) {\n        this.claimCall = true; // this.getViewClaimData();\n      } else if (this.isEditClaim) {\n        this.getEditClaimData();\n      }\n    }\n\n    getViewClaimData() {\n      this.claimService.getClaimById(this.claimId.toString()).subscribe(res => {\n        if (res === null) {\n          this.claimError();\n        } else {\n          this.claimInfoModel.claimViewModel = res?.claimFormData;\n          this.previewVisible = true;\n\n          if (!!res.lstLinkedClaimDetails) {\n            this.claimInfoModel.claimViewModel.lstLinkedClaimDetails = res.lstLinkedClaimDetails;\n          }\n\n          this.claimInfoModel.isAddClaim = false;\n          this.claimInfoModel.isViewClaim = true;\n          this.claimInfoModel.isEditClaim = false;\n          this.claimInfoModel.isParentClaim = res?.claimFormData?.parentClaimForm837Pid?.toString() === this.claimId.toString() ? true : false;\n        }\n      });\n    }\n\n    getEditClaimData() {\n      if (this.claimInfoModel.resubmissionCode.length > 0 && this.claimInfoModel.allStatesBySearchstring.length > 0 && this.claimInfoModel.taxonomyCode.length > 0) {\n        if (this.claimCall) {\n          this.claimCall = false;\n          this.claimService.getClaimById(this.claimId.toString()).subscribe(res => {\n            if (!!res) {\n              this.previewVisible = true;\n              this.claimInfoModel.claimViewModel = res.claimFormData;\n\n              if (!!res.lstLinkedClaimDetails) {\n                this.claimInfoModel.claimViewModel.lstLinkedClaimDetails = res.lstLinkedClaimDetails;\n              }\n\n              this.claimInfoModel.isParentClaim = res?.claimFormData?.parentClaimForm837Pid?.toString() === this.claimId.toString() ? true : false;\n              this.claimInfoModel.isAddClaim = false;\n              this.claimInfoModel.isViewClaim = false;\n              this.claimInfoModel.isEditClaim = true;\n              this.claimData = res;\n            }\n          });\n        }\n      }\n    }\n\n    fillPayerDetails() {\n      this.memberService.fetchPayerDetail(this.claimInfoModel?.profileMember?.planCode).subscribe(res => {\n        this.claimInfoModel.payerItem = res;\n      });\n    }\n\n    fillMember() {\n      if (this.isProviderSelectData) {\n        this.fillTaxnonmyCode();\n      }\n    }\n\n    getBillingProvider(e) {\n      this.claimInfoModel.billingProviderInfo = e;\n      this.setBillingProviderDetails();\n    }\n\n    getReferringProvider(e) {\n      /**/\n      let referring = {\n        displayReferringAddress: e.providerFirstAddress,\n        displayReferringCity: e.providerCity,\n        displayReferringGroupId: e.providerNPI,\n        displayReferringName: (!e.providerLastName ? '' : e.providerLastName) + ' ' + (!e.providerFirstName ? '' : e.providerFirstName) + ' ' + (!e.providerMiddleName ? '' : e.providerMiddleName),\n        displayReferringState: e.providerState,\n        displayReferringTaxId: e.taxId,\n        displayReferringTaxonomy: e.providerTaxonomy,\n        displayReferringZip: e.providerZip\n      };\n      this.providerData.referringData = referring;\n      let refer = {\n        referringProviderLastName: e.providerLastName,\n        referringProviderMiddleName: e.providerMiddleName,\n        referringProviderFirstName: e.providerFirstName,\n        referringProviderFirstAddress: e.providerFirstAddress,\n        referringProviderSecondAddress: '',\n        referringProviderCity: e.providerCity,\n        referringProviderState: e.providerState,\n        referringProviderZip: e.providerZip,\n        referringProviderPhoneNo: '',\n        referringProviderIdentifier: '',\n        referringProviderTaxonomy: e.providerTaxonomy,\n        referringProviderFullName: referring.displayReferringName,\n        referringProviderFullAddress: e.providerFirstAddress,\n        referringProviderSSN: '',\n        providerUniqueId: '',\n        nPINumber: e.providerNPI,\n        taxId: e.taxId\n      };\n      this.claimInfoModel.provider.referringProvider = refer;\n      this.claimInfoModel.referringProviderInfo = e;\n    }\n\n    removeReferringProvider(e) {\n      this.providerData.referringData = null;\n      this.claimInfoModel.referringProviderInfo = null;\n      this.claimInfoModel.provider.referringProvider = null;\n    }\n\n    getRenderringProvider(e) {\n      this.claimInfoModel.renderingProviderInfo = e;\n      this.setRenderringProviderDetails();\n    }\n\n    getSupervising(e) {\n      this.claimInfoModel.provider.supervisingProvider = e;\n    }\n\n    removeSupervisingProvider(e) {\n      this.providerData.supervisingData = null;\n      this.claimInfoModel.supervisingProviderInfo = null;\n      this.claimInfoModel.provider.supervisingProvider = null;\n    }\n\n    getFacility(e) {\n      this.claimInfoModel.provider.facility = e;\n      this.setFacilityProviderDetails();\n    }\n\n    setFacilityDetails(e) {\n      let referingProviderInfo = {\n        facilityName: e?.facilityName,\n        facilityOrganizationNPI: e?.organizationNPI,\n        facilityAddress1: e?.address?.addressLine1,\n        facilityAddress2: e?.address?.addressLine2,\n        facilityCity: e?.address?.city,\n        facilityState: e?.address?.state,\n        facilityZip: e?.address?.postalCode,\n        facilityIdentifier1: '',\n        facilityIdentifier2: '',\n        facilityId: e?.facilityInformationID,\n        facilityFullAddress: e?.fullAddress\n      };\n      this.claimInfoModel.provider.facility = referingProviderInfo;\n      this.setFacilityProviderDetails();\n    }\n\n    getSelectedMember(e) {\n      this.isSelectedMember = true;\n      this.claimInfoModel.profileMember = e.selectedMemberData;\n      this.claimInfoModel.memberResult = e.selectedMemberInfo;\n      this.claimInfoModel.serviceLineInfo = null;\n\n      if (this.isExistMemberAvaiable) {\n        this.enableServicepanel = true;\n      } else {\n        this.claimInfoModel.billingProviderInfo = null;\n        this.claimInfoModel.facilityInfo = null;\n        this.claimInfoModel.facilityProviderInfo = null;\n      }\n\n      this.fillPayerDetails(); //this.fetchMember();\n    }\n\n    getIpaCodes(e) {\n      this.ipaCodeItems = e;\n    }\n\n    servicePanelOpen() {\n      if (this.claimInfoModel.provider?.billingProvider && this.claimInfoModel.provider?.facility && this.claimInfoModel.provider?.renderingProvider) {\n        this.isServiceOpened();\n      }\n    }\n\n    checkIfFacilityAvailable() {\n      return Object.values(this.claimInfoModel?.provider?.facility).every(x => !!!x || !!x && x.trim().length == 0);\n    }\n\n    fillTaxnonmyCode() {\n      let taxonomy = this.cacheService.localStorageGetItem(LocalStorageKey.allSpecialityTaxonomyCodes);\n\n      if (!!taxonomy) {\n        this.callClaimSub();\n        let data = taxonomy;\n        this.claimInfoModel.taxonomyCode = !!data && data.length > 0 ? data : [];\n      } else {\n        this.providerManagementService.fetchSpecialityData('').subscribe(res => {\n          if (res.length > 0) {\n            this.claimInfoModel.taxonomyCode = res;\n          }\n\n          this.callClaimSub();\n        });\n      }\n    }\n\n    selectedSearchMemberItems(e) {\n      this.memberSearch = e;\n      this.fetchCPT();\n      this.fetchICD();\n    }\n\n    openMember() {\n      if (!this.isFromMember) {\n        this.panelProviderExpand = false;\n        this.panelMemberExpand = !this.panelMemberExpand;\n      }\n\n      let data = {\n        searchResult: this.memberSearch,\n        panelData: this.claimInfoModel\n      };\n      this.subjectService.setSearchMemberInfo(data);\n      this.isMemberOpened();\n    }\n\n    getBillingResult(e) {\n      this.billingResult = e;\n    }\n\n    getFacilityResult(e) {\n      this.facilityResult = e;\n    }\n\n    getReferringResult(e) {\n      this.referringResult = e;\n    }\n\n    getRenderringResult(e) {\n      this.renderringResult = e;\n    }\n\n    getSupervisingResult(e) {\n      this.supervisingResult = e;\n    }\n\n    openProvider() {\n      if (this.claimInfoModel.profileMember) {\n        this.panelProviderExpand = true; //if (!this.claimInfoModel.payerItem) {\n\n        this.fillMember(); //   this.fillPayerDetails();\n        ///}\n\n        if (!this.isProviderSelectData) {\n          this.panelProviderExpand = true;\n          this.panelMemberExpand = false;\n          this.subjectService.setSearchBillingProviderInfo(this.billingResult);\n          this.subjectService.setSearchFacilityProviderInfo(this.facilityResult);\n          this.subjectService.setSearchReferringProviderInfo(this.referringResult);\n          this.subjectService.setSearchRenderringProviderInfo(this.renderringResult);\n          this.subjectService.setSearchSupervisingProviderInfo(this.supervisingResult);\n\n          if (this.providerData.displayBillingName) {\n            if (this.claimInfoModel.facilityProviderInfo) this.providerData.isUseFacilityNameAndNPI = this.claimInfoModel.facilityProviderInfo.useFacilitynameandnpi;\n            this.subjectService.setSelectedProviderData(this.providerData);\n          }\n        } else {\n          this.panelMemberExpand = !this.panelMemberExpand;\n          this.providerData.isUseFacilityNameAndNPI = false;\n          if (this.claimInfoModel.facilityProviderInfo) this.providerData.isUseFacilityNameAndNPI = this.claimInfoModel.facilityProviderInfo.useFacilitynameandnpi;\n          this.subjectService.setSelectedProviderData(this.providerData);\n        }\n\n        if (this.panelProviderExpand && !this.panelServiceExpand) {\n          this.subjectService.setSetServiceLine();\n        }\n\n        this.servicePanelOpen();\n        this.isProviderOpened();\n      }\n    }\n\n    openService() {\n      if (!this.claimInfoModel.payerItem) {\n        this.fillMember(); //  this.fillPayerDetails();\n      }\n\n      if (this.claimInfoModel?.provider?.billingProvider && this.claimInfoModel?.provider?.renderingProvider && this.claimInfoModel?.provider?.facility && !this.checkIfFacilityAvailable()) {\n        this.previewVisible = false;\n        this.panelServiceExpand = true;\n        this.panelProviderExpand = false;\n        this.panelMemberExpand = false;\n      }\n    }\n\n    getproviderInfoData(e) {\n      this.providerData = e;\n    }\n\n    claimRefill(e) {\n      this.isExistMemberAvaiable = false;\n      this.locationOfService = this.claimInfoModel.placeOfServiceCode = \"11\";\n\n      if (e === ClaimCreationType.newClaim) {\n        this.claimInfoModel.serviceLineInfo = null;\n        this.providerData = {\n          isUseFacilityNameAndNPI: false,\n          billingData: null,\n          displayBillingName: '',\n          displayFacilityName: '',\n          displayReferrringName: '',\n          displayRenderringName: '',\n          displaySupervisingName: '',\n          facilityData: null,\n          referringData: null,\n          renderringData: null,\n          supervisingData: null\n        };\n        this.panelServiceExpand = this.enableServicepanel = this.panelProviderExpand = false;\n        this.billingResult = null;\n        this.facilityResult = null;\n        this.referringResult = null;\n        this.renderringResult = null;\n        this.supervisingResult = null;\n        this.memberSearch = null;\n        let oldData = this.claimInfoModel;\n        this.claimInfoModel = {\n          useFacilityNameAndNPI: false,\n          renderingProviderInfo: null,\n          supervisingProviderInfo: null,\n          facilityProviderInfo: null,\n          serviceLineInfo: null,\n          billingProviderInfo: null,\n          facilityInfo: null,\n          allStatesBySearchstring: [],\n          qualifierDataByType: oldData?.qualifierDataByType,\n          ndcQtyQualifierDataByType: oldData?.ndcQtyQualifierDataByType,\n          ndcQualifierDataByType: oldData?.ndcQualifierDataByType,\n          placeOfServiceCode: this.locationOfService,\n          resubmissionCode: [],\n          taxonomyCode: [],\n          provider: new ProviderInfo(),\n          isAddClaim: true,\n          isEditClaim: false,\n          isViewClaim: false,\n          isParentClaim: false,\n          cPTCodes: [],\n          icdCodes: [],\n          claimViewModel: new CreateClaimModel()\n        };\n        this.panelMemberExpand = true;\n        this.panelProviderExpand = false;\n        this.panelServiceExpand = false;\n      }\n\n      if (e === ClaimCreationType.sameMember) {\n        this.billingResult = null;\n        this.facilityResult = null;\n        this.referringResult = null;\n        this.renderringResult = null;\n        this.supervisingResult = null;\n        let oldData = this.claimInfoModel;\n        this.claimInfoModel = {\n          useFacilityNameAndNPI: false,\n          renderingProviderInfo: null,\n          supervisingProviderInfo: null,\n          facilityProviderInfo: null,\n          serviceLineInfo: null,\n          billingProviderInfo: null,\n          facilityInfo: null,\n          placeOfServiceCode: this.locationOfService,\n          allStatesBySearchstring: oldData?.allStatesBySearchstring,\n          qualifierDataByType: oldData?.qualifierDataByType,\n          ndcQtyQualifierDataByType: oldData?.ndcQtyQualifierDataByType,\n          ndcQualifierDataByType: oldData?.ndcQualifierDataByType,\n          isAddClaim: true,\n          isEditClaim: false,\n          isViewClaim: false,\n          isParentClaim: false,\n          resubmissionCode: oldData?.resubmissionCode,\n          taxonomyCode: oldData?.taxonomyCode,\n          memberResult: oldData?.memberResult,\n          profileMember: oldData?.profileMember,\n          provider: {\n            billingProvider: oldData.provider.billingProvider ? oldData.provider.billingProvider : null,\n            facility: oldData.provider.facility ? oldData.provider.facility : null,\n            referringProvider: oldData.provider.referringProvider ? oldData.provider.referringProvider : null,\n            renderingProvider: oldData.provider.renderingProvider ? oldData.provider.renderingProvider : null,\n            supervisingProvider: oldData.provider.supervisingProvider ? oldData.provider.supervisingProvider : null\n          },\n          payerItem: oldData.payerItem,\n          cPTCodes: oldData.cPTCodes,\n          icdCodes: oldData.icdCodes,\n          claimViewModel: new CreateClaimModel()\n        }; //this.isProviderSelectData = true;\n\n        this.panelMemberExpand = true;\n        this.isExistMemberAvaiable = true;\n        this.panelProviderExpand = true;\n        this.enableServicepanel = this.panelServiceExpand = this.panelProviderExpand = true;\n        this.servicelineResult = null; // let data = {\n        //   selectedMemberData: this.claimInfoModel?.profileMember,\n        //   selectedMemberInfo: this.claimInfoModel?.memberResult\n        // }\n        // this.getSelectedMember(data);\n\n        this.isMemberOpened();\n        this.isProviderOpened();\n      }\n\n      if (e === ClaimCreationType.sameProvider) {\n        this.isProviderFrom = true;\n        let oldData = this.claimInfoModel;\n        this.claimInfoModel = {\n          useFacilityNameAndNPI: false,\n          renderingProviderInfo: null,\n          supervisingProviderInfo: null,\n          facilityProviderInfo: null,\n          serviceLineInfo: null,\n          billingProviderInfo: null,\n          facilityInfo: null,\n          placeOfServiceCode: this.locationOfService,\n          allStatesBySearchstring: oldData?.allStatesBySearchstring,\n          qualifierDataByType: oldData?.qualifierDataByType,\n          ndcQtyQualifierDataByType: oldData?.ndcQtyQualifierDataByType,\n          ndcQualifierDataByType: oldData?.ndcQualifierDataByType,\n          isAddClaim: true,\n          isEditClaim: false,\n          isViewClaim: false,\n          isParentClaim: false,\n          resubmissionCode: oldData?.resubmissionCode,\n          taxonomyCode: oldData?.taxonomyCode,\n          memberResult: oldData?.memberResult,\n          profileMember: null,\n          provider: {\n            billingProvider: null,\n            facility: null,\n            referringProvider: null,\n            renderingProvider: oldData.provider.renderingProvider,\n            supervisingProvider: null\n          },\n          payerItem: oldData.payerItem,\n          cPTCodes: oldData.cPTCodes,\n          icdCodes: oldData.icdCodes,\n          claimViewModel: new CreateClaimModel()\n        };\n        let uniqueContractId = this.claimInfoModel?.provider?.renderingProvider?.uniqueContractId;\n        let providerUniqueId = this.claimInfoModel?.provider?.renderingProvider?.providerUniqueId;\n        this.memberSearch.searchMember.searchParameter.uniqueContractID = uniqueContractId === undefined || uniqueContractId === null || uniqueContractId === '' ? null : uniqueContractId;\n        this.memberSearch.searchMember.searchParameter.uniqueProviderID = providerUniqueId === undefined || providerUniqueId === null || providerUniqueId === '' ? null : providerUniqueId;\n        this.panelServiceExpand = false;\n        let data = {\n          searchResult: this.memberSearch,\n          panelData: this.claimInfoModel\n        };\n        this.subjectService.setSearchMemberInfo(data);\n        this.isMemberOpened();\n      }\n\n      this.previewVisible = false;\n    }\n\n    setMemberDetails(e) {\n      this.isProviderSelectData = false;\n      this.getSelectedMember(e);\n    }\n\n    selectedServicelines(event) {\n      this.claimInfoModel.serviceLineInfo = event;\n    }\n\n    setPreview(event) {\n      this.setServiceLine(event);\n      this.fillState();\n      this.fillTaxnonmyCode();\n      this.fillResubmission();\n      this.fillQual();\n\n      if (!this.previewVisible) {\n        this.previewVisible = true;\n      }\n    }\n\n    setServiceLine(event) {\n      this.claimInfoModel.serviceLineInfo = event;\n      this.claimInfoModel.placeOfServiceCode = this.locationOfService;\n      this.claimInfoModel.isAddClaim = true;\n      this.servicelineResult = event;\n      this.claimInfoModel.claimViewModel = new CreateClaimModel();\n      this.claimInfoModel.claimViewModel.notes = [];\n    }\n\n    placeOfServiceData() {\n      if (!!this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices)) {\n        this.allPlaceOfServices = this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices);\n      } else {\n        this.placeOfService.fetchchAllPlaceOfServices().subscribe(res => {\n          this.allPlaceOfServices = res;\n        });\n      }\n    }\n\n    fetchMember() {\n      this.memberService.fetchMemberProvider(this.claimInfoModel?.profileMember).subscribe(res => {\n        let oldRenderringDetails = this.claimInfoModel.provider == null ? null : this.claimInfoModel.provider.renderingProvider;\n        this.claimInfoModel.provider = res;\n\n        if (res?.renderingProvider) {\n          this.claimInfoModel.provider.renderingProvider.providerFullName = (!res?.renderingProvider?.providerLastName ? '' : res?.renderingProvider?.providerLastName) + ' ' + (!res?.renderingProvider?.providerFirstName ? '' : res?.renderingProvider?.providerFirstName) + ' ' + (!res?.renderingProvider?.providerMiddleName ? '' : res?.renderingProvider?.providerMiddleName);\n        }\n\n        if (this.isProviderFrom && !res?.renderingProvider && oldRenderringDetails) {\n          this.claimInfoModel.provider.renderingProvider = oldRenderringDetails;\n        }\n\n        this.providerData.displayRenderringName = this.providerData.displaySupervisingName = this.providerData.displayFacilityName = this.providerData.displayBillingName = this.providerData.displayReferrringName = \"\";\n        this.subjectService.setSelectedProviderData(this.providerData);\n        if (res.facility?.facilityAddress1 || res.facility?.facilityName || res.facility?.facilityOrganizationNPI) this.claimInfoModel.facilityInfo = res.facility;\n        this.showErrorBilling();\n        this.showErrorFacility();\n\n        if (this.memberSearch.result.length === 1 && !this.claimInfoModel.provider.renderingProvider && this.memberSearch.result[0].pcpNpi) {\n          this.memberSearch.searchMember.p_SubscriberID = this.memberSearch.result[0].pcpNpi;\n          let billing = {\n            sortBy: 'ProviderNPI',\n            sortOrder: 'ASC',\n            limit: 10000,\n            index: 0,\n            ipaName: null,\n            ipaCode: this.memberSearch.searchMember.selectedIPACodes,\n            taxId: null,\n            address1: null,\n            firstName: null,\n            providerNPI: this.memberSearch.searchMember.p_SubscriberID\n          };\n          this.renderingProviderService.fetchRenderingProviderByDOS(billing).subscribe(res => {\n            if (res.length > 0) {\n              if (res[0].providerNPI && res[0].providerNPI.includes(this.memberSearch.searchMember.p_SubscriberID)) {\n                this.claimInfoModel.renderingProviderInfo = res[0];\n                this.setRenderringProviderDetails();\n\n                if (!this.claimInfoModel.facilityInfo) {\n                  this.searchFacility(this.claimInfoModel.renderingProviderInfo?.providerUniqueId);\n                }\n              }\n            }\n\n            this.showErrorRenderring();\n          });\n        } else {\n          this.showErrorRenderring();\n        }\n      });\n    }\n\n    searchFacility(npi) {\n      this.providerMgmtService.fetchProviderData(npi).subscribe(res => {\n        let data = JSON.parse(JSON.stringify(res.content.practiceInformationList));\n        this.setRenderProviderFacilityDetails(data[0]);\n        this.showErrorFacility();\n      });\n    }\n\n    setRenderProviderFacilityDetails(data) {\n      let referingProviderInfo = {\n        facilityName: data?.facilityName,\n        facilityOrganizationNPI: data?.facilityNPI,\n        facilityAddress1: data?.facilityAddressLine1,\n        facilityAddress2: data?.facilityAddressLine2,\n        facilityCity: data?.facilityAddressCity,\n        facilityState: data?.facilityAddressState,\n        facilityZip: data?.facilityAddressZipCode,\n        facilityIdentifier1: '',\n        facilityIdentifier2: '',\n        facilityId: data?.facilityId,\n        facilityFullAddress: data?.facilityAddressLine1 + ' ' + data?.facilityAddressLine2\n      };\n      this.claimInfoModel.provider.facility = referingProviderInfo;\n    }\n\n    getAddress(firstAddress, secondAddress) {\n      let result = [];\n\n      if (firstAddress) {\n        result.push(firstAddress);\n      }\n\n      if (secondAddress) {\n        result.push(secondAddress);\n      }\n\n      return result.join(' ');\n    }\n\n    claimError() {\n      const dialogRef = this.dialog.open(ClaimErrorComponent, {\n        height: '530px',\n        width: '1100px',\n        panelClass: 'custom-dialog-containers'\n      });\n      dialogRef.afterClosed().subscribe(res => {\n        this.subjectService.setCloseTabRefresh(localStorage.getItem('claimTapName'));\n        localStorage.removeItem('claimTapName');\n      });\n    }\n\n    showErrorBilling() {\n      if (this.claimInfoModel.provider?.billingProvider) {\n        return false;\n      }\n\n      this.enableServicepanel = false;\n      return true;\n    }\n\n    showErrorRenderring() {\n      if (this.claimInfoModel.provider?.renderingProvider) {\n        return false;\n      }\n\n      this.enableServicepanel = false;\n      return true;\n    }\n\n    showErrorFacility() {\n      if (this.claimInfoModel.provider?.facility) {\n        if (!this.claimInfoModel?.provider?.facility?.facilityFullAddress.trim() && !this.claimInfoModel?.provider?.facility?.facilityName) {\n          this.enableServicepanel = false;\n          return true;\n        }\n\n        return false;\n      }\n\n      this.enableServicepanel = false;\n      return true;\n    }\n\n    fetchCPT() {\n      let fromDate = '';\n\n      if (this.memberSearch?.searchMember?.p_DOSFrom) {\n        fromDate = this.memberSearch?.searchMember?.p_DOSFrom;\n      }\n\n      if (this.claimInfoModel?.profileMember?.dOSFrom) {\n        fromDate = this.claimInfoModel?.profileMember?.dOSFrom;\n      }\n\n      if (fromDate) {\n        let cpt = localStorage.getItem(LocalStorageKey.allCPT);\n        let cptCodes;\n\n        if (!!cpt) {\n          cptCodes = JSON.parse(JSLZString.decompress(cpt));\n          ;\n        }\n\n        let result = [];\n        let dosFrom = new Date(fromDate).getTime();\n\n        for (const item of cptCodes) {\n          let add_date = new Date(item.add_date).getTime();\n          let term_date = new Date(item.term_date).getTime();\n\n          if ((add_date < dosFrom || add_date == dosFrom) && (term_date > dosFrom || term_date == dosFrom) && !!!result.find(e => e.mdmCode == item.mdmCode)) {\n            result.push(item);\n          }\n        }\n\n        this.claimInfoModel.cPTCodes = result;\n      }\n    }\n\n    fetchICD() {\n      let fromDate = '';\n\n      if (this.memberSearch?.searchMember?.p_DOSFrom) {\n        fromDate = this.memberSearch?.searchMember?.p_DOSFrom;\n      }\n\n      if (this.claimInfoModel?.profileMember?.dOSFrom) {\n        fromDate = this.claimInfoModel?.profileMember?.dOSFrom;\n      }\n\n      if (fromDate) {\n        let dosFrom = new Date(fromDate).getTime();\n        let icdData = localStorage.getItem('allICD');\n        let icdCodes = [];\n\n        if (!!icdData) {\n          icdCodes = JSON.parse(JSLZString.decompress(icdData));\n          ;\n        }\n\n        let filterredIcd = [];\n\n        for (const item of icdCodes) {\n          let add_date = new Date(item.addDate).getTime();\n          let term_date = new Date(item.termDate).getTime();\n\n          if ((add_date < dosFrom || add_date == dosFrom) && (term_date > dosFrom || term_date == dosFrom)) {\n            filterredIcd.push(item);\n          }\n        }\n\n        this.claimInfoModel.icdCodes = filterredIcd;\n      } // for (let i = 1; i < 13; i++) {\n      //   this['allIcdCodes' + i] = result;\n      // }\n\n    }\n\n    getFullMemberName(member) {\n      let mName = '';\n      if (member.lastName) mName = member.lastName;\n      if (member.firstName) mName = mName + ' ' + member.firstName;\n      if (member.middleName) mName = mName + ' ' + member.middleName;\n      return mName;\n    }\n\n    setBillingProviderDetails() {\n      let billingProvider = {\n        billingProviderMiddleName: '',\n        billingProviderFirstName: '',\n        billingProviderFirstAddress: this.claimInfoModel.billingProviderInfo?.payToDetails[0].payToStreet,\n        accountCode: this.claimInfoModel.billingProviderInfo?.payToDetails[0].ipaDetails[0].ipaCode,\n        federalTaxNumber: this.claimInfoModel.billingProviderInfo?.taxIdOrSSN,\n        providerUniqueId: '',\n        isIndividualorOrganizational: '',\n        billingProviderLastOrOrganizationName: this.claimInfoModel.billingProviderInfo?.payToDetails[0].paytoName,\n        billingProviderNPI: this.claimInfoModel.billingProviderInfo?.payToDetails[0].payToNPI,\n        billingProviderSecondAddress: this.claimInfoModel.billingProviderInfo?.payToDetails[0].payToSuite,\n        billingProviderCity: this.claimInfoModel.billingProviderInfo?.payToDetails[0].payToCity,\n        billingProviderCounty: this.claimInfoModel.billingProviderInfo?.payToDetails[0].payToCounty,\n        billingProviderCountry: this.claimInfoModel.billingProviderInfo?.payToDetails[0].payToCountry,\n        billingProviderState: this.claimInfoModel.billingProviderInfo?.payToDetails[0].payToState,\n        billingProviderZip: this.claimInfoModel.billingProviderInfo?.payToDetails[0].payToZip,\n        billingProviderPhoneNo: '',\n        billingProviderTaxonomy: this.claimInfoModel.billingProviderInfo?.payToDetails[0].billingProviderTaxonomy,\n        billingProviderEmail: '',\n        billingGroupNPI: '',\n        billingProviderTaxId: this.claimInfoModel.billingProviderInfo?.taxIdOrSSN,\n        billingContactFirstName: '',\n        billingContactMiddleName: '',\n        billingContactLastName: '',\n        billingContactAddress1: '',\n        billingContactAddress2: '',\n        billingContactState: '',\n        billingContactCity: '',\n        billingContactZip: '',\n        billingProviderSSN: this.claimInfoModel.billingProviderInfo?.taxIdOrSSN,\n        insuranceCompanyCode: '',\n        billingContactPersonName: this.claimInfoModel.billingProviderInfo?.contactPeople[0]?.contactPersonName,\n        billingContactPersonPhoneNumber: this.claimInfoModel.billingProviderInfo?.contactPeople[0]?.contactPersonPhone,\n        billingContactPersonFaxNumber: this.claimInfoModel.billingProviderInfo?.contactPeople[0]?.contactPersonFax,\n        billingContactPersonEmailAddress: this.claimInfoModel.billingProviderInfo?.contactPeople[0]?.contactPersonEmail,\n        billingProviderFullName: this.claimInfoModel.billingProviderInfo?.payToDetails[0].paytoName,\n        billingProviderDisplayName: this.claimInfoModel.billingProviderInfo?.payToDetails[0].paytoName,\n        billingProviderFullAddress: this.claimInfoModel.billingProviderInfo?.payToDetails[0].address1\n      };\n      this.claimInfoModel.provider.billingProvider = billingProvider;\n    }\n\n    setRenderringProviderDetails() {\n      let renderring = {\n        providerUniqueId: this.claimInfoModel?.renderingProviderInfo?.providerUniqueId,\n        accountCode: '',\n        providerNPI: this.claimInfoModel?.renderingProviderInfo?.providerNPI,\n        providerFirstName: this.claimInfoModel?.renderingProviderInfo?.providerFirstName,\n        providerMiddleName: this.claimInfoModel?.renderingProviderInfo?.providerMiddleName,\n        providerLastName: this.claimInfoModel?.renderingProviderInfo?.providerLastName,\n        speciality: this.claimInfoModel?.renderingProviderInfo?.speciality,\n        taxonomy: this.claimInfoModel?.renderingProviderInfo?.providerTaxonomy,\n        taxId: this.claimInfoModel?.renderingProviderInfo?.taxId,\n        providerFirstAddress: this.claimInfoModel?.renderingProviderInfo?.providerFirstAddress,\n        providerSecondAddress: this.claimInfoModel?.renderingProviderInfo?.providerSecondAddress,\n        providerCity: this.claimInfoModel?.renderingProviderInfo?.providerCity,\n        providerState: this.claimInfoModel?.renderingProviderInfo?.providerState,\n        providerZip: this.claimInfoModel?.renderingProviderInfo?.providerZip,\n        providerPhoneNo: this.claimInfoModel?.renderingProviderInfo?.providerPhoneNo,\n        federalTaxNumber: this.claimInfoModel?.renderingProviderInfo?.taxId,\n        uniqueContractId: this.claimInfoModel?.renderingProviderInfo?.uniqueContractId,\n        pPID: '',\n        iPACode: this.claimInfoModel?.renderingProviderInfo?.ipaCode,\n        providerFullName: this.claimInfoModel?.renderingProviderInfo?.providerLastName + ' ' + this.claimInfoModel?.renderingProviderInfo?.providerFirstName + ' ' + (this.claimInfoModel?.renderingProviderInfo?.providerMiddleName ? this.claimInfoModel?.renderingProviderInfo?.providerMiddleName : ''),\n        providerFullAddress: ''\n      };\n      this.claimInfoModel.provider.renderingProvider = renderring;\n    }\n\n    setFacilityProviderDetails() {\n      let referingProviderInfo = {\n        facilityName: this.claimInfoModel.provider.facility?.facilityName,\n        facilityOrganizationNPI: this.claimInfoModel.provider?.facility?.facilityOrganizationNPI,\n        facilityAddress1: this.claimInfoModel.provider?.facility?.facilityAddress1,\n        facilityAddress2: this.claimInfoModel.provider?.facility?.facilityAddress2,\n        facilityCity: this.claimInfoModel.provider?.facility?.facilityCity,\n        facilityState: this.claimInfoModel.provider?.facility?.facilityState,\n        facilityZip: this.claimInfoModel.provider?.facility?.facilityZip,\n        facilityIdentifier1: '',\n        facilityIdentifier2: '',\n        facilityId: this.claimInfoModel.provider?.facility?.facilityId,\n        facilityFullAddress: this.claimInfoModel.provider?.facility?.facilityFullAddress\n      };\n      this.claimInfoModel.provider.facility = referingProviderInfo;\n    }\n\n    isMemberOpened() {\n      this.panelMemberExpand = true;\n      this.panelServiceExpand = this.panelProviderExpand = false;\n    }\n\n    isProviderOpened() {\n      this.panelProviderExpand = true;\n      this.panelServiceExpand = this.panelMemberExpand = false;\n    }\n\n    isServiceOpened() {\n      this.panelServiceExpand = true;\n      this.panelProviderExpand = this.panelMemberExpand = false;\n    }\n\n    dosSelection() {\n      const dialogRef = this.dialog.open(DosSelectionDateComponent, {\n        width: '35vw',\n        height: '40vh',\n        panelClass: 'custom-dialog-containers',\n        data: this.claimInfoModel.profileMember\n      });\n      dialogRef.afterClosed().subscribe(res => {\n        this.claimInfoModel.profileMember.dOSFrom = res?.dOSFrom;\n        this.claimInfoModel.profileMember.dOSTo = res?.dOSTo;\n        /**/\n\n        this.fetchCPT();\n        this.fetchICD();\n        this.subjectService.setSelectedMemberClaim(this.claimInfoModel.profileMember);\n      });\n    }\n\n    resetPcpInfo($event) {\n      this.isProviderFrom = false;\n    }\n\n  }\n\n  CreateClaimSelectionComponent.ɵfac = function CreateClaimSelectionComponent_Factory(t) {\n    return new (t || CreateClaimSelectionComponent)(i0.ɵɵdirectiveInject(i1.MemberService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.AllStatesBySearchstringService), i0.ɵɵdirectiveInject(i3.SubjectService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.ClaimService), i0.ɵɵdirectiveInject(i6.ProviderManagementService), i0.ɵɵdirectiveInject(i7.RenderingProviderService), i0.ɵɵdirectiveInject(i8.AllPlaceOfServicesService), i0.ɵɵdirectiveInject(i6.ProviderManagementService), i0.ɵɵdirectiveInject(i9.QualifierDataByTypeService), i0.ɵɵdirectiveInject(i10.AllResubmissionCodeService), i0.ɵɵdirectiveInject(i11.NgxSpinnerService), i0.ɵɵdirectiveInject(i12.GetAllCPTCodeService), i0.ɵɵdirectiveInject(i13.GetAllICDCodeService), i0.ɵɵdirectiveInject(i14.CacheService));\n  };\n\n  CreateClaimSelectionComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CreateClaimSelectionComponent,\n    selectors: [[\"app-create-claim-selection\"]],\n    viewQuery: function CreateClaimSelectionComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(ServiceLineComponent, 7);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ServiceLineForm = _t.first);\n      }\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[\"class\", \"container-fluid\", 4, \"ngIf\"], [\"class\", \"dropdown-fix\", 4, \"ngIf\"], [1, \"container-fluid\"], [1, \"create-claim-title\", \"mt-25\", 2, \"color\", \"#023781\", \"font-weight\", \"bold\", \"margin-left\", \"6px\"], [1, \"mat-card\", \"mt-3\", \"create-claim-form-styles\"], [1, \"row\", 3, \"click\"], [1, \"col-md-11\"], [1, \"menuitemschaild\", \"mt-8\", \"pointer\", \"mb-2\"], [4, \"ngIf\"], [1, \"col-md-1\"], [\"class\", \"arrow-icon pointer fa\", 3, \"ngClass\", 4, \"ngIf\"], [\"style\", \"padding: 8px 24px 16px\", 4, \"ngIf\"], [\"style\", \"color: red;font-size: 12px;font-weight: bold;\", 4, \"ngIf\"], [\"style\", \"color: red;font-size: 12px; font-weight: bold;\", 4, \"ngIf\"], [1, \"mat-card\", \"mt-3\", \"create-claim-form-styles\", \"service-lines\", \"mb-10\"], [1, \"arrow-icon\", \"pointer\", \"fa\", 3, \"ngClass\"], [2, \"padding\", \"8px 24px 16px\"], [3, \"claimFormData\", \"selectedMember\", \"selectedSearchMemberItems\", \"ipaCodes\", \"resetPcp\"], [2, \"color\", \"red\", \"font-size\", \"12px\", \"font-weight\", \"bold\"], [3, \"ipaCodes\", \"claimFormData\", \"selectedBillingProvider\", \"searchBillingResult\", \"selectedReferringProvider\", \"searchReferringResult\", \"removeReferringProvider\", \"selectedRenderringProvider\", \"searchRenderringResult\", \"selectedSupervising\", \"searchSupervisingResult\", \"removeSupervisingProvider\", \"selectedFacility\", \"setFacilityDetails\", \"searchFacilityResult\", \"providerSelected\"], [1, \"row\", \"service-line\"], [1, \"col-md-4\"], [1, \"dashboard-label\"], [\"placeholder\", \"Location Of Service\", \"appendTo\", \"body\", \"bindLabel\", \"text\", \"bindValue\", \"value\", 1, \"form-control\", 3, \"clearable\", \"ngModel\", \"ngModelChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"cptCodes\", \"claimFormData\", \"selectedMember\", \"selectedServicelines\", \"setPreview\", \"setServiceLine\"], [3, \"value\"], [1, \"dropdown-fix\"], [\"class\", \"btn-common bckBtn common-btn btn-height\", \"style\", \"margin-right: 1.5rem !important;\", 3, \"click\", 4, \"ngIf\"], [3, \"claimFormData\", \"allPlaceOfServices\", \"claimData\", \"closeClaim\"], [1, \"btn-common\", \"bckBtn\", \"common-btn\", \"btn-height\", 2, \"margin-right\", \"1.5rem !important\", 3, \"click\"], [1, \"fa\", \"fa-chevron-circle-left\", \"icon-margin\"]],\n    template: function CreateClaimSelectionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CreateClaimSelectionComponent_div_0_Template, 33, 10, \"div\", 0);\n        i0.ɵɵtemplate(1, CreateClaimSelectionComponent_div_1_Template, 3, 4, \"div\", 1);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.claimType === ctx.addClaim && !ctx.previewVisible);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.previewVisible);\n      }\n    },\n    styles: [\".pointer[_ngcontent-%COMP%]{cursor:pointer}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.create-claim-form-styles[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.create-claim-form-styles[_ngcontent-%COMP%]   .form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.create-claim-form-styles[_ngcontent-%COMP%]   .search-icon-alignment[_ngcontent-%COMP%]{position:relative}.create-claim-form-styles[_ngcontent-%COMP%]   .search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.create-claim-form-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.create-claim-form-styles[_ngcontent-%COMP%]   .table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.create-claim-form-styles[_ngcontent-%COMP%]   .radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.create-claim-form-styles[_ngcontent-%COMP%]   .form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.mat-mdc-form-field[_ngcontent-%COMP%] + .mat-mdc-form-field[_ngcontent-%COMP%]{margin-left:8px}.mt-25[_ngcontent-%COMP%]{margin-top:25px}.mt-10[_ngcontent-%COMP%]{margin-top:10px}.mat-expansion-panel[_ngcontent-%COMP%]:not(.mat-expanded)   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover:not([aria-disabled=true]){background:#ECF4FC!important}.strong-text[_ngcontent-%COMP%]{font-weight:700}.header-bg[_ngcontent-%COMP%]{background-color:#f5f5f5;cursor:pointer}.header-bg[_ngcontent-%COMP%]:hover{background-color:#edeaea}.remitStatus[_ngcontent-%COMP%]{text-align:left;font-size:14px!important}.bgColor[_ngcontent-%COMP%]{background:#ECF4FC}.tbl[_ngcontent-%COMP%]{margin-top:10px;min-width:100%;overflow-x:auto;overflow-y:auto;z-index:9}.wd-100[_ngcontent-%COMP%]{width:100%}.border-0[_ngcontent-%COMP%]{border:0px}.ht-30[_ngcontent-%COMP%]{height:30px!important}.ml-5[_ngcontent-%COMP%]{margin-left:5px}.ml-25[_ngcontent-%COMP%]{margin-left:25px}.mlm[_ngcontent-%COMP%]{margin-left:-30px!important}.w-auto[_ngcontent-%COMP%]{width:auto!important}.mr-30[_ngcontent-%COMP%]{margin-right:30px!important}.btnmrgn1[_ngcontent-%COMP%]{margin-bottom:-52px!important}.mt-50[_ngcontent-%COMP%]{margin-top:50px}.mt-8[_ngcontent-%COMP%]{margin-top:8px}.mb-10[_ngcontent-%COMP%]{margin-bottom:10px}.ht-50[_ngcontent-%COMP%]{height:50px!important}.ht-28[_ngcontent-%COMP%]{height:28px!important}.wdt-120[_ngcontent-%COMP%]{width:120px}.wdt-100[_ngcontent-%COMP%]{width:100px}.menuitemschaild[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:15px;line-height:24px;color:#617798}.boldTxt[_ngcontent-%COMP%]{margin:0 12px;font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:14px}.cursor[_ngcontent-%COMP%]{cursor:pointer}.fa-plus[_ngcontent-%COMP%]{color:green!important}.fa-minus[_ngcontent-%COMP%]{color:red!important}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.plain-text[_ngcontent-%COMP%]{font-size:11px}.mt-22[_ngcontent-%COMP%]{margin-top:22px}.flr[_ngcontent-%COMP%]{float:right!important}.btn-mrg[_ngcontent-%COMP%]{margin-right:-160px}.fa-times[_ngcontent-%COMP%]:before{color:#fb5858}.Green-Color[_ngcontent-%COMP%]{background-color:#077e25;border-color:#077e25}.mat-card[_ngcontent-%COMP%]{padding:2px!important}.bckBtn[_ngcontent-%COMP%]{margin-top:11px;float:right;width:65px;margin-right:75px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}.service-lines[_ngcontent-%COMP%]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{white-space:break-spaces!important;font-size:12px}.dropdown-fix[_ngcontent-%COMP%]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{white-space:break-spaces!important;font-size:12px}.arrow-icon[_ngcontent-%COMP%]{color:#617798;font-size:20px;margin-top:5px}\"]\n  });\n  return CreateClaimSelectionComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}