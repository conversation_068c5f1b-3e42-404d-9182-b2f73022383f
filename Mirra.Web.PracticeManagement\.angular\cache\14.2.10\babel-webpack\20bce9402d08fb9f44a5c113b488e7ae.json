{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-spinner\";\nimport * as i2 from \"src/app/services/Member/member.service\";\nimport * as i3 from \"src/app/services/ProviderManagement/provider-management.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"ag-grid-angular\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"@angular/common\";\nexport let SearchInsuredPopupComponent = /*#__PURE__*/(() => {\n  class SearchInsuredPopupComponent {\n    constructor(spinner, memberSearhService, providerManagementService, searchMemberInsureInfoForm) {\n      this.spinner = spinner;\n      this.memberSearhService = memberSearhService;\n      this.providerManagementService = providerManagementService;\n      this.searchMemberInsureInfoForm = searchMemberInsureInfoForm;\n      this.IpaData = [];\n      this.memberResultItems = [];\n      this.DateToDisplay = \"\";\n      this.paginationPageSize = 10;\n      this.rowSelection = 'single';\n      this.columnDefs = [{\n        headerName: 'Subscriber ID',\n        width: 150,\n        resizable: true,\n        field: 'subscriberID',\n        tooltipField: \"subscriberID\"\n      }, {\n        headerName: 'Name',\n        width: 180,\n        resizable: true,\n        field: 'memberFullName',\n        tooltipField: 'memberFullName',\n        sortable: true\n      }, {\n        headerName: 'DOB',\n        width: 150,\n        resizable: true,\n        field: 'dateOfBirth',\n        tooltipField: \"dateOfBirth\",\n        cellRenderer: data => {\n          return moment(data.value).format('MM/DD/YYYY');\n        }\n      }, {\n        headerName: 'Gender',\n        width: 150,\n        resizable: true,\n        field: 'gender',\n        tooltipField: \"gender\"\n      }, {\n        headerName: 'PCP',\n        width: 150,\n        resizable: true,\n        field: 'pcpFullName',\n        tooltipField: \"pcpFullName\"\n      }, {\n        headerName: 'Insurance',\n        width: 250,\n        resizable: true,\n        tooltipField: \"insuranceCompanyName\",\n        field: 'insuranceCompanyName'\n      }, {\n        headerName: 'Effective Date',\n        width: 150,\n        resizable: true,\n        field: 'effectiveDate',\n        tooltipField: \"effectiveDate\",\n        cellRenderer: data => {\n          return moment(data.value).format('MM/DD/YYYY');\n        }\n      }, {\n        headerName: 'Term Date',\n        width: 150,\n        resizable: true,\n        field: 'terminationDate',\n        tooltipField: \"terminationDate\",\n        cellRenderer: data => {\n          return moment(data.value).format('MM/DD/YYYY');\n        }\n      }];\n      this.dialogRef.disableClose = true;\n      this.createForm(); //this.fillIPACodes();\n\n      this.fillMember();\n    }\n\n    ngOnInit() {\n      this.searchinsureGridOptions = {\n        rowHeight: 30,\n        getRowStyle: this.changeRowColor,\n        defaultColDef: {\n          lockVisible: true,\n          initialWidth: 100,\n          sortable: true,\n          filter: true,\n          floatingFilter: false,\n          suppressMenu: true,\n          wrapText: true,\n          autoHeight: true,\n          floatingFilterComponentParams: {\n            suppressFilterButton: true\n          }\n        },\n        style: {\n          width: '100%',\n          height: '100%',\n          flex: '1 1 auto'\n        },\n        pagination: {\n          enable: true,\n          size: 10\n        },\n        totalRowsCount: 0\n      };\n    }\n\n    changeRowColor(params) {\n      if (params.node.rowIndex % 2 === 0) {\n        return {\n          'background-color': '#f1f0f0'\n        };\n      } else {\n        return {\n          'background-color': 'white'\n        };\n      }\n    }\n\n    onDate(event) {\n      this.DateToDisplay = \"Select a Range\";\n    }\n\n    createForm() {\n      this.searchMemberInsureInfo = this.searchMemberInsureInfoForm.group({\n        insuredOSFrom: new FormControl(''),\n        insuredOSTo: new FormControl(''),\n        insuresubscriberID: new FormControl(''),\n        insurefirstName: new FormControl(''),\n        insurelastName: new FormControl('')\n      });\n      return this.searchMemberInsureInfo;\n    }\n\n    fillMember() {\n      let member = {\n        // isActive: true,\n        p_DOSFrom: this.searchMemberInsureInfo.controls['insuredOSFrom'].value ? new Date(this.searchMemberInsureInfo.controls['insuredOSFrom'].value).toISOString() : '2020-05-16T00:00:00.000Z',\n        p_DOSTo: this.searchMemberInsureInfo.controls['insuredOSTo'].value ? new Date(this.searchMemberInsureInfo.controls['insuredOSFrom'].value).toISOString() : '2021-05-16T00:00:00.000Z',\n        paginationOption: {\n          pageNumber: 1,\n          pageOffset: 5\n        },\n        childSearchParameter: null,\n        defaultSortOptions: [{\n          columnName: \"FIRSTNAME\",\n          sortingOrder: 0\n        }],\n        searchParameter: {\n          dOSFrom: this.searchMemberInsureInfo.controls['insuredOSFrom'].value ? new Date(this.searchMemberInsureInfo.controls['insuredOSFrom'].value).toISOString() : '2020-05-16T00:00:00.000Z',\n          dOSTo: this.searchMemberInsureInfo.controls['insuredOSTo'].value ? new Date(this.searchMemberInsureInfo.controls['insuredOSFrom'].value).toISOString() : '2021-05-16T00:00:00.000Z',\n          firstName: this.searchMemberInsureInfo.controls['insurefirstName'].value,\n          lastName: this.searchMemberInsureInfo.controls['insurelastName'].value,\n          subscriberID: this.searchMemberInsureInfo.controls['insuresubscriberID'].value\n        }\n      };\n      this.memberSearhService.fetchAllMemberResult(member).subscribe(res => {\n        this.memberResultItems = res;\n      });\n    }\n\n    submit() {\n      this.fillMember();\n    }\n\n    onSelectionChanged(e) {\n      {\n        const selectedRows = this.gridApi.getSelectedRows();\n        selectedRows.forEach(element => {});\n      }\n    }\n\n    onGridReady(params) {\n      this.gridApi = params.api;\n    }\n\n    closeAll() {\n      this.dialogRef.close();\n    }\n\n  }\n\n  SearchInsuredPopupComponent.ɵfac = function SearchInsuredPopupComponent_Factory(t) {\n    return new (t || SearchInsuredPopupComponent)(i0.ɵɵdirectiveInject(i1.NgxSpinnerService), i0.ɵɵdirectiveInject(i2.MemberService), i0.ɵɵdirectiveInject(i3.ProviderManagementService), i0.ɵɵdirectiveInject(i4.FormBuilder));\n  };\n\n  SearchInsuredPopupComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SearchInsuredPopupComponent,\n    selectors: [[\"app-search-insured-popup\"]],\n    decls: 37,\n    vars: 14,\n    consts: [[3, \"formGroup\", \"ngSubmit\"], [1, \"dailog-header\"], [\"mat-dialog-title\", \"\"], [\"mat-button\", \"\", \"mat-dialog-title\", \"\", 1, \"dailog-close\", 3, \"click\"], [1, \"row\"], [1, \"col-md-2\"], [1, \"dashboard-label\"], [\"type\", \"date\", \"formControlName\", \"insuredOSFrom\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [\"type\", \"date\", \"placeholder\", \"MM-DD-YYYY\", \"formControlName\", \"insuredOSTo\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [\"type\", \"text\", \"placeholder\", \"Subscriber Id\", \"formControlName\", \"insuresubscriberID\", 1, \"form-control\"], [\"type\", \"text\", \"placeholder\", \"Last Name\", \"formControlName\", \"insurefirstName\", 1, \"form-control\"], [\"type\", \"text\", \"placeholder\", \"First Name\", \"formControlName\", \"insurelastName\", 1, \"form-control\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"mt-25\", \"flr\"], [1, \"row\", \"grid-deal-search\", \"mb-2\"], [\"id\", \"insureMember\", 1, \"ag-theme-alpine\", \"ag-grid-view\", 3, \"rowData\", \"columnDefs\", \"gridOptions\", \"pagination\", \"accentedSort\", \"rowSelection\", \"paginationNumberFormatter\", \"enableCellTextSelection\", \"paginationPageSize\", \"overlayNoRowsTemplate\", \"selectionChanged\", \"gridReady\"]],\n    template: function SearchInsuredPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0);\n        i0.ɵɵlistener(\"ngSubmit\", function SearchInsuredPopupComponent_Template_form_ngSubmit_0_listener() {\n          return ctx.submit();\n        });\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\")(3, \"h3\", 2);\n        i0.ɵɵtext(4, \"Insured\\u2019s ID Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"div\")(6, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function SearchInsuredPopupComponent_Template_button_click_6_listener() {\n          return ctx.closeAll();\n        });\n        i0.ɵɵtext(7, \"X\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"mat-dialog-content\")(9, \"div\", 4)(10, \"div\", 5)(11, \"label\", 6);\n        i0.ɵɵtext(12, \" DOS From \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"input\", 7);\n        i0.ɵɵlistener(\"change\", function SearchInsuredPopupComponent_Template_input_change_13_listener($event) {\n          return ctx.onDate($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 5)(15, \"label\", 6);\n        i0.ɵɵtext(16, \" DOS To \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"input\", 8);\n        i0.ɵɵlistener(\"change\", function SearchInsuredPopupComponent_Template_input_change_17_listener($event) {\n          return ctx.onDate($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 5)(19, \"label\", 6);\n        i0.ɵɵtext(20, \" Subscriber Id \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(21, \"input\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 5)(23, \"label\", 6);\n        i0.ɵɵtext(24, \" Last Name \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(25, \"input\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"div\", 5)(27, \"label\", 6);\n        i0.ɵɵtext(28, \" First Name \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(29, \"input\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"div\", 5)(31, \"button\", 12);\n        i0.ɵɵtext(32, \" Search \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtext(33);\n        i0.ɵɵpipe(34, \"json\");\n        i0.ɵɵelementStart(35, \"div\", 13)(36, \"ag-grid-angular\", 14);\n        i0.ɵɵlistener(\"selectionChanged\", function SearchInsuredPopupComponent_Template_ag_grid_angular_selectionChanged_36_listener($event) {\n          return ctx.onSelectionChanged($event);\n        })(\"gridReady\", function SearchInsuredPopupComponent_Template_ag_grid_angular_gridReady_36_listener($event) {\n          return ctx.onGridReady($event);\n        });\n        i0.ɵɵelementEnd()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.searchMemberInsureInfo);\n        i0.ɵɵadvance(33);\n        i0.ɵɵtextInterpolate1(\" sadasdasd \", i0.ɵɵpipeBind1(34, 12, ctx.memberResultItems), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"rowData\", ctx.memberResultItems)(\"columnDefs\", ctx.columnDefs)(\"gridOptions\", ctx.searchinsureGridOptions)(\"pagination\", true)(\"accentedSort\", true)(\"rowSelection\", ctx.rowSelection)(\"paginationNumberFormatter\", ctx.searchinsureGridOptions.pagination.formatter)(\"enableCellTextSelection\", true)(\"paginationPageSize\", ctx.paginationPageSize)(\"overlayNoRowsTemplate\", ctx.overlayNoRowsTemplate);\n      }\n    },\n    dependencies: [i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i5.AgGridAngular, i4.FormGroupDirective, i4.FormControlName, i6.MatDialogTitle, i6.MatDialogContent, i7.JsonPipe],\n    styles: [\".create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.create-claim-form-styles[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.create-claim-form-styles[_ngcontent-%COMP%]   .form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.create-claim-form-styles[_ngcontent-%COMP%]   .search-icon-alignment[_ngcontent-%COMP%]{position:relative}.create-claim-form-styles[_ngcontent-%COMP%]   .search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.create-claim-form-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.create-claim-form-styles[_ngcontent-%COMP%]   .table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.create-claim-form-styles[_ngcontent-%COMP%]   .radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.create-claim-form-styles[_ngcontent-%COMP%]   .form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.mat-mdc-form-field[_ngcontent-%COMP%] + .mat-mdc-form-field[_ngcontent-%COMP%]{margin-left:8px}.mt-25[_ngcontent-%COMP%]{margin-top:25px}.mt-10[_ngcontent-%COMP%]{margin-top:10px}.mat-expansion-panel[_ngcontent-%COMP%]:not(.mat-expanded)   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover:not([aria-disabled=true]){background:#ECF4FC!important}.strong-text[_ngcontent-%COMP%]{font-weight:700}.header-bg[_ngcontent-%COMP%]{background-color:#f5f5f5;cursor:pointer}.header-bg[_ngcontent-%COMP%]:hover{background-color:#edeaea}.remitStatus[_ngcontent-%COMP%]{text-align:left;font-size:14px!important}.bgColor[_ngcontent-%COMP%]{background:#ECF4FC}.tbl[_ngcontent-%COMP%]{margin-top:10px;min-width:100%;overflow-x:auto;overflow-y:auto;z-index:9}.wd-100[_ngcontent-%COMP%]{width:100%}.border-0[_ngcontent-%COMP%]{border:0px}.ht-30[_ngcontent-%COMP%]{height:30px!important}.ml-5[_ngcontent-%COMP%]{margin-left:5px}.ml-25[_ngcontent-%COMP%]{margin-left:25px}.mlm[_ngcontent-%COMP%]{margin-left:-30px!important}.w-auto[_ngcontent-%COMP%]{width:auto!important}.mr-30[_ngcontent-%COMP%]{margin-right:30px!important}.btnmrgn1[_ngcontent-%COMP%]{margin-bottom:-52px!important}.mt-50[_ngcontent-%COMP%]{margin-top:50px}.mt-8[_ngcontent-%COMP%]{margin-top:8px}.mb-10[_ngcontent-%COMP%]{margin-bottom:10px}.ht-50[_ngcontent-%COMP%]{height:50px!important}.ht-28[_ngcontent-%COMP%]{height:28px!important}.wdt-120[_ngcontent-%COMP%]{width:120px}.wdt-100[_ngcontent-%COMP%]{width:100px}.menuitemschaild[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:15px;line-height:24px;color:#617798}.boldTxt[_ngcontent-%COMP%]{margin:0 12px;font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:14px}.cursor[_ngcontent-%COMP%]{cursor:pointer}.fa-plus[_ngcontent-%COMP%]{color:green!important}.fa-minus[_ngcontent-%COMP%]{color:red!important}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.plain-text[_ngcontent-%COMP%]{font-size:11px}.mt-22[_ngcontent-%COMP%]{margin-top:22px}.flr[_ngcontent-%COMP%]{float:right!important}.btn-mrg[_ngcontent-%COMP%]{margin-right:-160px}.fa-times[_ngcontent-%COMP%]:before{color:#fb5858}.Green-Color[_ngcontent-%COMP%]{background-color:#077e25;border-color:#077e25}.mat-card[_ngcontent-%COMP%]{padding:2px!important}.bckBtn[_ngcontent-%COMP%]{margin-top:11px;float:right;width:50px;margin-right:75px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}.grid-deal-search[_ngcontent-%COMP%]{width:100%;flex:1 1 auto;--bs-gutter-x: 0rem!important;height:40vh}.btn-flex[_ngcontent-%COMP%]{display:flex;justify-content:space-between}\"]\n  });\n  return SearchInsuredPopupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}