{"ast": null, "code": "export const ClaimStatus = [{\n  statusCode: 'UACH',\n  status: 'Initiated',\n  imagePath: '',\n  totalClaims: 0,\n  groupClaim: 'INIT'\n}, {\n  statusCode: 'ON',\n  status: 'Open',\n  imagePath: './assets/icons/dashboard-icons/Open.svg',\n  totalClaims: 0,\n  groupClaim: 'INIT'\n}, {\n  statusCode: 'OH',\n  status: 'On Hold',\n  imagePath: './assets/icons/dashboard-icons/onHold.svg',\n  totalClaims: 0,\n  groupClaim: 'INIT'\n}, {\n  statusCode: 'NTR',\n  status: 'Re Submission',\n  imagePath: './assets/icons/dashboard-icons/Re-Submission.svg',\n  totalClaims: 0,\n  groupClaim: 'INIT'\n}, // {\n//     statusCode: 'NTR', status: 'NTR', imagePath: './assets/icons/dashboard-icons/Re-Submission.svg', totalClaims: 0, groupClaim: 'INIT'\n// },\n{\n  statusCode: 'AC',\n  status: 'Accepted',\n  imagePath: './assets/icons/dashboard-icons/Accepted.svg',\n  totalClaims: 0,\n  groupClaim: 'INIT'\n}, {\n  statusCode: 'DP',\n  status: 'Dispatched',\n  imagePath: './assets/icons/dashboard-icons/Dispatched.svg',\n  totalClaims: 0,\n  groupClaim: 'INIT'\n}, {\n  statusCode: 'UAP',\n  status: 'Received by CH',\n  imagePath: '',\n  totalClaims: 0,\n  groupClaim: 'RECEIVED'\n}, {\n  statusCode: 'UACH',\n  status: 'Un Ack by CH',\n  imagePath: './assets/icons/dashboard-icons/Un-Ack-by-CH.svg',\n  totalClaims: 0,\n  groupClaim: 'RECEIVED'\n}, {\n  statusCode: 'RCH',\n  status: 'Rejected by CH',\n  imagePath: './assets/icons/dashboard-icons/Rejected-by-CH.svg',\n  totalClaims: 0,\n  groupClaim: 'RECEIVED'\n}, {\n  statusCode: 'ANCH',\n  status: 'Ack by CH',\n  imagePath: './assets/icons/dashboard-icons/Ack-by-CH.svg',\n  totalClaims: 0,\n  groupClaim: 'RECEIVED'\n}, {\n  statusCode: 'ABCH',\n  status: 'Accepted by CH',\n  imagePath: './assets/icons/dashboard-icons/Accepted-by-CH.svg',\n  totalClaims: 0,\n  groupClaim: 'RECEIVED'\n}, {\n  statusCode: 'UAP',\n  status: 'Received by Payer',\n  imagePath: '',\n  totalClaims: 0,\n  groupClaim: 'RECEIVEDPAYER'\n}, {\n  statusCode: 'UAP',\n  status: 'Un Ack by Payer',\n  imagePath: './assets/icons/dashboard-icons/Un-Ack-by-Payer.svg',\n  totalClaims: 0,\n  groupClaim: 'RECEIVEDPAYER'\n}, {\n  statusCode: 'RP',\n  status: 'Rejected by Payer',\n  imagePath: './assets/icons/dashboard-icons/Rejected-by-Payer.svg',\n  totalClaims: 0,\n  groupClaim: 'RECEIVEDPAYER'\n}, {\n  statusCode: 'AP',\n  status: 'Ack by Payer',\n  imagePath: './assets/icons/dashboard-icons/Ack-by-CH-payer.svg',\n  totalClaims: 0,\n  groupClaim: 'RECEIVEDPAYER'\n}, {\n  statusCode: 'ABP',\n  status: 'Accepted by Payer',\n  imagePath: './assets/icons/dashboard-icons/Accepted-by-Payer.svg',\n  totalClaims: 0,\n  groupClaim: 'RECEIVEDPAYER'\n}, {\n  statusCode: 'DBP',\n  status: 'Payment Received',\n  imagePath: '',\n  totalClaims: 0,\n  groupClaim: 'PAYMENT'\n}, {\n  statusCode: 'Pend',\n  status: 'Pending',\n  imagePath: './assets/icons/dashboard-icons/Pending.svg',\n  totalClaims: 0,\n  groupClaim: 'PAYMENT'\n}, {\n  statusCode: 'DBP',\n  status: 'Denied by Payer',\n  imagePath: './assets/icons/dashboard-icons/Denied-by-Payer.svg',\n  totalClaims: 0,\n  groupClaim: 'PAYMENT'\n}, {\n  statusCode: 'EOB',\n  status: 'EOB Received',\n  imagePath: './assets/icons/dashboard-icons/EOB-Receieved.svg',\n  totalClaims: 0,\n  groupClaim: 'PAYMENT'\n}];\nexport const ClaimCreationType = {\n  sameMember: 1,\n  sameProvider: 2,\n  newClaim: 3\n};\nexport const autoLogoutConfig = {\n  idleTime: 3600,\n  timeOutTime: 300\n};\nexport const LocalStorageKey = {\n  getQualList: 'QualList',\n  resubmissionCode: 'resubmissionCode',\n  userIpas: 'userIpas',\n  allIpasString: 'allIpasString',\n  userIpaDropdown: 'userIpaDropdown',\n  allSpecialityTaxonomyCodes: 'allSpecialityTaxonomyCodes',\n  allPlaceOfServices: 'allPlaceOfServices',\n  allStates: 'allStates',\n  allCPT: 'allCPT',\n  allCPTCharges: 'allCPTCharges',\n  storageSecurityKey: 'w9X&J7^kR!4z@bP0Lh$eQcA1Fg#mN3TuZy2Wsa',\n  selectedICDCodesForCreateClaim: 'selectedICDCodesForCreateClaim'\n};\nexport const PriceCost = {\n  zeroPrice: '0.00',\n  zeroPointZeroOnePrice: '0.01'\n};", "map": null, "metadata": {}, "sourceType": "module"}