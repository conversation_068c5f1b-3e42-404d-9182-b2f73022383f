{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/remit-process/remit-process.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\n\nfunction ProcessRemitComponent_ng_container_27_tr_11_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 22)(4, \"span\", 15);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const eachSvc_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(eachSvc_r4.productId01);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getCptStatusCss(eachSvc_r4.classificationStatus));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(eachSvc_r4.classificationStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 6, eachSvc_r4.chargeAmount, \"USD\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, eachSvc_r4.paymentAmount, \"USD\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(eachSvc_r4.classificationDescription);\n  }\n}\n\nfunction ProcessRemitComponent_ng_container_27_tr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 17);\n    i0.ɵɵtext(2, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 18)(4, \"table\", 19)(5, \"thead\")(6, \"tr\")(7, \"th\", 14);\n    i0.ɵɵtext(8, \"CPT Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 14);\n    i0.ɵɵtext(10, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 20);\n    i0.ɵɵtext(12, \"Charge Amount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 20);\n    i0.ɵɵtext(14, \"Payment Amount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Description\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"tbody\");\n    i0.ɵɵtemplate(18, ProcessRemitComponent_ng_container_27_tr_11_tr_18_Template, 14, 12, \"tr\", 12);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const eachResult_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", eachResult_r1.serviceClassifications);\n  }\n}\n\nfunction ProcessRemitComponent_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\")(2, \"td\")(3, \"i\", 13);\n    i0.ɵɵlistener(\"click\", function ProcessRemitComponent_ng_container_27_Template_i_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const eachResult_r1 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.toggleSvc(eachResult_r1));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 14);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 14)(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, ProcessRemitComponent_ng_container_27_tr_11_Template, 19, 1, \"tr\", 16);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const eachResult_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getToggleCss(eachResult_r1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(eachResult_r1.claimSubmitterIdentifier);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getCptStatusCss(eachResult_r1.classificationStatus));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(eachResult_r1.classificationStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(eachResult_r1.classificationDescription);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachResult_r1.status);\n  }\n}\n\nexport let ProcessRemitComponent = /*#__PURE__*/(() => {\n  class ProcessRemitComponent {\n    constructor(remitProcessService) {\n      this.remitProcessService = remitProcessService;\n      this.classificationResultWithToggle = [];\n      this.remitFileId = \"0\";\n    }\n\n    ngOnInit() {}\n\n    onFileChange(event) {\n      this.remitFile = event.target.files[0];\n      console.log(this.remitFile);\n    }\n\n    toggleSvc(clpDetails) {\n      let position = -1;\n\n      for (let i = 0; i < this.classificationResultWithToggle.length; i++) {\n        if (this.classificationResultWithToggle[i].claimPaymentInformationMasterId === clpDetails.claimPaymentInformationMasterId) {\n          position = i;\n          break;\n        }\n      }\n\n      if (position > -1) {\n        this.classificationResultWithToggle[position].status = !this.classificationResultWithToggle[position].status;\n      }\n    }\n\n    getToggleCss(clpDetails) {\n      let cssClass = \"fa \";\n\n      if (clpDetails.status) {\n        cssClass = cssClass + \"fa-minus\";\n      } else {\n        cssClass = cssClass + \"fa-plus\";\n      }\n\n      return cssClass;\n    }\n\n    exportToExcel() {}\n\n    getCptStatusCss(classificationStatus) {\n      let cssClass = \"badge\";\n\n      if (classificationStatus == \"Unknown\") {\n        cssClass = cssClass + \" text-bg-warning\";\n      } else if (classificationStatus == \"Denied\") {\n        cssClass = cssClass + \" text-bg-danger\";\n      } else if (classificationStatus == \"Undetermined\") {\n        cssClass = cssClass + \" text-bg-secondary\";\n      } else {\n        cssClass = cssClass + \" text-bg-success\";\n      }\n\n      return cssClass;\n    }\n\n    filterClassifications(event) {\n      this.filterText = event.target.value;\n    }\n\n    processRemit() {\n      var that = this;\n      that.processButtonDisabled = true;\n      this.remitProcessService.processRemitFile(this.remitFile).subscribe(data => {\n        that.classificationResultWithToggle = new Array();\n        that.classificationResult = data.content;\n        that.classificationResult.forEach(function (_each) {\n          let newEach = { ..._each,\n            show: false\n          };\n          that.classificationResultWithToggle.push(newEach);\n        });\n        that.classificationResultsFiltered = [...that.classificationResultWithToggle];\n        that.processButtonDisabled = false;\n      });\n    }\n\n    filteredClassifications() {\n      let clonedList = [...this.classificationResultWithToggle];\n\n      if (this.filterText === undefined || this.filterText === null) {\n        return clonedList;\n      }\n\n      if (this.filterText.length < 2) {\n        return clonedList;\n      }\n\n      let that = this;\n      return clonedList.filter(function (el) {\n        return el.claimSubmitterIdentifier.indexOf(that.filterText) > 0;\n      });\n    }\n\n  }\n\n  ProcessRemitComponent.ɵfac = function ProcessRemitComponent_Factory(t) {\n    return new (t || ProcessRemitComponent)(i0.ɵɵdirectiveInject(i1.RemitProcessService));\n  };\n\n  ProcessRemitComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProcessRemitComponent,\n    selectors: [[\"app-process-remit\"]],\n    decls: 28,\n    vars: 3,\n    consts: [[1, \"container\"], [1, \"row\"], [1, \"col-sm\"], [\"type\", \"file\", \"id\", \"fileUpload\", 1, \"form-control\", 3, \"change\"], [\"type\", \"text\", \"placeholder\", \"Filter Claim Id\", 1, \"form-control\", 3, \"ngModel\", \"keyup\"], [1, \"row\", 2, \"margin-top\", \"10px\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"table\", \"table-striped\"], [\"scope\", \"col\", 2, \"width\", \"10px\"], [1, \"fa\", \"fa-file-excel-o\", 2, \"cursor\", \"pointer\", 3, \"click\"], [\"scope\", \"col\", 1, \"text-center\", 2, \"width\", \"100px\"], [\"scope\", \"col\"], [4, \"ngFor\", \"ngForOf\"], [2, \"cursor\", \"pointer\", 3, \"ngClass\", \"click\"], [1, \"text-center\", 2, \"width\", \"100px\"], [3, \"ngClass\"], [4, \"ngIf\"], [\"colspan\", \"1\"], [\"colspan\", \"4\"], [1, \"table-bordered\"], [1, \"text-end\", 2, \"width\", \"150px\"], [1, \"text-center\"], [1, \"align-middle\", \"text-center\"], [1, \"text-end\"]],\n    template: function ProcessRemitComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\");\n        i0.ɵɵtext(2, \"\\u00A0\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 0)(4, \"div\", 1)(5, \"div\", 2)(6, \"input\", 3);\n        i0.ɵɵlistener(\"change\", function ProcessRemitComponent_Template_input_change_6_listener($event) {\n          return ctx.onFileChange($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 2)(8, \"input\", 4);\n        i0.ɵɵlistener(\"keyup\", function ProcessRemitComponent_Template_input_keyup_8_listener($event) {\n          return ctx.filterClassifications($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 5)(10, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function ProcessRemitComponent_Template_button_click_10_listener() {\n          return ctx.processRemit();\n        });\n        i0.ɵɵtext(11, \"Process and Classify Remit\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(12, \"div\");\n        i0.ɵɵtext(13, \"\\u00A0\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"div\")(15, \"table\", 7)(16, \"thead\")(17, \"tr\")(18, \"th\", 8)(19, \"i\", 9);\n        i0.ɵɵlistener(\"click\", function ProcessRemitComponent_Template_i_click_19_listener() {\n          return ctx.exportToExcel();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"th\", 10);\n        i0.ɵɵtext(21, \"Claim Id\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"th\", 10);\n        i0.ɵɵtext(23, \"Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"th\", 11);\n        i0.ɵɵtext(25, \"Classification Description\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(26, \"tbody\");\n        i0.ɵɵtemplate(27, ProcessRemitComponent_ng_container_27_Template, 12, 6, \"ng-container\", 12);\n        i0.ɵɵelementEnd()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngModel\", ctx.filterText);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.processButtonDisabled);\n        i0.ɵɵadvance(17);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredClassifications());\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i2.CurrencyPipe]\n  });\n  return ProcessRemitComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}