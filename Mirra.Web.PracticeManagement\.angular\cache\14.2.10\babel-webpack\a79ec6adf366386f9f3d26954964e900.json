{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nexport let AddressMismatchModalComponent = /*#__PURE__*/(() => {\n  class AddressMismatchModalComponent {\n    constructor(dialogRef) {\n      this.dialogRef = dialogRef;\n    }\n\n    ngOnInit() {}\n\n    OnClose() {\n      this.dialogRef.close(false);\n    }\n\n  }\n\n  AddressMismatchModalComponent.ɵfac = function AddressMismatchModalComponent_Factory(t) {\n    return new (t || AddressMismatchModalComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef));\n  };\n\n  AddressMismatchModalComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddressMismatchModalComponent,\n    selectors: [[\"app-address-mismatch-modal\"]],\n    decls: 60,\n    vars: 0,\n    consts: [[1, \"row\", \"SemiBold\", \"OuterBox\"], [1, \"row\", \"mt-4\"], [1, \"col-10\", \"MainHeading\"], [1, \"col\", \"text-end\", \"CloseIcon\"], [\"alt\", \"Close\", \"src\", \"../../../assets/images/modals/SaveModal/CloseIcon.png\", \"id\", \"ModalClose\", 1, \"Modal-Close-Image\", 3, \"click\"], [1, \"col\", \"PleaseMapLine\"], [1, \"row\", \"BoxStyling\"], [1, \"row\"], [1, \"col\", \"NestedHeading\"], [1, \"row\", \"mt-4\", \"Grey-Heading\"], [1, \"col\", \"Heading\"], [1, \"row\", \"mt-3\"], [1, \"col\", \"Value\"], [\"type\", \"text\", \"placeholder\", \"Address Line 1\", \"id\", \"Address Line 1\", \"name\", \"Address Line 1\", \"disabled\", \"\"], [\"type\", \"text\", \"placeholder\", \"City\", \"id\", \"City\", \"name\", \"City\", \"disabled\", \"\"], [\"type\", \"text\", \"placeholder\", \"State\", \"id\", \"State\", \"name\", \"State\", \"disabled\", \"\"], [\"type\", \"text\", \"placeholder\", \"ZipCode\", \"id\", \"ZipCode\", \"name\", \"ZipCode\", \"disabled\", \"\"], [1, \"row\", \"mt-5\", \"mb-3\"], [1, \"col\"], [1, \"col\", \"text-center\"], [1, \"btn\", \"MarginRight\", 3, \"click\"]],\n    template: function AddressMismatchModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵtext(3, \" Address Mismatch Alert \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 3)(5, \"img\", 4);\n        i0.ɵɵlistener(\"click\", function AddressMismatchModalComponent_Template_img_click_5_listener() {\n          return ctx.OnClose();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"div\", 1)(7, \"div\", 5);\n        i0.ɵɵtext(8, \" Below Addresses are not matching with the Smarty Street Address. Please map the given Addresses with the Addresses mentioned below: \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 6)(10, \"div\", 7)(11, \"div\", 8);\n        i0.ɵɵtext(12, \" Billing Provider \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10);\n        i0.ɵɵtext(15, \" Address Line 1 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"div\", 10);\n        i0.ɵɵtext(17, \" City \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"div\", 10);\n        i0.ɵɵtext(19, \" State \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"div\", 10);\n        i0.ɵɵtext(21, \" ZipCode \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 11)(23, \"div\", 12);\n        i0.ɵɵelement(24, \"input\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 12);\n        i0.ɵɵelement(26, \"input\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"div\", 12);\n        i0.ɵɵelement(28, \"input\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"div\", 12);\n        i0.ɵɵelement(30, \"input\", 16);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(31, \"div\", 6)(32, \"div\", 7)(33, \"div\", 8);\n        i0.ɵɵtext(34, \" Payer \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(35, \"div\", 9)(36, \"div\", 10);\n        i0.ɵɵtext(37, \" Address Line 1 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"div\", 10);\n        i0.ɵɵtext(39, \" City \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"div\", 10);\n        i0.ɵɵtext(41, \" State \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"div\", 10);\n        i0.ɵɵtext(43, \" ZipCode \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(44, \"div\", 11)(45, \"div\", 12);\n        i0.ɵɵelement(46, \"input\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(47, \"div\", 12);\n        i0.ɵɵelement(48, \"input\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(49, \"div\", 12);\n        i0.ɵɵelement(50, \"input\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"div\", 12);\n        i0.ɵɵelement(52, \"input\", 16);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(53, \"div\", 17);\n        i0.ɵɵelementStart(54, \"div\", 7);\n        i0.ɵɵelement(55, \"div\", 18);\n        i0.ɵɵelementStart(56, \"div\", 19)(57, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function AddressMismatchModalComponent_Template_button_click_57_listener() {\n          return ctx.OnClose();\n        });\n        i0.ɵɵtext(58, \"Map\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(59, \"div\", 18);\n        i0.ɵɵelementEnd()();\n      }\n    },\n    styles: [\".SemiBold[_ngcontent-%COMP%]{font-family:Poppins-SemiBold}.PleaseMapLine[_ngcontent-%COMP%]{font-size:14px}.MainHeading[_ngcontent-%COMP%]{text-align:center;font-size:25px}.Heading[_ngcontent-%COMP%]{font-family:Poppins-SemiBold;font-size:17px}.Error[_ngcontent-%COMP%]{color:red;font-family:Poppins-SemiBold}.btn[_ngcontent-%COMP%]{height:35px;color:#fff;box-shadow:0 0 8px #0000001a;border-radius:8px;opacity:1;background-color:#0074bc;font-family:Poppins-SemiBold;font-size:14px}.reset[_ngcontent-%COMP%]{background-color:#fff;color:#0074bc;border:solid 1px #0074bc}input[_ngcontent-%COMP%]{background-color:#fff;outline:none;border:1px solid #D9DADE;font-family:Poppins;font-size:12px;width:132px;border-radius:8px;height:34px}.MarginRight[_ngcontent-%COMP%]{margin-right:7px}.Modal-Close-Image[_ngcontent-%COMP%]{cursor:pointer}.Grey-Heading[_ngcontent-%COMP%]{color:#646464}.NestedHeading[_ngcontent-%COMP%]{font-size:21px;font-family:Poppins-Bold}.Value[_ngcontent-%COMP%]{font-family:Poppins-Medium}.BoxStyling[_ngcontent-%COMP%]{background:#F8F8F8 0% 0% no-repeat padding-box;border:1px solid #CCCCCC;border-radius:6px;opacity:1;margin:12px 5px;padding:8px 0;font-family:Poppins-SemiBold}.OuterBox[_ngcontent-%COMP%]{margin-right:20px}.CloseIcon[_ngcontent-%COMP%]{margin-right:-30px}\"]\n  });\n  return AddressMismatchModalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}