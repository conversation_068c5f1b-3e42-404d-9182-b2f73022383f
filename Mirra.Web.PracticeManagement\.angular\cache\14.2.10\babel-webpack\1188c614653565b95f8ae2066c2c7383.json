{"ast": null, "code": "import { FormControl, Validators } from '@angular/forms';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../../../../shared/directives/numbers-and-alphabets-only.directive\";\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction SubscriberInfoComponent_input_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 6);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, (ctx_r0.f.subscriberID == null ? null : ctx_r0.f.subscriberID.invalid) && (ctx_r0.f.subscriberID.dirty || ctx_r0.f.subscriberID.touched) && (ctx_r0.f.subscriberID == null ? null : ctx_r0.f.subscriberID.errors == null ? null : ctx_r0.f.subscriberID.errors.required)));\n  }\n}\n\nfunction SubscriberInfoComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtext(1, \" Please enter Insured\\u2019s no \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SubscriberInfoComponent_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r2.f.subscriberID.value && ctx_r2.f.subscriberID.value.length > 0 ? ctx_r2.f.subscriberID.value : \"-\");\n  }\n}\n\nexport let SubscriberInfoComponent = /*#__PURE__*/(() => {\n  class SubscriberInfoComponent {\n    constructor(subscriberInfoform) {\n      this.subscriberInfoform = subscriberInfoform;\n    }\n\n    ngOnInit() {\n      this.patchValue();\n    }\n\n    patchValue() {\n      if (this.claimFormData.isAddClaim) {\n        this.subscriberInfo.patchValue({\n          subscriberID: this.subscriberId\n        });\n        this.subscriberInfo.updateValueAndValidity();\n        submitValidateAllFields.validateDisableControl(this.subscriberInfo, [\"\"]);\n      } else if (this.claimFormData.isEditClaim || this.claimFormData.isViewClaim) {\n        this.subscriberInfo.patchValue({\n          subscriberID: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.nm109SubscriberIdCode\n        });\n        this.subscriberInfo.disable(); // if (this.claimFormData?.claimViewModel?.claimFormStatusCode === \"ON\" && this.claimFormData.isEditClaim )\n        //   this.subscriberInfo.enable();\n      }\n\n      if (!!this.subscriberInfo.controls.subscriberID.value) {\n        this.subscriberInfo.controls.subscriberID.setValue(this.subscriberInfo.controls.subscriberID.value.trim().replace(/[^a-zA-Z0-9]/g, ''));\n      }\n    }\n\n    createForm() {\n      this.subscriberInfo = this.subscriberInfoform.group({\n        subscriberID: new FormControl(this.subscriberId, [Validators.required])\n      });\n      return this.subscriberInfo;\n    }\n\n    get f() {\n      return this.subscriberInfo.controls;\n    }\n\n    validateForm() {\n      if (this.subscriberInfo.invalid) {\n        submitValidateAllFields.validateAllFields(this.subscriberInfo);\n        return false;\n      }\n\n      return true;\n    }\n\n  }\n\n  SubscriberInfoComponent.ɵfac = function SubscriberInfoComponent_Factory(t) {\n    return new (t || SubscriberInfoComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n  };\n\n  SubscriberInfoComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SubscriberInfoComponent,\n    selectors: [[\"app-subscriber-info\"]],\n    inputs: {\n      subscriberId: \"subscriberId\",\n      claimFormData: \"claimFormData\"\n    },\n    decls: 7,\n    vars: 4,\n    consts: [[3, \"formGroup\"], [1, \"col\"], [1, \"form-title\"], [\"tabindex\", \"8\", \"type\", \"text\", \"required\", \"\", \"formControlName\", \"subscriberID\", \"numbersAndAlphabetsOnly\", \"\", \"placeholder\", \"Click on the Search Icon to Select the Member\", \"class\", \"form-control height27 w-100\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"form-control\", 4, \"ngIf\"], [\"tabindex\", \"8\", \"type\", \"text\", \"required\", \"\", \"formControlName\", \"subscriberID\", \"numbersAndAlphabetsOnly\", \"\", \"placeholder\", \"Click on the Search Icon to Select the Member\", 1, \"form-control\", \"height27\", \"w-100\", 3, \"ngClass\"], [1, \"invalid-feedback\"], [1, \"form-control\"]],\n    template: function SubscriberInfoComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"p\", 2);\n        i0.ɵɵtext(3, \"1A. Insured\\u2019s I.D. Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, SubscriberInfoComponent_input_4_Template, 1, 3, \"input\", 3);\n        i0.ɵɵtemplate(5, SubscriberInfoComponent_div_5_Template, 2, 0, \"div\", 4);\n        i0.ɵɵtemplate(6, SubscriberInfoComponent_span_6_Template, 2, 1, \"span\", 5);\n        i0.ɵɵelementEnd()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.subscriberInfo);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isViewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.subscriberID == null ? null : ctx.f.subscriberID.invalid) && ctx.f.subscriberID.errors.required);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isViewClaim);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i3.NumbersAndAlphabetsOnlyDirective],\n    styles: [\".create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.search-icon-alignment[_ngcontent-%COMP%]{position:relative}.search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claims-labels[_ngcontent-%COMP%], .form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%], .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}\"]\n  });\n  return SubscriberInfoComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}