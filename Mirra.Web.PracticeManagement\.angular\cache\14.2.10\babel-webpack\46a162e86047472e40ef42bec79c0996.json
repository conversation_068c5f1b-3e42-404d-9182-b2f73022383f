{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, Validators } from '@angular/forms';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport { IcdViewHistoryComponent } from 'src/app/modals/icd-view-history/icd-view-history.component';\nimport { CptViewHistoryComponent } from 'src/app/modals/cpt-view-history/cpt-view-history.component';\nimport { LocalStorageKey, PriceCost } from 'src/app/shared/constant/constatnt';\nimport { debounceTime, distinctUntilChanged, first, Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/ClaimForm/get-all-cptcode.service\";\nimport * as i3 from \"src/app/services/ClaimForm/get-all-icdcode.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/services/cache-service/cache.service\";\nimport * as i6 from \"src/app/shared/services/subject.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@ng-select/ng-select\";\nimport * as i9 from \"@angular/material/tooltip\";\nimport * as i10 from \"../../../../../shared/directives/numbers-only.directive\";\nimport * as i11 from \"../../../../../shared/directives/number-only-twodecimal\";\n\nfunction ServiceLineComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 46);\n  }\n}\n\nfunction ServiceLineComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r89 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r89.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2, \" Searching ICD codes... \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \" Type 3 letters to search \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please map at least one ICD \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 46);\n  }\n}\n\nfunction ServiceLineComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r92 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r92.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \" Type 3 letters to search \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 46);\n  }\n}\n\nfunction ServiceLineComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r93 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r93.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \" Type 3 letters to search \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 46);\n  }\n}\n\nfunction ServiceLineComponent_ng_template_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r94 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r94.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \" Type 3 letters to search \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 46);\n  }\n}\n\nfunction ServiceLineComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r95 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r95.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_ng_template_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \" Type 3 letters to search \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 46);\n  }\n}\n\nfunction ServiceLineComponent_ng_template_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r96 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r96.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_ng_template_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \" Type 3 letters to search \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 46);\n  }\n}\n\nfunction ServiceLineComponent_ng_template_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r97 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r97.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_ng_template_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \" Type 3 letters to search \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r98 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r98.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r99 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r99.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_div_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r100 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r100.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r101 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r101.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_div_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_124_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_ng_template_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r102 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", item_r102.text, \" \");\n  }\n}\n\nfunction ServiceLineComponent_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ICD Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Diagnosis or Nature of illness or Injury should not match with other Diagnosis \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please maintain continuity while filling in the Diagnosis codes \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Invalid ICD code entered. Please provide a valid ICD code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_ng_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r119 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r119);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", item_r119.cpt, \" - \", item_r119.shortDescription, \" \");\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Modifier1 must be of 2 characters in length. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ServiceLineComponent_div_161_div_10_div_1_Template, 2, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const serviceData_r103 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceData_r103.get(\"m1\")) == null ? null : tmp_0_0.hasError(\"maxlength\"));\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Modifier2 must be of 2 characters in length. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ServiceLineComponent_div_161_div_13_div_1_Template, 2, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const serviceData_r103 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceData_r103.get(\"m2\")) == null ? null : tmp_0_0.hasError(\"maxlength\"));\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Modifier3 must be of 2 characters in length. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ServiceLineComponent_div_161_div_16_div_1_Template, 2, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const serviceData_r103 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceData_r103.get(\"m3\")) == null ? null : tmp_0_0.hasError(\"maxlength\"));\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Modifier4 must be of 2 characters in length. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ServiceLineComponent_div_161_div_19_div_1_Template, 2, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const serviceData_r103 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceData_r103.get(\"m4\")) == null ? null : tmp_0_0.hasError(\"maxlength\"));\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_24_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_24_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ServiceLineComponent_div_161_div_24_div_1_Template, 2, 0, \"div\", 80);\n    i0.ɵɵtemplate(2, ServiceLineComponent_div_161_div_24_div_2_Template, 2, 0, \"div\", 80);\n    i0.ɵɵtemplate(3, ServiceLineComponent_div_161_div_24_div_3_Template, 2, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const serviceData_r103 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceData_r103.get(\"d1\")) == null ? null : tmp_0_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = serviceData_r103.get(\"d1\")) == null ? null : tmp_1_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = serviceData_r103.get(\"d1\")) == null ? null : tmp_2_0.hasError(\"dublicate\"));\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ServiceLineComponent_div_161_div_27_div_1_Template, 2, 0, \"div\", 80);\n    i0.ɵɵtemplate(2, ServiceLineComponent_div_161_div_27_div_2_Template, 2, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const serviceData_r103 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceData_r103.get(\"d2\")) == null ? null : tmp_0_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = serviceData_r103.get(\"d2\")) == null ? null : tmp_1_0.hasError(\"dublicate\"));\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_30_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ServiceLineComponent_div_161_div_30_div_1_Template, 2, 0, \"div\", 80);\n    i0.ɵɵtemplate(2, ServiceLineComponent_div_161_div_30_div_2_Template, 2, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const serviceData_r103 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceData_r103.get(\"d3\")) == null ? null : tmp_0_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = serviceData_r103.get(\"d3\")) == null ? null : tmp_1_0.hasError(\"dublicate\"));\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_33_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ServiceLineComponent_div_161_div_33_div_1_Template, 2, 0, \"div\", 80);\n    i0.ɵɵtemplate(2, ServiceLineComponent_div_161_div_33_div_2_Template, 2, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const serviceData_r103 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceData_r103.get(\"d4\")) == null ? null : tmp_0_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = serviceData_r103.get(\"d4\")) == null ? null : tmp_1_0.hasError(\"dublicate\"));\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" The unit charges field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" The days and units field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" The field days and units must be between 1 and 99999999. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineComponent_div_161_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r143 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"i\", 82);\n    i0.ɵɵlistener(\"click\", function ServiceLineComponent_div_161_div_62_Template_i_click_1_listener() {\n      i0.ɵɵrestoreView(_r143);\n      const i_r104 = i0.ɵɵnextContext().index;\n      const ctx_r141 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r141.removeService(i_r104));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction ServiceLineComponent_div_161_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r145 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"div\", 54)(3, \"ng-select\", 55);\n    i0.ɵɵlistener(\"ngModelChange\", function ServiceLineComponent_div_161_Template_ng_select_ngModelChange_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r144 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r144.OnCPTCodeChange($event, i_r104));\n    });\n    i0.ɵɵtemplate(4, ServiceLineComponent_div_161_ng_option_4_Template, 2, 3, \"ng-option\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ServiceLineComponent_div_161_div_5_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 13)(7, \"div\", 57)(8, \"div\", 58)(9, \"input\", 59);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r146 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r146.changeforInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ServiceLineComponent_div_161_div_10_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 58)(12, \"input\", 60);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_12_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r147 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r147.changeforInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ServiceLineComponent_div_161_div_13_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 58)(15, \"input\", 61);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_15_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r148 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r148.changeforInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, ServiceLineComponent_div_161_div_16_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 58)(18, \"input\", 62);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_18_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r149 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r149.changeforInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ServiceLineComponent_div_161_div_19_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 13)(21, \"div\", 57)(22, \"div\", 63)(23, \"input\", 64);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_23_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r150 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r150.changeforInput($event));\n    })(\"keyup\", function ServiceLineComponent_div_161_Template_input_keyup_23_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r151 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r151.diagonisiPointer($event, \"d1\", i_r104));\n    })(\"blur\", function ServiceLineComponent_div_161_Template_input_blur_23_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r152 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r152.diagnoisiPointer($event, \"d1\", i_r104));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, ServiceLineComponent_div_161_div_24_Template, 4, 3, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 63)(26, \"input\", 65);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_26_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r153 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r153.changeforInput($event));\n    })(\"keyup\", function ServiceLineComponent_div_161_Template_input_keyup_26_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r154 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r154.diagonisiPointer($event, \"d2\", i_r104));\n    })(\"blur\", function ServiceLineComponent_div_161_Template_input_blur_26_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r155 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r155.diagnoisiPointer($event, \"d2\", i_r104));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, ServiceLineComponent_div_161_div_27_Template, 3, 2, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 63)(29, \"input\", 66);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_29_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r156 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r156.changeforInput($event));\n    })(\"keyup\", function ServiceLineComponent_div_161_Template_input_keyup_29_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r157 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r157.diagonisiPointer($event, \"d3\", i_r104));\n    })(\"blur\", function ServiceLineComponent_div_161_Template_input_blur_29_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r158 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r158.diagnoisiPointer($event, \"d3\", i_r104));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(30, ServiceLineComponent_div_161_div_30_Template, 3, 2, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 63)(32, \"input\", 67);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_32_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r159 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r159.changeforInput($event));\n    })(\"keyup\", function ServiceLineComponent_div_161_Template_input_keyup_32_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r160 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r160.diagonisiPointer($event, \"d4\", i_r104));\n    })(\"blur\", function ServiceLineComponent_div_161_Template_input_blur_32_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r161 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r161.diagnoisiPointer($event, \"d4\", i_r104));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, ServiceLineComponent_div_161_div_33_Template, 3, 2, \"div\", 20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 35)(35, \"input\", 68);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_35_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r162 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r162.changeforInput($event));\n    })(\"keyup\", function ServiceLineComponent_div_161_Template_input_keyup_35_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r163 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r163.charges(i_r104));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, ServiceLineComponent_div_161_div_36_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 36)(38, \"input\", 69);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_38_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r164 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r164.changeforInput($event));\n    })(\"keyup\", function ServiceLineComponent_div_161_Template_input_keyup_38_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const i_r104 = restoredCtx.index;\n      const ctx_r165 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r165.charges(i_r104));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, ServiceLineComponent_div_161_div_39_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(40, ServiceLineComponent_div_161_div_40_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 36);\n    i0.ɵɵelement(42, \"input\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 36)(44, \"div\", 5)(45, \"input\", 71);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_45_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r166 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r166.changeforInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"label\", 72);\n    i0.ɵɵtext(47, \"Yes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 5)(49, \"input\", 73);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_49_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r167 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r167.changeforInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"label\", 74);\n    i0.ɵɵtext(51, \"No\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(52, \"div\", 75)(53, \"div\")(54, \"div\", 5)(55, \"input\", 76);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_55_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r168 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r168.changeforInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"label\", 77);\n    i0.ɵɵtext(57, \"Yes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 5)(59, \"input\", 78);\n    i0.ɵɵlistener(\"change\", function ServiceLineComponent_div_161_Template_input_change_59_listener($event) {\n      i0.ɵɵrestoreView(_r145);\n      const ctx_r169 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r169.changeforInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"label\", 79);\n    i0.ɵɵtext(61, \"No\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(62, ServiceLineComponent_div_161_div_62_Template, 2, 0, \"div\", 80);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const serviceData_r103 = ctx.$implicit;\n    const i_r104 = ctx.index;\n    const ctx_r88 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    let tmp_13_0;\n    let tmp_14_0;\n    let tmp_15_0;\n    let tmp_16_0;\n    let tmp_17_0;\n    let tmp_18_0;\n    let tmp_19_0;\n    let tmp_20_0;\n    let tmp_21_0;\n    let tmp_22_0;\n    let tmp_23_0;\n    let tmp_24_0;\n    let tmp_25_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r104);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(28, _c0, ((tmp_2_0 = serviceData_r103.get(\"cptCode\")) == null ? null : tmp_2_0.invalid) && (serviceData_r103.get(\"cptCode\").dirty || serviceData_r103.get(\"cptCode\").touched) && (((tmp_2_0 = serviceData_r103.get(\"cptCode\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors.required) || ((tmp_2_0 = serviceData_r103.get(\"cptCode\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors.invalidCptlength))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r88.cptCodes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = serviceData_r103.get(\"cptCode\")) == null ? null : tmp_4_0.invalid) && serviceData_r103.get(\"cptCode\").errors.required);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(30, _c0, ((tmp_5_0 = serviceData_r103.get(\"m1\")) == null ? null : tmp_5_0.invalid) && (((tmp_5_0 = serviceData_r103.get(\"m1\")) == null ? null : tmp_5_0.dirty) || ((tmp_5_0 = serviceData_r103.get(\"m1\")) == null ? null : tmp_5_0.touched))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = serviceData_r103.get(\"m1\")) == null ? null : tmp_6_0.invalid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(32, _c0, ((tmp_7_0 = serviceData_r103.get(\"m2\")) == null ? null : tmp_7_0.invalid) && (((tmp_7_0 = serviceData_r103.get(\"m2\")) == null ? null : tmp_7_0.dirty) || ((tmp_7_0 = serviceData_r103.get(\"m2\")) == null ? null : tmp_7_0.touched))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = serviceData_r103.get(\"m2\")) == null ? null : tmp_8_0.invalid) && (((tmp_8_0 = serviceData_r103.get(\"m2\")) == null ? null : tmp_8_0.dirty) || ((tmp_8_0 = serviceData_r103.get(\"m2\")) == null ? null : tmp_8_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(34, _c0, ((tmp_9_0 = serviceData_r103.get(\"m3\")) == null ? null : tmp_9_0.invalid) && (((tmp_9_0 = serviceData_r103.get(\"m3\")) == null ? null : tmp_9_0.dirty) || ((tmp_9_0 = serviceData_r103.get(\"m3\")) == null ? null : tmp_9_0.touched))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = serviceData_r103.get(\"m3\")) == null ? null : tmp_10_0.invalid) && (((tmp_10_0 = serviceData_r103.get(\"m3\")) == null ? null : tmp_10_0.dirty) || ((tmp_10_0 = serviceData_r103.get(\"m3\")) == null ? null : tmp_10_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(36, _c0, ((tmp_11_0 = serviceData_r103.get(\"m4\")) == null ? null : tmp_11_0.invalid) && (((tmp_11_0 = serviceData_r103.get(\"m4\")) == null ? null : tmp_11_0.dirty) || ((tmp_11_0 = serviceData_r103.get(\"m4\")) == null ? null : tmp_11_0.touched))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = serviceData_r103.get(\"m4\")) == null ? null : tmp_12_0.invalid) && (((tmp_12_0 = serviceData_r103.get(\"m4\")) == null ? null : tmp_12_0.dirty) || ((tmp_12_0 = serviceData_r103.get(\"m4\")) == null ? null : tmp_12_0.touched)));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(38, _c0, ((tmp_13_0 = serviceData_r103.get(\"d1\")) == null ? null : tmp_13_0.invalid) && (((tmp_13_0 = serviceData_r103.get(\"d1\")) == null ? null : tmp_13_0.dirty) || ((tmp_13_0 = serviceData_r103.get(\"d1\")) == null ? null : tmp_13_0.touched))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = serviceData_r103.get(\"d1\")) == null ? null : tmp_14_0.invalid) && (((tmp_14_0 = serviceData_r103.get(\"d1\")) == null ? null : tmp_14_0.dirty) || ((tmp_14_0 = serviceData_r103.get(\"d1\")) == null ? null : tmp_14_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(40, _c0, ((tmp_15_0 = serviceData_r103.get(\"d2\")) == null ? null : tmp_15_0.invalid) && (((tmp_15_0 = serviceData_r103.get(\"d2\")) == null ? null : tmp_15_0.dirty) || ((tmp_15_0 = serviceData_r103.get(\"d2\")) == null ? null : tmp_15_0.touched))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = serviceData_r103.get(\"d2\")) == null ? null : tmp_16_0.invalid) && (((tmp_16_0 = serviceData_r103.get(\"d2\")) == null ? null : tmp_16_0.dirty) || ((tmp_16_0 = serviceData_r103.get(\"d2\")) == null ? null : tmp_16_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(42, _c0, ((tmp_17_0 = serviceData_r103.get(\"d3\")) == null ? null : tmp_17_0.invalid) && (((tmp_17_0 = serviceData_r103.get(\"d3\")) == null ? null : tmp_17_0.dirty) || ((tmp_17_0 = serviceData_r103.get(\"d3\")) == null ? null : tmp_17_0.touched))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = serviceData_r103.get(\"d3\")) == null ? null : tmp_18_0.invalid) && (((tmp_18_0 = serviceData_r103.get(\"d3\")) == null ? null : tmp_18_0.dirty) || ((tmp_18_0 = serviceData_r103.get(\"d3\")) == null ? null : tmp_18_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(44, _c0, ((tmp_19_0 = serviceData_r103.get(\"d4\")) == null ? null : tmp_19_0.invalid) && (((tmp_19_0 = serviceData_r103.get(\"d4\")) == null ? null : tmp_19_0.dirty) || ((tmp_19_0 = serviceData_r103.get(\"d4\")) == null ? null : tmp_19_0.touched))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = serviceData_r103.get(\"d4\")) == null ? null : tmp_20_0.invalid) && (((tmp_20_0 = serviceData_r103.get(\"d4\")) == null ? null : tmp_20_0.dirty) || ((tmp_20_0 = serviceData_r103.get(\"d4\")) == null ? null : tmp_20_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(46, _c0, ((tmp_21_0 = serviceData_r103.get(\"charges\")) == null ? null : tmp_21_0.invalid) && (((tmp_21_0 = serviceData_r103.get(\"charges\")) == null ? null : tmp_21_0.dirty) || ((tmp_21_0 = serviceData_r103.get(\"charges\")) == null ? null : tmp_21_0.touched)) && ((tmp_21_0 = serviceData_r103.get(\"charges\")) == null ? null : tmp_21_0.errors)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_22_0 = serviceData_r103.get(\"charges\")) == null ? null : tmp_22_0.invalid) && serviceData_r103.get(\"charges\").errors.required);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(48, _c0, ((tmp_23_0 = serviceData_r103.get(\"daysunit\")) == null ? null : tmp_23_0.invalid) && (((tmp_23_0 = serviceData_r103.get(\"daysunit\")) == null ? null : tmp_23_0.dirty) || ((tmp_23_0 = serviceData_r103.get(\"daysunit\")) == null ? null : tmp_23_0.touched)) && ((tmp_23_0 = serviceData_r103.get(\"daysunit\")) == null ? null : tmp_23_0.errors)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_24_0 = serviceData_r103.get(\"daysunit\")) == null ? null : tmp_24_0.invalid) && serviceData_r103.get(\"daysunit\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_25_0 = serviceData_r103.get(\"daysunit\")) == null ? null : tmp_25_0.invalid) && (serviceData_r103.get(\"daysunit\").errors.max || serviceData_r103.get(\"daysunit\").errors.min));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", true);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngIf\", ctx_r88.serviceLine.length > 1);\n  }\n}\n\nexport let ServiceLineComponent = /*#__PURE__*/(() => {\n  class ServiceLineComponent {\n    constructor(serviceLineform, getAllCPTCodeService, ICDService, dialog, cacheService, subjectServce) {\n      this.serviceLineform = serviceLineform;\n      this.getAllCPTCodeService = getAllCPTCodeService;\n      this.ICDService = ICDService;\n      this.dialog = dialog;\n      this.cacheService = cacheService;\n      this.subjectServce = subjectServce;\n      this.serviceDesc1 = '';\n      this.serviceDesc2 = '';\n      this.serviceDesc3 = '';\n      this.serviceDesc4 = '';\n      this.serviceDesc5 = '';\n      this.serviceDesc6 = '';\n      this.minimumcharacterTohitAPI = 2;\n      this.selectedServicelines = new EventEmitter();\n      this.setPreview = new EventEmitter();\n      this.setServiceLine = new EventEmitter();\n      this.diagonsisPointerLength = 4;\n      this.isCPTICDLoad = false;\n      this.allCPTCharges = [];\n      this.newSelectedICDsFromICDViewHistory = [];\n      this.emptyFormControlsIndex = [];\n      this.viewHistoryICDList = [];\n      this.viewHistoryCPTList = [];\n      this.icdSearch = new Subject();\n      this.destroy$ = new Subject();\n      this.selectedICDCodes = [];\n      this.createForm();\n    }\n\n    ngOnInit() {\n      this.cacheServiceRefresh();\n      this.icdSearch.pipe(debounceTime(800), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(searchTerm => {\n        this.getICDBySearch(searchTerm);\n      });\n      this.payerItem = this.claimFormData.payerItem;\n      this.selectedMember = this.claimFormData.profileMember;\n\n      if (this.claimFormData.serviceLineInfo) {\n        this.servicelineData = null;\n        this.servicelineData = this.claimFormData.serviceLineInfo;\n        this.patchValue();\n      } else {\n        this.addServiceLine();\n      }\n\n      this.controlDisable(0);\n      this.fetchchAllCPTCharges();\n    }\n\n    get serviceLine() {\n      return this.servicelineInfo.get('serviceLines');\n    }\n\n    addServiceLine() {\n      let charges = this.payerItem?.payerId === \"59354\" || this.payerItem?.payerId === \"20133\" || this.payerItem?.payerId === \"41212\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n      const empGroup = this.serviceLineform.group({\n        cptCode: new FormControl(null, [Validators.required]),\n        desc: new FormControl(''),\n        m1: new FormControl('', [Validators.maxLength(2)]),\n        m2: new FormControl('', [Validators.maxLength(2)]),\n        m3: new FormControl('', [Validators.maxLength(2)]),\n        m4: new FormControl('', [Validators.maxLength(2)]),\n        d1: new FormControl('', [Validators.required]),\n        d2: new FormControl(''),\n        d3: new FormControl(''),\n        d4: new FormControl(''),\n        charges: new FormControl(charges, [Validators.required]),\n        daysunit: new FormControl('1', Validators.required),\n        totalcharges: new FormControl(charges),\n        ePSDT: new FormControl(null),\n        eMG: new FormControl(null),\n        proceduceC: new FormControl(''),\n        proceduceCount: new FormControl(''),\n        ndcUnitPrice: new FormControl(''),\n        lineNote: new FormControl(''),\n        ndcQtyQual: new FormControl(''),\n        anesStart: new FormControl(''),\n        ndcQty: new FormControl(''),\n        anesStop1: new FormControl(''),\n        anesStop2: new FormControl(''),\n        anesStop3: new FormControl(''),\n        ndcQual: new FormControl(''),\n        ndcCode: new FormControl('')\n      });\n      empGroup.controls['totalcharges'].disable();\n\n      for (let diagnosisControl = 1; diagnosisControl <= this.diagonsisPointerLength; diagnosisControl++) {\n        empGroup.controls['d' + diagnosisControl].disable();\n      }\n\n      this.serviceLine.push(empGroup);\n    }\n\n    createForm() {\n      this.servicelineInfo = this.serviceLineform.group({\n        icdType: new FormControl({\n          value: 'ICD10',\n          disabled: true\n        }),\n        icdVersion: new FormControl('ICD10'),\n        icdCode1: new FormControl(null, [Validators.required]),\n        icdCode2: new FormControl(null),\n        icdCode3: new FormControl(null),\n        icdCode4: new FormControl(null),\n        icdCode5: new FormControl(null),\n        icdCode6: new FormControl(null),\n        icdCode7: new FormControl(null),\n        icdCode8: new FormControl(null),\n        icdCode9: new FormControl(null),\n        icdCode10: new FormControl(null),\n        icdCode11: new FormControl(null),\n        icdCode12: new FormControl(null),\n        serviceLines: this.serviceLineform.array([])\n      });\n      return this.servicelineInfo;\n    }\n\n    get f() {\n      return this.servicelineInfo.controls;\n    }\n\n    removeService(i) {\n      this.serviceLine.removeAt(i);\n    }\n\n    getICDBySearch(searchTerm) {\n      if (!!searchTerm && searchTerm.length > 3) {\n        let request = {\n          searchString: searchTerm,\n          dos: this.claimFormData.profileMember.dOSFrom,\n          icdVersion: this.servicelineInfo.controls.icdType.value\n        };\n        this.ICDService.fetchICDBySearch(request).subscribe(icdCodes => {\n          this.icdCodes = icdCodes || [];\n        });\n      }\n    }\n\n    icdViewHistory() {\n      let request = {\n        subscriberId: this.claimFormData.memberResult.subscriberID,\n        dos: this.claimFormData.profileMember.dOSFrom,\n        icdVersion: this.servicelineInfo.controls.icdType.value\n      };\n      this.ICDService.getICDViewHistory(request).subscribe(resp => {\n        let icdList = [];\n        let icdCodesNonHistoryCount = 0;\n\n        if (resp.statusCode == 200 && (resp.content || []).length > 0) {\n          icdList = resp.content;\n          icdList = icdList.map(icd => ({ ...icd,\n            isSelected: false\n          }));\n\n          for (let i = 1; i <= 12; i++) {\n            let selectedICD = this.servicelineInfo.controls['icdCode' + i].value;\n\n            if (!!selectedICD) {\n              icdList.filter(icdItem => icdItem.icd === selectedICD).forEach(icd => {\n                icd.isSelected = true;\n              });\n\n              if (icdList.filter(icdItem => icdItem.icd === selectedICD).length == 0) {\n                icdCodesNonHistoryCount = icdCodesNonHistoryCount + 1;\n              }\n            } else {\n              icdCodesNonHistoryCount = icdCodesNonHistoryCount;\n            }\n          }\n\n          this.viewHistoryICDList = icdList;\n          this.icdDialogOpen(icdCodesNonHistoryCount);\n        } else {\n          this.icdDialogOpen(0);\n        }\n      });\n    }\n\n    icdDialogOpen(icdCodesNonHistoryCount) {\n      let dialogRef = this.dialog.open(IcdViewHistoryComponent, {\n        height: this.viewHistoryICDList.length > 0 ? '650px' : '200px',\n        width: this.viewHistoryICDList.length > 0 ? '1100px' : '800px',\n        autoFocus: false,\n        restoreFocus: false,\n        maxHeight: '90vh',\n        panelClass: 'custom-dialog-containers',\n        data: {\n          icdVersion: this.servicelineInfo.controls.icdType.value,\n          icdCodesNonHistoryCount: icdCodesNonHistoryCount,\n          icdList: this.viewHistoryICDList\n        }\n      });\n      dialogRef.afterClosed().subscribe(data => {\n        if (!!data && (data.selectedICDs || []).length > 0) {\n          this.pushICDCodesFromICDViewHistory(data.selectedICDs);\n          this.clearNonMatchedICdsFromFormControls(data.selectedICDs);\n        } else if (!!data && (data.selectedICDs || []).length == 0) {\n          // this.patchAllICDsEmpty();\n          this.clearNonMatchedICdsFromFormControls(data.selectedICDs);\n        }\n      });\n    }\n\n    patchAllICDsEmpty() {\n      for (let i = 1; i <= 12; i++) {\n        this.servicelineInfo.controls['icdCode' + i].patchValue(null);\n      }\n    }\n\n    pushICDCodesFromICDViewHistory(viewHistoryICDCodes) {\n      const newIcdCodes = viewHistoryICDCodes.map(icdItem => {\n        return {\n          text: icdItem.displayCode_v1,\n          value: icdItem.icd,\n          addDate: icdItem.add_date,\n          termDate: icdItem.term_date\n        };\n      });\n\n      if (newIcdCodes.length > 0) {\n        this.icdCodes = newIcdCodes;\n        newIcdCodes.forEach(icdCode => {\n          this.selectedICDCodesPush(icdCode);\n        });\n      }\n    }\n\n    clearNonMatchedICdsFromFormControls(selectedICDS) {\n      let existingICDs = [];\n      this.emptyFormControlsIndex = [];\n\n      for (let i = 1; i <= 12; i++) {\n        let existingICD = this.servicelineInfo.controls['icdCode' + i].value;\n        const isMatchedICDFound = selectedICDS.find(icdItem => icdItem.icd === existingICD);\n        const isVieWHistoryyICDItem = this.viewHistoryICDList.find(icd => icd.icd === existingICD);\n\n        if (!!isMatchedICDFound) {\n          existingICDs.push(isMatchedICDFound.icd);\n        } else if (!!isVieWHistoryyICDItem && !isMatchedICDFound) {\n          this.servicelineInfo.controls['icdCode' + i].patchValue(null);\n          this.servicelineInfo.controls['icdCode' + i];\n          this.emptyFormControlsIndex.push(i);\n        } else if (isMatchedICDFound == null && isVieWHistoryyICDItem == null && !!existingICD) {\n          //  this.emptyFormControlsIndex.push(i);\n          existingICDs.push(existingICD);\n        } else {\n          this.emptyFormControlsIndex.push(i);\n        }\n      }\n\n      this.newSelectedICDsFromICDViewHistory = selectedICDS.filter(icdItem => !existingICDs.some(icd => icdItem.icd === icd));\n      this.patchSelectedICDInfo();\n    }\n\n    patchSelectedICDInfo() {\n      for (var i = 0; i < this.newSelectedICDsFromICDViewHistory.length; i++) {\n        if (this.newSelectedICDsFromICDViewHistory.length <= this.emptyFormControlsIndex.length) {\n          this.servicelineInfo.controls['icdCode' + this.emptyFormControlsIndex[i]].patchValue(this.newSelectedICDsFromICDViewHistory[i].icd);\n          this.servicelineInfo.controls['icdCode' + this.emptyFormControlsIndex[i]].enable();\n\n          if (this.emptyFormControlsIndex.length < 12) {\n            this.servicelineInfo.controls['icdCode' + this.emptyFormControlsIndex[i + 1]].enable();\n          }\n        }\n      }\n    }\n\n    cptViewHistory() {\n      let request = {\n        subscribeID: this.claimFormData.memberResult.subscriberID,\n        dos: new Date(this.claimFormData.profileMember.dOSFrom)\n      };\n      this.getAllCPTCodeService.getCPTViewHistory(request).subscribe(resp => {\n        let cptList = [];\n\n        if (resp.statusCode == 200 && (resp.content || []).length > 0) {\n          cptList = resp.content;\n          cptList = cptList.map(cpt => ({ ...cpt,\n            isSelected: false\n          }));\n        }\n\n        const existingServiceLines = this.serviceLine.value;\n        existingServiceLines?.forEach(serviceLine => {\n          cptList.filter(cptItem => cptItem.cpt === serviceLine.cptCode).forEach(cpt => {\n            cpt.isSelected = true;\n          });\n        });\n        this.viewHistoryCPTList = cptList;\n        let dialogRef = this.dialog.open(CptViewHistoryComponent, {\n          height: '650px',\n          width: '1100px',\n          autoFocus: false,\n          restoreFocus: false,\n          maxHeight: '90vh',\n          panelClass: 'custom-dialog-containers',\n          data: {\n            cptList: cptList\n          }\n        });\n        dialogRef.afterClosed().subscribe(data => {\n          if (!!data && (data.selectedCPTCodes || []).length > 0) {\n            this.mapServiceLinesSelecteCPCodes(data.selectedCPTCodes);\n          } // if selected cpts is zero then index 0 in existing service line cpt should be empty and remove remaining all.\n          else if (!!data && (data.selectedCPTCodes || []).length == 0) {\n            if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\n              const servicelineMatchedFromViewHistoryCPTCodes = this.serviceLine.value.filter(serviceLineItem => this.viewHistoryCPTList.some(selecteItm => serviceLineItem.cptCode === selecteItm.cpt));\n\n              if (!!servicelineMatchedFromViewHistoryCPTCodes && servicelineMatchedFromViewHistoryCPTCodes.length > 0) {\n                servicelineMatchedFromViewHistoryCPTCodes.forEach(item => {\n                  const index = this.serviceLine.controls.findIndex(control => control.value.cptCode === item.cptCode);\n                  let charges = this.payerItem?.payerId === \"59354\" || this.payerItem?.payerId === \"20133\" || this.payerItem?.payerId === \"41212\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n                  let cptCharge = this.allCPTCharges.filter(item => item.code == item.cptCode)[0]?.charge;\n\n                  if (!!cptCharge) {\n                    charges = cptCharge;\n                  }\n\n                  if (index == 0) {\n                    this.serviceLine.at(index).patchValue({\n                      cptCode: null,\n                      charges: Number.parseFloat(charges).toFixed(2)\n                    });\n                  } else {\n                    this.serviceLine.removeAt(index);\n                  }\n                });\n              } // this.serviceLine.controls[0].patchValue({\n              //   cptCode: null,\n              //   dayUnitChanges: '0.00'\n              // });\n              // this.isShowDelete = false;\n              // while (this.serviceLine.controls.length > 1) {\n              //   this.serviceLine.removeAt(this.serviceLine.controls.length - 1);  // Remove last control\n              // }\n\n            }\n          }\n        });\n      });\n    }\n\n    mapServiceLinesSelecteCPCodes(selectedCPTCodes) {\n      let getExistingFirstServiceLine;\n\n      if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\n        getExistingFirstServiceLine = this.serviceLine.controls[0]; // copy this values to auto fill to new adding service\n        /// filter  Unused  servicelines based on selected CPT and remove it from servicelines controls.\n\n        if (selectedCPTCodes.length > 0 && this.serviceLine.controls.length > 0) {\n          const servicelineNonMatchedFromSelectedCPTCodes = this.serviceLine.value.filter(serviceLineItem => !selectedCPTCodes.some(selecteItm => serviceLineItem.cptCode === selecteItm.cpt)); // Remove unMatched values from serviceLines Controls.      \n\n          servicelineNonMatchedFromSelectedCPTCodes.forEach(item => {\n            // Find the index of the control where the 'id' matches\n            if (!!item.cptCode) {\n              const isViewHistoryItem = this.viewHistoryCPTList.find(cpt => cpt.cpt == item.cptCode);\n              const index = this.serviceLine.controls.findIndex(control => control.value.cptCode === item.cptCode);\n              let charges = this.payerItem?.payerId === \"59354\" || this.payerItem?.payerId === \"20133\" || this.payerItem?.payerId === \"41212\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n              let cptCharge = this.allCPTCharges.filter(item => item.code == item.cptCode)[0]?.charge;\n\n              if (!!cptCharge) {\n                charges = cptCharge;\n              } // Remove the control if found\n\n\n              if (index !== -1 && !!isViewHistoryItem) {\n                if (index == 0) {\n                  this.serviceLine.at(index).patchValue({\n                    cptCode: null,\n                    charges: charges\n                  });\n                } else {\n                  this.serviceLine.removeAt(index);\n                }\n              }\n            }\n          });\n        }\n\n        let existingServiceLines = this.serviceLine.value; // get selected cptCodes except from existingservice lines.\n\n        selectedCPTCodes = selectedCPTCodes.filter(selectedItem => !existingServiceLines.some(existingItem => selectedItem.cpt === existingItem.cptCode)); // if existing service line is one with dates , selected cpt is one if both are different \n\n        if (selectedCPTCodes.length == 1 && existingServiceLines.length == 0) {\n          this.serviceLine.controls.push(getExistingFirstServiceLine);\n          this.serviceLine.controls[0].patchValue({\n            cptCode: selectedCPTCodes[0].cpt,\n            desc: selectedCPTCodes[0].shortDescription\n          });\n          selectedCPTCodes = [];\n        } else if (selectedCPTCodes.length == 1 && existingServiceLines.length == 1) {\n          // if existing cpt is null update existing row of cpt.\n          if (existingServiceLines.length == 1 && !existingServiceLines[0].cptCode) {\n            this.serviceLine.controls[0].patchValue({\n              cptCode: selectedCPTCodes[0].cpt,\n              desc: selectedCPTCodes[0].shortDescription\n            });\n            this.OnCPTCodeChange(selectedCPTCodes[0].cpt, 0);\n            selectedCPTCodes.shift(); // 0 index remove\n          } else {}\n        } //else{\n        // for new selected cpts addding to service lines.\n\n\n        selectedCPTCodes.forEach(cptCodeDetails => {\n          this.patchSelecteCPTCodesFromHistory(cptCodeDetails, getExistingFirstServiceLine);\n        }); //}\n      }\n    }\n\n    patchSelecteCPTCodesFromHistory(cptCodeDetails, getExistingFirstServiceLine) {\n      let daysUnits = this.serviceLine.at(0).get('daysunit').value;\n\n      if (!!cptCodeDetails.cpt) {\n        // existing service line with empty cptCode patch values\n        const index = this.serviceLine.controls.findIndex(control => control.value.cptCode === null);\n\n        if (index !== -1) {\n          this.serviceLine.at(index).patchValue({\n            cptCode: cptCodeDetails.cpt,\n            desc: cptCodeDetails.shortDescription\n          });\n          this.OnCPTCodeChange(cptCodeDetails.cpt, index);\n        } else {\n          let charges = this.payerItem?.payerId === \"59354\" || this.payerItem?.payerId === \"20133\" || this.payerItem?.payerId === \"41212\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n          let cptCharge = this.allCPTCharges.filter(item => item.code == cptCodeDetails.cpt)[0]?.charge;\n\n          if (!!cptCharge) {\n            charges = cptCharge;\n          }\n\n          const empGroup = this.serviceLineform.group({\n            emg: new FormControl(null),\n            desc: new FormControl(cptCodeDetails.shortDescription),\n            cptCode: new FormControl(cptCodeDetails.cpt, [Validators.required]),\n            m1: new FormControl('', [Validators.maxLength(2)]),\n            m2: new FormControl('', [Validators.maxLength(2)]),\n            m3: new FormControl('', [Validators.maxLength(2)]),\n            m4: new FormControl('', [Validators.maxLength(2)]),\n            d1: new FormControl('1', [Validators.required]),\n            d2: new FormControl(''),\n            d3: new FormControl(''),\n            d4: new FormControl(''),\n            charges: new FormControl(Number(charges).toFixed(2), Validators.required),\n            daysunit: new FormControl('1', [Validators.required, Validators.max(99999999), Validators.min(1)]),\n            totalcharges: new FormControl((Number(charges) * daysUnits).toFixed(2)),\n            ePSDT: new FormControl(null),\n            eMG: new FormControl(null),\n            proceduceC: new FormControl(''),\n            proceduceCount: new FormControl(''),\n            ndcUnitPrice: new FormControl(''),\n            lineNote: new FormControl(''),\n            ndcQtyQual: new FormControl(''),\n            anesStart: new FormControl(''),\n            ndcQty: new FormControl(''),\n            anesStop1: new FormControl(''),\n            anesStop2: new FormControl(''),\n            anesStop3: new FormControl(''),\n            ndcQual: new FormControl(''),\n            ndcCode: new FormControl('')\n          });\n\n          for (let diagnosis = 1; diagnosis <= this.diagonsisPointerLength; diagnosis++) {\n            empGroup.get('d' + diagnosis).enable();\n          }\n\n          this.serviceLine.push(empGroup);\n        }\n      } else {}\n    }\n\n    OnCPTCodeChange(codeValue, index) {\n      let daysUnits = !!this.serviceLine.at(index).get('daysunit').value ? this.serviceLine.at(index).get('daysunit').value : 1;\n      let cptCharge = this.payerItem?.payerId === \"59354\" || this.payerItem?.payerId === \"20133\" || this.payerItem?.payerId === \"41212\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n\n      if (codeValue) {\n        let cptChargeFromAllCpt = this.allCPTCharges.filter(item => item.code == codeValue)[0]?.charge;\n\n        if (!!cptChargeFromAllCpt) {\n          cptCharge = cptChargeFromAllCpt;\n        }\n\n        let totalCharges = Number(cptCharge) * Number(daysUnits);\n        this.serviceLine.at(index).patchValue({\n          charges: Number.parseFloat(cptCharge).toFixed(2),\n          totalcharges: totalCharges.toFixed(2),\n          daysunit: daysUnits\n        });\n        this.serviceLine.at(index).patchValue({\n          d1: '1'\n        });\n\n        for (let diagnosis = 1; diagnosis <= this.diagonsisPointerLength; diagnosis++) {\n          this.serviceLine.at(index).get('d' + diagnosis).enable();\n        }\n      } else {\n        this.serviceLine.at(index).patchValue({\n          charges: Number.parseFloat(cptCharge).toFixed(2),\n          totalcharges: (Number.parseFloat(cptCharge) * daysUnits).toFixed(2)\n        });\n        this.serviceLine.at(index).patchValue({\n          d1: '',\n          d2: '',\n          d3: '',\n          d4: ''\n        });\n        this.serviceLine.at(index).get('cptCode').setErrors({\n          required: true\n        });\n\n        for (let diagnosis = 1; diagnosis <= this.diagonsisPointerLength; diagnosis++) {\n          this.serviceLine.at(index).get('d' + diagnosis).disable();\n        }\n      }\n\n      this.setPreviewValues();\n    }\n\n    fetchchAllCPTCharges() {\n      if (this.cacheService.localStorageGetItem(LocalStorageKey.allCPTCharges)) {\n        this.allCPTCharges = this.cacheService.localStorageGetItem(LocalStorageKey.allCPTCharges);\n        return;\n      }\n\n      this.getAllCPTCodeService.fetchchAllCPTCharges().pipe(first()).subscribe(response => {\n        this.allCPTCharges = response.content;\n      });\n    }\n\n    enableIcdControl(icdCode, index) {\n      if (icdCode) {\n        this.selectedICDCodesPush(icdCode);\n        this.ICDDropdownTextDiagonsisCheck(false);\n        let selectedValue = [];\n\n        for (let i = 1; i < 13; i++) {\n          if (index != i) if (this.servicelineInfo.controls['icdCode' + i].value) {\n            selectedValue.push(this.servicelineInfo.controls['icdCode' + i].value);\n          }\n\n          if (this.servicelineInfo.controls['icdCode' + i].value && i != 12) {\n            this.servicelineInfo.controls['icdCode' + (i + 1)].enable();\n          }\n        }\n\n        if (index > 1) {\n          if (selectedValue.find(t => t == this.servicelineInfo.controls['icdCode' + index].value)) this.servicelineInfo.controls['icdCode' + index].setErrors({\n            dublicate: true\n          });\n        }\n      } else {\n        for (let i = 1; i <= 12; i++) {\n          if (this.servicelineInfo.controls['icdCode' + i].value && i != 12) {\n            this.servicelineInfo.controls['icdCode' + (i + 1)].enable();\n          }\n        }\n\n        this.ICDDropdownTextDiagonsisCheck(true);\n\n        for (let j = 0; j <= this.serviceLine.length - 1; j++) {\n          for (let i = 1; i <= 4; i++) {\n            if (this.serviceLine.at(j).get('d' + i).value && Number.parseInt(this.serviceLine.at(j).get('d' + i).value) === index) {\n              this.serviceLine.at(j).get('d' + i).setErrors({\n                numberMax: true\n              });\n            }\n          }\n        }\n      }\n\n      this.setPreviewValues();\n    }\n\n    selectedICDCodesPush(icdCode) {\n      if (!!icdCode && !!icdCode.text) {\n        this.selectedICDCodes = this.selectedICDCodes.length > 0 ? [...this.selectedICDCodes, icdCode] : [icdCode];\n      }\n    }\n\n    setPreviewValues() {\n      this.selectedServicelines.emit(this.servicelineInfo.value);\n    }\n\n    changeforInput(e) {\n      this.setPreviewValues();\n    }\n\n    ICDDropdownTextDiagonsisCheck(isValid) {\n      let diagonisiPointerAllowedLength = 0;\n\n      for (let i = 1; i < 13; i++) {\n        if (this.servicelineInfo.controls['icdCode' + i].value) {\n          diagonisiPointerAllowedLength++;\n        }\n      }\n\n      for (let j = 0; j <= this.serviceLine.length - 1; j++) {\n        for (let i = 1; i <= 4; i++) {\n          if (this.serviceLine.at(j).get('d' + i).value && (Number.parseInt(this.serviceLine.at(j).get('d' + i).value) > diagonisiPointerAllowedLength || Number.parseInt(this.serviceLine.at(j).get('d' + i).value) === 0)) {\n            this.serviceLine.at(j).get('d' + i).setErrors({\n              numberMax: true\n            });\n          } else {\n            if (i === 1 && !this.serviceLine.at(j).get('d' + i).value) this.serviceLine.at(j).get('d' + i).setErrors({\n              required: true\n            });else this.serviceLine.at(j).get('d' + i).setErrors(null);\n          }\n        }\n      }\n\n      this.setPreviewValues();\n    }\n\n    diagonisiPointer(e, controlname, index) {\n      let ictLength = 0;\n      let textValue = Number.parseInt(e.srcElement.value);\n\n      for (let i = 1; i < 13; i++) {\n        if (this.servicelineInfo.controls['icdCode' + i].value) {\n          ictLength++;\n        }\n      } //hummana check \n\n\n      if (this.claimFormData?.payerItem.payerId === \"61102\" || this.claimFormData?.payerItem.payerId === \"61101\") {\n        if (isNaN(textValue)) {\n          this.serviceLine.at(index).get(controlname).setErrors(null);\n        } else if (textValue <= ictLength && textValue > 0) {\n          this.serviceLine.at(index).get(controlname).setErrors(null);\n          this.dublicateCheck(index, controlname);\n        } else {\n          this.serviceLine.at(index).get(controlname).setErrors({\n            numberMax: true\n          });\n        }\n      } else {\n        this.validateServiceLine();\n\n        for (let i = 1; i <= 4; i++) {\n          if (this.serviceLine.at(index).get('d' + i).value != '' && 'd' + i != controlname && this.serviceLine.at(index).get(controlname).value === this.serviceLine.at(index).get('d' + i).value && controlname != \"d1\") {\n            this.serviceLine.at(index).get(controlname).setErrors(null);\n          }\n        }\n\n        if (textValue <= ictLength && textValue > 0) {} else {\n          this.serviceLine.at(index).get(controlname).setErrors({\n            numberMax: true\n          });\n        }\n\n        this.dublicateCheck(index, controlname);\n      }\n    }\n\n    validateServiceLine() {\n      for (let i = 1; i < 12; i++) {\n        if (this.servicelineInfo.controls['icdCode' + i].value) {\n          this.servicelineInfo.controls['icdCode' + i].setErrors(null);\n        }\n      }\n\n      for (let i = 1; i < 12; i++) {\n        for (let h = 1; h <= 13 - 1; h++) {\n          if (i != h && this.servicelineInfo.controls['icdCode' + i].value && this.servicelineInfo.controls['icdCode' + i].value) {\n            if (this.servicelineInfo.controls['icdCode' + i].value === this.servicelineInfo.controls['icdCode' + h].value) {\n              this.servicelineInfo.controls['icdCode' + h].setErrors({\n                dublicate: true\n              });\n            }\n          }\n        }\n      }\n\n      for (let i = 1; i < 12; i++) {\n        if (this.servicelineInfo.controls['icdCode' + i].errors?.continued) {\n          this.servicelineInfo.controls['icdCode' + i].setErrors(null);\n        }\n      }\n\n      for (let i = 1; i < 12; i++) {\n        if (this.servicelineInfo.controls['icdCode' + i].value === null && this.servicelineInfo.controls['icdCode' + (i + 1)].value != null) {\n          this.servicelineInfo.controls['icdCode' + i].setErrors({\n            continued: true\n          });\n        }\n      }\n    }\n\n    controlDisable(index) {\n      this.servicelineInfo.controls['icdCode1'].enable();\n\n      for (let i = 2; i < 13; i++) {\n        this.servicelineInfo.controls['icdCode' + i].disable();\n      }\n\n      for (let i = 2; i < 13; i++) {\n        if (this.servicelineInfo.controls['icdCode' + (i - 1)].value) {\n          this.servicelineInfo.controls['icdCode' + i].enable();\n        }\n      }\n    }\n\n    dublicateCheck(index, controlName) {\n      for (let i = 1; i <= 4; i++) {\n        if (this.serviceLine.at(index).get('d' + i).value != '' && 'd' + i != controlName && this.serviceLine.at(index).get(controlName).value === this.serviceLine.at(index).get('d' + i).value) {\n          this.serviceLine.at(index).get(controlName).setErrors({\n            dublicate: true\n          });\n        }\n      }\n    }\n\n    diagnoisiPointer(e, controlName, index) {\n      if (e.srcElement.value === '' && controlName != \"d1\") {\n        this.serviceLine.at(index).get(controlName).setErrors(null);\n      }\n    }\n\n    charges(i) {\n      let days = this.serviceLine.at(i).get('daysunit').value;\n      let charges = this.serviceLine.at(i).get('charges').value;\n      this.serviceLine.at(i).patchValue({\n        totalcharges: (days * charges).toFixed(2)\n      });\n    }\n\n    isShow() {\n      this.servicelineData = this.servicelineInfo.value;\n      this.servicelineData.icdCodes = !!this.icdCodes ? JSON.parse(JSON.stringify(this.icdCodes)) : [];\n      this.validateServiceLine();\n      this.validateCPTCode();\n      this.servicelineData?.serviceLines?.forEach(ele => {\n        if (ele.desc === '') {\n          if (this.cptCodes) ele.desc = this.cptCodes.find(t => t.cpt === ele.cptCode)?.shortDescription;\n        }\n      });\n\n      for (let i = 0; i < this.serviceLine.length; i++) {\n        for (let j = 1; j < 5; j++) {\n          if (this.serviceLine.at(i).get('m' + j).value != '' && this.serviceLine.at(i).get('m' + j).value.length != 2) this.serviceLine.at(i).get('m' + j).setErrors({\n            maxlength: true\n          });\n        }\n      }\n\n      for (let i = 0; i < this.serviceLine.length; i++) {\n        for (let j = 1; j < 5; j++) {\n          if (j === 1 && this.serviceLine.at(i).get('d1').value === '') this.serviceLine.at(i).get('d1').setErrors({\n            required: true\n          });\n        }\n      }\n\n      if (this.servicelineInfo.invalid || this.serviceLine.invalid) {\n        submitValidateAllFields.validateAllFields(this.servicelineInfo);\n        return;\n      }\n\n      this.selectedICDSPushToCache();\n      this.setPreview.emit(this.servicelineInfo.value);\n    }\n\n    selectedICDSPushToCache() {\n      let UniqueICDCodes = this.selectedICDCodes.filter((v, i, a) => a.findIndex(t => t.text === v.text) === i);\n      this.cacheService.localStorageSetItem(LocalStorageKey.selectedICDCodesForCreateClaim, UniqueICDCodes);\n    }\n\n    patchValue() {\n      let controlEnableCount = 1;\n\n      for (let i = 1; i < 13; i++) {\n        if (this.servicelineData['icdCode' + i]) {\n          this.servicelineInfo.controls['icdCode' + i].enable();\n          controlEnableCount = i;\n          this.servicelineInfo.controls['icdCode' + i].patchValue(this.servicelineData['icdCode' + i]);\n        }\n      }\n\n      if (controlEnableCount != 12) this.servicelineInfo.controls['icdCode' + (controlEnableCount + 1)].enable();\n      this.servicelineData?.serviceLines?.forEach(ele => {\n        // if (this.serviceLine.length < 6) {\n        let amountCalculation = Number.parseFloat(ele.daysunit) * Number.parseFloat(ele.charges);\n        const empGroup = this.serviceLineform.group({\n          cptCode: this.serviceLine.length === 0 ? new FormControl(ele.cptCode, [Validators.required]) : new FormControl(ele.cptCode),\n          desc: new FormControl(ele.desc),\n          m1: new FormControl(ele.m1),\n          m2: new FormControl(ele.m2),\n          m3: new FormControl(ele.m3),\n          m4: new FormControl(ele.m4),\n          d1: new FormControl(ele.d1),\n          d2: new FormControl(ele.d2),\n          d3: new FormControl(ele.d3),\n          d4: new FormControl(ele.d4),\n          charges: new FormControl(Number.parseFloat(ele.charges).toFixed(2), [Validators.required]),\n          daysunit: new FormControl(ele.daysunit, [Validators.required]),\n          totalcharges: new FormControl(amountCalculation.toFixed(2)),\n          ePSDT: new FormControl(ele.ePSDT),\n          eMG: new FormControl(ele.eMG),\n          proceduceC: new FormControl(''),\n          proceduceCount: new FormControl(''),\n          ndcUnitPrice: new FormControl(''),\n          lineNote: new FormControl(''),\n          ndcQtyQual: new FormControl(''),\n          anesStart: new FormControl(''),\n          ndcQty: new FormControl(''),\n          anesStop1: new FormControl(''),\n          anesStop2: new FormControl(''),\n          anesStop3: new FormControl(''),\n          ndcQual: new FormControl(''),\n          ndcCode: new FormControl('')\n        });\n        empGroup.controls['totalcharges'].disable();\n        this.serviceLine.push(empGroup); // }\n      }); //this.fetchCPT();\n    }\n\n    validateCPTCode() {\n      for (let i = 0; i < this.serviceLine.length; i++) {\n        for (let j = 1; j < 5; j++) {\n          if (!this.serviceLine.at(i).get('d' + j).errors?.numberMax) this.serviceLine.at(i).get('d' + j).setErrors(null);\n        }\n      }\n\n      for (let i = 0; i < this.serviceLine.length; i++) {\n        for (let h = 1; h <= this.serviceLine.length - 1; h++) {\n          if (this.serviceLine.at(i).get('cptCode').value === this.serviceLine.at(h).get('cptCode').value) {\n            for (let j = 1; j <= 4; j++) {\n              for (let k = 1; k <= 4; k++) {\n                if (i != h && this.serviceLine.at(i).get('d' + j).value && this.serviceLine.at(h).get('d' + k).value && Number.parseInt(this.serviceLine.at(i).get('d' + j).value) === Number.parseInt(this.serviceLine.at(h).get('d' + k).value) && this.serviceLine.at(i).get('cptCode').value === this.serviceLine.at(h).get('cptCode').value) {\n                  this.serviceLine.at(i).get('d' + j).setErrors({\n                    dublicate: true\n                  });\n                  this.serviceLine.at(h).get('d' + k).setErrors({\n                    dublicate: true\n                  });\n                }\n              }\n            }\n          }\n        }\n\n        for (let j = 1; j <= 4; j++) {\n          for (let k = 1; k <= 4; k++) {\n            if (j != k && Number.parseInt(this.serviceLine.at(i).get('d' + j).value) === Number.parseInt(this.serviceLine.at(i).get('d' + k).value)) {\n              this.serviceLine.at(i).get('d' + j).setErrors({\n                dublicate: true\n              });\n            }\n          }\n        }\n      }\n    }\n\n    cacheServiceRefresh() {\n      if (!!this.cacheService.localStorageGetItem(LocalStorageKey.selectedICDCodesForCreateClaim)) {\n        this.icdCodes = this.cacheService.localStorageGetItem(LocalStorageKey.selectedICDCodesForCreateClaim);\n        this.selectedICDCodes = this.icdCodes;\n      }\n    }\n\n  }\n\n  ServiceLineComponent.ɵfac = function ServiceLineComponent_Factory(t) {\n    return new (t || ServiceLineComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.GetAllCPTCodeService), i0.ɵɵdirectiveInject(i3.GetAllICDCodeService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.CacheService), i0.ɵɵdirectiveInject(i6.SubjectService));\n  };\n\n  ServiceLineComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ServiceLineComponent,\n    selectors: [[\"app-service-line\"]],\n    inputs: {\n      selectedMember: \"selectedMember\",\n      cptCodes: \"cptCodes\",\n      claimFormData: \"claimFormData\"\n    },\n    outputs: {\n      selectedServicelines: \"selectedServicelines\",\n      setPreview: \"setPreview\",\n      setServiceLine: \"setServiceLine\"\n    },\n    decls: 175,\n    vars: 137,\n    consts: [[1, \"service-line-component\", 3, \"formGroup\"], [1, \"row\"], [1, \"col-md-4\"], [1, \"dashboard-label\", 2, \"margin-right\", \"1.5rem !important\"], [1, \"radio-flex\", 2, \"margin-top\", \"0.5rem\"], [1, \"form-check\", \"form-check-inline\", \"radio-flex\"], [\"type\", \"radio\", \"formControlName\", \"icdType\", \"value\", \"ICD9\", \"id\", \"icdCode\", 1, \"form-check-input\"], [\"for\", \"OutsideLabYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"icdType\", \"value\", \"ICD10\", \"id\", \"icdCode\", \"checked\", \"\", 1, \"form-check-input\"], [\"for\", \"OutsideLabNo\", 1, \"create-claim-radio-labels\"], [\"href\", \"javascript:void(0);\", \"id\", \"viewicdCodesHistory\", \"title\", \"View ICD History\", 1, \"view-history\", 3, \"click\"], [1, \"fa\", \"fa-history\"], [1, \"col-md-12\", \"mt-10\"], [1, \"col-md-2\"], [\"appendTo\", \"body\", \"placeholder\", \"1. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode1\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"virtualScroll\", \"searchable\", \"clearable\", \"typeahead\", \"ngClass\", \"change\"], [\"assignto\", \"\"], [\"ng-suffix-tmp\", \"\"], [\"ng-option-tmp\", \"\", \"ng-label-tmp\", \"\"], [\"ng-loadingtext-tmp\", \"\"], [\"ng-notfound-tmp\", \"\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"appendTo\", \"body\", \"placeholder\", \"2. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode2\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"searchable\", \"clearable\", \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"3. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode3\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"searchable\", \"clearable\", \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"4. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode4\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"searchable\", \"clearable\", \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"5. ICD Code Type 3+ letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode5\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"searchable\", \"clearable\", \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"6. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode6\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"searchable\", \"clearable\", \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"7. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode7\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"searchable\", \"clearable\", \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"8. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode8\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"9. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode9\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"10. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode10\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"11. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode11\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"12. ICD Code Type 3 letters\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"icdCode12\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"typeahead\", \"virtualScroll\", \"ngClass\", \"change\"], [1, \"row\", \"mt-4\"], [1, \"dashboard-label\"], [\"href\", \"javascript:void(0);\", \"id\", \"viewcptCodesHistory\", \"title\", \"View CPT History\", 1, \"view-history\", 3, \"click\"], [1, \"col-md-2\", \"wdt-120\"], [1, \"col-md-1\"], [\"formArrayName\", \"serviceLines\", 1, \"col-md-12\", \"mt-10\"], [3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-12\"], [1, \"row\", \"mt-3\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"Green-Color\", \"success-btn\", \"btn-height\", 3, \"click\"], [1, \"material-icons\", \"icon-margin\", \"icon-height\", \"icon-align\"], [1, \"col-md-7\"], [1, \"col-md-3\", \"flr\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"flr\", \"primary-btn\", \"btn-height\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"text-muted\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-2\"], [1, \"text-center\", \"text-muted\", \"p-2\"], [1, \"fas\", \"fa-search\", \"me-2\"], [1, \"invalid-feedback\"], [3, \"formGroupName\"], [1, \"row\", 2, \"margin-bottom\", \"10px\"], [1, \"col-md-2\", \"cpt-frop\"], [\"placeholder\", \"CPT  Type 3 letter\", \"appendTo\", \"body\", \"bindLabel\", \"shortDescription\", \"bindValue\", \"cpt\", \"formControlName\", \"cptCode\", 1, \"form-control\", \"form-control-sm\", 3, \"virtualScroll\", \"ngClass\", \"ngModelChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"table-flex\", \"overflow-control\"], [1, \"col-sd-2\"], [\"type\", \"text\", \"placeholder\", \"M1\", \"maxlength\", \"2\", \"numbersOnly\", \"\", \"formControlName\", \"m1\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\"], [\"type\", \"text\", \"placeholder\", \"M2\", \"maxlength\", \"2\", \"numbersOnly\", \"\", \"formControlName\", \"m2\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\"], [\"type\", \"text\", \"placeholder\", \"M3\", \"maxlength\", \"2\", \"numbersOnly\", \"\", \"formControlName\", \"m3\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\"], [\"type\", \"text\", \"placeholder\", \"M4\", \"maxlength\", \"2\", \"numbersOnly\", \"\", \"formControlName\", \"m4\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\"], [1, \"col-sd-1\"], [\"type\", \"text\", \"placeholder\", \"D1\", \"formControlName\", \"d1\", \"numbersOnly\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\", \"keyup\", \"blur\"], [\"type\", \"text\", \"placeholder\", \"D2\", \"formControlName\", \"d2\", \"numbersOnly\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\", \"keyup\", \"blur\"], [\"type\", \"text\", \"placeholder\", \"D3\", \"formControlName\", \"d3\", \"numbersOnly\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\", \"keyup\", \"blur\"], [\"type\", \"text\", \"placeholder\", \"D4\", \"formControlName\", \"d4\", \"numbersOnly\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\", \"keyup\", \"blur\"], [\"type\", \"text\", \"formControlName\", \"charges\", \"numbersTwoDecimalOnly\", \"\", 1, \"form-control\", \"form-control-sm\", \"wdt-100\", 3, \"ngClass\", \"change\", \"keyup\"], [\"type\", \"text\", \"formControlName\", \"daysunit\", \"numbersOnly\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\", \"keyup\"], [\"type\", \"text\", \"formControlName\", \"totalcharges\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"radio\", \"formControlName\", \"ePSDT\", \"value\", \"Yes\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"ePSDTYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"ePSDT\", \"value\", \"No\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"ePSDTNo\", 1, \"create-claim-radio-labels\"], [1, \"col-md-1\", 2, \"display\", \"inline-flex\"], [\"type\", \"radio\", \"formControlName\", \"eMG\", \"value\", \"Yes\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"eMGYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"eMG\", \"value\", \"No\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"eMGNo\", 1, \"create-claim-radio-labels\"], [4, \"ngIf\"], [3, \"value\"], [\"matTooltip\", \"Remove\", \"matTooltipPosition\", \"above\", \"aria-hidden\", \"true\", 1, \"fa\", \"fa-times\", 3, \"click\"]],\n    template: function ServiceLineComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"label\", 3);\n        i0.ɵɵtext(4, \" Diagnosis or Nature of Illness or Injury \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5);\n        i0.ɵɵelement(7, \"input\", 6);\n        i0.ɵɵelementStart(8, \"label\", 7);\n        i0.ɵɵtext(9, \"ICD9\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 5);\n        i0.ɵɵelement(11, \"input\", 8);\n        i0.ɵɵelementStart(12, \"label\", 9);\n        i0.ɵɵtext(13, \"ICD10\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 5)(15, \"a\", 10);\n        i0.ɵɵlistener(\"click\", function ServiceLineComponent_Template_a_click_15_listener() {\n          return ctx.icdViewHistory();\n        });\n        i0.ɵɵelement(16, \"span\", 11);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(17, \"div\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"div\", 12)(19, \"div\", 1)(20, \"div\", 13)(21, \"ng-select\", 14, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_21_listener($event) {\n          return ctx.enableIcdControl($event, 1);\n        });\n        i0.ɵɵtemplate(23, ServiceLineComponent_ng_template_23_Template, 1, 0, \"ng-template\", 16);\n        i0.ɵɵtemplate(24, ServiceLineComponent_ng_template_24_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵtemplate(25, ServiceLineComponent_ng_template_25_Template, 3, 0, \"ng-template\", 18);\n        i0.ɵɵtemplate(26, ServiceLineComponent_ng_template_26_Template, 3, 0, \"ng-template\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(27, ServiceLineComponent_div_27_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(28, ServiceLineComponent_div_28_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(29, ServiceLineComponent_div_29_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(30, ServiceLineComponent_div_30_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(31, ServiceLineComponent_div_31_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"div\", 13)(33, \"ng-select\", 21, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_33_listener($event) {\n          return ctx.enableIcdControl($event, 2);\n        });\n        i0.ɵɵtemplate(35, ServiceLineComponent_ng_template_35_Template, 1, 0, \"ng-template\", 16);\n        i0.ɵɵtemplate(36, ServiceLineComponent_ng_template_36_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵtemplate(37, ServiceLineComponent_ng_template_37_Template, 3, 0, \"ng-template\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(38, ServiceLineComponent_div_38_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(39, ServiceLineComponent_div_39_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(40, ServiceLineComponent_div_40_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(41, ServiceLineComponent_div_41_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"div\", 13)(43, \"ng-select\", 22, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_43_listener($event) {\n          return ctx.enableIcdControl($event, 3);\n        });\n        i0.ɵɵtemplate(45, ServiceLineComponent_ng_template_45_Template, 1, 0, \"ng-template\", 16);\n        i0.ɵɵtemplate(46, ServiceLineComponent_ng_template_46_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵtemplate(47, ServiceLineComponent_ng_template_47_Template, 3, 0, \"ng-template\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(48, ServiceLineComponent_div_48_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(49, ServiceLineComponent_div_49_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(50, ServiceLineComponent_div_50_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(51, ServiceLineComponent_div_51_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(52, \"div\", 13)(53, \"ng-select\", 23, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_53_listener($event) {\n          return ctx.enableIcdControl($event, 4);\n        });\n        i0.ɵɵtemplate(55, ServiceLineComponent_ng_template_55_Template, 1, 0, \"ng-template\", 16);\n        i0.ɵɵtemplate(56, ServiceLineComponent_ng_template_56_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵtemplate(57, ServiceLineComponent_ng_template_57_Template, 3, 0, \"ng-template\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(58, ServiceLineComponent_div_58_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(59, ServiceLineComponent_div_59_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(60, ServiceLineComponent_div_60_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(61, ServiceLineComponent_div_61_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"div\", 13)(63, \"ng-select\", 24, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_63_listener($event) {\n          return ctx.enableIcdControl($event, 5);\n        });\n        i0.ɵɵtemplate(65, ServiceLineComponent_ng_template_65_Template, 1, 0, \"ng-template\", 16);\n        i0.ɵɵtemplate(66, ServiceLineComponent_ng_template_66_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵtemplate(67, ServiceLineComponent_ng_template_67_Template, 3, 0, \"ng-template\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(68, ServiceLineComponent_div_68_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(69, ServiceLineComponent_div_69_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(70, ServiceLineComponent_div_70_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(71, ServiceLineComponent_div_71_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"div\", 13)(73, \"ng-select\", 25, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_73_listener($event) {\n          return ctx.enableIcdControl($event, 6);\n        });\n        i0.ɵɵtemplate(75, ServiceLineComponent_ng_template_75_Template, 1, 0, \"ng-template\", 16);\n        i0.ɵɵtemplate(76, ServiceLineComponent_ng_template_76_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵtemplate(77, ServiceLineComponent_ng_template_77_Template, 3, 0, \"ng-template\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(78, ServiceLineComponent_div_78_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(79, ServiceLineComponent_div_79_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(80, ServiceLineComponent_div_80_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(81, ServiceLineComponent_div_81_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(82, \"div\", 12)(83, \"div\", 1)(84, \"div\", 13)(85, \"ng-select\", 26, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_85_listener($event) {\n          return ctx.enableIcdControl($event, 7);\n        });\n        i0.ɵɵtemplate(87, ServiceLineComponent_ng_template_87_Template, 1, 0, \"ng-template\", 16);\n        i0.ɵɵtemplate(88, ServiceLineComponent_ng_template_88_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵtemplate(89, ServiceLineComponent_ng_template_89_Template, 3, 0, \"ng-template\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(90, ServiceLineComponent_div_90_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(91, ServiceLineComponent_div_91_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(92, ServiceLineComponent_div_92_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(93, ServiceLineComponent_div_93_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(94, \"div\", 13)(95, \"ng-select\", 27, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_95_listener($event) {\n          return ctx.enableIcdControl($event, 8);\n        });\n        i0.ɵɵtemplate(97, ServiceLineComponent_ng_template_97_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(98, ServiceLineComponent_div_98_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(99, ServiceLineComponent_div_99_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(100, ServiceLineComponent_div_100_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(101, ServiceLineComponent_div_101_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(102, \"div\", 13)(103, \"ng-select\", 28, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_103_listener($event) {\n          return ctx.enableIcdControl($event, 9);\n        });\n        i0.ɵɵtemplate(105, ServiceLineComponent_ng_template_105_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(106, ServiceLineComponent_div_106_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(107, ServiceLineComponent_div_107_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(108, ServiceLineComponent_div_108_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(109, ServiceLineComponent_div_109_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(110, \"div\", 13)(111, \"ng-select\", 29, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_111_listener($event) {\n          return ctx.enableIcdControl($event, 10);\n        });\n        i0.ɵɵtemplate(113, ServiceLineComponent_ng_template_113_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(114, ServiceLineComponent_div_114_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(115, ServiceLineComponent_div_115_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(116, ServiceLineComponent_div_116_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(117, ServiceLineComponent_div_117_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(118, \"div\", 13)(119, \"ng-select\", 30, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_119_listener($event) {\n          return ctx.enableIcdControl($event, 11);\n        });\n        i0.ɵɵtemplate(121, ServiceLineComponent_ng_template_121_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(122, ServiceLineComponent_div_122_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(123, ServiceLineComponent_div_123_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(124, ServiceLineComponent_div_124_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(125, ServiceLineComponent_div_125_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(126, \"div\", 13)(127, \"ng-select\", 31, 15);\n        i0.ɵɵlistener(\"change\", function ServiceLineComponent_Template_ng_select_change_127_listener($event) {\n          return ctx.enableIcdControl($event, 12);\n        });\n        i0.ɵɵtemplate(129, ServiceLineComponent_ng_template_129_Template, 1, 1, \"ng-template\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(130, ServiceLineComponent_div_130_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(131, ServiceLineComponent_div_131_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(132, ServiceLineComponent_div_132_Template, 2, 0, \"div\", 20);\n        i0.ɵɵtemplate(133, ServiceLineComponent_div_133_Template, 2, 0, \"div\", 20);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(134, \"div\", 32)(135, \"div\", 13)(136, \"label\", 33);\n        i0.ɵɵtext(137, \"CPT\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(138, \" \\u00A0\");\n        i0.ɵɵelementStart(139, \"a\", 34);\n        i0.ɵɵlistener(\"click\", function ServiceLineComponent_Template_a_click_139_listener() {\n          return ctx.cptViewHistory();\n        });\n        i0.ɵɵelement(140, \"span\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(141, \"div\", 13)(142, \"label\", 33);\n        i0.ɵɵtext(143, \"Modifiers\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(144, \"div\", 13)(145, \"label\", 33);\n        i0.ɵɵtext(146, \"Diagnosis Pointer\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(147, \"div\", 35)(148, \"label\", 33);\n        i0.ɵɵtext(149, \"Unit Charge ($)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(150, \"div\", 36)(151, \"label\", 33);\n        i0.ɵɵtext(152, \"Days\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(153, \"div\", 36)(154, \"label\", 33);\n        i0.ɵɵtext(155, \"Charges ($)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(156, \"div\", 36);\n        i0.ɵɵtext(157, \"EPSDT \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(158, \"div\", 36);\n        i0.ɵɵtext(159, \"EMG \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(160, \"div\", 37);\n        i0.ɵɵtemplate(161, ServiceLineComponent_div_161_Template, 63, 50, \"div\", 38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(162, \"div\", 39)(163, \"div\", 40)(164, \"div\", 13)(165, \"button\", 41);\n        i0.ɵɵlistener(\"click\", function ServiceLineComponent_Template_button_click_165_listener() {\n          return ctx.addServiceLine();\n        });\n        i0.ɵɵelementStart(166, \"i\", 42);\n        i0.ɵɵtext(167, \"playlist_add\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(168, \" Add Service \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(169, \"div\", 43);\n        i0.ɵɵelementStart(170, \"div\", 44)(171, \"button\", 45);\n        i0.ɵɵlistener(\"click\", function ServiceLineComponent_Template_button_click_171_listener() {\n          return ctx.isShow();\n        });\n        i0.ɵɵelementStart(172, \"i\", 42);\n        i0.ɵɵtext(173, \"preview\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(174, \" Preview \");\n        i0.ɵɵelementEnd()()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.servicelineInfo);\n        i0.ɵɵadvance(21);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"virtualScroll\", true)(\"searchable\", true)(\"clearable\", true)(\"typeahead\", ctx.icdSearch)(\"ngClass\", i0.ɵɵpureFunction1(113, _c0, (ctx.f.icdCode1 == null ? null : ctx.f.icdCode1.invalid) && (ctx.f.icdCode1.dirty || ctx.f.icdCode1.touched)));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.icdCode1 == null ? null : ctx.f.icdCode1.invalid) && ctx.f.icdCode1.errors.required);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode1\"] == null ? null : ctx.f[\"icdCode1\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode1\"] == null ? null : ctx.f[\"icdCode1\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode1\"] == null ? null : ctx.f[\"icdCode1\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode1\"] == null ? null : ctx.f[\"icdCode1\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"searchable\", true)(\"clearable\", true)(\"ngClass\", i0.ɵɵpureFunction1(115, _c0, (ctx.f.icdCode2 == null ? null : ctx.f.icdCode2.invalid) && (ctx.f.icdCode2.dirty || ctx.f.icdCode2.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode2\"] == null ? null : ctx.f[\"icdCode2\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode2\"] == null ? null : ctx.f[\"icdCode2\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode2\"] == null ? null : ctx.f[\"icdCode2\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode2\"] == null ? null : ctx.f[\"icdCode2\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"searchable\", true)(\"clearable\", true)(\"ngClass\", i0.ɵɵpureFunction1(117, _c0, (ctx.f.icdCode3 == null ? null : ctx.f.icdCode3.invalid) && (ctx.f.icdCode3.dirty || ctx.f.icdCode3.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode3\"] == null ? null : ctx.f[\"icdCode3\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode3\"] == null ? null : ctx.f[\"icdCode3\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode3\"] == null ? null : ctx.f[\"icdCode3\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode3\"] == null ? null : ctx.f[\"icdCode3\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"searchable\", true)(\"clearable\", true)(\"ngClass\", i0.ɵɵpureFunction1(119, _c0, (ctx.f.icdCode4 == null ? null : ctx.f.icdCode4.invalid) && (ctx.f.icdCode4.dirty || ctx.f.icdCode4.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode4\"] == null ? null : ctx.f[\"icdCode4\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode4\"] == null ? null : ctx.f[\"icdCode4\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode4\"] == null ? null : ctx.f[\"icdCode4\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode4\"] == null ? null : ctx.f[\"icdCode4\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"searchable\", true)(\"clearable\", true)(\"ngClass\", i0.ɵɵpureFunction1(121, _c0, (ctx.f.icdCode5 == null ? null : ctx.f.icdCode5.invalid) && (ctx.f.icdCode5.dirty || ctx.f.icdCode5.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode5\"] == null ? null : ctx.f[\"icdCode5\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode5\"] == null ? null : ctx.f[\"icdCode5\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode5\"] == null ? null : ctx.f[\"icdCode5\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode5\"] == null ? null : ctx.f[\"icdCode5\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"searchable\", true)(\"clearable\", true)(\"ngClass\", i0.ɵɵpureFunction1(123, _c0, (ctx.f.icdCode6 == null ? null : ctx.f.icdCode6.invalid) && (ctx.f.icdCode6.dirty || ctx.f.icdCode6.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode6\"] == null ? null : ctx.f[\"icdCode6\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode6\"] == null ? null : ctx.f[\"icdCode6\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode6\"] == null ? null : ctx.f[\"icdCode6\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode6\"] == null ? null : ctx.f[\"icdCode6\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"searchable\", true)(\"clearable\", true)(\"ngClass\", i0.ɵɵpureFunction1(125, _c0, (ctx.f.icdCode7 == null ? null : ctx.f.icdCode7.invalid) && (ctx.f.icdCode7.dirty || ctx.f.icdCode7.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode7\"] == null ? null : ctx.f[\"icdCode7\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode7\"] == null ? null : ctx.f[\"icdCode7\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode7\"] == null ? null : ctx.f[\"icdCode7\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode7\"] == null ? null : ctx.f[\"icdCode7\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(127, _c0, (ctx.f.icdCode8 == null ? null : ctx.f.icdCode8.invalid) && (ctx.f.icdCode8.dirty || ctx.f.icdCode8.touched)));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode8\"] == null ? null : ctx.f[\"icdCode8\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode8\"] == null ? null : ctx.f[\"icdCode8\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode8\"] == null ? null : ctx.f[\"icdCode8\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode8\"] == null ? null : ctx.f[\"icdCode8\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(129, _c0, (ctx.f.icdCode9 == null ? null : ctx.f.icdCode9.invalid) && (ctx.f.icdCode9.dirty || ctx.f.icdCode9.touched)));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode9\"] == null ? null : ctx.f[\"icdCode9\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode9\"] == null ? null : ctx.f[\"icdCode9\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode9\"] == null ? null : ctx.f[\"icdCode9\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode9\"] == null ? null : ctx.f[\"icdCode9\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(131, _c0, (ctx.f.icdCode10 == null ? null : ctx.f.icdCode10.invalid) && (ctx.f.icdCode10.dirty || ctx.f.icdCode10.touched)));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode10\"] == null ? null : ctx.f[\"icdCode10\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode10\"] == null ? null : ctx.f[\"icdCode10\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode10\"] == null ? null : ctx.f[\"icdCode10\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode10\"] == null ? null : ctx.f[\"icdCode10\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(133, _c0, (ctx.f.icdCode11 == null ? null : ctx.f.icdCode11.invalid) && (ctx.f.icdCode11.dirty || ctx.f.icdCode11.touched)));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode11\"] == null ? null : ctx.f[\"icdCode11\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode11\"] == null ? null : ctx.f[\"icdCode11\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode11\"] == null ? null : ctx.f[\"icdCode11\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode11\"] == null ? null : ctx.f[\"icdCode11\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.icdCodes)(\"typeahead\", ctx.icdSearch)(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(135, _c0, (ctx.f.icdCode12 == null ? null : ctx.f.icdCode12.invalid) && (ctx.f.icdCode12.dirty || ctx.f.icdCode12.touched)));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode12\"] == null ? null : ctx.f[\"icdCode12\"].hasError(\"atleast\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode12\"] == null ? null : ctx.f[\"icdCode12\"].hasError(\"dublicate\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode12\"] == null ? null : ctx.f[\"icdCode12\"].hasError(\"continued\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.f[\"icdCode12\"] == null ? null : ctx.f[\"icdCode12\"].hasError(\"invalidIcdlength\"));\n        i0.ɵɵadvance(28);\n        i0.ɵɵproperty(\"ngForOf\", ctx.serviceLine.controls);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i8.NgSelectComponent, i8.NgOptionComponent, i8.NgOptionTemplateDirective, i8.NgLabelTemplateDirective, i8.NgNotFoundTemplateDirective, i8.NgLoadingTextTemplateDirective, i9.MatTooltip, i10.OnlyNumberDirective, i11.OnlyWithDeciamlaTwoNumberDirective],\n    styles: [\".create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.create-claim-form-styles[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.create-claim-form-styles[_ngcontent-%COMP%]   .form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.create-claim-form-styles[_ngcontent-%COMP%]   .search-icon-alignment[_ngcontent-%COMP%]{position:relative}.create-claim-form-styles[_ngcontent-%COMP%]   .search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.create-claim-form-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.create-claim-form-styles[_ngcontent-%COMP%]   .table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.create-claim-form-styles[_ngcontent-%COMP%]   .radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.create-claim-form-styles[_ngcontent-%COMP%]   .form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claim-form-styles[_ngcontent-%COMP%]   .create-claims-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .create-claim-radio-labels[_ngcontent-%COMP%], .create-claim-form-styles[_ngcontent-%COMP%]   .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.mat-mdc-form-field[_ngcontent-%COMP%] + .mat-mdc-form-field[_ngcontent-%COMP%]{margin-left:8px}.mt-25[_ngcontent-%COMP%]{margin-top:25px}.mt-10[_ngcontent-%COMP%]{margin-top:10px}.mat-expansion-panel[_ngcontent-%COMP%]:not(.mat-expanded)   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover:not([aria-disabled=true]){background:#ECF4FC!important}.strong-text[_ngcontent-%COMP%]{font-weight:700}.header-bg[_ngcontent-%COMP%]{background-color:#f5f5f5;cursor:pointer}.header-bg[_ngcontent-%COMP%]:hover{background-color:#edeaea}.remitStatus[_ngcontent-%COMP%]{text-align:left;font-size:14px!important}.bgColor[_ngcontent-%COMP%]{background:#ECF4FC}.tbl[_ngcontent-%COMP%]{margin-top:10px;min-width:100%;overflow-x:auto;overflow-y:auto;z-index:9}.wd-100[_ngcontent-%COMP%]{width:100%}.border-0[_ngcontent-%COMP%]{border:0px}.ht-30[_ngcontent-%COMP%]{height:30px!important}.ml-5[_ngcontent-%COMP%]{margin-left:5px}.ml-25[_ngcontent-%COMP%]{margin-left:25px}.mlm[_ngcontent-%COMP%]{margin-left:-30px!important}.w-auto[_ngcontent-%COMP%]{width:auto!important}.mr-30[_ngcontent-%COMP%]{margin-right:30px!important}.btnmrgn1[_ngcontent-%COMP%]{margin-bottom:-52px!important}.mt-50[_ngcontent-%COMP%]{margin-top:50px}.mt-8[_ngcontent-%COMP%]{margin-top:8px}.mb-10[_ngcontent-%COMP%]{margin-bottom:10px}.ht-50[_ngcontent-%COMP%]{height:50px!important}.ht-28[_ngcontent-%COMP%]{height:28px!important}.wdt-120[_ngcontent-%COMP%]{width:120px}.wdt-100[_ngcontent-%COMP%]{width:100px}.menuitemschaild[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:15px;line-height:24px;color:#617798}.boldTxt[_ngcontent-%COMP%]{margin:0 12px;font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:14px}.cursor[_ngcontent-%COMP%]{cursor:pointer}.fa-plus[_ngcontent-%COMP%]{color:green!important}.fa-minus[_ngcontent-%COMP%]{color:red!important}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.plain-text[_ngcontent-%COMP%]{font-size:11px}.mt-22[_ngcontent-%COMP%]{margin-top:22px}.flr[_ngcontent-%COMP%]{float:right!important}.btn-mrg[_ngcontent-%COMP%]{margin-right:-160px}.fa-times[_ngcontent-%COMP%]:before{color:#fb5858}.Green-Color[_ngcontent-%COMP%]{background-color:#077e25;border-color:#077e25}.mat-card[_ngcontent-%COMP%]{padding:2px!important}.bckBtn[_ngcontent-%COMP%]{margin-top:11px;float:right;width:50px;margin-right:75px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}.cpt-frop[_ngcontent-%COMP%]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{white-space:break-spaces!important;font-size:12px}.service-line-component[_ngcontent-%COMP%]{font-size:14px}.service-line-component[_ngcontent-%COMP%]     .form-control{height:2rem!important;min-height:unset!important}.service-line-component[_ngcontent-%COMP%]     .ng-select-container{height:100%!important}.fa-times[_ngcontent-%COMP%]{cursor:pointer;margin-left:1rem}.overflow-control[_ngcontent-%COMP%]{width:100%;overflow-x:auto}.success-btn[_ngcontent-%COMP%]:active{border-bottom:0px;padding-top:4px}.success-btn[_ngcontent-%COMP%]{border-bottom:2px solid #015015;outline:none!important}.success-btn[_ngcontent-%COMP%]:hover{background:#015015;border:0;color:#fff!important}.success-btn[_ngcontent-%COMP%]:focus{outline:none!important}.view-history[_ngcontent-%COMP%]{height:25px;padding:2px 3px;background-color:#0d6efd;color:#fff}.view-history[_ngcontent-%COMP%]:hover{background-color:#191970!important}.service-line[_ngcontent-%COMP%]{padding-left:708px;margin-right:-195px;margin-bottom:-59px}app-service-line[_ngcontent-%COMP%]   ng-select.form-control[_ngcontent-%COMP%]{position:relative}app-service-line[_ngcontent-%COMP%]   ng-select.form-control[_ngcontent-%COMP%]   .ng-placeholder[_ngcontent-%COMP%]{color:#6c757d;font-style:italic}app-service-line[_ngcontent-%COMP%]   ng-select.form-control.ng-select-focused[_ngcontent-%COMP%]{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}app-service-line[_ngcontent-%COMP%]   ng-select.form-control[_ngcontent-%COMP%]   .ng-dropdown-panel[_ngcontent-%COMP%]{border:1px solid #007bff;box-shadow:0 4px 12px #00000026}app-service-line[_ngcontent-%COMP%]   ng-select.form-control[_ngcontent-%COMP%]   .ng-dropdown-panel[_ngcontent-%COMP%]   .ng-option[_ngcontent-%COMP%]{padding:8px 12px}app-service-line[_ngcontent-%COMP%]   ng-select.form-control[_ngcontent-%COMP%]   .ng-dropdown-panel[_ngcontent-%COMP%]   .ng-option.ng-option-highlighted[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1976d2}app-service-line[_ngcontent-%COMP%]   ng-select.form-control[_ngcontent-%COMP%]   .ng-dropdown-panel[_ngcontent-%COMP%]   .ng-option.ng-option-selected[_ngcontent-%COMP%]{background-color:#007bff;color:#fff}app-service-line[_ngcontent-%COMP%]   ng-select.form-control[_ngcontent-%COMP%]   .ng-dropdown-panel[_ngcontent-%COMP%]   .ng-option.ng-option-disabled[_ngcontent-%COMP%]{text-align:center;color:#6c757d;font-style:italic}\"]\n  });\n  return ServiceLineComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}