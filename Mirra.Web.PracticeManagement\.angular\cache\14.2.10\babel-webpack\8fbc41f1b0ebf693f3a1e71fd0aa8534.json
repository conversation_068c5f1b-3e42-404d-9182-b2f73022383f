{"ast": null, "code": "import { FormControl, Validators } from '@angular/forms';\nimport { faxNumberValidator, noWhitespaceValidator } from 'src/app/shared/functions/customFormValidators';\nimport { PREVILEGES } from 'src/app/common/common-static';\nimport { AddFacilityPopComponent } from '../../facility/add-facility-pop/add-facility-pop.component';\nimport { LocalStorageKey } from 'src/app/shared/constant/constatnt';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/services/subject.service\";\nimport * as i2 from \"src/app/shared/services/masterdata.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/services/ProviderManagement/provider-management.service\";\nimport * as i5 from \"src/app/services/Notification/notification.service\";\nimport * as i6 from \"src/app/shared/services/mastershares.service\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"src/app/services/cache-service/cache.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"src/app/shared/services/global.service\";\nimport * as i11 from \"@angular/material/radio\";\nimport * as i12 from \"@ng-select/ng-select\";\nimport * as i13 from \"../../../../shared/directives/numbers-only.directive\";\nimport * as i14 from \"@angular/material/datepicker\";\n\nfunction AddProviderComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" NPI Number is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" NPI Number, Please enter only 10 digit number. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" NPI already exists. Please try with some other NPI or edit existing provider. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Last Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" First Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Specialty Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Plan Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Primary IPA is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Group is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Facility is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 50)(2, \"label\", 8);\n    i0.ɵɵtext(3, \"Address Line 1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 50)(6, \"label\", 8);\n    i0.ɵɵtext(7, \"Address Line 2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 50)(10, \"label\", 8);\n    i0.ɵɵtext(11, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 50)(14, \"label\", 8);\n    i0.ɵɵtext(15, \"State\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 70);\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AddProviderComponent_div_73_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 50)(2, \"label\", 8);\n    i0.ɵɵtext(3, \"County\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 50)(6, \"label\", 8);\n    i0.ɵɵtext(7, \"Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 50)(10, \"label\", 8);\n    i0.ɵɵtext(11, \"Zip Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 73);\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AddProviderComponent_div_73_mat_radio_group_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-radio-group\", 74)(1, \"mat-radio-button\", 75);\n    i0.ɵɵtext(2, \"CAP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-radio-button\", 76);\n    i0.ɵɵtext(4, \"FFS\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"invalid-radio\": a0\n  };\n};\n\nfunction AddProviderComponent_div_73_mat_radio_group_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"mat-radio-group\", 77);\n    i0.ɵɵlistener(\"change\", function AddProviderComponent_div_73_mat_radio_group_34_Template_mat_radio_group_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r47.changeBillingProviderType($event));\n    });\n    i0.ɵɵelementStart(1, \"mat-radio-button\", 78);\n    i0.ɵɵtext(2, \"Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-radio-button\", 79);\n    i0.ɵɵtext(4, \"Self\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r22.f.billingProviderType.invalid && (ctx_r22.f.billingProviderType.dirty || ctx_r22.f.billingProviderType.touched) && ctx_r22.f.billingProviderType.errors));\n  }\n}\n\nfunction AddProviderComponent_div_73_input_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 80);\n    i0.ɵɵlistener(\"change\", function AddProviderComponent_div_73_input_36_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.useSameAsFacility($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"checked\", ctx_r23.f[\"useSameAsFacility\"].value == \"ON\");\n  }\n}\n\nfunction AddProviderComponent_div_73_label_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 8);\n    i0.ɵɵtext(1, \"Use Same as Facility\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction AddProviderComponent_div_73_input_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 81);\n  }\n\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, ctx_r25.f.billingProviderName.invalid && (ctx_r25.f.billingProviderName.dirty || ctx_r25.f.billingProviderName.touched) && ctx_r25.f.billingProviderName.errors));\n  }\n}\n\nfunction AddProviderComponent_div_73_ng_select_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 82, 83);\n    i0.ɵɵlistener(\"change\", function AddProviderComponent_div_73_ng_select_42_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r53);\n      const ctx_r52 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r52.changeBillingProviderSelect($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r26.groupsData)(\"ngClass\", i0.ɵɵpureFunction1(3, _c1, ctx_r26.f.billingProviderName.invalid && (ctx_r26.f.billingProviderName.dirty || ctx_r26.f.billingProviderName.touched) && ctx_r26.f.billingProviderName.errors));\n  }\n}\n\nfunction AddProviderComponent_div_73_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Billing Provider is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Billing Tax Id is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Billing Tax Id, Please enter only 10 digit number. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_input_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 84);\n  }\n}\n\nfunction AddProviderComponent_div_73_input_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 85);\n  }\n\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c1, ctx_r31.f.billingProviderAddressLine1.invalid && (ctx_r31.f.billingProviderAddressLine1.dirty || ctx_r31.f.billingProviderAddressLine1.touched) && ctx_r31.f.billingProviderAddressLine1.errors))(\"readonly\", ctx_r31.f.billingProviderType.value == \"Group\");\n  }\n}\n\nfunction AddProviderComponent_div_73_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Address Line 1 is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_input_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 86);\n  }\n\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"readonly\", ctx_r33.f.billingProviderType.value == \"Group\");\n  }\n}\n\nfunction AddProviderComponent_div_73_input_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 87);\n  }\n\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, ctx_r34.f.billingProviderCity.invalid && (ctx_r34.f.billingProviderCity.dirty || ctx_r34.f.billingProviderCity.touched) && ctx_r34.f.billingProviderCity.errors));\n  }\n}\n\nfunction AddProviderComponent_div_73_ng_select_67_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r56 = ctx.item;\n    i0.ɵɵtextInterpolate(item_r56.cityStateCounty);\n  }\n}\n\nfunction AddProviderComponent_div_73_ng_select_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 88, 89);\n    i0.ɵɵlistener(\"change\", function AddProviderComponent_div_73_ng_select_67_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.changeBillingProviderCitySelect($event));\n    });\n    i0.ɵɵtemplate(2, AddProviderComponent_div_73_ng_select_67_ng_template_2_Template, 1, 1, \"ng-template\", 90);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r35.citiesData)(\"ngClass\", i0.ɵɵpureFunction1(3, _c1, ctx_r35.f.billingProviderCity.invalid && (ctx_r35.f.billingProviderCity.dirty || ctx_r35.f.billingProviderCity.touched) && ctx_r35.f.billingProviderCity.errors));\n  }\n}\n\nfunction AddProviderComponent_div_73_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" City is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_input_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 91);\n  }\n\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, ctx_r37.f.billingProviderState.invalid && (ctx_r37.f.billingProviderState.dirty || ctx_r37.f.billingProviderState.touched) && ctx_r37.f.billingProviderState.errors));\n  }\n}\n\nfunction AddProviderComponent_div_73_ng_select_73_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"uppercase\");\n    i0.ɵɵpipe(2, \"uppercase\");\n  }\n\n  if (rf & 2) {\n    const item_r63 = ctx.item;\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(1, 2, item_r63.stateCode), \" - \", i0.ɵɵpipeBind1(2, 4, item_r63.stateName), \"\");\n  }\n}\n\nfunction AddProviderComponent_div_73_ng_select_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-select\", 92, 93);\n    i0.ɵɵtemplate(2, AddProviderComponent_div_73_ng_select_73_ng_template_2_Template, 3, 6, \"ng-template\", 90);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r38.statesData)(\"ngClass\", i0.ɵɵpureFunction1(3, _c1, ctx_r38.f.billingProviderState.invalid && (ctx_r38.f.billingProviderState.dirty || ctx_r38.f.billingProviderState.touched) && ctx_r38.f.billingProviderState.errors));\n  }\n}\n\nfunction AddProviderComponent_div_73_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" State is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Zip Code is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Invalid Zip Code. Please Enter 5 or 9 Digit Number \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_input_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 94);\n  }\n\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, ctx_r42.f.billingProviderContactPersonName.invalid && (ctx_r42.f.billingProviderContactPersonName.dirty || ctx_r42.f.billingProviderContactPersonName.touched) && ctx_r42.f.billingProviderContactPersonName.errors));\n  }\n}\n\nfunction AddProviderComponent_div_73_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Contact Person Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Phone Number is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Please enter a valid email address. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddProviderComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5);\n    i0.ɵɵtext(2, \" Facility \");\n    i0.ɵɵelementStart(3, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function AddProviderComponent_div_73_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.addNewFacility());\n    });\n    i0.ɵɵelementStart(4, \"i\", 35);\n    i0.ɵɵtext(5, \"add_business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Add Facility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 5)(8, \"ng-select\", 36, 37);\n    i0.ɵɵlistener(\"change\", function AddProviderComponent_div_73_Template_ng_select_change_8_listener($event) {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r68 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r68.changeFacilitySelect($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, AddProviderComponent_div_73_div_10_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, AddProviderComponent_div_73_div_11_Template, 17, 0, \"div\", 38);\n    i0.ɵɵtemplate(12, AddProviderComponent_div_73_div_12_Template, 13, 0, \"div\", 38);\n    i0.ɵɵelementStart(13, \"div\", 39)(14, \"label\", 8);\n    i0.ɵɵtext(15, \"Contract Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"br\");\n    i0.ɵɵtemplate(17, AddProviderComponent_div_73_mat_radio_group_17_Template, 5, 0, \"mat-radio-group\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 39)(19, \"label\", 8);\n    i0.ɵɵtext(20, \" Effective Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 41);\n    i0.ɵɵlistener(\"click\", function AddProviderComponent_div_73_Template_input_click_21_listener() {\n      i0.ɵɵrestoreView(_r67);\n\n      const _r20 = i0.ɵɵreference(23);\n\n      return i0.ɵɵresetView(_r20.open());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"mat-datepicker\", null, 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 39)(25, \"label\", 8);\n    i0.ɵɵtext(26, \" Termination Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"input\", 43);\n    i0.ɵɵlistener(\"click\", function AddProviderComponent_div_73_Template_input_click_27_listener() {\n      i0.ɵɵrestoreView(_r67);\n\n      const _r21 = i0.ɵɵreference(29);\n\n      return i0.ɵɵresetView(_r21.open());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"mat-datepicker\", null, 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 45)(31, \"label\", 8);\n    i0.ɵɵtext(32, \"Type of Billing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"br\");\n    i0.ɵɵtemplate(34, AddProviderComponent_div_73_mat_radio_group_34_Template, 5, 3, \"mat-radio-group\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 47);\n    i0.ɵɵtemplate(36, AddProviderComponent_div_73_input_36_Template, 1, 1, \"input\", 48);\n    i0.ɵɵtemplate(37, AddProviderComponent_div_73_label_37_Template, 2, 0, \"label\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 50)(39, \"label\", 8);\n    i0.ɵɵtext(40, \" Billing Provider* \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(41, AddProviderComponent_div_73_input_41_Template, 1, 3, \"input\", 51);\n    i0.ɵɵtemplate(42, AddProviderComponent_div_73_ng_select_42_Template, 2, 5, \"ng-select\", 52);\n    i0.ɵɵtemplate(43, AddProviderComponent_div_73_div_43_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 50)(45, \"label\", 8);\n    i0.ɵɵtext(46, \" Billing Tax Id* \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 53);\n    i0.ɵɵtemplate(48, AddProviderComponent_div_73_div_48_Template, 2, 0, \"div\", 10);\n    i0.ɵɵtemplate(49, AddProviderComponent_div_73_div_49_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 50)(51, \"label\", 8);\n    i0.ɵɵtext(52, \" Print On Claim \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(53, AddProviderComponent_div_73_input_53_Template, 1, 0, \"input\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 50)(55, \"label\", 8);\n    i0.ɵɵtext(56, \" Address Line 1* \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(57, AddProviderComponent_div_73_input_57_Template, 1, 4, \"input\", 55);\n    i0.ɵɵtemplate(58, AddProviderComponent_div_73_div_58_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"div\", 50)(60, \"label\", 8);\n    i0.ɵɵtext(61, \" Address Line 2 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(62, AddProviderComponent_div_73_input_62_Template, 1, 1, \"input\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 50)(64, \"label\", 8);\n    i0.ɵɵtext(65, \" City* \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(66, AddProviderComponent_div_73_input_66_Template, 1, 3, \"input\", 57);\n    i0.ɵɵtemplate(67, AddProviderComponent_div_73_ng_select_67_Template, 3, 5, \"ng-select\", 58);\n    i0.ɵɵtemplate(68, AddProviderComponent_div_73_div_68_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 50)(70, \"label\", 8);\n    i0.ɵɵtext(71, \" State* \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(72, AddProviderComponent_div_73_input_72_Template, 1, 3, \"input\", 59);\n    i0.ɵɵtemplate(73, AddProviderComponent_div_73_ng_select_73_Template, 3, 5, \"ng-select\", 60);\n    i0.ɵɵtemplate(74, AddProviderComponent_div_73_div_74_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"div\", 50)(76, \"label\", 8);\n    i0.ɵɵtext(77, \" Zip Code* \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(78, \"input\", 61);\n    i0.ɵɵtemplate(79, AddProviderComponent_div_73_div_79_Template, 2, 0, \"div\", 10);\n    i0.ɵɵtemplate(80, AddProviderComponent_div_73_div_80_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"div\", 50)(82, \"label\", 8);\n    i0.ɵɵtext(83, \" Contact Person Name* \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(84, AddProviderComponent_div_73_input_84_Template, 1, 3, \"input\", 62);\n    i0.ɵɵtemplate(85, AddProviderComponent_div_73_div_85_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"div\", 50)(87, \"label\", 8);\n    i0.ɵɵtext(88, \" Phone Number* \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(89, \"input\", 63);\n    i0.ɵɵtemplate(90, AddProviderComponent_div_73_div_90_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"div\", 50)(92, \"label\", 8);\n    i0.ɵɵtext(93, \" Fax Number \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"input\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(95, \"div\", 50)(96, \"label\", 8);\n    i0.ɵɵtext(97, \" Email* \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(98, \"input\", 65);\n    i0.ɵɵtemplate(99, AddProviderComponent_div_73_div_99_Template, 2, 0, \"div\", 10);\n    i0.ɵɵtemplate(100, AddProviderComponent_div_73_div_100_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const _r20 = i0.ɵɵreference(23);\n\n    const _r21 = i0.ɵɵreference(29);\n\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r14.facilityData)(\"ngClass\", i0.ɵɵpureFunction1(42, _c1, ctx_r14.f.facilityDescription.invalid && (ctx_r14.f.facilityDescription.dirty || ctx_r14.f.facilityDescription.touched) && ctx_r14.f.facilityDescription.errors));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.facilityDescription == null ? null : ctx_r14.f.facilityDescription.invalid) && ctx_r14.f.facilityDescription.errors.required && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f.facilityDescription == null ? null : ctx_r14.f.facilityDescription.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f.facilityDescription == null ? null : ctx_r14.f.facilityDescription.value);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"max\", ctx_r14.f.terminationDate.value)(\"matDatepicker\", _r20);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"min\", ctx_r14.getLargerDate(ctx_r14.f.terminationDate.value))(\"matDatepicker\", _r21);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f[\"billingProviderType\"].value == \"Self\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f[\"billingProviderType\"].value == \"Self\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f.billingProviderType.value != \"Group\" && ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f.billingProviderType.value == \"Group\" && ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingProviderName == null ? null : ctx_r14.f.billingProviderName.invalid) && ctx_r14.f.billingProviderName.errors.required && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(44, _c1, ctx_r14.f.billingTaxId.invalid && (ctx_r14.f.billingTaxId.dirty || ctx_r14.f.billingTaxId.touched) && ctx_r14.f.billingTaxId.errors))(\"readonly\", ctx_r14.f.billingProviderType.value == \"Group\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingTaxId == null ? null : ctx_r14.f.billingTaxId.invalid) && ctx_r14.f.billingTaxId.errors.required && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingTaxId == null ? null : ctx_r14.f.billingTaxId.invalid) && ctx_r14.f.billingTaxId.errors.minlength && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingProviderAddressLine1 == null ? null : ctx_r14.f.billingProviderAddressLine1.invalid) && ctx_r14.f.billingProviderAddressLine1.errors.required && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f.billingProviderType.value == \"Group\" && ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f.billingProviderType.value != \"Group\" && ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingProviderCity == null ? null : ctx_r14.f.billingProviderCity.invalid) && ctx_r14.f.billingProviderCity.errors.required && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f.billingProviderType.value == \"Group\" && ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f.billingProviderType.value != \"Group\" && ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingProviderState == null ? null : ctx_r14.f.billingProviderState.invalid) && ctx_r14.f.billingProviderState.errors.required && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(46, _c1, ctx_r14.f.billingProviderZipCode.invalid && (ctx_r14.f.billingProviderZipCode.dirty || ctx_r14.f.billingProviderZipCode.touched) && ctx_r14.f.billingProviderZipCode.errors))(\"readonly\", ctx_r14.f.billingProviderType.value == \"Group\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingProviderZipCode == null ? null : ctx_r14.f.billingProviderZipCode.invalid) && ctx_r14.f.billingProviderZipCode.errors.required && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingProviderZipCode == null ? null : ctx_r14.f.billingProviderZipCode.invalid) && ctx_r14.f.billingProviderZipCode.errors.minlength && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.mode != \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingProviderContactPersonName == null ? null : ctx_r14.f.billingProviderContactPersonName.invalid) && ctx_r14.f.billingProviderContactPersonName.errors.required && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(48, _c1, ctx_r14.f.billingProviderContactPhone.invalid && (ctx_r14.f.billingProviderContactPhone.dirty || ctx_r14.f.billingProviderContactPhone.touched) && ctx_r14.f.billingProviderContactPhone.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingProviderContactPhone == null ? null : ctx_r14.f.billingProviderContactPhone.invalid) && ctx_r14.f.billingProviderContactPhone.errors.required && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(50, _c1, ctx_r14.f.billingProviderContactEmail.invalid && (ctx_r14.f.billingProviderContactEmail.dirty || ctx_r14.f.billingProviderContactEmail.touched) && ctx_r14.f.billingProviderContactEmail.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingProviderContactEmail == null ? null : ctx_r14.f.billingProviderContactEmail.invalid) && ctx_r14.f.billingProviderContactEmail.errors.required && ctx_r14.isSubmitted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.f.billingProviderContactEmail == null ? null : ctx_r14.f.billingProviderContactEmail.invalid) && ctx_r14.f.billingProviderContactEmail.errors.email && ctx_r14.isSubmitted);\n  }\n}\n\nexport let AddProviderComponent = /*#__PURE__*/(() => {\n  class AddProviderComponent {\n    constructor(subService, masterDataService, builder, providerMgmtService, notificationService, masterSharedService, dialog, cacheService, datepipe, globalApi) {\n      this.subService = subService;\n      this.masterDataService = masterDataService;\n      this.builder = builder;\n      this.providerMgmtService = providerMgmtService;\n      this.notificationService = notificationService;\n      this.masterSharedService = masterSharedService;\n      this.dialog = dialog;\n      this.cacheService = cacheService;\n      this.datepipe = datepipe;\n      this.globalApi = globalApi;\n      this.genderData = [];\n      this.isSubmitted = false;\n      this.today = new Date();\n      this.addTab = false;\n      this.mode = 'add';\n      this.mainHeading = 'Add Provider';\n      this.addFacilityView = false;\n      this.isAddNewFacilityBtnShow = false;\n      this.isCreateProviderBtnShow = false;\n      this.isViewEditProviderBtnShow = false;\n      this.insuranceData = [];\n      this.isCheckNpi = false;\n    }\n\n    ngOnInit() {\n      this.createDemographicForm();\n      this.getPrivilegesByRole();\n      this.getAllMasterData();\n      this.specialtyData = this.cacheService.localStorageGetItem(LocalStorageKey.allSpecialityTaxonomyCodes);\n      this.subService.getRefreshFacilityList().subscribe(res => {\n        if (!!res) {\n          this.getFacilityData();\n          this.subService.resetRefreshFacilityList();\n        }\n      });\n\n      if (!!this.subService.currentTab && this.subService.currentTab.name == 'Add New Provider') {\n        this.addTab = true;\n      }\n    }\n\n    cancel() {\n      if (this.mode == 'add') {\n        this.subService.setCloseTabRefresh('Add New Provider');\n      }\n    }\n\n    createDemographicForm() {\n      this.addForm = this.builder.group({\n        npiNumber: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(10), Validators.minLength(10)]),\n        firstName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        lastName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        middleName: new FormControl(null),\n        gender: new FormControl(null),\n        isMidLevelPhysician: new FormControl(false),\n        planName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        ipaName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        billingProviderType: new FormControl('Group', Validators.required),\n        contractType: new FormControl(\"CAP\"),\n        contractId: new FormControl(null),\n        effectiveDate: new FormControl(null),\n        terminationDate: new FormControl(null),\n        specialtyName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        groupName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        groupTaxId: new FormControl(null),\n        billingProviderName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        billingTaxId: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(9), Validators.minLength(9)]),\n        printOnClaim: new FormControl(null),\n        billingProviderAddressLine1: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        billingProviderAddressLine2: new FormControl(null),\n        billingProviderCity: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        billingProviderState: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        billingProviderZipCode: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(9), Validators.minLength(9)]),\n        billingProviderContactPersonName: new FormControl(null, [Validators.required, noWhitespaceValidator]),\n        billingProviderContactPhone: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.maxLength(10), Validators.minLength(10)]),\n        billingProviderContactFax: new FormControl(null, faxNumberValidator),\n        billingProviderContactEmail: new FormControl(null, [Validators.required, noWhitespaceValidator, Validators.email]),\n        useSameAsFacility: new FormControl(null),\n        facilityDescription: new FormControl(null, [Validators.required]),\n        facilityAddress1: new FormControl(null),\n        facilityAddress2: new FormControl(null),\n        facilityCity: new FormControl(null),\n        facilityState: new FormControl(null),\n        facilityCounty: new FormControl(null),\n        facilityCountry: new FormControl(null),\n        facilityZipCode: new FormControl(null),\n        billingProviderCode: new FormControl(null),\n        groupSpecialty: new FormControl(null),\n        groupTaxonomyCode: new FormControl(null),\n        groupTaxonomyDescription: new FormControl(null),\n        groupSpecialtyMDMCode: new FormControl(null),\n        groupCode_CMS: new FormControl(null),\n        groupNPI: new FormControl(null),\n        groupId: new FormControl(null),\n        billingProviderNpi: new FormControl(null),\n        facilityId: new FormControl(null),\n        facilityName: new FormControl(null),\n        ipA_AMCode: new FormControl(null),\n        planCode_CMS: new FormControl(null),\n        specialtyCode_CMS: new FormControl(null),\n        taxonomyCode: new FormControl(null)\n      });\n    }\n\n    createFacilityForm() {\n      let facilityForm = this.builder.group({\n        facilityDescription: new FormControl(null, [Validators.required, noWhitespaceValidator])\n      });\n      return facilityForm;\n    }\n\n    changePlanNameSelect(selectedPlan) {\n      if (!!selectedPlan) {\n        this.addForm.controls['planCode_CMS'].setValue(selectedPlan.mdmCode);\n      } else {\n        this.addForm.controls['planCode_CMS'].setValue(null);\n      }\n    }\n\n    changeIPASelect(selectedIPA) {\n      if (!!selectedIPA) {\n        this.addForm.controls['ipA_AMCode'].setValue(selectedIPA.mdmCode);\n      } else {\n        this.addForm.controls['ipA_AMCode'].setValue(null);\n      }\n    }\n\n    get f() {\n      return this.addForm.controls;\n    }\n\n    getAllMasterData() {\n      this.masterDataService.fetchchMasterData('Gender').subscribe(res => {\n        this.genderData = res;\n      });\n      this.providerMgmtService.fetchGroupsData('').subscribe(res => {\n        this.groupsData = !!res && res.statusCode == 200 && !!res.content ? res.content : [];\n      });\n      this.providerMgmtService.fetchIPAData('').subscribe(res => {\n        this.ipaData = !!res.content ? res.content : [];\n      });\n      this.getFacilityData();\n      this.getFacilityMasterData();\n    }\n\n    getFacilityMasterData() {\n      this.masterSharedService.getFacilityMasterData().subscribe({\n        next: res => {\n          //this.citiesData = res.cities;\n          this.statesData = res.states;\n          this.insuranceData = res.insurance;\n        },\n        error: err => {\n          console.error('Error loading facility master data:', err);\n        }\n      });\n    }\n\n    getFacilityData() {\n      this.providerMgmtService.fetchFacilityData('').subscribe(res => {\n        this.facilityData = !!res.content ? res.content : [];\n      });\n    }\n\n    changeSpecialtySelect(selectedSpecialty) {\n      if (selectedSpecialty) {\n        this.addForm.patchValue({\n          taxonomyCode: selectedSpecialty.taxonomyCode ?? null,\n          specialtyCode_CMS: selectedSpecialty.mdmCode ?? null\n        });\n      } else {\n        this.addForm.patchValue({\n          taxonomyCode: null,\n          specialtyCode_CMS: null\n        });\n      }\n    }\n\n    changeGroupSelect(selectedGroup) {\n      if (!!selectedGroup) {\n        this.addForm.controls['groupTaxId'].setValue(!!selectedGroup.taxID ? selectedGroup.taxID : null);\n        this.addForm.controls['groupCode_CMS'].setValue(!!selectedGroup.groupMDMCode ? selectedGroup.groupMDMCode : null);\n        this.addForm.controls['groupNPI'].setValue(!!selectedGroup.npiNumber ? selectedGroup.npiNumber : null);\n        this.addForm.controls['groupId'].setValue(!!selectedGroup.groupID ? selectedGroup.groupID : null);\n      } else {\n        this.addForm.controls['groupTaxId'].setValue(null);\n        this.addForm.controls['groupCode_CMS'].setValue(null);\n        this.addForm.controls['groupNPI'].setValue(null);\n        this.addForm.controls['groupId'].setValue(null);\n      } //  this.addForm.controls['groupTaxId'].setValue(this.addProvider.practiceInformationList.groupTaxId);\n\n    }\n\n    changeBillingProviderSelect(selectedBillingProvider) {\n      const provider = selectedBillingProvider || {};\n\n      const safeValue = (val, formatFn) => val != null ? formatFn ? formatFn(val) : val : null;\n\n      this.addForm.patchValue({\n        billingProviderCode: safeValue(provider.groupMDMCode),\n        groupSpecialty: safeValue(provider.specialtyName),\n        groupTaxonomyCode: safeValue(provider.taxonomyDesc),\n        groupTaxonomyDescription: safeValue(provider.groupMDMCode),\n        groupSpecialtyMDMCode: safeValue(provider.specTaxonomyMDMCode),\n        billingProviderName: safeValue(provider.groupname),\n        billingTaxId: safeValue(provider.taxID),\n        printOnClaim: safeValue(provider.groupname),\n        billingProviderAddressLine1: safeValue(provider.street),\n        billingProviderAddressLine2: safeValue(provider.building),\n        billingProviderCity: safeValue(provider.city),\n        billingProviderState: safeValue(provider.state),\n        billingProviderZipCode: safeValue(provider.zipCode),\n        billingProviderContactPersonName: safeValue(provider.contactPersonName),\n        billingProviderContactPhone: safeValue(provider.contactPersonPhone, this.replacePhoneAndFax.bind(this)),\n        billingProviderContactFax: safeValue(provider.contactPersonFax, this.replacePhoneAndFax.bind(this)),\n        billingProviderContactEmail: safeValue(provider.contactPersonEmail)\n      });\n    }\n\n    resetBillingProviderGroupVariables() {\n      this.addForm.controls['billingProviderCode'].setValue(null);\n      this.addForm.controls['groupSpecialtyMDMCode'].setValue(null);\n      this.addForm.controls['groupSpecialty'].setValue(null);\n      this.addForm.controls['groupTaxonomyCode'].setValue(null);\n      this.addForm.controls['groupTaxonomyDescription'].setValue(null);\n    }\n\n    changeBillingProviderCitySelect(selectedCity) {\n      if (!!selectedCity) {\n        this.addForm.controls['billingProviderState'].setValue(selectedCity.stateCode);\n      }\n    }\n\n    changeBillingProviderType(e) {\n      this.resetBillingProviderGroupVariables();\n\n      if (e.value == 'Group') {} else {\n        //  let name = this.getName(this.f['lastName'].value, this.demographicForm['firstName'].value);\n        //  let npi = this.demographicForm['npiNumber'].value;\n        if (!!this.selectedFacilityInfo) {\n          //this.addForm.controls['useSameAsFacility'].setValue('ON');\n          let contact;\n\n          if (!!this.selectedFacilityInfo.contact) {\n            contact = this.selectedFacilityInfo.contact;\n          } else {\n            if (!!this.selectedFacilityInfo.contactPerson) {\n              contact = this.selectedFacilityInfo.contactPerson[0];\n            }\n          }\n        } else {\n          this.addForm.controls['useSameAsFacility'].setValue('OFF');\n        }\n      }\n    }\n\n    replacePhoneAndFax(number) {\n      if (!!number) {\n        number = number.replace('(', '');\n        number = number.replace(')', '');\n        number = number.replace('-', '');\n        number = number.replace(' ', '');\n      }\n\n      return number;\n    }\n\n    getName(lastName, firstName) {\n      let result = [];\n\n      if (lastName) {\n        result.push(lastName);\n      }\n\n      if (firstName) {\n        result.push(firstName);\n      }\n\n      return result.join(' ');\n    }\n\n    changeFacilitySelect(facilitySelected) {\n      const address = facilitySelected?.address;\n      this.selectedFacilityInfo = JSON.parse(JSON.stringify(facilitySelected));\n      this.addForm.patchValue({\n        facilityAddress1: address?.addressLine1 || null,\n        facilityAddress2: address?.addressLine2 || null,\n        facilityCity: address?.city || null,\n        facilityState: address?.state || null,\n        facilityCounty: address?.county || null,\n        facilityCountry: address?.countryCode || null,\n        facilityZipCode: address?.postalCode || null,\n        facilityId: facilitySelected?.facilityID || null,\n        facilityName: facilitySelected?.facilityName || null\n      });\n    }\n\n    useSameAsFacility(event) {\n      const value = event.target.checked;\n      this.addForm.controls['useSameAsFacility'].setValue(value ? 'ON' : 'OFF');\n      const address = this.selectedFacilityInfo?.address || {};\n      this.addForm.patchValue({\n        billingProviderAddressLine1: value ? address.addressLine1 || null : null,\n        billingProviderAddressLine2: value ? address.addressLine2 || null : null,\n        billingProviderCity: value ? address.city || null : null,\n        billingProviderState: value ? address.state || null : null,\n        billingProviderZipCode: value ? address.postalCode || null : null\n      });\n    }\n\n    checkIfNPIExists() {\n      this.providerMgmtService.CheckNpi(this.addForm.controls['npiNumber'].value).subscribe(res => {\n        if (!!res && !!res.content && res.content) {\n          this.addForm.controls['npiNumber'].setErrors({\n            invalidFormat: true\n          });\n          this.isCheckNpi = true;\n        } else {\n          this.addProvider();\n        }\n      });\n    }\n\n    saveProvider() {\n      this.isCheckNpi = true;\n      /* this.addForm.controls['npiNumber'].markAsTouched();\r\n       this.addForm.controls['npiNumber'].updateValueAndValidity();\r\n       if (this.addForm.controls['npiNumber'].invalid) {\r\n         this.isCheckNpi = true;\r\n         return;\r\n       }*/\n\n      this.isSubmitted = true;\n\n      if (this.addForm.invalid) {\n        this.addForm.markAllAsTouched();\n        return;\n      }\n\n      this.checkIfNPIExists();\n    }\n\n    addProvider() {\n      this.providerMgmtService.AddProvider(this.getProperRequestBody()).subscribe(res => {\n        if (res.statusCode == 200) {\n          this.notificationService.showSuccess('', 'Provider Added Successfully.', 4000);\n          this.subService.setRefreshProviderInfo(true);\n          this.addForm.reset();\n          this.cancel();\n        } else {\n          this.notificationService.showError('', 'Provider could not be added. Please try again later.', 4000);\n        }\n      }, error => {\n        this.notificationService.showError('', 'Provider could not be added. Please try again later.', 4000);\n      });\n    }\n\n    getProperRequestBody() {\n      return {\n        demographicInformation: {\n          firstName: this.addForm.controls['firstName'].value,\n          gender: this.addForm.controls['gender'].value,\n          isMidLevelPhysician: this.addForm.controls['isMidLevelPhysician'].value,\n          lastName: this.addForm.controls['lastName'].value,\n          middleName: this.addForm.controls['middleName'].value,\n          npiNumber: this.addForm.controls['npiNumber'].value\n        },\n        practiceInformationList: [{\n          billingProviderAddressLine1: this.addForm.controls['billingProviderAddressLine1'].value,\n          billingProviderAddressLine2: this.addForm.controls['billingProviderAddressLine2'].value,\n          billingProviderCity: this.addForm.controls['billingProviderCity'].value,\n          billingProviderCode: this.addForm.controls['billingProviderCode'].value,\n          billingProviderContactEmail: this.addForm.controls['billingProviderContactEmail'].value,\n          billingProviderContactFax: this.addForm.controls['billingProviderContactFax'].value,\n          billingProviderContactPersonName: this.addForm.controls['billingProviderContactPersonName'].value,\n          billingProviderContactPhone: this.addForm.controls['billingProviderContactPhone'].value,\n          billingProviderName: this.addForm.controls['billingProviderName'].value,\n          billingProviderNpi: this.addForm.controls['billingProviderNpi'].value,\n          billingProviderState: this.addForm.controls['billingProviderState'].value,\n          billingProviderType: this.addForm.controls['billingProviderType'].value,\n          billingProviderZipCode: this.addForm.controls['billingProviderZipCode'].value,\n          billingTaxId: this.addForm.controls['billingTaxId'].value,\n          contractType: this.addForm.controls['contractType'].value,\n          effectiveDate: this.addForm.controls['effectiveDate'].value,\n          facilityDetails: {\n            facilityAddressCity: this.addForm.controls['facilityCity'].value,\n            facilityAddressCountry: this.addForm.controls['facilityCountry'].value,\n            facilityAddressCounty: this.addForm.controls['facilityCounty'].value,\n            facilityAddressLine1: this.addForm.controls['facilityAddress1'].value,\n            facilityAddressLine2: this.addForm.controls['facilityAddress2'].value,\n            facilityAddressState: this.addForm.controls['facilityState'].value,\n            facilityAddressZipCode: this.addForm.controls['facilityZipCode'].value,\n            facilityDescription: this.addForm.controls['facilityDescription'].value,\n            facilityId: this.addForm.controls['facilityId'].value,\n            facilityName: this.addForm.controls['facilityName'].value\n          },\n          groupCode_CMS: this.addForm.controls['groupCode_CMS'].value,\n          groupId: this.addForm.controls['groupId'].value,\n          groupNPI: this.addForm.controls['groupNPI'].value,\n          groupName: this.addForm.controls['groupName'].value,\n          groupSpecialty: this.addForm.controls['groupSpecialty'].value,\n          groupSpecialtyMDMCode: this.addForm.controls['groupSpecialtyMDMCode'].value,\n          groupTaxId: this.addForm.controls['groupTaxId'].value,\n          groupTaxonomyCode: this.addForm.controls['groupTaxonomyCode'].value,\n          groupTaxonomyDescription: this.addForm.controls['groupTaxonomyDescription'].value,\n          ipA_AMCode: this.addForm.controls['ipA_AMCode'].value,\n          ipaName: this.addForm.controls['ipaName'].value,\n          planCode_CMS: this.addForm.controls['planCode_CMS'].value,\n          planName: this.addForm.controls['planName'].value,\n          printOnClaim: this.addForm.controls['printOnClaim'].value,\n          requestedByEmail: JSON.parse(localStorage.getItem('userFirstName')),\n          requestedByName: JSON.parse(localStorage.getItem('userFirstName')),\n          requestedByUUID: JSON.parse(localStorage.getItem('userFirstName')),\n          specialtyCode_CMS: this.addForm.controls['specialtyCode_CMS'].value,\n          specialtyName: this.addForm.controls['specialtyName'].value,\n          taxonomyCode: this.addForm.controls['taxonomyCode'].value,\n          terminationDate: this.addForm.controls['terminationDate'].value,\n          useSameAsFacility: 'NO'\n        }]\n      };\n    }\n\n    getLargerDate(date) {\n      const date1 = new Date(date).getTime();\n      const date2 = this.today.getTime();\n\n      if (date1 < date2) {\n        return this.today;\n      } else if (date2 < date1) {\n        return date;\n      } else {\n        return this.today;\n      }\n    }\n\n    addNewFacility() {\n      const dialogRef = this.dialog.open(AddFacilityPopComponent, {\n        position: {\n          right: '0%'\n        },\n        height: '100%',\n        width: '50%'\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result) {\n          this.getFacilityData();\n        }\n      });\n    }\n\n    addNewFacilityInEdit() {\n      this.selectedFacilityInfoCopy = JSON.parse(JSON.stringify(this.selectedFacilityInfo));\n      this.selectedFacilityInfo = null;\n      this.addForm.controls.practiceInformationList.reset();\n      this.addFacilityView = true;\n      this.addForm.controls.practiceInformationList.controls.billingProviderType.setValue('Group');\n      this.addForm.markAsUntouched();\n      this.addForm.controls.demographicInformation.markAsUntouched();\n      this.addForm.controls.practiceInformationList.markAsUntouched();\n      this.addForm.controls.practiceInformationList.controls.facilityDetails.markAsUntouched();\n    }\n\n    getPrivilegesByRole() {\n      const privielagesDetails = this.globalApi.getPrivilegesByRole();\n      this.isAddNewFacilityBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_AddFacility).length > 0 ? true : false;\n      this.isCreateProviderBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_AddProvider).length > 0 ? true : false;\n      this.isViewEditProviderBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.ProviderVilla_ProviderBridge_EditProvider).length > 0 ? true : false;\n    }\n\n  }\n\n  AddProviderComponent.ɵfac = function AddProviderComponent_Factory(t) {\n    return new (t || AddProviderComponent)(i0.ɵɵdirectiveInject(i1.SubjectService), i0.ɵɵdirectiveInject(i2.MasterdataService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.ProviderManagementService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i6.MasterSharedService), i0.ɵɵdirectiveInject(i7.MatDialog), i0.ɵɵdirectiveInject(i8.CacheService), i0.ɵɵdirectiveInject(i9.DatePipe), i0.ɵɵdirectiveInject(i10.GlobalService));\n  };\n\n  AddProviderComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddProviderComponent,\n    selectors: [[\"app-add-provider\"]],\n    decls: 82,\n    vars: 42,\n    consts: [[1, \"container-fluid\", \"add-provider\"], [1, \"create-claim-title\", 2, \"color\", \"#023781\", \"font-weight\", \"bold\", \"margin-left\", \"6px\"], [1, \"mat-card\", \"mt-3\", \"create-claim-form-styles\", \"p-2\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-12\"], [1, \"f-size16\", \"menuitemschaild\"], [1, \"col\"], [1, \"dashboard-label\"], [\"type\", \"text\", \"placeholder\", \"NPI\", \"numbersOnly\", \"\", \"formControlName\", \"npiNumber\", \"maxlength\", \"10\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Last Name\", \"formControlName\", \"lastName\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"First Name\", \"formControlName\", \"firstName\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Middle Name\", \"formControlName\", \"middleName\", 1, \"form-control\"], [\"bindValue\", \"keyItem\", \"bindLabel\", \"keyItem\", \"placeholder\", \"Gender\", \"formControlName\", \"gender\", \"aria-placeholder\", \"Gender\", 1, \"form-control\", 3, \"virtualScroll\", \"items\"], [\"facilityTypeSelect\", \"\"], [1, \"row\", \"pt-5\"], [\"bindValue\", \"taxonomyDescription\", \"bindLabel\", \"taxonomyDescription\", \"placeholder\", \"Select Specialty\", \"formControlName\", \"specialtyName\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"specialtySelect\", \"\"], [\"bindValue\", \"payerName\", \"bindLabel\", \"payerName\", \"placeholder\", \"Select Plan\", \"formControlName\", \"planName\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"planSelect\", \"\"], [\"bindValue\", \"name\", \"bindLabel\", \"name\", \"placeholder\", \"Select Primary IPA\", \"formControlName\", \"ipaName\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"ipaSelect\", \"\"], [\"bindValue\", \"groupname\", \"bindLabel\", \"groupname\", \"placeholder\", \"Select Group\", \"formControlName\", \"groupName\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"groupSelect\", \"\"], [\"type\", \"text\", \"readonly\", \"\", \"placeholder\", \"Group Tax ID\", \"formControlName\", \"groupTaxId\", \"name\", \"taxId\", \"id\", \"taxId\", 1, \"form-control\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"row\", \"pt-2\"], [1, \"col-md-6\"], [1, \"col-md-6\", \"d-flex\", \"justify-content-end\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"primary-btn\", \"btn-height\"], [1, \"fa\", \"fa-floppy-o\", \"icon-margin\"], [\"type\", \"button\", 1, \"btn-white\", \"btn-common\", \"common-btn\", \"btn-height\", 3, \"click\"], [1, \"invalid-feedback\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"ml-10\", \"primary-btn\", \"btn-height\", 3, \"click\"], [1, \"material-icons\", \"icon-margin\", \"icon-height\", \"icon-align\"], [\"bindValue\", \"facilityDescription\", \"bindLabel\", \"facilityDescription\", \"placeholder\", \"Select Facility\", \"formControlName\", \"facilityDescription\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"facilitySelect\", \"\"], [\"class\", \"col-md-12 row\", 4, \"ngIf\"], [1, \"col-md-4\"], [\"aria-label\", \"Select an option\", \"class\", \"form-control radio-form-control\", \"formControlName\", \"contractType\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"effectiveDate\", \"name\", \"effectiveDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", 3, \"max\", \"matDatepicker\", \"click\"], [\"effectiveDate\", \"\"], [\"matInput\", \"\", \"formControlName\", \"terminationDate\", \"name\", \"terminationDate\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", 3, \"min\", \"matDatepicker\", \"click\"], [\"terminationDate\", \"\"], [1, \"col-md-10\"], [\"aria-label\", \"Select an option\", \"class\", \"form-control radio-form-control\", \"formControlName\", \"billingProviderType\", 3, \"ngClass\", \"change\", 4, \"ngIf\"], [1, \"col-md-2\"], [\"type\", \"checkbox\", \"style\", \"margin-right: 0.7rem;\", 3, \"checked\", \"change\", 4, \"ngIf\"], [\"class\", \"dashboard-label\", 4, \"ngIf\"], [1, \"col-md-3\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Blling Provider\", \"class\", \"form-control\", \"name\", \"BillingProvider\", \"id\", \"BillingProvider\", \"formControlName\", \"billingProviderName\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control\", \"bindValue\", \"groupname\", \"bindLabel\", \"groupname\", \"placeholder\", \"Select\", \"formControlName\", \"billingProviderName\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\", 4, \"ngIf\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Tax Id\", \"formControlName\", \"billingTaxId\", \"name\", \"Billing\", \"id\", \"Billing\", 1, \"form-control\", 3, \"ngClass\", \"readonly\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"formControlName\", \"printOnClaim\", \"placeholder\", \"Print on Claim\", \"class\", \"form-control\", \"name\", \"Claim\", \"id\", \"Claim\", 4, \"ngIf\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Address Line 1\", \"formControlName\", \"billingProviderAddressLine1\", \"class\", \"form-control\", \"name\", \"Address1\", \"id\", \"Address1\", 3, \"ngClass\", \"readonly\", 4, \"ngIf\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Address Line 2\", \"formControlName\", \"billingProviderAddressLine2\", \"class\", \"form-control\", \"name\", \"Address2\", \"id\", \"Address2\", 3, \"readonly\", 4, \"ngIf\"], [\"type\", \"text\", \"readonly\", \"\", \"placeholder\", \"City\", \"class\", \"form-control\", \"name\", \"BillingProviderCity\", \"id\", \"BillingProviderCity\", \"formControlName\", \"billingProviderCity\", 3, \"ngClass\", 4, \"ngIf\"], [\"appendTo\", \"body\", \"class\", \"form-control\", \"bindValue\", \"cityName\", \"bindLabel\", \"cityName\", \"placeholder\", \"Select City\", \"formControlName\", \"billingProviderCity\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\", 4, \"ngIf\"], [\"type\", \"text\", \"readonly\", \"\", \"placeholder\", \"State\", \"class\", \"form-control\", \"name\", \"billingProviderState\", \"id\", \"billingProviderState\", \"formControlName\", \"billingProviderState\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control\", \"placeholder\", \"Select State\", \"bindLabel\", \"stateCode\", \"bindValue\", \"stateCode\", \"formControlName\", \"billingProviderState\", 3, \"virtualScroll\", \"items\", \"ngClass\", 4, \"ngIf\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Zip Code\", \"numbersOnly\", \"\", \"formControlName\", \"billingProviderZipCode\", \"name\", \"Zip\", \"id\", \"Zip\", \"maxlength\", \"9\", 1, \"form-control\", 3, \"ngClass\", \"readonly\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Contact Person Name\", \"class\", \"form-control\", \"name\", \"Contact\", \"id\", \"Contact\", \"formControlName\", \"billingProviderContactPersonName\", 3, \"ngClass\", 4, \"ngIf\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Phone Number\", \"name\", \"Phone\", \"id\", \"Phone\", \"numbersOnly\", \"\", \"formControlName\", \"billingProviderContactPhone\", \"maxlength\", \"10\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Fax Number\", \"formControlName\", \"billingProviderContactFax\", \"numbersOnly\", \"\", \"name\", \"Fax\", \"maxlength\", \"10\", \"id\", \"Fax\", 1, \"form-control\"], [\"type\", \"email\", \"autocomplete\", \"off\", \"placeholder\", \"Email\", \"name\", \"Email\", \"id\", \"Email\", \"formControlName\", \"billingProviderContactEmail\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-md-12\", \"row\"], [\"type\", \"text\", \"readonly\", \"\", \"formControlName\", \"facilityAddress1\", \"name\", \"facilityAddress1\", \"placeholder\", \"Address Line 1\", 1, \"form-control\"], [\"type\", \"text\", \"readonly\", \"\", \"formControlName\", \"facilityAddress2\", \"name\", \"facilityAddress2\", \"placeholder\", \"Address Line 2\", 1, \"form-control\"], [\"type\", \"text\", \"readonly\", \"\", \"formControlName\", \"facilityCity\", \"name\", \"facilityCity\", \"placeholder\", \"City\", 1, \"form-control\"], [\"type\", \"text\", \"readonly\", \"\", \"formControlName\", \"facilityState\", \"name\", \"facilityState\", \"placeholder\", \"State\", 1, \"form-control\"], [\"type\", \"text\", \"readonly\", \"\", \"formControlName\", \"facilityCounty\", \"name\", \"facilityCounty\", \"placeholder\", \"County\", 1, \"form-control\"], [\"type\", \"text\", \"readonly\", \"\", \"formControlName\", \"facilityCountry\", \"name\", \"facilityCountry\", \"placeholder\", \"Country\", 1, \"form-control\"], [\"type\", \"text\", \"readonly\", \"\", \"formControlName\", \"facilityZipCode\", \"name\", \"facilityZipCode\", \"placeholder\", \"Zip Code\", 1, \"form-control\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"contractType\", 1, \"form-control\", \"radio-form-control\"], [\"value\", \"CAP\", 2, \"margin-right\", \"10%\"], [\"value\", \"FFS\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"billingProviderType\", 1, \"form-control\", \"radio-form-control\", 3, \"ngClass\", \"change\"], [\"value\", \"Group\", 2, \"margin-right\", \"2%\"], [\"value\", \"Self\"], [\"type\", \"checkbox\", 2, \"margin-right\", \"0.7rem\", 3, \"checked\", \"change\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Blling Provider\", \"name\", \"BillingProvider\", \"id\", \"BillingProvider\", \"formControlName\", \"billingProviderName\", 1, \"form-control\", 3, \"ngClass\"], [\"bindValue\", \"groupname\", \"bindLabel\", \"groupname\", \"placeholder\", \"Select\", \"formControlName\", \"billingProviderName\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"billingProviderSelect\", \"\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"formControlName\", \"printOnClaim\", \"placeholder\", \"Print on Claim\", \"name\", \"Claim\", \"id\", \"Claim\", 1, \"form-control\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Address Line 1\", \"formControlName\", \"billingProviderAddressLine1\", \"name\", \"Address1\", \"id\", \"Address1\", 1, \"form-control\", 3, \"ngClass\", \"readonly\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Address Line 2\", \"formControlName\", \"billingProviderAddressLine2\", \"name\", \"Address2\", \"id\", \"Address2\", 1, \"form-control\", 3, \"readonly\"], [\"type\", \"text\", \"readonly\", \"\", \"placeholder\", \"City\", \"name\", \"BillingProviderCity\", \"id\", \"BillingProviderCity\", \"formControlName\", \"billingProviderCity\", 1, \"form-control\", 3, \"ngClass\"], [\"appendTo\", \"body\", \"bindValue\", \"cityName\", \"bindLabel\", \"cityName\", \"placeholder\", \"Select City\", \"formControlName\", \"billingProviderCity\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"change\"], [\"billingProviderCitySelect\", \"\"], [\"ng-option-tmp\", \"\"], [\"type\", \"text\", \"readonly\", \"\", \"placeholder\", \"State\", \"name\", \"billingProviderState\", \"id\", \"billingProviderState\", \"formControlName\", \"billingProviderState\", 1, \"form-control\", 3, \"ngClass\"], [\"placeholder\", \"Select State\", \"bindLabel\", \"stateCode\", \"bindValue\", \"stateCode\", \"formControlName\", \"billingProviderState\", 1, \"form-control\", 3, \"virtualScroll\", \"items\", \"ngClass\"], [\"billingProviderStateSelect\", \"\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"placeholder\", \"Contact Person Name\", \"name\", \"Contact\", \"id\", \"Contact\", \"formControlName\", \"billingProviderContactPersonName\", 1, \"form-control\", 3, \"ngClass\"]],\n    template: function AddProviderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"label\", 1);\n        i0.ɵɵtext(2, \"Add Provider \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 2)(4, \"form\", 3);\n        i0.ɵɵlistener(\"ngSubmit\", function AddProviderComponent_Template_form_ngSubmit_4_listener() {\n          return ctx.saveProvider();\n        });\n        i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"label\", 6);\n        i0.ɵɵtext(8, \" Demographic Information \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 4)(10, \"div\", 7)(11, \"label\", 8);\n        i0.ɵɵtext(12, \" NPI* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(13, \"input\", 9);\n        i0.ɵɵtemplate(14, AddProviderComponent_div_14_Template, 2, 0, \"div\", 10);\n        i0.ɵɵtemplate(15, AddProviderComponent_div_15_Template, 2, 0, \"div\", 10);\n        i0.ɵɵtemplate(16, AddProviderComponent_div_16_Template, 2, 0, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 7)(18, \"label\", 8);\n        i0.ɵɵtext(19, \" Last Name* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(20, \"input\", 11);\n        i0.ɵɵtemplate(21, AddProviderComponent_div_21_Template, 2, 0, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 7)(23, \"label\", 8);\n        i0.ɵɵtext(24, \" First Name* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(25, \"input\", 12);\n        i0.ɵɵtemplate(26, AddProviderComponent_div_26_Template, 2, 0, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"div\", 7)(28, \"label\", 8);\n        i0.ɵɵtext(29, \" Middle Name \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(30, \"input\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"div\", 7)(32, \"label\", 8);\n        i0.ɵɵtext(33, \" Gender \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(34, \"ng-select\", 14, 15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(36, \"div\", 16)(37, \"div\", 5)(38, \"label\", 6);\n        i0.ɵɵtext(39, \" General Information \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(40, \"div\", 4)(41, \"div\", 7)(42, \"label\", 8);\n        i0.ɵɵtext(43, \" Provider Specialty* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"ng-select\", 17, 18);\n        i0.ɵɵlistener(\"change\", function AddProviderComponent_Template_ng_select_change_44_listener($event) {\n          return ctx.changeSpecialtySelect($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(46, AddProviderComponent_div_46_Template, 2, 0, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(47, \"div\", 7)(48, \"label\", 8);\n        i0.ɵɵtext(49, \" Plan* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"ng-select\", 19, 20);\n        i0.ɵɵlistener(\"change\", function AddProviderComponent_Template_ng_select_change_50_listener($event) {\n          return ctx.changePlanNameSelect($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(52, AddProviderComponent_div_52_Template, 2, 0, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"div\", 7)(54, \"label\", 8);\n        i0.ɵɵtext(55, \" Primary IPA* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(56, \"ng-select\", 21, 22);\n        i0.ɵɵlistener(\"change\", function AddProviderComponent_Template_ng_select_change_56_listener($event) {\n          return ctx.changeIPASelect($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(58, AddProviderComponent_div_58_Template, 2, 0, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(59, \"div\", 7)(60, \"label\", 8);\n        i0.ɵɵtext(61, \" Group* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"ng-select\", 23, 24);\n        i0.ɵɵlistener(\"change\", function AddProviderComponent_Template_ng_select_change_62_listener($event) {\n          return ctx.changeGroupSelect($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(64, AddProviderComponent_div_64_Template, 2, 0, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(65, \"div\", 7)(66, \"label\", 8);\n        i0.ɵɵtext(67, \" Group Tax ID \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(68, \"input\", 25);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(69, \"div\", 16)(70, \"div\", 7)(71, \"label\", 6);\n        i0.ɵɵtext(72, \"Practice Information \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(73, AddProviderComponent_div_73_Template, 101, 52, \"div\", 26);\n        i0.ɵɵelementStart(74, \"div\", 27);\n        i0.ɵɵelement(75, \"div\", 28);\n        i0.ɵɵelementStart(76, \"div\", 29)(77, \"button\", 30);\n        i0.ɵɵelement(78, \"i\", 31);\n        i0.ɵɵtext(79, \"Save\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(80, \"button\", 32);\n        i0.ɵɵlistener(\"click\", function AddProviderComponent_Template_button_click_80_listener() {\n          return ctx.cancel();\n        });\n        i0.ɵɵtext(81, \"Cancel\");\n        i0.ɵɵelementEnd()()()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"formGroup\", ctx.addForm);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(28, _c1, ctx.f[\"npiNumber\"].invalid && ctx.f[\"npiNumber\"].errors && (ctx.isSubmitted || ctx.isCheckNpi)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.npiNumber == null ? null : ctx.f.npiNumber.invalid) && ctx.f.npiNumber.errors.required && (ctx.isSubmitted || ctx.isCheckNpi));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.npiNumber == null ? null : ctx.f.npiNumber.invalid) && ctx.f.npiNumber.errors.minlength && (ctx.isSubmitted || ctx.isCheckNpi));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.npiNumber == null ? null : ctx.f.npiNumber.errors == null ? null : ctx.f.npiNumber.errors.invalidFormat) && ctx.isCheckNpi);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(30, _c1, ctx.f[\"lastName\"].invalid && ctx.f[\"lastName\"].errors && ctx.isSubmitted));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.lastName == null ? null : ctx.f.lastName.invalid) && ctx.f.lastName.errors.required && ctx.isSubmitted);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(32, _c1, ctx.f[\"firstName\"].invalid && ctx.f[\"firstName\"].errors && ctx.isSubmitted));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.firstName == null ? null : ctx.f.firstName.invalid) && ctx.f.firstName.errors.required && ctx.isSubmitted);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.genderData);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.specialtyData)(\"ngClass\", i0.ɵɵpureFunction1(34, _c1, (ctx.f.specialtyName == null ? null : ctx.f.specialtyName.invalid) && ((ctx.f.specialtyName == null ? null : ctx.f.specialtyName.dirty) || (ctx.f.specialtyName == null ? null : ctx.f.specialtyName.touched)) && ctx.isSubmitted));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.specialtyName == null ? null : ctx.f.specialtyName.invalid) && (ctx.f.specialtyName.errors == null ? null : ctx.f.specialtyName.errors[\"required\"]) && ctx.isSubmitted);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.insuranceData)(\"ngClass\", i0.ɵɵpureFunction1(36, _c1, ctx.f.planName.invalid && (ctx.f.planName.dirty || ctx.f.planName.touched) && ctx.f.planName.errors));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.planName == null ? null : ctx.f.planName.errors == null ? null : ctx.f.planName.errors.required) && ((ctx.f.planName == null ? null : ctx.f.planName.dirty) || (ctx.f.planName == null ? null : ctx.f.planName.touched) || ctx.isSubmitted));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.ipaData)(\"ngClass\", i0.ɵɵpureFunction1(38, _c1, ctx.f.ipaName.invalid && (ctx.f.ipaName.dirty || ctx.f.ipaName.touched) && ctx.f.ipaName.errors));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.ipaName == null ? null : ctx.f.ipaName.invalid) && ctx.f.ipaName.errors.required && ctx.isSubmitted);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx.groupsData)(\"ngClass\", i0.ɵɵpureFunction1(40, _c1, ctx.f.groupName.invalid && (ctx.f.groupName.dirty || ctx.f.groupName.touched) && ctx.f.groupName.errors));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (ctx.f.groupName == null ? null : ctx.f.groupName.invalid) && ctx.f.groupName.errors.required && ctx.isSubmitted);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", ctx.isAddNewFacilityBtnShow);\n      }\n    },\n    dependencies: [i9.NgClass, i9.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MaxLengthValidator, i3.FormGroupDirective, i3.FormControlName, i11.MatRadioGroup, i11.MatRadioButton, i12.NgSelectComponent, i12.NgOptionTemplateDirective, i13.OnlyNumberDirective, i14.MatDatepicker, i14.MatDatepickerInput, i9.UpperCasePipe],\n    styles: [\".menuitemschaild[_ngcontent-%COMP%]{font-size:20px}label[_ngcontent-%COMP%]{font-size:15px;padding-left:0!important}\"]\n  });\n  return AddProviderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}