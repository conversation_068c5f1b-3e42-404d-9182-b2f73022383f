<div class="container-fluid add-member" [ngClass]="{'view-form' : mode == 'view'}">
    <!-- add member -->
    <div class="mat-card mt-3 create-claim-form-styles ">
        <div style="width: 100%">
            <label class="create-claim-title mt-15"
                style="color: #023781;font-weight: bold;margin-left: 21px; font-size: 22px;">{{mainHeading}} </label>
            <button *ngIf="mode != 'add' && !showActivityLog && !!memberDataForView" (click)="openActivityLog()"
                type="button" class="btn-primary activity-button primary-btn btn-height"><i
                    class="fa fa-history icon-margin"></i>View Activity Log</button>
            <button *ngIf="mode != 'add' && showActivityLog" (click)="closeActivityLog()" type="button"
                class="btn-common-danger activity-button danger-btn btn-height">Close Activity Log</button>
        </div>
        <form [formGroup]="addForm">
            <div class="mt-3 internal-div" *ngIf="showActivityLog">
                <app-member-activity-log [memberDetails]="memberDataForView"></app-member-activity-log>
            </div>
            <div class=" mt-3 internal-div">
                <!-- start  Member Basic Details -->
                <label class="f-size16 menuitemschaild "> Member Basic Details </label>
                <div class="row menuitemschaild mt-8">
                    <div class="col-md-12">
                        <div class="row f-size13">
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    Subscriber Id*
                                </label>
                                <input type="text" placeholder="Subscriber Id" *ngIf="mode != 'view'"
                                    class="form-control" name="SubscriberId" id="SId" formControlName="subscriberID"
                                    autocomplete="off"
                                    [ngClass]="{ 'invalid-fc': f['subscriberID'].invalid && (f['subscriberID'].dirty || f['subscriberID'].touched) && f['subscriberID'].errors }"
                                    (blur)="checkIfSubscriberIdAlreadyExists()">
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f['subscriberID']?.invalid  && (f['subscriberID'].dirty || f['subscriberID'].touched) && f['subscriberID'].errors">
                                    Subscriber Id is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.subscriberID}}</span>
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    Last Name*
                                </label>
                                <input type="text" formControlName="lastName" *ngIf="mode != 'view'"
                                    placeholder="Last Name" class="form-control" name="Lname" id="LastName"
                                    autocomplete="off"
                                    [ngClass]="{ 'invalid-fc': f['lastName'].invalid && (f['lastName'].dirty || f['lastName'].touched) && f['lastName'].errors }">
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.lastName?.invalid && (f.lastName.dirty || f.lastName.touched) && f.lastName.errors">
                                    Last Name is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.lastName}}</span>
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    First Name*
                                </label>
                                <input type="text" formControlName="firstName" *ngIf="mode != 'view'"
                                    placeholder="First Name" class="form-control" name="Fname" id="FirstName"
                                    autocomplete="off"
                                    [ngClass]="{ 'invalid-fc': f['firstName'].invalid && (f['firstName'].dirty || f['firstName'].touched) && f['firstName'].errors }">
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.firstName?.invalid && (f.firstName.dirty || f.firstName.touched) && f.firstName.errors">
                                    First Name is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.firstName}}</span>
                            </div>
                            <div class="col-md-2">

                                <label class="dashboard-label">
                                    MI
                                </label>
                                <input type="text" formControlName="middleName" *ngIf="mode != 'view'"
                                    placeholder="Middle Initial" class="form-control" name="mi" id="mi"
                                    autocomplete="off">
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.middleName}}</span>
                            </div>

                            <div class="col-md-2">

                                <label class="dashboard-label">
                                    Gender*
                                </label>
                                <select *ngIf="mode !='view'" class="form-select" id="Gender" name="gender"
                                    formControlName="gender" aria-label=".form-select-sm example"
                                    [ngClass]="{ 'invalid-fc': f['gender'].invalid && (f['gender'].dirty || f['gender'].touched) && f['gender'].errors }">
                                    <option hidden [value]="null" selected class="Selected-Option">SELECT</option>
                                    <option *ngFor="let genderOption of genderData">{{genderOption.keyItem}}</option>
                                </select>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.gender?.invalid && (f.gender.dirty || f.gender.touched)  && f.gender.errors">
                                    Gender is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.gender}}</span>
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    Date of Birth*
                                </label>
                                <input matInput class="form-control mat-datepicker-background" [max]="today"
                                    [matDatepicker]="dateOfBirth" autocomplete="off" formControlName="dateOfBirth"
                                    (click)="dateOfBirth.open()" placeholder="MM/DD/YYYY"
                                    [ngClass]="{ 'invalid-fc': f['dateOfBirth'].invalid && (f['dateOfBirth'].dirty || f['dateOfBirth'].touched) && f['dateOfBirth'].errors }">
                                <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                                <mat-datepicker #dateOfBirth></mat-datepicker>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.dateOfBirth?.invalid && (f.dateOfBirth.dirty || f.dateOfBirth.touched) && f.dateOfBirth.errors">
                                    Date of Birth is Required
                                </div>
                                <!-- <input [type]="mode != 'view' ? 'date' : 'text'" class="form-control " formControlName="dateOfBirth"  placeholder="MM-DD-YYYY" max="{{today | date:'yyyy-MM-dd'}}" autocomplete="off"
                            [ngClass]="{ 'invalid-fc': f['dateOfBirth'].invalid && (f['dateOfBirth'].dirty || f['dateOfBirth'].touched) && f['dateOfBirth'].errors }"> -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row menuitemschaild mt-8">
                    <div class="col-md-12">
                        <div class="row f-size13">
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    MBR Eff Date*
                                </label>
                                <input matInput class="form-control mat-datepicker-background"
                                    [max]="getSmallerDate(f['personTerminationDate'].value)"
                                    [matDatepicker]="personEffectiveDate" autocomplete="off"
                                    formControlName="personEffectiveDate" (click)="personEffectiveDate.open()"
                                    placeholder="MM/DD/YYYY"
                                    [ngClass]="{ 'invalid-fc': f['personEffectiveDate'].invalid && (f['personEffectiveDate'].dirty || f['personEffectiveDate'].touched) && f['personEffectiveDate'].errors }">
                                <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                                <mat-datepicker #personEffectiveDate></mat-datepicker>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.personEffectiveDate?.invalid && (f.personEffectiveDate.dirty || f.personEffectiveDate.touched)  && f.personEffectiveDate.errors">
                                    Mbr Eff Date is Required
                                </div>
                                <!-- <input [type]="mode != 'view' ? 'date' : 'text'" class="form-control " formControlName="personEffectiveDate" autocomplete="off" max="{{getSmallerDate(f['personTerminationDate'].value) | date:'yyyy-MM-dd'}}" placeholder="MM-DD-YYYY"
                            [ngClass]="{ 'invalid-fc': f['personEffectiveDate'].invalid && (f['personEffectiveDate'].dirty || f['personEffectiveDate'].touched) && f['personEffectiveDate'].errors }"> -->
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    MBR Term Date*
                                </label>
                                <input matInput class="form-control mat-datepicker-background"
                                    [min]="f['personEffectiveDate'].value" [matDatepicker]="personTerminationDate"
                                    autocomplete="off" formControlName="personTerminationDate"
                                    (click)="personTerminationDate.open()" placeholder="MM/DD/YYYY"
                                    [ngClass]="{ 'invalid-fc': f['personTerminationDate'].invalid && (f['personTerminationDate'].dirty || f['personTerminationDate'].touched) && f['personTerminationDate'].errors }">
                                <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                                <mat-datepicker #personTerminationDate></mat-datepicker>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.personTerminationDate?.invalid &&(f.personTerminationDate.dirty || f.personTerminationDate.touched) &&   f.personTerminationDate.errors">
                                    MBR Term Date is Required
                                </div>
                                <!-- <input [type]="mode != 'view' ? 'date' : 'text'" class="form-control " autocomplete="off" min="{{f['personEffectiveDate'].value | date: 'yyyy-MM-dd'}}"  formControlName="personTerminationDate"  placeholder="MM-DD-YYYY"
                            [ngClass]="{ 'invalid-fc': f['personTerminationDate'].invalid && (f['personTerminationDate'].dirty || f['personTerminationDate'].touched) && f['personTerminationDate'].errors }"> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end Member Basic Details-->

            <div class=" mt-3 internal-div">
                <!-- Member Address Details -->
                <label class=" f-size16 menuitemschaild "> Member Address Details </label>

                <div class="row menuitemschaild mt-8">
                    <div class="col-md-12">
                        <div class="row f-size13">
                            <div class="col-md-3">

                                <label class="dashboard-label">
                                    Address Line 1*
                                </label>
                                <input type="text" autocomplete="off" formControlName="addressLine1"
                                    *ngIf="mode != 'view'" placeholder="Address Line 1" class="form-control"
                                    name="address1" id="address1"
                                    [ngClass]="{ 'invalid-fc': f['addressLine1'].invalid && (f['addressLine1'].dirty || f['addressLine1'].touched) && f['addressLine1'].errors }">
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.addressLine1?.invalid  &&(f.addressLine1.dirty || f.addressLine1.touched)  && f.addressLine1.errors">
                                    Address Line 1 is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.addressLine1}}</span>
                            </div>

                            <div class="col-md-3">

                                <label class="dashboard-label">
                                    Address Line 2
                                </label>
                                <input type="text" autocomplete="off" formControlName="addressLine2"
                                    *ngIf="mode != 'view'" placeholder="Address Line 2" class="form-control"
                                    name="address2" id="address2">
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.addressLine2}}</span>
                            </div>
                            <div class="col-md-4">
                                <label class="dashboard-label">
                                    City*
                                </label>
                                <ng-select *ngIf="mode !='view'" #citySelect [items]="citiesData" class="form-control"
                                    bindValue="cityName" bindLabel="cityName" (change)="changeCitySelect($event)"
                                    placeholder="Type to search city (min 2 characters)" [virtualScroll]="true"
                                    formControlName="city"
                                    [ngClass]="{ 'invalid-fc': f['city'].invalid && (f['city'].dirty || f['city'].touched) && f['city'].errors }"
                                    [typeahead]="citySearchSubject" [minTermLength]="2">
                                    <ng-template ng-option-tmp let-item="item" let-index="index"
                                        let-search="searchTerm">{{item.cityStateCounty}}</ng-template></ng-select>
                                <!--  <input type="text"  formControlName="city" *ngIf="mode != 'view'" placeholder="CITY" class="form-control" name="city" id="city" autocomplete="off"
                            [ngClass]="{ 'invalid-fc is-invalid': f['city'].invalid && (f['city'].dirty || f['city'].touched) && f['city'].errors }">-->
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.city?.invalid  &&(f.city.dirty || f.city.touched) && f.city.errors">
                                    City is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.city}}</span>
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    State*
                                </label>
                                <ng-select *ngIf="mode !='view'" [virtualScroll]="true"
                                    class="form-control uppercase-select" #citySelect [items]="statesData"
                                    bindValue="stateCode" bindLabel="stateName" placeholder="Select State"
                                    formControlName="state"
                                    [ngClass]="{ 'invalid-fc': f['state'].invalid && (f['state'].dirty || f['state'].touched) && f['state'].errors }">
                                </ng-select>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.state?.invalid  &&(f.state.dirty || f.state.touched) && f.state.errors">
                                    State is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.state}}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row menuitemschaild mt-8">
                    <div class="col-md-12">
                        <div class="row f-size13">
                            <div class="col-md-3">
                                <label class="dashboard-label">
                                    County*
                                </label>
                                <ng-select *ngIf="mode !='view'" [virtualScroll]="true" #countySelect
                                    [items]="countiesData" class="form-control uppercase-select" bindValue="countyName"
                                    bindLabel="countyName" (change)="changeCountySelect($event)"
                                    placeholder="Select County" formControlName="county"
                                    [ngClass]="{ 'invalid-fc': f['county'].invalid && (f['county'].dirty || f['county'].touched) && f['county'].errors }"></ng-select>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.county?.invalid &&(f.county.dirty || f.county.touched)  && f.county.errors">
                                    County is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.county}}</span>
                            </div>
                            <div class="col-md-3">
                                <label class="dashboard-label">
                                    Country*
                                </label>
                                <ng-select *ngIf="mode !='view'" [virtualScroll]="true" #countrySelect
                                    [items]="countriesData" class="form-control uppercase-select" bindValue="name"
                                    bindLabel="name" (change)="changeCountrySelect($event)" placeholder="Select Country"
                                    formControlName="country"
                                    [ngClass]="{ 'invalid-fc': f['country'].invalid && (f['country'].dirty || f['country'].touched) && f['country'].errors }"></ng-select>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.country?.invalid  &&(f.country.dirty || f.country.touched)  && f.country.errors">
                                    Country is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.country}}</span>
                            </div>
                            <div class="col-md-2">

                                <label class="dashboard-label">
                                    Zip Code*
                                </label>
                                <input type="text" autocomplete="off" placeholder="Zip Code" class="form-control"
                                    minlength="9" maxlength="9" name="zip" id="zip" (blur)="zipCodeOnBlur($event)"
                                    formControlName="zipCode" numbersOnly
                                    [ngClass]="{ 'invalid-fc': f['zipCode'].invalid && (f['zipCode'].dirty || f['zipCode'].touched) && f['zipCode'].errors }">
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.zipCode?.invalid &&(f.zipCode.dirty || f.zipCode.touched) && f.zipCode.errors.required">
                                    Zip Code is Required
                                </div>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.zipCode?.invalid && (f.zipCode.dirty || f.zipCode.touched)  && !f.zipCode.errors.required && f.zipCode.errors">
                                    Invalid Zip Code. Please enter only 9 digit number.
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    Phone Number*
                                </label>
                                <input type="text" autocomplete="off" numbersOnly placeholder="Phone Number"
                                    minlength="10" maxlength="10" class="form-control" name="phone" id="phone"
                                    formControlName="number"
                                    [ngClass]="{ 'invalid-fc': f['number'].invalid && (f['number'].dirty || f['number'].touched) && f['number'].errors }">
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.number?.invalid   &&(f.number.dirty || f.number.touched) && f.number.errors.required && f.number.errors">
                                    Phone Number is Required
                                </div>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.number?.invalid   &&(f.number.dirty || f.number.touched) && !f.number.errors.required && f.number.errors">
                                    Please enter only 10 digit number.
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    Addr Eff Date*
                                </label>
                                <input matInput class="form-control mat-datepicker-background"
                                    [max]="getSmallerDate(f['addressTerminationDate'].value)"
                                    [matDatepicker]="addressEffectiveDate" autocomplete="off"
                                    formControlName="addressEffectiveDate" (click)="addressEffectiveDate.open()"
                                    placeholder="MM/DD/YYYY"
                                    [ngClass]="{ 'invalid-fc': f['addressEffectiveDate'].invalid && (f['addressEffectiveDate'].dirty || f['addressEffectiveDate'].touched) && f['addressEffectiveDate'].errors }">
                                <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                                <mat-datepicker #addressEffectiveDate></mat-datepicker>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.addressEffectiveDate?.invalid && (f.addressEffectiveDate.dirty || f.addressEffectiveDate.touched) && f.addressEffectiveDate.errors.required && f.addressEffectiveDate.errors">
                                    Addr Eff Date is Required
                                </div>
                                <!-- <input [type]="mode != 'view' ? 'date' : 'text'" autocomplete="off" formControlName="addressEffectiveDate" class="form-control " max="{{getSmallerDate(f['addressTerminationDate'].value) | date:'yyyy-MM-dd'}}" placeholder="MM-DD-YYYY"
                               [ngClass]="{ 'invalid-fc': f['addressEffectiveDate'].invalid && (f['addressEffectiveDate'].dirty || f['addressEffectiveDate'].touched) && f['addressEffectiveDate'].errors }"> -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row menuitemschaild mt-8">
                    <div class="col-md-12">
                        <div class="row f-size13">
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    Addr Term Date*
                                </label>
                                <input matInput class="form-control mat-datepicker-background"
                                    [min]="f['addressEffectiveDate'].value" [matDatepicker]="addressTerminationDate"
                                    autocomplete="off" formControlName="addressTerminationDate"
                                    (click)="addressTerminationDate.open()" placeholder="MM/DD/YYYY"
                                    [ngClass]="{ 'invalid-fc': f['addressTerminationDate'].invalid && (f['addressTerminationDate'].dirty || f['addressTerminationDate'].touched) && f['addressTerminationDate'].errors }">
                                <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                                <mat-datepicker #addressTerminationDate></mat-datepicker>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.addressTerminationDate?.invalid  &&(f.addressTerminationDate.dirty || f.addressTerminationDate.touched)&& f.addressTerminationDate.errors.required && f.addressTerminationDate.errors">
                                    Addr Term Date Required
                                </div>
                                <!-- <input [type]="mode != 'view' ? 'date' : 'text'" autocomplete="off" class="form-control" min="{{f['addressEffectiveDate'].value | date: 'yyyy-MM-dd'}}" formControlName="addressTerminationDate" placeholder="MM-DD-YYYY"
                              [ngClass]="{ 'invalid-fc': f['addressTerminationDate'].invalid && (f['addressTerminationDate'].dirty || f['addressTerminationDate'].touched) && f['addressTerminationDate'].errors }"> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class=" mt-3 internal-div">
                <!-- my code -->
                <label class=" f-size16 menuitemschaild "> Insurance Details </label>
                <div class="row menuitemschaild mt-8">
                    <div class="col-md-12">
                        <div class="row f-size13">
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    Patient Relationship*
                                </label>
                                <input type="text" autocomplete="off" placeholder="Patient Relationship"
                                    formControlName="relationshipName" class="form-control" name="PatientRelationship"
                                    id="pRelationship">
                            </div>
                            <div class="col-md-3">
                                <label class="dashboard-label">
                                    Insurance Company*
                                </label>
                                <ng-select *ngIf="mode !='view'" [virtualScroll]="true" #insuranceCompanySelect
                                    [items]="insuranceCompaniesData" class="form-control uppercase-select"
                                    bindValue="payerName" bindLabel="payerName"
                                    (change)="changeInsuranceCompanySelect($event)"
                                    placeholder="Select Insurance Company" formControlName="insuranceCompanyName"
                                    [ngClass]="{ 'invalid-fc': f['insuranceCompanyName'].invalid && (f['insuranceCompanyName'].dirty || f['insuranceCompanyName'].touched) && f['insuranceCompanyName'].errors }"></ng-select>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.insuranceCompanyName?.invalid  &&(f.insuranceCompanyName.dirty || f.insuranceCompanyName.touched)&& f.insuranceCompanyName.errors.required && f.insuranceCompanyName.errors">
                                    Insurance Company is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.insuranceCompanyName}}</span>
                            </div>
                            <div class="col-md-3">
                                <label class="dashboard-label">
                                    Plan Name*
                                </label>
                                <ng-select *ngIf="mode !='view'" [virtualScroll]="true"
                                    (click)="checkIfPlanNameCanBeSelected()" #planNameSelect [items]="planData"
                                    class="form-control uppercase-select" bindValue="planName" bindLabel="planName"
                                    (change)="changePlanNameSelect($event)" placeholder="Select Plan Name"
                                    formControlName="planName"
                                    [ngClass]="{ 'invalid-fc': f['planName'].invalid && (f['planName'].dirty || f['planName'].touched) && f['planName'].errors }"></ng-select>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.planName?.invalid  &&(f.planName.dirty || f.planName.touched)&& f.planName.errors.required && f.planName.errors">
                                    Plan Name is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.planName}}</span>
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    Plan Eff Date*
                                </label>
                                <input matInput class="form-control mat-datepicker-background"
                                    [max]="getSmallerDate(f['planTerminationDate'].value)"
                                    [matDatepicker]="planEffectiveDate" autocomplete="off"
                                    formControlName="planEffectiveDate" (click)="planEffectiveDate.open()"
                                    placeholder="MM/DD/YYYY"
                                    [ngClass]="{ 'invalid-fc': f['planEffectiveDate'].invalid && (f['planEffectiveDate'].dirty || f['planEffectiveDate'].touched) && f['planEffectiveDate'].errors }">
                                <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                                <mat-datepicker #planEffectiveDate></mat-datepicker>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.planEffectiveDate?.invalid && (f.planEffectiveDate.dirty || f.planEffectiveDate.touched) && f.planEffectiveDate.errors.required && f.planEffectiveDate.errors">
                                    Plan Eff Date is Required
                                </div>
                                <!-- <input [type]="mode != 'view' ? 'date' : 'text'" autocomplete="off" class="form-control " formControlName="planEffectiveDate" max="{{getSmallerDate(f['planTerminationDate'].value) | date:'yyyy-MM-dd'}}" placeholder="MM-DD-YYYY"
                            [ngClass]="{ 'invalid-fc': f['planEffectiveDate'].invalid && (f['planEffectiveDate'].dirty || f['planEffectiveDate'].touched) && f['planEffectiveDate'].errors }"> -->
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    Plan Term Date*
                                </label>
                                <input matInput class="form-control mat-datepicker-background"
                                    [min]="f['planEffectiveDate'].value" [matDatepicker]="planTerminationDate"
                                    autocomplete="off" formControlName="planTerminationDate"
                                    (click)="planTerminationDate.open()" placeholder="MM/DD/YYYY"
                                    [ngClass]="{ 'invalid-fc': f['planTerminationDate'].invalid && (f['planTerminationDate'].dirty || f['planTerminationDate'].touched) && f['planTerminationDate'].errors }">
                                <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                                <mat-datepicker #planTerminationDate></mat-datepicker>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.planTerminationDate?.invalid && (f.planTerminationDate.dirty || f.planTerminationDate.touched) && f.planTerminationDate.errors.required && f.planTerminationDate.errors">
                                    Plan Term Date is Required
                                </div>
                                <!-- <input [type]="mode != 'view' ? 'date' : 'text'" autocomplete="off" class="form-control" min="{{f['planEffectiveDate'].value | date: 'yyyy-MM-dd'}}" formControlName="planTerminationDate" placeholder="MM-DD-YYYY"
                            [ngClass]="{ 'invalid-fc': f['planTerminationDate'].invalid && (f['planTerminationDate'].dirty || f['planTerminationDate'].touched) && f['planTerminationDate'].errors }"> -->
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <!-- end Member Address Details -->
            <!-- start PCP Details -->
            <div class=" mt-3 internal-div">
                <label class=" f-size16 menuitemschaild "> PCP Details </label>
                <div class="row menuitemschaild mt-8">
                    <div class="col-md-12">
                        <div class="row f-size13">
                            <div class="col-md-4">
                                <label class="dashboard-label">
                                    IPA Name*
                                </label>
                                <ng-select *ngIf="mode !='view'" [virtualScroll]="true" #ipaSelect [items]="ipaData"
                                    class="form-control uppercase-select" bindValue="name" bindLabel="name"
                                    (change)="changeIPASelect($event)" placeholder="Select Account"
                                    formControlName="ipaName"
                                    [ngClass]="{ 'invalid-fc': f['ipaName'].invalid && (f['ipaName'].dirty || f['ipaName'].touched) && f['ipaName'].errors }"></ng-select>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.ipaName?.invalid && (f.ipaName.dirty || f.ipaName.touched) && f.ipaName.errors.required && f.ipaName.errors">
                                    IPA Name is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.ipaName}}</span>
                            </div>
                            <div class="col-md-4">
                                <label class="dashboard-label">
                                    PCP Name*
                                </label>
                                <ng-select *ngIf="mode !='view'" [virtualScroll]="true"
                                    (click)="checkIfPCPNameCanBeSelected()" #pcpNameSelect [items]="pcpData"
                                    class="form-control uppercase-select" bindValue="fullName" bindLabel="fullName"
                                    (change)="changePCPNameSelect($event)" placeholder="Select PCP Name"
                                    formControlName="pcpName"
                                    [ngClass]="{ 'invalid-fc': f['pcpName'].invalid && (f['pcpName'].dirty || f['pcpName'].touched) && f['pcpName'].errors }"></ng-select>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.pcpName?.invalid && (f.pcpName.dirty || f.pcpName.touched) && f.pcpName.errors.required && f.pcpName.errors">
                                    PCP Name is Required
                                </div>
                                <span class="form-control"
                                    *ngIf="mode =='view' && !!memberDataForView">{{memberDataForView.pcpName}}</span>
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    PCP Eff From*
                                </label>
                                <input matInput class="form-control mat-datepicker-background"
                                    [max]="getSmallerDate(f['pcpTerminationDate'].value)"
                                    [matDatepicker]="pcpEffectiveDate" autocomplete="off"
                                    formControlName="pcpEffectiveDate" (click)="pcpEffectiveDate.open()"
                                    placeholder="MM/DD/YYYY"
                                    [ngClass]="{ 'invalid-fc': f['pcpEffectiveDate'].invalid && (f['pcpEffectiveDate'].dirty || f['pcpEffectiveDate'].touched) && f['pcpEffectiveDate'].errors }">
                                <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                                <mat-datepicker #pcpEffectiveDate></mat-datepicker>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.pcpEffectiveDate?.invalid && (f.pcpEffectiveDate.dirty || f.pcpEffectiveDate.touched) && f.pcpEffectiveDate.errors.required && f.pcpEffectiveDate.errors">
                                    PCP Eff From is Required
                                </div>
                                <!-- <input [type]="mode != 'view' ? 'date' : 'text'" autocomplete="off" class="form-control " formControlName="pcpEffectiveDate" max="{{getSmallerDate(f['pcpTerminationDate'].value) | date:'yyyy-MM-dd'}}" placeholder="MM-DD-YYYY"
                            [ngClass]="{ 'invalid-fc': f['pcpEffectiveDate'].invalid && (f['pcpEffectiveDate'].dirty || f['pcpEffectiveDate'].touched) && f['pcpEffectiveDate'].errors }"> -->
                            </div>
                            <div class="col-md-2">
                                <label class="dashboard-label">
                                    PCP Eff To*
                                </label>
                                <input matInput class="form-control mat-datepicker-background"
                                    [min]="f['pcpEffectiveDate'].value" [matDatepicker]="pcpTerminationDate"
                                    autocomplete="off" formControlName="pcpTerminationDate"
                                    (click)="pcpTerminationDate.open()" placeholder="MM/DD/YYYY"
                                    [ngClass]="{ 'invalid-fc': f['pcpTerminationDate'].invalid && (f['pcpTerminationDate'].dirty || f['pcpTerminationDate'].touched) && f['pcpTerminationDate'].errors }">
                                <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                                <mat-datepicker #pcpTerminationDate></mat-datepicker>
                                <div class="invalid-feedback invalid-show"
                                    *ngIf="f.pcpTerminationDate?.invalid && (f.pcpTerminationDate.dirty || f.pcpTerminationDate.touched) && f.pcpTerminationDate.errors.required && f.pcpTerminationDate.errors">
                                    PCP Eff To is Required
                                </div>
                                <!-- <input [type]="mode != 'view' ? 'date' : 'text'" autocomplete="off" class="form-control" min="{{f['pcpEffectiveDate'].value | date: 'yyyy-MM-dd'}}" formControlName="pcpTerminationDate" placeholder="MM-DD-YYYY"
                            [ngClass]="{ 'invalid-fc': f['pcpTerminationDate'].invalid && (f['pcpTerminationDate'].dirty || f['pcpTerminationDate'].touched) && f['pcpTerminationDate'].errors }"> -->
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <!--end PCP Details -->
            <!-- Attachments -->
            <!-- <div class=" mt-3 internal-div">
                    <label class=" f-size16 menuitemschaild "> Attachments </label>
                    <div class="dashboard-content mt-2" *ngIf="!!memberDataForView && attachments.length ==0 && mode == 'view'">
                        <div class="content">
                          <p>No attachment Available for selected Member.</p>
                        </div>
                      </div>
                    <div class="row f-size13 menuitemschaild mt-8" *ngIf="attachments.length > 0">
                          <div class="col-md-3">
                              <label class="dashboard-label">
                               Document Name
                              </label>
                          </div>
                          <div class="col-md-3">
                              <label class="dashboard-label">
                               Document Type
                              </label>
                           </div>
                          <div class="col-md-3">
                           <label class="dashboard-label">
                              Upload Document
                           </label>
                       </div>
                       <div class="col-md-1 ">
                           <label class="dashboard-label">
                               Preview
                            </label>
                       </div>
                       <div class="col-md-2" *ngIf="mode != 'view'">
                       <label class="dashboard-label">
                           Action
                        </label>
                       </div>
                    </div>
                    <div *ngIf="mode == 'view'">
                        <div class="row f-size13 menuitemschaild mt-8" *ngFor="let attachment of attachments; let index = index">
                            <div class="col-md-3">
                                <span class="form-control">{{attachment['title']}}</span>
                            </div>
                            <div class="col-md-3">
                                <span class="form-control">{{attachment['categoryName']}}</span>
                            </div>
                            <div class="col-md-3">
                                <span class="form-control">{{attachment['description']}}</span>
                            </div>
                            <div class="col-md-1 ">
                                    <button type="button" class="btn btn-primary btn-sm wdt-35"> 
                                    <i class="fa fa-eye" aria-hidden="true"></i></button>
                            </div>
                            </div>
                    </div>
                    <div *ngIf="mode != 'view'">
                        <div class="row f-size13 menuitemschaild mt-8" *ngFor="let attachment of attachments; let index = index">
                        <div class="col-md-3">
                            <select class="form-select" *ngIf="!(!!attachment.documentId)" placeholder="Title"  id="title" name="title" [ngModelOptions]="{standalone: true}" [(ngModel)]="attachment['title']" aria-label=".form-select-sm example">  
                                <option *ngFor="let type of attachmentTypesData">{{type.keyItem}}</option> </select>
                            <span class="attachment-form-control form-control" *ngIf="(!!attachment.documentId)">{{attachment['title']}}</span>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" placeholder="Type" *ngIf="!(!!attachment.documentId)" id="categoryName" name="categoryName" [ngModelOptions]="{standalone: true}" [(ngModel)]="attachment['categoryName']" aria-label=".form-select-sm example">  
                                <option *ngFor="let type of attachmentTypesData">{{type.keyItem}}</option> </select>
                            <span class="attachment-form-control form-control" *ngIf="(!!attachment.documentId)">{{attachment['categoryName']}}</span>
                            </div>
                        <div class="col-md-3">
                            <input type="file" autocomplete="off" class="form-control" *ngIf="!(!!attachment.documentId)" accept=".jpeg, .pdf, .png, .jpg" (change)="checkFileValidity($event, attachment)" title="Only jpeg, pdf, png, jpg files are allowed">
                            <span class="attachment-form-control form-control" *ngIf="(!!attachment.documentId)">{{attachment['description']}}</span>
                        </div>
                        <div class="col-md-1 ">
                                <button type="button" class="btn btn-primary btn-sm form-control wdt-35" (click)="preview(attachment)" [disabled]="!(!!attachment.document)"> 
                                <i class="fa fa-eye" aria-hidden="true"></i></button>
                        </div>
                        <div class="col-md-2" *ngIf="mode != 'view'">
                        <button type="button" *ngIf="attachments.length - 1 == index" [disabled]="checkIfAddAttachmentDisable()" style="pointer-events: initial !important;" [title]="checkIfAddAttachmentDisable() ? 'Please add document(s) for all earlier attachments or remove earlier attachments to add new attachment.' : ''" (click)="addNewAttachment()" class="btn btn-primary btn-sm form-control wdt-35 " > 
                            <i class="fa fa-plus" aria-hidden="true"></i></button>
                        <button type="button" *ngIf="attachments.length > 1" (click)="removeAttachment(index)" class="btn btn-primary btn-sm form-control wdt-35 " > 
                            <i class="fa fa-minus" aria-hidden="true"></i></button>
                        </div>
                        </div>
                    </div>
                </div> -->
            <!-- <iframe [src]="url | sanitizeUrl" *ngIf="previewAvailable"></iframe> -->
            <!--end Attachments -->
            <div class="row menuitemschaild mt-8 bottom-div">
                <div class="col-md-6"></div>
                <div class="col-md-6 text-align-R">
                    <button type="button" class="btn btn-primary primary-btn btn-height" *ngIf="mode == 'add'"
                        (click)="addMember()"><i class="fa fa-floppy-o icon-margin"></i>Save</button>
                    <button type="button" class="btn btn-primary primary-btn btn-height" *ngIf="mode == 'edit'"
                        (click)="addMember()"><i
                            class="material-icons icon-margin icon-height icon-align">save_as</i>Update</button>
                    <button type="button" class="btn btn-primary primary-btn btn-height"
                        *ngIf="isViewEditBtnShow && mode == 'view'" (click)="openEditMember()"><i
                            class="material-icons icon-margin icon-height icon-align">edit</i>Edit</button>
                    <button type="button" class="btn-white btn-common common-btn btn-height"
                        (click)="cancel()">Cancel</button>
                </div>
            </div>
        </form>
    </div>
</div>