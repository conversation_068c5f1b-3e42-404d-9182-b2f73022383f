{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { BillingProviderFinalResult } from 'src/app/models/ClaimForm/Providers/BillingProviderResult';\nimport { FfsSearchRadioRenderer } from 'src/app/shared/Renderer/create-claim/FfsSearchRadioRenderer';\nimport * as _ from 'lodash';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"src/app/services/billing-provider/billing-provider.service\";\nimport * as i3 from \"src/app/services/UserAuthentication/UserAuthentication.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"ag-grid-angular\";\nimport * as i6 from \"@angular/material/button\";\nexport let BillingProviderPopupComponent = /*#__PURE__*/(() => {\n  class BillingProviderPopupComponent {\n    constructor(dialogRef, billingProviderService, userAuthenticationService) {\n      this.dialogRef = dialogRef;\n      this.billingProviderService = billingProviderService;\n      this.userAuthenticationService = userAuthenticationService;\n      this.billingProviderGridOptions = {};\n      this.paginationPageSize = 25;\n      this.billingProviderCriteria = {};\n      this.billingProviderResult = [];\n      this.billingProviderResultOrig = [];\n      this.destroy$ = new Subject();\n      this.columnDefs = [{\n        headerName: 'NPI',\n        width: 200,\n        resizable: true,\n        field: 'npi',\n        tooltipField: \"npi\"\n      }, //{ headerName: 'Name', width: 180, resizable: true, field: 'name', tooltipField: 'name', sortable: true },\n      {\n        headerName: 'Address',\n        width: 180,\n        resizable: true,\n        field: 'address',\n        tooltipField: \"address\"\n      }, {\n        headerName: 'Tax ID',\n        width: 250,\n        resizable: true,\n        tooltipField: \"taxId\",\n        field: 'taxId'\n      }, {\n        headerName: 'IPA Name',\n        width: 220,\n        resizable: true,\n        field: 'ipaName',\n        tooltipField: \"ipaName\"\n      }, {\n        headerName: 'Action',\n        width: 200,\n        resizable: true,\n        field: 'action',\n        tooltipField: \"action\",\n        headerClass: \"ag-center-header\",\n        cellClass: \"ag-center-cell\",\n        cellRenderer: FfsSearchRadioRenderer\n      }];\n      this.SelectedRow = [];\n      dialogRef.disableClose = true;\n    }\n\n    ngOnDestroy() {\n      this.destroy$.next(true);\n      this.destroy$.unsubscribe();\n    }\n\n    ngOnInit() {\n      this.billingProviderGridOptions = {\n        rowHeight: 30,\n        getRowStyle: this.changeRowColor,\n        defaultColDef: {\n          lockVisible: true,\n          initialWidth: 100,\n          sortable: true,\n          filter: true,\n          floatingFilter: true,\n          suppressMenu: true,\n          wrapText: true,\n          autoHeight: true,\n          floatingFilterComponentParams: {\n            suppressFilterButton: true\n          }\n        },\n        style: {\n          width: '100%',\n          height: '100%',\n          flex: '1 1 auto'\n        },\n        pagination: {\n          enable: true,\n          size: 25\n        },\n        totalRowsCount: 0\n      };\n    }\n\n    changeRowColor(params) {\n      if (params.node.rowIndex % 2 === 0) {\n        return {\n          'background-color': '#f1f0f0'\n        };\n      } else {\n        return {\n          'background-color': 'white'\n        };\n      }\n    }\n\n    closeAll() {\n      this.dialogRef.close();\n    }\n\n    fetchBillingproviderResult() {\n      this.billingProviderCriteria.iPACode = \",undefined,,IPA00000001,IPA00000006,IPA00000007,IPA00000008,IPA00000009,,IPA00000004,,IPA00000010,IPA00000011,IPA00000005,\";\n      this.billingProviderCriteria.sortBy = \"ProviderNPI\";\n      this.billingProviderCriteria.sortOrder = \"ASC\";\n      this.billingProviderCriteria.index = 0;\n      this.billingProviderCriteria.limit = 50;\n      this.billingProviderService.fetchBillingProviderResult(this.billingProviderCriteria).pipe(takeUntil(this.destroy$)).subscribe(res => {\n        this.billingProviderResultOrig = res;\n        this.billingProviderResult = [];\n\n        for (let i = 0; i < this.billingProviderResultOrig.length; i++) {\n          let billingProviderItem = new BillingProviderFinalResult();\n          billingProviderItem.taxIdOrSSN = this.billingProviderResultOrig[i].taxIdOrSSN;\n          billingProviderItem.VendorID = this.billingProviderResultOrig[i].vendorID;\n          billingProviderItem.name = this.billingProviderResultOrig[i].payToDetails[0].paytoName;\n          billingProviderItem.address = this.billingProviderResultOrig[i].payToDetails[0].address1;\n          billingProviderItem.taxId = this.billingProviderResultOrig[i].taxIdOrSSN;\n          billingProviderItem.npi = this.billingProviderResultOrig[i].payToDetails[0].payToNPI;\n          billingProviderItem.ipaName = this.billingProviderResultOrig[i].payToDetails[0].ipaDetails[0].ipaName;\n          this.billingProviderResult.push(billingProviderItem);\n        }\n\n        this.billingProviderGridOptions.totalRowsCount = this.billingProviderResult?.length;\n      });\n    }\n\n    cancel() {\n      this.dialogRef.close();\n    }\n\n    onCellClicked(e) {\n      let pos = _.findIndex(this.billingProviderResultOrig, {\n        taxIdOrSSN: e.data.taxIdOrSSN\n      });\n\n      this.SelectedRow[0] = this.billingProviderResultOrig[pos];\n      this.userAuthenticationService.selectedIPA = this.SelectedRow[0].payToDetails[0].ipaDetails[0].ipaCode;\n      if (this.SelectedRow != null && this.SelectedRow.length != 0) this.dialogRef.close(this.SelectedRow);else return;\n    }\n\n    onChange(val) {\n      this.SelectedRow[0] = val;\n      this.SetSelected();\n    }\n\n    SetSelected() {\n      this.userAuthenticationService.selectedIPA = this.SelectedRow[0].payToDetails[0].ipaDetails[0].ipaCode;\n      if (this.SelectedRow != null && this.SelectedRow.length != 0) this.dialogRef.close(this.SelectedRow);else return;\n    }\n\n  }\n\n  BillingProviderPopupComponent.ɵfac = function BillingProviderPopupComponent_Factory(t) {\n    return new (t || BillingProviderPopupComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(i2.BillingProviderService), i0.ɵɵdirectiveInject(i3.UserAuthenticationService));\n  };\n\n  BillingProviderPopupComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BillingProviderPopupComponent,\n    selectors: [[\"app-billing-provider-popup\"]],\n    decls: 38,\n    vars: 14,\n    consts: [[1, \"dailog-header\"], [\"mat-dialog-title\", \"\"], [\"mat-button\", \"\", \"mat-dialog-title\", \"\", 1, \"dailog-close\", 3, \"click\"], [1, \"btn-flex\"], [1, \"row\"], [1, \"col-md-2\"], [\"for\", \"flexCheckDefault\", 1, \"form-check-label\", \"contract-labels\"], [\"type\", \"text\", \"placeholder\", \"NPI\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"Name\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"Address\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"Tax Id\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"IPA Name\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [1, \"mt-2\"], [\"type\", \"button\", 1, \"btn-primary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn-common\", 3, \"click\"], [1, \"mt-4\"], [1, \"row\", \"grid-deal-search\"], [\"id\", \"memberSearchGrid\", 1, \"ag-theme-alpine\", \"ag-grid-view\", 3, \"rowData\", \"columnDefs\", \"accentedSort\", \"gridOptions\", \"pagination\", \"paginationPageSize\", \"overlayNoRowsTemplate\", \"paginationNumberFormatter\", \"enableCellTextSelection\", \"cellClicked\"]],\n    template: function BillingProviderPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\")(2, \"h3\", 1);\n        i0.ɵɵtext(3, \"Billing Providers\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\")(5, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function BillingProviderPopupComponent_Template_button_click_5_listener() {\n          return ctx.closeAll();\n        });\n        i0.ɵɵtext(6, \"X\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"mat-dialog-content\")(8, \"div\", 3)(9, \"div\", 4)(10, \"div\", 5)(11, \"label\", 6);\n        i0.ɵɵtext(12, \"NPI\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"input\", 7);\n        i0.ɵɵlistener(\"ngModelChange\", function BillingProviderPopupComponent_Template_input_ngModelChange_13_listener($event) {\n          return ctx.billingProviderCriteria.payToNPI = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 5)(15, \"label\", 6);\n        i0.ɵɵtext(16, \"Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"input\", 8);\n        i0.ɵɵlistener(\"ngModelChange\", function BillingProviderPopupComponent_Template_input_ngModelChange_17_listener($event) {\n          return ctx.billingProviderCriteria.paytoName = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 5)(19, \"label\", 6);\n        i0.ɵɵtext(20, \"Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"input\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function BillingProviderPopupComponent_Template_input_ngModelChange_21_listener($event) {\n          return ctx.billingProviderCriteria.address = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 5)(23, \"label\", 6);\n        i0.ɵɵtext(24, \"Tax Id\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"input\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function BillingProviderPopupComponent_Template_input_ngModelChange_25_listener($event) {\n          return ctx.billingProviderCriteria.taxID = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 5)(27, \"label\", 6);\n        i0.ɵɵtext(28, \"IPA Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"input\", 11);\n        i0.ɵɵlistener(\"ngModelChange\", function BillingProviderPopupComponent_Template_input_ngModelChange_29_listener($event) {\n          return ctx.billingProviderCriteria.ipaName = $event;\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(30, \"div\", 12)(31, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function BillingProviderPopupComponent_Template_button_click_31_listener() {\n          return ctx.fetchBillingproviderResult();\n        });\n        i0.ɵɵtext(32, \"Search\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function BillingProviderPopupComponent_Template_button_click_33_listener() {\n          return ctx.cancel();\n        });\n        i0.ɵɵtext(34, \"Clear\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(35, \"div\", 15)(36, \"div\", 16)(37, \"ag-grid-angular\", 17);\n        i0.ɵɵlistener(\"cellClicked\", function BillingProviderPopupComponent_Template_ag_grid_angular_cellClicked_37_listener($event) {\n          return ctx.onCellClicked($event);\n        });\n        i0.ɵɵelementEnd()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngModel\", ctx.billingProviderCriteria.payToNPI);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.billingProviderCriteria.paytoName);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.billingProviderCriteria.address);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.billingProviderCriteria.taxID);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.billingProviderCriteria.ipaName);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"rowData\", ctx.billingProviderResult)(\"columnDefs\", ctx.columnDefs)(\"accentedSort\", true)(\"gridOptions\", ctx.billingProviderGridOptions)(\"pagination\", true)(\"paginationPageSize\", ctx.paginationPageSize)(\"overlayNoRowsTemplate\", ctx.overlayNoRowsTemplate)(\"paginationNumberFormatter\", ctx.billingProviderGridOptions.pagination.formatter)(\"enableCellTextSelection\", true);\n      }\n    },\n    dependencies: [i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.AgGridAngular, i6.MatButton, i1.MatDialogTitle, i1.MatDialogContent],\n    styles: [\".grid-deal-search[_ngcontent-%COMP%]{width:100%;flex:1 1 auto;--bs-gutter-x: 0rem!important;height:40vh}.btn-flex[_ngcontent-%COMP%]{display:flex;justify-content:space-between}\"]\n  });\n  return BillingProviderPopupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}