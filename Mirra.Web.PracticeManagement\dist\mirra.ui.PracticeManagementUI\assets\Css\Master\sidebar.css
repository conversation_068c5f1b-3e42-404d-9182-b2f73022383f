/* Side Nav Menu CSS Start */

#sidebar-container {
    max-width: 205px;
    cursor: pointer;
    min-height: 70vh;
    height: 100% !important;
    background-color: #005488;
    padding: 0;
}

.row {
    font-family: 'Poppins-SemiBold';
    position: sticky;
    float: left;
    top: 0;
    left: 0;
    height: 120% !important;
}

.ProductName {
    color: white;
    font-size: 12px;
    margin-top: 5px;
}


/* Sidebar sizes when expanded and expanded */

.sidebar-expanded {
    height: 800px;
}

.sidebar-collapsed {
    height: 100%;
    width: 50px;
}


/* Menu item*/

#sidebar-container .list-group a {
    height: 40px;
    color: white;
    font-size: 12px;
}


/* Submenu item*/

#sidebar-container .list-group .sidebar-submenu a {
    border: none;
    height: 36px;
    padding-left: 20px;
}

.sidebar-submenu {
    font-size: 0.9rem;
}

.Sidebar-Toggler {
    background-color: unset;
    margin-right: -19px;
    ;
}

.SideBar-Toggler-Image {
    height: 25px;
    width: 25px;
}


/* Separators */

.sidebar-separator-title {
    background-color: #005488;
    height: 35px;
}

.sidebar-separator {
    background-color: #005488;
    height: 20px;
}

.logo-separator {
    background-color: rgb(48, 74, 131);
    height: 60px;
}


/* Closed submenu icon */

#sidebar-container .list-group .list-group-item[aria-expanded="false"] .submenu-icon::after {
    display: inline;
    text-align: right;
    padding-left: 10px;
}


/* Opened submenu icon */

#sidebar-container .list-group .list-group-item[aria-expanded="true"] .submenu-icon::after {
    display: inline;
    text-align: right;
    padding-left: 10px;
}

div {
    display: flex;
    justify-content: space-between;
}

.Logo-Li {
    height: 70px;
    cursor: default!important;
}

.Logo-Li:hover {
    background-color: unset!important;
}

.bg-primary-dark {
    background-color: #005488;
}

.list-group-item {
    padding: .5rem 0.4rem 0.5rem 1rem;
}

.list-group-item:hover {
    background-color: #0D3B57;
}

.list-group-item:active {
    color: black;
}

.bg-primary-edited:hover {
    background-color: #0D3B57;
}

.bg-primary-edited {
    background-color: #0074BC;
}

#sidebar-container .list-group a span.align-items-start,
#sidebar-container .list-group a object {
    width: 25px;
    padding: 0 6px 0 0;
}

object {
    pointer-events: none;
}

#sidebar-container .list-group a span.menu-collapsed {
    flex: auto;
    padding-left: 15px;
}


/* Side Nav Menu CSS End */


/* Side Nav Menu CSS End */

.rotated-image {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}

.Parent-Tab {
    font-size: 12px;
}

.W105 {
    width: 105%!important;
}

#collapse-icon {
    cursor: pointer;
}

.shrinkMargin{
    margin: 17px 0 0 0;
}


/* 
@media screen and (max-width: 1400px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }
  .sidebar a {float: left;}
  div.content {margin-left: 0;}
} */

