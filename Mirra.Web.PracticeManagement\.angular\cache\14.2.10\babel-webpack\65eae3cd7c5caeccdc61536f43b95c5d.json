{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { APP_INITIALIZER, NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { EnvServiceProvider } from './shared/services/env/env.service.provider';\nimport { AppInitService } from './shared/services/envappconfig/app-init.service';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { GlobalService } from './shared/services/global.service';\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\nimport { AgGridModule } from 'ag-grid-angular';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { BsDatepickerModule } from 'ngx-bootstrap/datepicker';\nimport { AvatarModule } from 'ngx-avatars';\nimport { ProcessRemitComponent } from './components/process-remit/process-remit.component';\nimport { RemitWorklistComponent } from './components/remit-worklist/remit-worklist.component';\nimport { MenuListItemComponent } from './components/menu-list-item/menu-list-item.component';\nimport { FfsComponent } from './components/create-claim/ffs/ffs.component';\nimport { CapComponent } from './components/create-claim/cap/cap.component';\nimport { AppointmentScheduleComponent } from './components/Patient-Scheduling/appointment-schedule/appointment-schedule.component';\nimport { RemitProcessService } from './services/remit-process/remit-process.service';\nimport { NewAppointmentPopupComponent } from './components/popups/patient-scheduling/new-appointment-popup/new-appointment-popup.component';\nimport { NewPatientPopupComponent } from './components/popups/patient-scheduling/new-patient-popup/new-patient-popup.component';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { AutocompleteLibModule } from 'angular-ng-autocomplete';\nimport { SearchResultPopupComponent } from './components/popups/patient-scheduling/search-result-popup/search-result-popup.component';\nimport { FilterPopupComponent } from './components/popups/dashboard-popups/filter-popup/filter-popup.component';\nimport { BillingProviderPopupComponent } from './components/popups/billing-provider-popup/billing-provider-popup.component';\nimport { RenderingProviderPopupComponent } from './components/rendering-provider-popup/rendering-provider-popup.component';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport { AssignRemitComponent } from './components/remit-worklist/assign-remit/assign-remit.component';\nimport { EditAppointmentPopupComponent } from './components/popups/patient-scheduling/edit-appointment-popup/edit-appointment-popup.component';\nimport { ToastrModule } from 'ngx-toastr';\nimport { NewClaimComponent } from './components/New-Claim/new-claim.component';\nimport { UserAuthenticationService } from './services/UserAuthentication/UserAuthentication.service';\nimport { HashLocationStrategy, LocationStrategy } from '@angular/common';\nimport { SaveModalComponent } from './modals/save-modal/save-modal.component';\nimport { AddPatientComponent } from './components/patient/add-patient/add-patient.component';\nimport { FilesComponent } from './components/files/files.component';\nimport { ProfessionalClaimsComponentComponent } from './components/files/professional-claims-component/professional-claims-component.component';\nimport { AckFiles999Component } from './components/files/ack-files999.component/ack-files999.component.component';\nimport { AckFiles997Component } from './components/files/ack-files997.component/ack-files997.component.component';\nimport { ClaimAckReport277Component } from './components/files/claim-ack-report277.component/claim-ack-report277.component.component';\nimport { Received835FilesComponent } from './components/files/era835.component/era835.component.component'; // import { TableModule } from 'primeng/table';\n\nimport { SidebarModule } from './components/side-bar/side-bar.module';\nimport { SanitizeUrlPipe } from './shared/pipes/sanitize-url.pipe';\nimport { FilePreviewDialogComponent } from './shared/components/file-preview-dialog/file-preview-dialog.component';\nimport { DateFormatService } from './shared/services/dateformat';\nimport { ClaimStatusModule } from './components/create-claim/claim-status/claim-status.module';\nimport { DashboardModule } from './components/dashboard/dashboard.module';\nimport { RendererModule } from './shared/Renderer/renderer.module';\nimport { MembersModule } from './components/members/members.module';\nimport { FileModule } from './components/file/file.module';\nimport { AppMaterialModule } from './material-module';\nimport { MatRangeDatePickerModule } from './shared/components/form-inputs/mat-range-date-picker/mat-range-date-picker.module';\nimport { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';\nimport { CustomDateAdapter, MY_DATE_FORMAT } from './common/custom-date-adapter';\nimport { ProvidersModule } from './components/providers/providers.module';\nimport { UpdateClaimComponent } from './modals/update-claim/update-claim.component';\nimport { AddressMismatchComponent } from './shared/components/address-mismatch/address-mismatch.component';\nimport { LoaderInterceptor } from './shared/interceptor/loader.interceptor';\nimport { NetworkErrorInterceptor } from './shared/interceptor/network-error.interceptor';\nimport { SpinnerComponent } from './shared/components/spinner/spinner.component';\nimport { DublicateClaimComponent } from './modals/dublicate-claim/dublicate-claim.component';\nimport { AuthInterceptor } from './shared/interceptor/auth.interceptor';\nimport { NgIdleKeepaliveModule } from '@ng-idle/keepalive';\nimport { PopoverModule } from 'ngx-bootstrap/popover';\nimport { SubjectService } from './shared/services/subject.service';\nimport { ForgotPasswordComponent } from './components/forgot-password/forgot-password.component';\nimport { ResetPasswordComponent } from './components/reset-password/reset-password.component';\nimport { ChangePasswordComponent } from './components/change-password/change-password.component';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { ReportsModule } from './components/reports/reports.module';\nimport { InputDirectivesModule } from './shared/directives/input-directives.module';\nimport { LoginModule } from './components/login/login.module';\nimport { PowerBIModule } from './components/power-bi-reports/power-bi-reports.module';\nimport { SearchClaimModule } from './components/search-claim/search-claim.module';\nimport { MatMomentDateModule } from '@angular/material-moment-adapter';\nimport { InsuranceModule } from './components/insurance/insurance.module';\nimport { ConfirmMessagePopupComponent } from './modals/confirm-message-popup/confirm-message-popup.component';\nimport { CacheService } from './services/cache-service/cache.service';\nimport { DisplayUpperSaveLowerDirective } from './shared/directives/display-upper-save-lower-directive';\nimport { SelectAllComponent } from './shared/components/select-all/select-all.component';\nexport function init_app(appLoadService) {\n  return () => appLoadService.init();\n}\nlet AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, ProcessRemitComponent, RemitWorklistComponent, MenuListItemComponent, FfsComponent, CapComponent, FilterPopupComponent, AppointmentScheduleComponent, NewAppointmentPopupComponent, EditAppointmentPopupComponent, NewPatientPopupComponent, SearchResultPopupComponent, BillingProviderPopupComponent, RenderingProviderPopupComponent, AssignRemitComponent, NewClaimComponent, SaveModalComponent, DublicateClaimComponent, ForgotPasswordComponent, ResetPasswordComponent, UpdateClaimComponent, AddPatientComponent, FilesComponent, ProfessionalClaimsComponentComponent, AckFiles999Component, AckFiles997Component, ClaimAckReport277Component, Received835FilesComponent, SanitizeUrlPipe, FilePreviewDialogComponent, AddressMismatchComponent, SpinnerComponent, ChangePasswordComponent, ConfirmMessagePopupComponent, DisplayUpperSaveLowerDirective, SelectAllComponent],\n  imports: [BrowserModule, BrowserAnimationsModule, CommonModule, AppRoutingModule, ReactiveFormsModule, HttpClientModule, FormsModule, AgGridModule, DragDropModule, NgxSpinnerModule.forRoot(), AvatarModule, FullCalendarModule, NgSelectModule, AutocompleteLibModule, BsDatepickerModule.forRoot(), ToastrModule.forRoot({\n    toastClass: 'ngx-toastr custom-toast-width',\n    preventDuplicates: true,\n    progressBar: true,\n    positionClass: 'toast-top-right'\n  }), ClaimStatusModule, RendererModule, MembersModule, FileModule, AppMaterialModule, MatRangeDatePickerModule, LoginModule, DashboardModule, ProvidersModule, PopoverModule.forRoot(), NgIdleKeepaliveModule.forRoot(), SidebarModule, MatDialogModule, ReportsModule, PowerBIModule, InputDirectivesModule, SearchClaimModule, MatMomentDateModule, InsuranceModule],\n  providers: [GlobalService, EnvServiceProvider, SubjectService, {\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  }, AppInitService, RemitProcessService, UserAuthenticationService, DatePipe, DateFormatService, CacheService, {\n    provide: APP_INITIALIZER,\n    useFactory: init_app,\n    deps: [AppInitService],\n    multi: true\n  }, {\n    provide: DateAdapter,\n    useClass: CustomDateAdapter,\n    deps: [MAT_DATE_LOCALE]\n    /*MomentDateAdapter*/\n\n  }, {\n    provide: MAT_DATE_FORMATS,\n    useValue: MY_DATE_FORMAT\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: LoaderInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: NetworkErrorInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);\nexport { AppModule };", "map": null, "metadata": {}, "sourceType": "module"}