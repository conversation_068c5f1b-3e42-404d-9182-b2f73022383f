{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { first, Subject, takeUntil } from 'rxjs';\nimport { CLAIM_GRID_DEFCOLUMNS, ShowHideCheckboxClaimsGrid } from 'src/app/common/claim-grid-columns';\nimport { CLAIM_TYPE, COMMON_METHODS, COMMON_VALUES, PREVILEGES, SwalFire } from 'src/app/common/common-static';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport { GRID_CONFIG } from 'src/app/shared/config-ag-grid/config-grid';\nimport { ResubmitConfimComponent } from '../../popups/resubmit-confim/resubmit-confim.component';\nimport * as moment from 'moment';\nimport { Workbook } from 'exceljs';\nimport * as fs from 'file-saver';\nimport { ValidationMsgs } from 'src/app/common/common-static';\nimport { DateFormatter } from 'src/app/shared/functions/dateFormatterFunction';\nimport { SelectAllComponent } from 'src/app/shared/components/select-all/select-all.component';\nimport * as _ from 'lodash';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"src/app/shared/services/subject.service\";\nimport * as i3 from \"src/app/services/ProviderManagement/provider-management.service\";\nimport * as i4 from \"src/app/services/Providers/provider.service\";\nimport * as i5 from \"src/app/services/Member/member.service\";\nimport * as i6 from \"src/app/services/Dashboard/claim-report-service.service\";\nimport * as i7 from \"src/app/services/claims-tracking/claims-tracking.service\";\nimport * as i8 from \"src/app/services/billing-provider/billing-provider.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"src/app/services/Notification/notification.service\";\nimport * as i11 from \"src/app/services/claim-buckets/dispatch-claim.service\";\nimport * as i12 from \"ngx-spinner\";\nimport * as i13 from \"src/app/shared/services/global.service\";\nimport * as i14 from \"src/app/services/ClaimForm/claim.service\";\nimport * as i15 from \"src/app/services/export-service/export.service\";\nimport * as i16 from \"@angular/common\";\nimport * as i17 from \"ag-grid-angular\";\nconst _c0 = [\"agGrid\"];\n\nfunction ClaimStatusComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ClaimStatusComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.generateDispatchClaims());\n    });\n    i0.ɵɵelementStart(2, \"i\", 13);\n    i0.ɵɵtext(3, \"drive_file_move\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" GENERATE \");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.selectedGridItems.length == 0);\n  }\n}\n\nfunction ClaimStatusComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ClaimStatusComponent_div_9_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.moveToAcceptedClaim());\n    });\n    i0.ɵɵelementStart(2, \"i\", 13);\n    i0.ɵɵtext(3, \"playlist_add_check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" MOVE TO ACCEPTED \");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedGridItems.length == 0);\n  }\n}\n\nfunction ClaimStatusComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ClaimStatusComponent_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.reSubmit());\n    });\n    i0.ɵɵelementStart(2, \"i\", 13);\n    i0.ɵɵtext(3, \"undo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" RESUBMIT \");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedRow.length == 0);\n  }\n}\n\nfunction ClaimStatusComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15)(3, \"div\", 16)(4, \"div\", 17)(5, \"input\", 18);\n    i0.ɵɵlistener(\"change\", function ClaimStatusComponent_div_11_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.ignoreClaimCheckEvnt($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 19);\n    i0.ɵɵtext(7, \" Ignored Claims \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 20);\n    i0.ɵɵelementStart(9, \"span\", 21);\n    i0.ɵɵtext(10, \"The Claim Reached maximum Limit of Re-Submissions\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 15);\n    i0.ɵɵelement(12, \"span\", 22);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 23)(16, \"div\", 24)(17, \"div\", 25)(18, \"span\", 26);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 27);\n    i0.ɵɵtext(21, \" \\u00D7 \");\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(19);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.unattendClaimsCount, \" REJECTED CLAIM(S) IN THIS QUEUE HAVE NOT BEEN RESUBMITTED SO FAR, PLEASE FILTER TO VIEW SUCH CLAIM(S). \");\n  }\n}\n\nfunction ClaimStatusComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15)(3, \"div\", 16)(4, \"div\", 17)(5, \"input\", 18);\n    i0.ɵɵlistener(\"change\", function ClaimStatusComponent_div_12_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.ignoreClaimCheckEvnt($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 19);\n    i0.ɵɵtext(7, \" Ignored Claims \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 20);\n    i0.ɵɵelementStart(9, \"span\", 21);\n    i0.ɵɵtext(10, \"The Claim Reached maximum Limit of Re-Submissions\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 15);\n    i0.ɵɵelement(12, \"span\", 22);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 23)(16, \"div\", 24)(17, \"div\", 25)(18, \"span\", 26);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 27);\n    i0.ɵɵtext(21, \" \\u00D7 \");\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(19);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.unattendClaimsCount, \" REJECTED CLAIM(S) IN THIS QUEUE HAVE NOT BEEN RESUBMITTED SO FAR, PLEASE FILTER TO VIEW SUCH CLAIM(S). \");\n  }\n}\n\nfunction ClaimStatusComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"The Claim Reached maximum Limit of Re-Submissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"div\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"span\", 29);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"DEACTIVATED CLAIMS \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"span\", 29);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"DEACTIVATED CLAIMS \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15);\n    i0.ɵɵelement(2, \"span\", 29);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"DEACTIVATED CLAIMS \");\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"PAYER IS NOT MAPPED TO CLEARING HOUSE\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"span\", 29);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"DEACTIVATED CLAIMS \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"The Claim Reached maximum Limit of Re-Submissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"The Claim Reached maximum Limit of Re-Submissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"The Claim Reached maximum Limit of Re-Submissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"The Claim Reached maximum Limit of Re-Submissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"The Claim Reached maximum Limit of Re-Submissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\", 21);\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"The Claim Reached maximum Limit of Re-Submissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"The Claim Reached maximum Limit of Re-Submissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15);\n    i0.ɵɵelement(2, \"span\", 22);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"RESUBMITTED CLAIMS\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"div\", 32);\n    i0.ɵɵelement(3, \"div\", 33)(4, \"div\", 33)(5, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 34);\n    i0.ɵɵtext(7, \"Loading....\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction ClaimStatusComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\")(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" rows selected. \");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r20.selectedRow == null ? null : ctx_r20.selectedRow.length);\n  }\n}\n\nfunction ClaimStatusComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\")(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" rows selected. \");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r21.selectedRow == null ? null : ctx_r21.selectedRow.length);\n  }\n}\n\nexport let ClaimStatusComponent = /*#__PURE__*/(() => {\n  class ClaimStatusComponent {\n    constructor(dailog, subjectService, providerMgmtService, providerService, memberService, claimReportService, claimsTrackingService, billingProviderService, billingForm, notificationService, dispatchClaimService, dialogRef, spinner, globalService, claimService, exportService) {\n      this.dailog = dailog;\n      this.subjectService = subjectService;\n      this.providerMgmtService = providerMgmtService;\n      this.providerService = providerService;\n      this.memberService = memberService;\n      this.claimReportService = claimReportService;\n      this.claimsTrackingService = claimsTrackingService;\n      this.billingProviderService = billingProviderService;\n      this.billingForm = billingForm;\n      this.notificationService = notificationService;\n      this.dispatchClaimService = dispatchClaimService;\n      this.dialogRef = dialogRef;\n      this.spinner = spinner;\n      this.globalService = globalService;\n      this.claimService = claimService;\n      this.exportService = exportService;\n      this.billingProviderFinalResult = [];\n      this.billingProviderResult = [];\n      this.selectedBillingProvider = new EventEmitter();\n      this.dashboardSerchCriteria = {};\n      this.selectedGridItems = [];\n      this.rowSelection = 'multiple';\n      this.selectedIPACodes = '';\n      this.allIPACode = '';\n      this.gridOptions = JSON.parse(JSON.stringify(GRID_CONFIG.scrollConfigDetails.checkboxScrollGridOptions));\n      this.claimGridModel = [];\n      this.claims = [];\n      this.columnDefs = [];\n      this.buttonVisibleType = '';\n      this.claimType = CLAIM_TYPE;\n      this.claimHeader = '';\n      this.selectedRow = [];\n      this.claimCount = 0;\n      this.currentTabTotalFileCount = 0;\n      this.overlayNoRowsTemplate = ValidationMsgs.no_data_available;\n      this.overlayLoadingTemplate = '<div style=\"padding: 20px; text-align: center;\"><div style=\"display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite;\"></div><div style=\"margin-top: 10px;\">Loading claims...</div></div><style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>';\n      this.isGridLoading = false;\n      this.isIgnoreHeaderShow = false;\n      this.isGenerateButtonShow = false;\n      this.isMoveToAcceptedButtonShow = false;\n      this.isResubmitButtonShow = false;\n      this.isPrivMoveSelectedOpenToAccepted = false;\n      this.isClaimBillingMngmntGenrtEDI837File = false;\n      this.billerPrivMoveAllOpenToAccepted = false;\n      this.privMoveAllOpenToAccepted = false;\n      this.isClaimsBillingManagementResubmission = false;\n      this.destroy$ = new Subject();\n      this.isFilterGrid = false;\n      this.frameworkComponents = {\n        selectAllComponent: SelectAllComponent\n      };\n      this.isAllSelectedGlobally = false;\n      this.excludedIds = new Set();\n      this.globalCheckboxSelectedGridItems = [];\n      this.isGridSelectedRowCountShow = false; // select row or de select row\n\n      this.isRowSelectable = params => {\n        ;\n        return ShowHideCheckboxClaimsGrid.showHideCheckbox(params) ? true : false;\n      };\n\n      this.unattendClaimsCount = 0;\n      this.getPrivileges();\n      this.createForm();\n      this.subjectRefresh();\n      dialogRef.disableClose = true;\n    }\n\n    ngOnInit() {\n      this.gridOptions.getRowStyle = this.changeRowColor; // Configure overlay templates\n\n      this.gridOptions.overlayLoadingTemplate = this.overlayLoadingTemplate;\n      this.gridOptions.overlayNoRowsTemplate = this.overlayNoRowsTemplate;\n    }\n\n    getCurrentTabGridColumnDef(claimTab) {\n      switch (claimTab) {\n        case CLAIM_TYPE.open:\n          {\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.OPEN_COLUMN_DEFS(this.privMoveAllOpenToAccepted, this.isPrivMoveSelectedOpenToAccepted, this.billerPrivMoveAllOpenToAccepted);\n            this.claimHeader = 'Open';\n            this.isMoveToAcceptedButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.onHold:\n          {\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ONHOLD_COLUMN_DEFS;\n            this.claimHeader = 'On Hold';\n            this.isGridSelectedRowCountShow = false;\n            break;\n          }\n\n        case CLAIM_TYPE.ntr:\n          {\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.RESUBMISSION_COLUMN_DEFS;\n            this.claimHeader = 'Resubmission';\n            this.isGridSelectedRowCountShow = false;\n            break;\n          }\n\n        case CLAIM_TYPE.accepted:\n          {\n            this.rowSelection = 'multiple';\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ACCEPTED_COLUMN_DEFS(this.isClaimBillingMngmntGenrtEDI837File);\n            this.claimHeader = 'Accepted';\n            this.isGenerateButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.dispatched:\n          {\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.DISPATCHED_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);\n            this.claimHeader = 'Dispatched';\n            this.isResubmitButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.unackByCH:\n          {\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.UN_ACK_BY_CH_COLUMN_DEFS;\n            this.claimHeader = 'Unacknowledged by CH';\n            this.isGridSelectedRowCountShow = false;\n            break;\n          }\n\n        case CLAIM_TYPE.rejectedByCH:\n          {\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.REJECTED_BY_CH_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);\n            this.claimHeader = 'Rejected By CH';\n            this.isIgnoreHeaderShow = true;\n            this.isResubmitButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.acknolwedgedByCH:\n          {\n            this.claimHeader = 'Acknowledged by CH';\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ACK_BY_CH_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);\n            this.isResubmitButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.acceptedByCH:\n          {\n            this.claimHeader = 'Accepted By CH';\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ACCEPTED_BY_CH_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);\n            this.isResubmitButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.unAckByPayer:\n          {\n            this.claimHeader = 'Unacknowledged by Payer';\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.UN_ACK_BY_PAYER_COLUMN_DEFS;\n            this.isResubmitButtonShow = false;\n            this.isGridSelectedRowCountShow = false;\n            break;\n          }\n\n        case CLAIM_TYPE.rejectedByPayer:\n          {\n            this.claimHeader = 'Rejected By Payer';\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.REJECTED_BY_PAYER_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);\n            this.isIgnoreHeaderShow = true;\n            this.isResubmitButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.acknolwedgedByPayer:\n          {\n            this.claimHeader = 'Acknowledged by Payer';\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ACK_BY_PAYER_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);\n            this.isResubmitButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.acceptedByPayer:\n          {\n            this.claimHeader = 'Accepted By Payer';\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ACCEPTED_BY_PAYER(this.isClaimsBillingManagementResubmission);\n            this.isResubmitButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.pending:\n          {\n            this.claimHeader = 'Pending';\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.PENDING_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);\n            this.isResubmitButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.eobReceived:\n          {\n            this.claimHeader = 'EOB Received';\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.EOB_RECIEVED(this.isClaimsBillingManagementResubmission);\n            this.isResubmitButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n\n        case CLAIM_TYPE.deniedByPayer:\n          {\n            this.claimHeader = 'Denied By Payer';\n            this.columnDefs = CLAIM_GRID_DEFCOLUMNS.DENIED_BY_PAYER(this.isClaimsBillingManagementResubmission);\n            this.isResubmitButtonShow = true;\n            this.isGridSelectedRowCountShow = true;\n            break;\n          }\n      }\n\n      this.buttonVisibleType = claimTab;\n    }\n\n    createForm() {\n      this.billingProviderInfo = this.billingForm.group({\n        address: new FormControl(''),\n        ipaCode: new FormControl(''),\n        tax: new FormControl(''),\n        name: new FormControl(''),\n        npi: new FormControl('')\n      });\n      return this.billingProviderInfo;\n    }\n\n    get f() {\n      return this.billingProviderInfo.controls;\n    }\n\n    searchBilling() {\n      this.ipaCodeItems?.forEach(ipa => {\n        this.allIPACode += ipa.ipaCode + \",\";\n      });\n\n      if (this.billingProviderInfo.invalid) {\n        submitValidateAllFields.validateAllFields(this.billingProviderInfo);\n        return;\n      }\n\n      this.fillBillingProvider();\n    }\n\n    fillBillingProvider() {\n      this.billingProviderFinalResult = [];\n      let billing = {\n        sortBy: 'ProviderNPI',\n        sortOrder: 'ASC',\n        limit: 50,\n        index: 0,\n        providerNPI: this.billingProviderInfo.controls['npi'].value === \"\" ? null : this.billingProviderInfo.controls['npi'].value,\n        firstName: this.billingProviderInfo.controls['name'].value === \"\" ? null : this.billingProviderInfo.controls['name'].value,\n        iPACode: this.selectedIPACodes === \"\" ? this.allIPACode : this.selectedIPACodes,\n        address1: this.billingProviderInfo.controls['address'].value === \"\" ? null : this.billingProviderInfo.controls['address'].value,\n        taxID: this.billingProviderInfo.controls['tax'].value === \"\" ? null : this.billingProviderInfo.controls['tax'].value\n      };\n      this.billingProviderService.fetchBillingProviderResult(billing).subscribe(res => {\n        if (res.length > 0) {\n          this.billingProviderResult = res;\n          res.forEach(e => {\n            e.payToDetails.forEach(p => {\n              let ipaName = '';\n              p.ipaDetails.forEach(ipa => {\n                ipaName += ipa.ipaName;\n              });\n              this.billingProviderFinalResult.push({\n                address: p.address1,\n                npi: p.payToNPI,\n                name: p.paytoName,\n                taxId: p.billingProviderTaxonomy,\n                ipaName: ipaName,\n                taxIdOrSSN: e.taxIdOrSSN,\n                VendorID: e.vendorID\n              });\n              /**/\n            });\n          });\n        }\n      });\n    }\n\n    changeRowColor(params) {\n      if (params.node.rowIndex % 2 === 0) {\n        return {\n          'background-color': '#f1f0f0'\n        };\n      } else {\n        return {\n          'background-color': 'white'\n        };\n      }\n    }\n\n    onGridReady(params) {\n      this.gridApi = params;\n      this.gridDataApi = params.api; // Set datasource for infinite scrolling - ag-grid will handle loading overlay automatically\n\n      params.api.setDatasource(this.getDataSource());\n    }\n\n    getDataSource() {\n      const dataSource = {\n        rowCount: undefined,\n        getRows: params => {\n          // Set loading state to true when starting to fetch data\n          this.isGridLoading = true;\n\n          if (this.currentTabTotalFileCount >= params.startRow) {\n            if (params.startRow == 0 && Object.keys(params.filterModel).length == 0 && Object.keys(params.sortModel).length == 0) {\n              this.isFilterGrid = false;\n              this.restFilterModel();\n              this.fetchClaimList(params);\n            } else {\n              this.restFilterModel();\n              this.getGridFilterModelData(params);\n            }\n          }\n        }\n      };\n      return { ...dataSource\n      };\n    }\n\n    fetchClaimList(params) {\n      // Let ag-grid handle loading overlay automatically for infinite scrolling\n      this.gridPayloadRequest(params);\n      this.claimReportService.getClaimList(this.dashboardSerchCriteria).pipe(takeUntil(this.destroy$)).subscribe(data => {\n        if (data.statusCode == 200) {\n          if (!!data.content && (data.content.claimList || []).length > 0) {\n            this.claims = data?.content?.claimList;\n\n            if (this.isFilterGrid) {\n              params.successCallback(this.claims, Number(data.content.totalRecordsCount));\n            } else {\n              if (this.currentTabTotalFileCount > 0 && this.claims.length > 0) {\n                params.successCallback(this.claims, this.currentTabTotalFileCount);\n              }\n            }\n          } else {\n            params.successCallback([], (data.content.claimList || []).length);\n          }\n\n          if (this.isAllSelectedGlobally) {\n            this.gridDataApi?.forEachNode(node => {\n              if (!!node.data && ShowHideCheckboxClaimsGrid.showHideCheckbox(node)) {\n                node.setSelected(true);\n                node.data.isGloballySelected = true; // Update the data model if needed\n\n                node.data.isSelected = true; // Update the data model if needed\n              }\n            });\n          }\n\n          this.bindDOSDate();\n          this.bindReasonForRejctecByCH();\n          this.claimGridModel = this.claims;\n        } else {\n          // API returned error status\n          params.successCallback([], 0);\n        } // Set loading state to false when API call completes\n\n\n        this.isGridLoading = false; // ag-grid will automatically handle hiding the loading overlay\n      }, error => {\n        // Set loading state to false on error\n        this.isGridLoading = false; // Call failCallback to let ag-grid know the request failed\n        // ag-grid will automatically handle the loading overlay\n\n        params.failCallback();\n      });\n    }\n\n    getGridFilterModelData(params) {\n      debugger; // Grid columns search filters without sorting\n\n      if (Object.keys(params.filterModel).length > 0 && Object.keys(params.sortModel).length == 0) {\n        this.gridFilterModel(params);\n        this.fetchClaimList(params);\n      } //Grid columns search filters with sorting\n      else if (Object.keys(params.filterModel).length > 0 && Object.keys(params.sortModel).length > 0) {\n        this.gridFilterModel(params);\n        this.GridSortModel(params);\n        this.fetchClaimList(params);\n      } //Grid columns without search filters with sorting\n      else if (Object.keys(params.filterModel).length == 0 && Object.keys(params.sortModel).length > 0) {\n        this.GridSortModel(params);\n        this.isFilterGrid = false;\n        this.fetchClaimList(params);\n      } else {\n        this.fetchClaimList(params);\n      }\n    }\n\n    gridFilterModel(params) {\n      if (!!params.filterModel?.claimControlNumber?.filter) {\n        // for grid claimControlNumber column filters.\n        this.dashboardSerchCriteria.claimControlNumber = params.filterModel?.claimControlNumber?.filter, this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.payer?.filter) {\n        this.dashboardSerchCriteria.payer = params.filterModel?.payer?.filter, this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.memberFullName?.filter) {\n        this.dashboardSerchCriteria.memFirstName = params.filterModel?.memberFullName?.filter, this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.dos) {\n        console.log(params.filterModel?.dos?.dateFrom);\n        let dateDos = COMMON_METHODS.validateDateForFilter(moment.utc(params.filterModel?.dos?.dateFrom).toDate());\n\n        if (dateDos) {\n          this.dashboardSerchCriteria.dOSFrom = moment.utc(params.filterModel?.dos?.dateFrom).toDate();\n          this.isFilterGrid = true;\n        } else {\n          this.isFilterGrid = true;\n        }\n      }\n\n      if (!!params.filterModel?.renderingProviderFullName?.filter) {\n        this.dashboardSerchCriteria.renderingProviderFirstName = params.filterModel?.renderingProviderFullName?.filter, this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.claimAmount?.filter) {\n        this.dashboardSerchCriteria.claimAmount = Number(COMMON_METHODS.removeDollarSign(params.filterModel?.claimAmount?.filter));\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.claimType?.filter) {\n        this.dashboardSerchCriteria.claimType = params.filterModel?.claimType?.filter;\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.dateCreated) {\n        this.dashboardSerchCriteria.dateCreated = moment.utc(params.filterModel?.dateCreated?.dateFrom).toDate();\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.age?.filter) {\n        this.dashboardSerchCriteria.age = params.filterModel?.age?.filter;\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.createdBy?.filter) {\n        this.dashboardSerchCriteria.createdBy = params.filterModel?.createdBy?.filter;\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.reviewedBy?.filter) {\n        this.dashboardSerchCriteria.reviewedBy = params.filterModel?.reviewedBy?.filter;\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.statusModifiedDate) {\n        if (this.dashboardSerchCriteria.claimFormStatusCode == CLAIM_TYPE.onHold) {\n          this.dashboardSerchCriteria.statusModifiedDate = moment.utc(params.filterModel?.statusModifiedDate?.dateFrom).toDate();\n          this.isFilterGrid = true;\n        } else {\n          this.dashboardSerchCriteria._999ProcessedOn_CH = moment.utc(params.filterModel?.statusModifiedDate?.dateFrom).toDate();\n          this.isFilterGrid = true;\n        }\n      }\n\n      if (!!params.filterModel?.fileType?.filter) {\n        this.dashboardSerchCriteria.fileType = params.filterModel?.fileType?.filter == '999' ? '005010X231A1' : params.filterModel?.fileType?.filter == '277' ? '005010X214' : params.filterModel?.fileType?.filter;\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.parent_patientctrlno?.filter) {\n        this.dashboardSerchCriteria.parent_patientctrlno = params.filterModel?.parent_patientctrlno?.filter;\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.sentOn) {\n        this.dashboardSerchCriteria.sentOn = moment.utc(params.filterModel?.sentOn?.dateFrom).toDate();\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.sentTo?.filter) {\n        this.dashboardSerchCriteria.sentTo = params.filterModel?.sentTo?.filter;\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?._999ProcessedOn_CH) {\n        this.dashboardSerchCriteria._999ProcessedOn_CH = moment.utc(params.filterModel?._999ProcessedOn_CH?.dateFrom).toDate();\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?._277ProcessedOn_CH) {\n        this.dashboardSerchCriteria._277ProcessedOn_CH = moment.utc(params.filterModel?._277ProcessedOn_CH?.dateFrom).toDate();\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?._277ProcessedOn_Payer) {\n        this.dashboardSerchCriteria._277ProcessedOn_Payer = moment.utc(params.filterModel?._277ProcessedOn_Payer?.dateFrom).toDate();\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?._277PendOnDate) {\n        this.dashboardSerchCriteria._277PendOnDate = moment.utc(params.filterModel?._277PendOnDate?.dateFrom).toDate();\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.paidAmount?.filter) {\n        this.dashboardSerchCriteria.paidAmount = Number(COMMON_METHODS.removeDollarSign(params.filterModel?.paidAmount?.filter));\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.paidOn) {\n        this.dashboardSerchCriteria.paidOn = moment.utc(params.filterModel?.paidOn?.dateFrom).toDate();\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.acceptedOn) {\n        this.dashboardSerchCriteria.acceptedOn = moment.utc(params.filterModel?.acceptedOn?.dateFrom).toDate();\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?._835PaidOnDate) {\n        this.dashboardSerchCriteria._835PaidOnDate = moment.utc(params.filterModel?._835PaidOnDate?.dateFrom).toDate();\n        this.isFilterGrid = true;\n      }\n\n      if (!!params.filterModel?.unackfor?.filter) {\n        this.dashboardSerchCriteria.unackfor = params.filterModel?.unackfor?.filter;\n        this.isFilterGrid = true;\n      }\n    }\n\n    restFilterModel() {\n      if (!!this.dashboardSerchCriteria.claimControlNumber) {\n        this.dashboardSerchCriteria.claimControlNumber = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.memFirstName) {\n        this.dashboardSerchCriteria.memFirstName = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.payer) {\n        this.dashboardSerchCriteria.payer = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.dOSFrom) {\n        this.dashboardSerchCriteria.dOSFrom = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.renderingProviderFirstName) {\n        this.dashboardSerchCriteria.renderingProviderFirstName = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.claimAmount) {\n        this.dashboardSerchCriteria.claimAmount = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.claimType) {\n        this.dashboardSerchCriteria.claimType = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.dateCreated) {\n        this.dashboardSerchCriteria.dateCreated = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.age) {\n        this.dashboardSerchCriteria.age = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.createdBy) {\n        this.dashboardSerchCriteria.createdBy = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.reviewedBy) {\n        this.dashboardSerchCriteria.reviewedBy = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.statusModifiedDate) {\n        this.dashboardSerchCriteria.statusModifiedDate = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.statusReason) {\n        this.dashboardSerchCriteria.statusReason = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.parent_patientctrlno) {\n        this.dashboardSerchCriteria.parent_patientctrlno = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.sentOn) {\n        this.dashboardSerchCriteria.sentOn = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.sentTo) {\n        this.dashboardSerchCriteria.sentTo = null;\n      }\n\n      if (!!this.dashboardSerchCriteria._999ProcessedOn_CH) {\n        this.dashboardSerchCriteria._999ProcessedOn_CH = null;\n      }\n\n      if (!!this.dashboardSerchCriteria._277ProcessedOn_CH) {\n        this.dashboardSerchCriteria._277ProcessedOn_CH = null;\n      }\n\n      if (!!this.dashboardSerchCriteria._277ProcessedOn_Payer) {\n        this.dashboardSerchCriteria._277ProcessedOn_Payer = null;\n      } // this.isFilterGrid = false;\n\n\n      if (!!this.dashboardSerchCriteria._277PendOnDate) {\n        this.dashboardSerchCriteria._277PendOnDate = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.paidAmount) {\n        this.dashboardSerchCriteria.paidAmount = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.paidOn) {\n        this.dashboardSerchCriteria.paidOn = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.acceptedOn) {\n        this.dashboardSerchCriteria.acceptedOn = null;\n      }\n\n      if (!!this.dashboardSerchCriteria._835PaidOnDate) {\n        this.dashboardSerchCriteria._835PaidOnDate = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.unackfor) {\n        this.dashboardSerchCriteria.unackfor = null;\n      }\n\n      if (!!this.dashboardSerchCriteria.fileType) {\n        this.dashboardSerchCriteria.fileType = null;\n      }\n    }\n\n    fetchAllClaimsListForGlobalCheckbox() {\n      this.spinner.show();\n      let allClaimListRequest = this.dashboardSerchCriteria;\n      allClaimListRequest.index = null;\n      this.claimReportService.getClaimList(allClaimListRequest).pipe(takeUntil(this.destroy$)).subscribe(data => {\n        if (data.statusCode == 200) {\n          if (!!data.content && (data.content.claimList || []).length > 0) {\n            let allClaimListResp = data?.content?.claimList;\n            allClaimListResp = allClaimListResp.filter(claim => claim.isLocked === false && claim.clearingHouseId !== null);\n            this.globalCheckboxSelectedGridItems = allClaimListResp;\n            this.spinner.hide();\n            this.allClaimListDispatchAlert(`Are you sure you want to generate <b>${allClaimListResp?.length}</b> Claims?`);\n          }\n        }\n      });\n\n      error => {\n        // this.error = error;\n        console.log(error);\n        this.spinner.hide();\n      };\n    }\n\n    allClaimListDispatchAlert(msg) {\n      SwalFire.confirmCancelSwlAlert(msg).then(result => {\n        if (result.isConfirmed) {\n          this.selectedGridItems = this.globalCheckboxSelectedGridItems;\n          this.dispatchAcceptedClaims();\n        } else if (result.isDenied) {}\n      });\n    }\n\n    GridSortModel(params) {\n      if (params.sortModel.length > 0) {\n        const firstSort = params.sortModel[0];\n        const agGridColId = firstSort.colId;\n        const sortDirection = firstSort.sort; // 'asc' or 'desc'\n\n        let backendSortingColumn = agGridColId; // Default to AG Grid's column ID\n        // Use a switch statement to map specific AG Grid column IDs to backend fields\n\n        switch (agGridColId) {\n          case 'memberFullName':\n            backendSortingColumn = 'MemFirstName';\n            break;\n\n          case 'renderingProviderFullName':\n            backendSortingColumn = 'RenderingProviderFirstName';\n            break;\n\n          case 'dos':\n            backendSortingColumn = 'DOSFrom';\n            break;\n          // Add more cases here for other columns if needed\n          // case 'someOtherAgGridCol':\n          //   backendSortingColumn = 'someOtherBackendField';\n          //   break;\n        } // Assign the determined backend sorting column and order\n\n\n        this.dashboardSerchCriteria.sortingColumn = backendSortingColumn;\n        this.dashboardSerchCriteria.sortOrder = sortDirection;\n      }\n    }\n\n    gridPayloadRequest(params) {\n      this.dashboardSerchCriteria.index = params.startRow;\n    }\n\n    onSelectionChanged(event) {\n      {\n        this.selectedGridItems = [];\n        const selectedNodes = event.api.getSelectedNodes();\n        const selectedData = selectedNodes.map(node => node.data);\n        console.log('Currently Selected Rows (visible/loaded):', selectedData);\n\n        if (this.buttonVisibleType == CLAIM_TYPE.accepted) {\n          const isGloballySelected = selectedData.filter(node => node?.isGloballySelected == true);\n          this.isAllSelectedGlobally = isGloballySelected.length > 0 ? true : false;\n        }\n\n        this.gridChangePushArrays(selectedData);\n        this.selectedRow = selectedData;\n      }\n    }\n\n    gridChangePushArrays(selectedRows) {\n      let claimsList = [];\n\n      switch (this.buttonVisibleType) {\n        case CLAIM_TYPE.open:\n        case CLAIM_TYPE.accepted:\n        case CLAIM_TYPE.dispatched:\n        case CLAIM_TYPE.rejectedByCH:\n        case CLAIM_TYPE.acceptedByCH:\n        case CLAIM_TYPE.acknolwedgedByCH:\n        case CLAIM_TYPE.unAckByPayer:\n        case CLAIM_TYPE.rejectedByPayer:\n        case CLAIM_TYPE.acknolwedgedByPayer:\n        case CLAIM_TYPE.acceptedByPayer:\n        case CLAIM_TYPE.pending:\n        case CLAIM_TYPE.eobReceived:\n        case CLAIM_TYPE.deniedByPayer:\n          {\n            selectedRows.forEach(selectedRowItem => {\n              // if (selectedRowItem.patientCtrlNo>0) {\n              this.selectedGridItems.push(selectedRowItem);\n              let claimsStatus = {\n                claimId: selectedRowItem.claimId,\n                //encounterNumber: \"2\",\n                patientControlNumber: selectedRowItem.claimControlNumber\n              };\n              claimsList.push(claimsStatus); //  }\n            });\n            let claimLst = {\n              claimsStatus: claimsList,\n              status: CLAIM_TYPE.accepted\n            };\n            this.claimsStatusList = claimLst;\n            break;\n          }\n      }\n    }\n\n    selectedIPACode(event) {\n      this.selectedIPACodes = '';\n      event.forEach(element => {\n        this.selectedIPACodes += this.selectedIPACodes + ',' + element.mdmCode;\n      });\n    }\n\n    onGridSizeChanged(e) {\n      this.gridOptions.api.sizeColumnsToFit();\n    }\n\n    generateDispatchClaims() {\n      if (this.isAllSelectedGlobally) {\n        this.fetchAllClaimsListForGlobalCheckbox();\n      } else {\n        this.dispatchAcceptedClaims();\n      }\n    }\n\n    refreshGrid() {\n      if (this.gridApi) {\n        //this.currentTabTotalFileCount = this.tabData.count- this.tabData.additionalCount;\n        // Reset loading state when refreshing grid\n        this.isGridLoading = false;\n        this.onGridReady(this.gridApi);\n      }\n    }\n\n    moveToAcceptedClaim() {\n      this.claimsStatusList.lastModifiedFirstName = JSON.parse(localStorage.getItem('userFirstName')), this.claimsStatusList.lastModifiedLastName = JSON.parse(localStorage.getItem('userLastName')), this.claimsStatusList.lastModifiedMiddleName = JSON.parse(localStorage.getItem('userMiddleName')), this.claimService.claimStatusChange(this.claimsStatusList).subscribe(res => {\n        if (res) {\n          this.notificationService.showSuccess('Selected claims are moved successfully to Accepted Tab.', ValidationMsgs.success, 4000);\n          this.selectedGridItems = [];\n          this.bucketCountRefresh();\n          this.refreshGrid();\n        }\n      }, error => {\n        // this.error = error;\n        // this.loading = false;\n        this.spinner.hide();\n      });\n    }\n\n    bindDOSDate() {\n      this.claims?.forEach(item => {\n        let startDate;\n        let endDate;\n\n        if (item.dos) {\n          if (item.dosFrom) {\n            startDate = moment(item.dosFrom).format('MM/DD/YYYY');\n          }\n\n          if (item.dosTo) {\n            endDate = moment(item.dosTo).format('MM/DD/YYYY');\n          }\n\n          if (startDate && endDate) {\n            item.dos = startDate + ' - ' + endDate;\n          } else if (startDate && !endDate) {\n            item.dos = startDate + ' - ' + '';\n          } else if (!startDate && !endDate) {\n            item.dos = '';\n          }\n        }\n      });\n    }\n\n    bindReasonForRejctecByCH() {\n      this.claims?.forEach(item => {\n        if (item.claimStatusCode == CLAIM_TYPE.rejectedByCH) {\n          if (item.fileType) {\n            if (item.fileType == COMMON_VALUES.fileType) {\n              item.statusReason = COMMON_VALUES._999;\n              item.statusModifiedDate = item._999ProcessedOn_CH;\n            } else {\n              item.statusReason = COMMON_VALUES._277;\n              item.statusModifiedDate = item._277ProcessedOn_CH;\n            }\n          } else {\n            item.statusReason = null;\n            item.statusModifiedDate = null;\n          }\n        }\n      });\n    }\n\n    dispatchAcceptedClaims() {\n      this.selectedGridItems.forEach(item => item.fileType = item.claimType);\n      this.dispatchClaimService.dispatchAcceptedClaims(this.selectedGridItems).subscribe(data => {\n        if (data.message == 'Claim dispatch done. Please check after some time') {\n          this.notificationService.showSuccess('Please check back in the 837 File List after some time.', `Claims Have been moved for Generation which are mapped to Clearing House.`, 4000);\n        } else if (data.message == 'No Claim found for dispatch') {\n          this.notificationService.showSuccess('', `There are no Claims for Generation`, 4000);\n        } else if (data.message == 'Please wait for sometime') {\n          this.notificationService.showSuccess('HOLD ON: DATE CHANGE AWAITED', `Clearing house may not accept the claims if there is a mismatch between received date and dispatched date. Please try again after 16 mins.`, 4000);\n        } else if (data.message.includes('Please wait for sometime')) {\n          let time = data.message.split('Please wait for sometime')[1];\n          this.notificationService.showWarning(`Clearing house may not accept the claims if there is a mismatch between received date and dispatched date. Please try again after ` + time + ` mins.`, 'HOLD ON: DATE CHANGE AWAITED', 4000);\n        }\n\n        this.selectedGridItems = [];\n        this.globalCheckboxSelectedGridItems = [];\n        this.refreshGrid();\n        this.subjectService.setFileRefreshAfterAdd(true);\n        this.spinner.show();\n      }, error => {\n        // this.error = error;\n        // this.loading = false;\n        this.spinner.hide();\n      });\n    }\n\n    ignoreClaimCheckEvnt(ev) {\n      if (ev.target.checked) {\n        this.dashboardSerchCriteria.ignoredClaims = true;\n      } else {\n        this.dashboardSerchCriteria.ignoredClaims = false;\n      }\n\n      this.refreshGrid();\n    }\n\n    ignoreClaims() {\n      this.claimsTrackingService.getIgnoreClaims(this.dashboardSerchCriteria).subscribe(data => {\n        this.claimGridModel = data?.content?.content;\n      }, error => {});\n      this.selectedGridItems = [];\n    }\n\n    subjectRefresh() {\n      this.subjectService.getTabClaimInfo().pipe(first()).subscribe(data => {\n        if (data) {\n          this.dashboardSerchCriteria = _.cloneDeep(data.dashboardSerchCriteria);\n          this.dashboardSerchCriteria.sortOrder = null;\n          this.dashboardSerchCriteria.sortingColumn = null;\n          this.getCurrentTabGridColumnDef(this.dashboardSerchCriteria.claimFormStatusCode); //this.refreshGrid();\n\n          this.claimCount = data.dashboardSerchCriteria.tempCount;\n          this.currentTabTotalFileCount = data.dashboardSerchCriteria.tempCount;\n          this.subjectService.resetTabClaimInfo();\n\n          if (this.dashboardSerchCriteria.claimFormStatusCode == CLAIM_TYPE.rejectedByPayer || this.dashboardSerchCriteria.claimFormStatusCode == CLAIM_TYPE.rejectedByCH) {\n            this.getUnattem();\n          }\n        }\n      });\n      this.subjectService.getBucketClaimGridRefresh().subscribe(isRefresh => {\n        if (isRefresh) {\n          this.refreshGrid();\n          this.selectedRow = [];\n          this.subjectService.resetBucketClaimGridRefresh();\n        }\n      }); // bucket count header refresh when click on Resubmit icon in Grid row.\n\n      this.subjectService.getIsClaimMovedOtherBucket().pipe(takeUntil(this.destroy$)).subscribe(isRefresh => {\n        if (isRefresh) {\n          this.claimCount = Number(this.claimCount) - 1;\n          this.subjectService.resetIsClaimMovedOtherBucket();\n        }\n      });\n      this.subjectService.getDashboardBucketCountData().pipe(takeUntil(this.destroy$)).subscribe(dashboardData => {\n        if (!!dashboardData) {\n          const dashboardObjectByStatusCode = dashboardData.find(claim => claim.statusCode == this.dashboardSerchCriteria.claimFormStatusCode);\n          this.dashboardSerchCriteria.tempCount = dashboardObjectByStatusCode.totalClaims;\n          this.currentTabTotalFileCount = dashboardObjectByStatusCode.totalClaims;\n          this.claimCount = dashboardObjectByStatusCode.totalClaims;\n          this.subjectService.resetDashboardBucketCountData();\n        }\n      });\n    }\n\n    reSubmit() {\n      if (this.claimsStatusList.claimsStatus.length > 1) {\n        let dialogOpen = this.dailog.open(ResubmitConfimComponent, {\n          height: '230px',\n          width: '500px',\n          panelClass: 'custom-dialog-containers'\n        });\n        dialogOpen.afterClosed().subscribe(filterQuery => {\n          if (filterQuery) {\n            this.claimListArrayString = this.claimsStatusList.claimsStatus.filter(item => item.claimId).map(i => i.claimId).toString();\n            this.reSubmitClaims();\n          }\n        });\n      } else {\n        this.claimListArrayString = this.claimsStatusList.claimsStatus.filter(item => item.claimId).map(i => i.claimId).toString();\n        this.reSubmitClaims();\n      }\n\n      this.selectedGridItems = [];\n    }\n\n    reSubmitClaims() {\n      this.claimReportService.resubmitClaim(this.claimListArrayString).subscribe(item => {\n        if (item.statusCode == 200) {\n          this.subjectService.setDashboardRefresh();\n          this.refreshGrid();\n          const claimId = item.message.split('-')[1];\n          let msg = this.claimListArrayString.length > 1 ? `Claims ${claimId} created in the Resubmission tab.` : `Claim ${claimId} created in the Resubmission tab.`;\n          this.notificationService.showSuccess(msg, ValidationMsgs.success, 4000);\n          this.selectedRow = [];\n        }\n      }, err => {\n        this.notificationService.showError(`${this.claimListArrayString} Claim not Created`, `Error!`, 4000);\n      });\n    }\n\n    bucketCountRefresh() {\n      this.claimCount = Number(this.claimCount) - this.claimsStatusList.claimsStatus.length;\n      this.currentTabTotalFileCount = this.claimCount;\n    }\n\n    export() {\n      let gridColumns = this.gridOptions.columnApi.getAllDisplayedColumns().filter(x => x.colDef.headerName != '').map(x => x.colDef);\n      this.exportToExcel(gridColumns);\n    }\n\n    exportToExcel(selectedColumns) {\n      const dataToDisplay = [];\n      this.gridApi.forEachNodeAfterFilterAndSort(node => {\n        if (!!node && node.data) {\n          dataToDisplay.push(node.data);\n        }\n      });\n      let workbook = new Workbook();\n      let worksheet = workbook.addWorksheet('search-claim');\n      let columns = [];\n      let numberOfColumnsToBeShown = 0;\n\n      for (const colItem of selectedColumns) {\n        if (colItem.headerName != '' && colItem.headerName != 'Action' && colItem.headerName != 'Select') {\n          columns.push({\n            header: colItem.headerName,\n            key: colItem.field,\n            width: 25\n          });\n          numberOfColumnsToBeShown = numberOfColumnsToBeShown + 1;\n        }\n      }\n\n      worksheet.columns = columns;\n      dataToDisplay.forEach(data => {\n        if (!!data.dateCreated) {\n          data.dateCreated = DateFormatter(data.dateCreated);\n        }\n\n        ;\n        worksheet.addRow(data, \"\");\n      });\n      worksheet.getRow(1).font = {\n        bold: true\n      };\n      worksheet.columns.forEach(column => {\n        const lengths = column.values.map(v => v.toString().length);\n        lengths.push(25);\n        let maxLength = Math.max(...lengths.filter(v => typeof v === 'number'));\n\n        if (maxLength > 400) {\n          maxLength = 400;\n        }\n\n        column.width = maxLength;\n      });\n      worksheet = this.exportService.addBorderToCells(worksheet, {\n        row: 1,\n        col: 1\n      }, {\n        row: dataToDisplay.length + 1,\n        col: numberOfColumnsToBeShown\n      }, 'thin');\n      worksheet = this.exportService.addBorderToCells(worksheet, {\n        row: 1,\n        col: 1\n      }, {\n        row: 1,\n        col: numberOfColumnsToBeShown\n      }, 'thin');\n\n      for (let i = 1; i < numberOfColumnsToBeShown + 1; i++) {\n        worksheet = this.exportService.addBorderToCells(worksheet, {\n          row: 1,\n          col: i\n        }, {\n          row: dataToDisplay.length + 1,\n          col: i\n        }, 'thin');\n      }\n\n      worksheet.getRow(1).eachCell(c => c.style.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: '6594EE'\n        }\n      });\n      workbook.xlsx.writeBuffer().then(data => {\n        let blob = new Blob([data], {\n          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n        });\n        fs.saveAs(blob, 'open-claims');\n      });\n    }\n\n    getPrivileges() {\n      const privielagesDetails = this.globalService.getPrivilegesByRole();\n      this.isClaimBillingMngmntGenrtEDI837File = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_GenerateEDI837File).length > 0 ? true : false;\n      this.billerPrivMoveAllOpenToAccepted = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_MoveAllOpenToAccepted_Biller).length > 0 ? true : false;\n      this.privMoveAllOpenToAccepted = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_MoveAllOpenToAccepted).length > 0 ? true : false;\n      this.isPrivMoveSelectedOpenToAccepted = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_MoveSelectedOpenToAccepted).length > 0 ? true : false;\n      this.isClaimsBillingManagementResubmission = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_Resubmission).length > 0 ? true : false;\n    }\n\n    getUnattem() {\n      this.claimReportService.getUnattendClaimsCount(this.dashboardSerchCriteria).pipe(takeUntil(this.destroy$)).subscribe(data => {\n        if (data.statusCode == 200) {\n          this.unattendClaimsCount = data?.content;\n        }\n      });\n    }\n\n    ngOnDestroy() {\n      if (this.gridApi) {\n        this.gridApi = null;\n      }\n\n      this.destroy$.next(true);\n      this.destroy$.unsubscribe();\n    }\n\n  }\n\n  ClaimStatusComponent.ɵfac = function ClaimStatusComponent_Factory(t) {\n    return new (t || ClaimStatusComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.SubjectService), i0.ɵɵdirectiveInject(i3.ProviderManagementService), i0.ɵɵdirectiveInject(i4.ProviderService), i0.ɵɵdirectiveInject(i5.MemberService), i0.ɵɵdirectiveInject(i6.ClaimReportServiceService), i0.ɵɵdirectiveInject(i7.ClaimsTrackingService), i0.ɵɵdirectiveInject(i8.BillingProviderService), i0.ɵɵdirectiveInject(i9.FormBuilder), i0.ɵɵdirectiveInject(i10.NotificationService), i0.ɵɵdirectiveInject(i11.DispatchClaimService), i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(i12.NgxSpinnerService), i0.ɵɵdirectiveInject(i13.GlobalService), i0.ɵɵdirectiveInject(i14.ClaimService), i0.ɵɵdirectiveInject(i15.ExportService));\n  };\n\n  ClaimStatusComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClaimStatusComponent,\n    selectors: [[\"app-claim-status\"]],\n    viewQuery: function ClaimStatusComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.agGrid = _t.first);\n      }\n    },\n    inputs: {\n      ipaCodeItems: \"ipaCodeItems\"\n    },\n    outputs: {\n      selectedBillingProvider: \"selectedBillingProvider\"\n    },\n    decls: 32,\n    vars: 30,\n    consts: [[1, \"container\", \"claim-status-component\"], [1, \"dashboard-flex\", \"mt-3\"], [1, \"bucket-header\"], [1, \"appointment-title\"], [2, \"color\", \"#617798\", \"font-size\", \"22px !important\"], [\"style\", \"align-self: center;\", 4, \"ngIf\"], [\"class\", \"outer-status-box\", 4, \"ngIf\"], [1, \"claim-status-grid-container\", \"grid-container\"], [\"id\", \"resultsGrid\", \"rowSelection\", \"multiple\", 1, \"claim-ag-grid\", \"ag-theme-alpine\", \"ag-grid-view\", 3, \"gridOptions\", \"columnDefs\", \"frameworkComponents\", \"overlayLoadingTemplate\", \"gridReady\", \"selectionChanged\"], [\"class\", \"claim-status-loading-overlay\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"align-self\", \"center\"], [\"type\", \"button\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"primary-btn\", 3, \"disabled\", \"click\"], [1, \"material-icons\", \"icon-margin\"], [1, \"outer-status-box\"], [1, \"inner-status-box\"], [1, \"radio-space\"], [1, \"form-check\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"PCP\", 1, \"form-check-label\"], [1, \"fa\", \"fa-exclamation-circle\", \"exclaimark\", \"exclamation-circle\", 2, \"margin-left\", \"0.3rem\"], [1, \"mr-2\"], [1, \"foo2\", \"blue\"], [1, \"row\"], [1, \"col\"], [\"role\", \"alert\", 1, \"alert\", \"alert-warning\", \"alert-dismissible\", \"d-flex\", \"rejected-claims-alert\"], [1, \"rejected-claims-text\"], [\"type\", \"button\", \"data-bs-dismiss\", \"alert\", \"aria-label\", \"Close\", 1, \"btn-close\", \"rejected-claims-close\"], [1, \"fa\", \"fa-exclamation-circle\", \"exclamation-circle\"], [1, \"foo2\", \"red\"], [1, \"claim-status-loading-overlay\"], [1, \"claim-status-loading-spinner\"], [1, \"claim-status-pulse-spinner\"], [1, \"claim-status-pulse-dot\"], [1, \"claim-status-loading-text\"]],\n    template: function ClaimStatusComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"p\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementStart(5, \"span\", 4);\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(7, \"span\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, ClaimStatusComponent_div_8_Template, 5, 1, \"div\", 5);\n        i0.ɵɵtemplate(9, ClaimStatusComponent_div_9_Template, 5, 1, \"div\", 5);\n        i0.ɵɵtemplate(10, ClaimStatusComponent_div_10_Template, 5, 1, \"div\", 5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(11, ClaimStatusComponent_div_11_Template, 22, 1, \"div\", 6);\n        i0.ɵɵtemplate(12, ClaimStatusComponent_div_12_Template, 22, 1, \"div\", 6);\n        i0.ɵɵtemplate(13, ClaimStatusComponent_div_13_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(14, ClaimStatusComponent_div_14_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(15, ClaimStatusComponent_div_15_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(16, ClaimStatusComponent_div_16_Template, 5, 0, \"div\", 6);\n        i0.ɵɵtemplate(17, ClaimStatusComponent_div_17_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(18, ClaimStatusComponent_div_18_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(19, ClaimStatusComponent_div_19_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(20, ClaimStatusComponent_div_20_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(21, ClaimStatusComponent_div_21_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(22, ClaimStatusComponent_div_22_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(23, ClaimStatusComponent_div_23_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(24, ClaimStatusComponent_div_24_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(25, ClaimStatusComponent_div_25_Template, 10, 0, \"div\", 6);\n        i0.ɵɵtemplate(26, ClaimStatusComponent_div_26_Template, 5, 0, \"div\", 6);\n        i0.ɵɵelementStart(27, \"div\", 7)(28, \"ag-grid-angular\", 8);\n        i0.ɵɵlistener(\"gridReady\", function ClaimStatusComponent_Template_ag_grid_angular_gridReady_28_listener($event) {\n          return ctx.onGridReady($event);\n        })(\"selectionChanged\", function ClaimStatusComponent_Template_ag_grid_angular_selectionChanged_28_listener($event) {\n          return ctx.onSelectionChanged($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(29, ClaimStatusComponent_div_29_Template, 8, 0, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(30, ClaimStatusComponent_div_30_Template, 5, 1, \"div\", 10);\n        i0.ɵɵtemplate(31, ClaimStatusComponent_div_31_Template, 5, 1, \"div\", 10);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\"\", ctx.claimHeader, \" Claims \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"#\", ctx.claimCount, \"\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isGenerateButtonShow && ctx.isClaimBillingMngmntGenrtEDI837File);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isMoveToAcceptedButtonShow && (ctx.billerPrivMoveAllOpenToAccepted || ctx.privMoveAllOpenToAccepted || ctx.isPrivMoveSelectedOpenToAccepted));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isResubmitButtonShow && ctx.isClaimsBillingManagementResubmission);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.rejectedByCH);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.rejectedByPayer);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.dispatched);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode === ctx.claimType.open);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode === ctx.claimType.onHold);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode === ctx.claimType.ntr);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.accepted);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode === ctx.claimType.unackByCH);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.acceptedByCH);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.acknolwedgedByCH);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.acknolwedgedByPayer);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.acceptedByPayer);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.eobReceived);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.pending);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode == ctx.claimType.deniedByPayer);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.dashboardSerchCriteria.claimFormStatusCode === ctx.claimType.unAckByPayer);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"grid-loading\", ctx.isGridLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"gridOptions\", ctx.gridOptions)(\"columnDefs\", ctx.columnDefs)(\"frameworkComponents\", ctx.frameworkComponents)(\"overlayLoadingTemplate\", ctx.overlayLoadingTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isGridLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isGenerateButtonShow && !ctx.isAllSelectedGlobally && ctx.isGridSelectedRowCountShow);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isGenerateButtonShow && ctx.isGridSelectedRowCountShow);\n      }\n    },\n    dependencies: [i16.NgIf, i17.AgGridAngular],\n    styles: [\".col-2[_ngcontent-%COMP%], .col-1[_ngcontent-%COMP%], .col[_ngcontent-%COMP%]{padding-right:0!important}.main-card[_ngcontent-%COMP%]{padding:.5rem!important;margin:1rem}label[_ngcontent-%COMP%]{margin-bottom:.3rem;font-size:.9rem!important}i[_ngcontent-%COMP%]{font-size:.9rem;padding:.25rem 0rem!important}.form-control[_ngcontent-%COMP%]{font-size:.9rem!important;height:2.36rem!important}.appointment-flex[_ngcontent-%COMP%]{border:none!important}.ellipse[_ngcontent-%COMP%]{background-color:#11afbc;height:15px;width:15px;border-radius:50%}.search-card[_ngcontent-%COMP%]{background-color:#f8f8f8;padding:.8rem!important}.align-self-center[_ngcontent-%COMP%]{align-self:center}.headingMembers[_ngcontent-%COMP%]{font-size:22px;margin-left:.7rem}  .search-member .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{white-space:break-spaces!important}button[_ngcontent-%COMP%]:disabled{opacity:.5!important}select[_ngcontent-%COMP%]{cursor:pointer}.claim-ag-grid[_ngcontent-%COMP%]{height:500px}.button-flex[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.claim-count[_ngcontent-%COMP%]{font-size:20px;font-weight:bolder;font-family:Poppins-SemiBold,Arial,Helvetica,sans-serif}.ignore[_ngcontent-%COMP%]{position:relative;white-space:nowrap;display:inline}.bucket-header[_ngcontent-%COMP%]{display:inline-flex;justify-content:space-between;width:100%}.claim-status-component[_ngcontent-%COMP%]     .ag-theme-alpine input[class^=ag-][type=text]:focus{margin:0 2px!important}.claim-status-component[_ngcontent-%COMP%]     .ag-theme-alpine .ag-checkbox-input-wrapper.ag-indeterminate:after{color:#2196f3!important}.resubmit-alignment[_ngcontent-%COMP%]{margin-left:1194px}.foo[_ngcontent-%COMP%]{float:left;width:10px;height:10px;margin:-16px;border:1px solid rgba(0,0,0,.2)}.foo2[_ngcontent-%COMP%]{float:left;width:12px;height:12px;margin-right:.3rem;margin-top:3px;border:1px solid rgba(0,0,0,.2)}.outer-status-box[_ngcontent-%COMP%]{width:100%;text-align:right;margin-top:.5rem;margin-bottom:.5rem}.exclamation-circle[_ngcontent-%COMP%]{color:#f89406;font-size:16px!important;padding:0!important;margin-right:.3rem}.inner-status-box[_ngcontent-%COMP%]{display:inline-flex;font-size:14px}.mr-2[_ngcontent-%COMP%]{margin-right:2rem}.blue[_ngcontent-%COMP%]{background:#31b0d5}.red[_ngcontent-%COMP%]{background:#ef4836}.resubmit-text[_ngcontent-%COMP%]{margin-top:-22px}.resubmit-align[_ngcontent-%COMP%]{margin-top:35px;margin-right:100px}.first-align[_ngcontent-%COMP%]{margin-right:100px;position:relative;left:441px;top:31px}.single-indicator[_ngcontent-%COMP%]{position:relative;left:267px}.rejected-by-ch-align[_ngcontent-%COMP%]{position:relative;bottom:-8px}.eob-resubmit[_ngcontent-%COMP%]{position:relative;top:9px}.eob-deactive[_ngcontent-%COMP%]{position:relative;top:35px}.icon-margin[_ngcontent-%COMP%]{padding:unset!important;font-size:12px!important;vertical-align:text-top}.export-claim[_ngcontent-%COMP%]{align-self:center;padding-left:786px}.rejected-claims-alert[_ngcontent-%COMP%]{background-color:#db8e21;border-color:#db8e21;color:#fff;margin-bottom:15px;text-align:left}.rejected-claims-text[_ngcontent-%COMP%]{flex:1;font-weight:800;text-transform:uppercase;font-size:12px}.rejected-claims-close[_ngcontent-%COMP%]{background:none;border:none;font-size:18px;font-weight:700;color:#fff;cursor:pointer;padding:12px 8px;margin-left:auto;display:flex;align-items:center;justify-content:center;min-width:24px;height:24px;border-radius:2px}.rejected-claims-close[_ngcontent-%COMP%]:hover{color:#fff;opacity:.8;background-color:#8a6d3b1a}.rejected-claims-close[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 2px #8a6d3b4d}.claim-status-grid-container[_ngcontent-%COMP%]     .ag-overlay-loading-center{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;background-color:#ffffffe6}.claim-status-grid-container[_ngcontent-%COMP%]     .ag-overlay-loading-wrapper{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px;border-radius:8px;background-color:#fff;box-shadow:0 4px 12px #0000001a;border:1px solid #e0e0e0}.claim-status-grid-container[_ngcontent-%COMP%]     .ag-overlay-loading-text{margin-top:15px;font-size:14px;font-weight:500;color:#333;text-align:center}.claim-status-grid-container[_ngcontent-%COMP%]     .spinner-border{width:2rem;height:2rem;border-width:.2em}.claim-status-grid-container[_ngcontent-%COMP%]     .spinner-border.text-primary{border-color:#007bff;border-right-color:transparent}.claim-status-grid-container[_ngcontent-%COMP%]     .ag-overlay-wrapper{position:absolute;inset:0;z-index:1000}.claim-status-grid-container[_ngcontent-%COMP%]{position:relative}.claim-status-grid-container.grid-loading[_ngcontent-%COMP%]   .claim-ag-grid[_ngcontent-%COMP%]{pointer-events:none}.claim-status-grid-container.grid-loading[_ngcontent-%COMP%]   .claim-ag-grid[_ngcontent-%COMP%]     .ag-header{filter:none;opacity:1;pointer-events:auto}.claim-status-grid-container.grid-loading[_ngcontent-%COMP%]   .claim-ag-grid[_ngcontent-%COMP%]     .ag-body-viewport{filter:blur(1.5px);opacity:.6;transition:filter .3s ease-in-out,opacity .3s ease-in-out}.claim-status-grid-container.grid-loading[_ngcontent-%COMP%]   .claim-ag-grid[_ngcontent-%COMP%]     .ag-header-viewport{filter:none;opacity:1}.claim-status-loading-overlay[_ngcontent-%COMP%]{position:absolute;inset:40px 0 0;background:linear-gradient(135deg,rgba(255,255,255,.9),rgba(240,248,255,.9));display:flex;align-items:center;justify-content:center;z-index:1500;backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px)}.claim-status-loading-spinner[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px}.claim-status-pulse-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:8px;height:60px}.claim-status-pulse-dot[_ngcontent-%COMP%]{width:16px;height:16px;border-radius:50%;background:linear-gradient(45deg,#007bff,#0056b3);animation:claim-status-pulse 1.4s ease-in-out infinite both;box-shadow:0 4px 12px #007bff4d}.claim-status-pulse-dot[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.claim-status-pulse-dot[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}.claim-status-pulse-dot[_ngcontent-%COMP%]:nth-child(3){animation-delay:0s}@keyframes claim-status-pulse{0%,80%,to{transform:scale(.6);opacity:.5;box-shadow:0 2px 6px #007bff33}40%{transform:scale(1.2);opacity:1;box-shadow:0 6px 20px #007bff99}}.claim-status-loading-text[_ngcontent-%COMP%]{margin-top:15px;font-size:14px;font-weight:500;color:#666;text-align:center}.claim-status-grid-container.grid-loading[_ngcontent-%COMP%]   .claim-ag-grid[_ngcontent-%COMP%]{user-select:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none}.claim-status-grid-container.grid-loading[_ngcontent-%COMP%]   .claim-ag-grid[_ngcontent-%COMP%]     .ag-header{user-select:auto;-webkit-user-select:auto;-moz-user-select:auto;-ms-user-select:auto;pointer-events:auto;position:relative;z-index:2001}.claim-status-grid-container.grid-loading[_ngcontent-%COMP%]   .claim-ag-grid[_ngcontent-%COMP%]     .ag-header-cell-text{color:#333;font-weight:600}.claim-status-grid-container.grid-loading[_ngcontent-%COMP%]   .claim-ag-grid[_ngcontent-%COMP%]     .ag-header-cell-menu-button, .claim-status-grid-container.grid-loading[_ngcontent-%COMP%]   .claim-ag-grid[_ngcontent-%COMP%]     .ag-header-cell-filter-button, .claim-status-grid-container.grid-loading[_ngcontent-%COMP%]   .claim-ag-grid[_ngcontent-%COMP%]     .ag-sort-indicator-container{pointer-events:auto;opacity:1}\"]\n  });\n  return ClaimStatusComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}