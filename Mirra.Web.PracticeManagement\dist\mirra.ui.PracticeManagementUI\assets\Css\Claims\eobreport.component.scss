table {
    margin: 10px;
    margin-top: 20px;
    border: 1px solid #cccccc;
    width: 98%;
    border-radius: 7px;
    border-collapse: inherit;
    border-spacing: 0px;
}

th {
    background: #f8f9fa;
    height: 60px;
}

tr {
    border-bottom: 1px solid #cccccc;
}

th {
    padding: 10px;
    font-family: "Poppins-Bold";
    border-bottom: 1px solid #cccccc;
    color: #495057;
    font-size: 12px;
}

td {
    padding: 10px;
    font-family: "Poppins-SemiBold";
    border-bottom: 1px solid #cccccc;
    color: #495057;
    font-size: 12px;
}

.body {
    background-color: white;
    border-radius: 8px;
    margin-top: 10px;
    min-height: 1000px;
    box-shadow: 0px 0px 8px #0000001a;
    padding-bottom: 40px;
}

.heading {
    color: #0074bc;
    font-size: 22px;
    padding-left: 10px;
    padding-top: 20px;
    font-family: "Poppins-SemiBold";
}

.width10 {
    width: 11%;
}

.underline {
    text-decoration: underline;
    cursor: pointer;
}
