.Form-Body {
    box-shadow: 0px 0px 8px #0000001a;
    margin-right: 5px;
    padding: 5px;
    border: 1px solid #cccccc;
    border-radius: 8px;
    opacity: 1;
    background-color: white;
}

// basic styling for all tds in table
.TableTd {
    text-overflow: ellipsis;
    padding: 1rem 0.5rem !important;
    color: #272d3b;
    font-size: 12px;
    font-family: "Poppins";
}

// background effect on hover on table row
.TableTr:hover {
    background-color: #e3f2fd;
}

.subscriberID {
    padding: 15px 0 15px 12px;
}

// style for checkbox and paginator of primeng
::ng-deep .p-checkbox-box {
    border-radius: 50% !important;
}

::ng-deep .p-paginator {
    justify-content: flex-end !important;
}

/* basic setting for all input type */

input[type="text"],
input[type="number"] {
    padding-left: 5px;
    border-radius: 6px;
    border: 1px solid #cccccc !important;
    height: 30px;
    outline: none;
    min-width: 40px;
    width: 90%;
    font-size: 10px;
    font-family: "Poppins-SemiBold";
}

// styling for input when on focus
input[type="text"]:focus,
input[type="number"]:focus {
    border: 1px solid #61abd4 !important;
}

// styling for table headers
th {
    padding-right: 0px !important;
    padding-left: 0px !important;
    background-color: white !important;
}
.PaddingRight {
    padding-right: 22px !important;
}

/* for setting second heading box*/

.Form-Header-2 {
    font-size: 18px;
    font-family: "Poppins-SemiBold";
    background-color: white;
    padding: 10px;
    align-items: center;
    padding-left: 0%;
}

.Form-Header-2-Heading {
    color: #0074bc;
}

// p-table{
//     margin-left: 10px!important;
// }

.TextRight {
    text-align: end;
    justify-content: flex-end;
}

.MarginRight {
    margin-right: 10px;
}

.MarginLeft {
    margin-left: 8px;
}

.btn {
    height: 35px;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
    margin-bottom: 5%;
}

.BillerProductivitySection {
    background: #f8f8f8 0% 0% no-repeat padding-box;
    border: 1px solid #cccccc;
    border-radius: 6px;
    opacity: 1;
    margin: 12px 0px;
    padding: 8px 0px;
    font-family: "Poppins-SemiBold";
}

.BillerHeadingRow1 {
    color: #646464;
    font-size: 12px;
    font-family: "Poppins";
}

.SecondBoxHeader {
    background: #e5e5e5 0% 0% no-repeat padding-box;
    border-radius: 6px 6px 0px 0px;
    opacity: 1;
    font-size: 12px;
    font-family: "Poppins-SemiBold";
}

.SecondBoxBody {
    font-size: 12px;
    font-family: "Poppins";
    background: #f8f8f8 0% 0% no-repeat padding-box;
    border-radius: 0px 0px 6px 6px;
    opacity: 1;
}

.BillerHeading {
    color: #005488;
    font-size: 16px;
}

input[type="date"] {
    padding-left: 5px;
    outline: none;
    border: 1px solid #d9dade;
    border-radius: 8px;
    height: 30px;
    opacity: 1;
    width: 130px;
    color: #000000;
    font-size: 12px;
    font-family: "Poppins-Medium";
}

.DateLabel {
    font-size: 12px;
    color: #646464;
    font-family: "Poppins-Medium";
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

.ThBtn {
    font-family: "Poppins-Bold";
    width: 9%;
    color: #5d5d5d !important;
}

.Margin-Left-Td {
    margin-left: 50px;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}

.MainFlexRow {
    flex-wrap: wrap;
    margin-top: 10px;
    padding: 5px;
}
/* applies on full form */

.Full-Form {
    margin-left: 5px;
    font-weight: bolder;
    padding: 5px;
    /* box-shadow: 0px 0px 8px #0000001A; */
    /* border-radius: 8px; */
    opacity: 1;
    background: #f4f7fc 0% 0% no-repeat padding-box;
}

/* for setting first heading box*/

.Form-Header-Row {
    /* position: sticky;
            top: 0px; */
    background-color: white;
    // height: 50px;
    align-items: center;
    box-shadow: 0px 0px 8px #0000001a;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
}

/* for setting heading of first header */

.Form-Header {
    font-size: 23px;
    justify-content: start;
    color: #272d3b;
    font-family: "Poppins-SemiBold";
}

.reset {
    color: #0074bc;
    background-color: white;
    border: 1px solid #0074bc;
}

.reset:hover {
    color: white;
    border-color: #0074bc;
    background-color: #0074bc;
}

/* basic form background setting */

fieldset {
    width: 92%;
    margin-right: 10px;
}

object {
    pointer-events: none;
    position: relative;
}

.ParentOfObject {
    cursor: pointer;
}

/* style icon */

.inner-addon {
    position: relative;
}

.inner-addon .ArrowDown {
    position: absolute;
    padding: 10px;
}

/* align icon */

.left-addon .ArrowDown {
    left: 0px;
}

.right-addon .ArrowDown {
    pointer-events: none;
    right: 8%;
}

.SpinnerCustomDesign {
    height: 12px;
    width: 12px;
    margin-left: 10%;
}

.ArrowDown {
    color: #c9c9c9;
    cursor: pointer;
    z-index: 100;
    position: relative;
    margin-top: -4px;
}

select:active::before,
select:focus {
    box-shadow: none;
}

.ellipse {
    background-color: #11afbc;
    height: 15px;
    width: 15px;
    border-radius: 50%;
}

 
::ng-deep .p-dropdown .p-dropdown-panel {
    top: -196px!important;
}
 

::ng-deep .mat-option {
    height: auto !important;
    padding: 6px 0px 6px 6px !important;
    overflow: hidden !important;
    font-size: 10px !important;
    font-family: "Poppins" !important;
}

::ng-deep .mat-option-text {
    overflow: initial !important;
}

::ng-deep .mat-select-panel mat-option.mat-option {
    margin: 1rem 0;
    overflow: visible;
    line-height: initial;
    word-wrap: break-word;
    white-space: pre-wrap;
}

::ng-deep .mat-option-text.mat-option-text {
    white-space: normal;
    line-height: normal;
}

.Width75 {
    width: 75% !important;
}

::ng-deep .p-datatable .p-sortable-column .p-sortable-column-icon {
    margin-left: 0px !important;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    color: rgba(0, 0, 0, 0);
    opacity: 1;
    display: block;
    background: url("../../Images/Claims/CalendarIcon.svg") no-repeat;
    width: 9px;
    height: 15px;
    border-width: thin;
}

.BorderLeft {
    border-left: 5px solid #11afbc !important;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-down:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-up-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}

.DisabledIcons {
    opacity: 0.5;
    pointer-events: none;
}
