{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { <PERSON><PERSON><PERSON><PERSON>, FormControl, FormGroup } from '@angular/forms';\nimport { first } from 'rxjs';\nimport { ValidationMsgs } from 'src/app/common/common-static';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/rendering-provider/renderng-provider.service\";\nimport * as i2 from \"src/app/shared/services/subject.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"ag-grid-angular\";\n\nfunction RenderProviderComponent_form_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"form\", 3);\n    i0.ɵɵlistener(\"ngSubmit\", function RenderProviderComponent_form_0_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.searchRender());\n    });\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"div\", 7)(5, \"div\", 8)(6, \"div\", 9)(7, \"label\", 10);\n    i0.ɵɵtext(8, \" NPI \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"label\", 10);\n    i0.ɵɵtext(12, \" Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 9)(15, \"label\", 10);\n    i0.ɵɵtext(16, \" Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 9)(19, \"label\", 10);\n    i0.ɵɵtext(20, \" Tax ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 9)(23, \"label\", 15);\n    i0.ɵɵtext(24, \" Plan Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"input\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 9)(27, \"label\", 10);\n    i0.ɵɵtext(28, \" IPA Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 18)(31, \"label\", 19);\n    i0.ɵɵtext(32, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"br\");\n    i0.ɵɵelementStart(34, \"button\", 20)(35, \"i\", 21);\n    i0.ɵɵtext(36, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37, \" Search \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function RenderProviderComponent_form_0_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.showGrid());\n    });\n    i0.ɵɵelement(39, \"i\", 23);\n    i0.ɵɵtext(40, \"Back\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.renderringProviderInfo);\n  }\n}\n\nfunction RenderProviderComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"ag-grid-angular\", 25);\n    i0.ɵɵlistener(\"gridSizeChanged\", function RenderProviderComponent_div_1_Template_ag_grid_angular_gridSizeChanged_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onGridSizeChanged($event));\n    })(\"selectionChanged\", function RenderProviderComponent_div_1_Template_ag_grid_angular_selectionChanged_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onSelectionChanged($event));\n    })(\"gridReady\", function RenderProviderComponent_div_1_Template_ag_grid_angular_gridReady_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onGridReady($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"rowData\", ctx_r1.renderingProviderResult)(\"columnDefs\", ctx_r1.columnDefs)(\"gridOptions\", ctx_r1.searchBillingGridOptions)(\"pagination\", true)(\"accentedSort\", true)(\"rowSelection\", ctx_r1.rowSelection)(\"paginationNumberFormatter\", ctx_r1.searchBillingGridOptions.pagination.formatter)(\"enableCellTextSelection\", true)(\"paginationPageSize\", ctx_r1.paginationPageSize)(\"overlayNoRowsTemplate\", ctx_r1.overlayNoRowsTemplate)(\"overlayLoadingTemplate\", ctx_r1.overlayLoadingTemplate);\n  }\n}\n\nfunction RenderProviderComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵtext(2, \" No Rendering Provider selected. \");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction RenderProviderComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 30);\n    i0.ɵɵtext(2, \" Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4, \" Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 30);\n    i0.ɵɵtext(6, \" City \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 30);\n    i0.ɵɵtext(8, \" State \");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction RenderProviderComponent_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.claimFormData == null ? null : ctx_r12.claimFormData.provider == null ? null : ctx_r12.claimFormData.provider.renderingProvider == null ? null : ctx_r12.claimFormData.provider.renderingProvider.providerFullName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.claimFormData == null ? null : ctx_r12.claimFormData.provider == null ? null : ctx_r12.claimFormData.provider.renderingProvider == null ? null : ctx_r12.claimFormData.provider.renderingProvider.providerFirstAddress, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.claimFormData == null ? null : ctx_r12.claimFormData.provider == null ? null : ctx_r12.claimFormData.provider.renderingProvider == null ? null : ctx_r12.claimFormData.provider.renderingProvider.providerCity, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.claimFormData == null ? null : ctx_r12.claimFormData.provider == null ? null : ctx_r12.claimFormData.provider.renderingProvider == null ? null : ctx_r12.claimFormData.provider.renderingProvider.providerState, \" \");\n  }\n}\n\nfunction RenderProviderComponent_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 30);\n    i0.ɵɵtext(2, \" Zip \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4, \" Provider NPI \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 30);\n    i0.ɵɵtext(6, \" SSN/Tax Id \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 30);\n    i0.ɵɵtext(8, \" Taxonomy \");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction RenderProviderComponent_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.claimFormData == null ? null : ctx_r14.claimFormData.provider == null ? null : ctx_r14.claimFormData.provider.renderingProvider == null ? null : ctx_r14.claimFormData.provider.renderingProvider.providerZip, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.claimFormData == null ? null : ctx_r14.claimFormData.provider == null ? null : ctx_r14.claimFormData.provider.renderingProvider == null ? null : ctx_r14.claimFormData.provider.renderingProvider.providerNPI, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.claimFormData == null ? null : ctx_r14.claimFormData.provider == null ? null : ctx_r14.claimFormData.provider.renderingProvider == null ? null : ctx_r14.claimFormData.provider.renderingProvider.taxId, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.claimFormData == null ? null : ctx_r14.claimFormData.provider == null ? null : ctx_r14.claimFormData.provider.renderingProvider == null ? null : ctx_r14.claimFormData.provider.renderingProvider.taxonomy, \" \");\n  }\n}\n\nfunction RenderProviderComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, RenderProviderComponent_div_2_div_1_Template, 3, 0, \"div\", 26);\n    i0.ɵɵtemplate(2, RenderProviderComponent_div_2_div_2_Template, 9, 0, \"div\", 26);\n    i0.ɵɵtemplate(3, RenderProviderComponent_div_2_div_3_Template, 9, 4, \"div\", 26);\n    i0.ɵɵtemplate(4, RenderProviderComponent_div_2_div_4_Template, 9, 0, \"div\", 27);\n    i0.ɵɵtemplate(5, RenderProviderComponent_div_2_div_5_Template, 9, 4, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!!(ctx_r2.claimFormData == null ? null : ctx_r2.claimFormData.provider == null ? null : ctx_r2.claimFormData.provider.renderingProvider));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r2.claimFormData == null ? null : ctx_r2.claimFormData.provider == null ? null : ctx_r2.claimFormData.provider.renderingProvider));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r2.claimFormData == null ? null : ctx_r2.claimFormData.provider == null ? null : ctx_r2.claimFormData.provider.renderingProvider));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r2.claimFormData == null ? null : ctx_r2.claimFormData.provider == null ? null : ctx_r2.claimFormData.provider.renderingProvider));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r2.claimFormData == null ? null : ctx_r2.claimFormData.provider == null ? null : ctx_r2.claimFormData.provider.renderingProvider));\n  }\n}\n\nexport let RenderProviderComponent = /*#__PURE__*/(() => {\n  class RenderProviderComponent {\n    constructor(renderingProviderService, subjectService, renderringForm) {\n      this.renderingProviderService = renderingProviderService;\n      this.subjectService = subjectService;\n      this.renderringForm = renderringForm;\n      this.paginationPageSize = 10;\n      this.renderingProviderResult = [];\n      this.selectedRenderringProvider = new EventEmitter();\n      this.searchRenderringResult = new EventEmitter();\n      this.changeSearchGrid = new EventEmitter();\n      this.overlayNoRowsTemplate = ValidationMsgs.no_data_available;\n      this.overlayLoadingTemplate = ValidationMsgs.overlayLoadingTemplate;\n      this.isShowGrid = false;\n      this.rowSelection = 'single';\n      this.allIPACode = '';\n      this.columnDefs = [{\n        headerName: 'NPI',\n        minWidth: 180,\n        resizable: true,\n        field: 'providerNPI',\n        tooltipField: 'providerNPI'\n      }, {\n        headerName: 'Name',\n        minWidth: 280,\n        resizable: true,\n        field: 'name',\n        tooltipField: 'name',\n        cellRenderer: this.getDisplayName\n      }, {\n        headerName: 'Address',\n        minWidth: 300,\n        resizable: true,\n        field: 'providerFirstAddress',\n        tooltipField: \"providerFirstAddress\"\n      }, {\n        headerName: 'Tax ID',\n        minWidth: 180,\n        resizable: true,\n        field: 'taxId',\n        tooltipField: \"taxId\"\n      }, {\n        headerName: 'Plan Name',\n        minWidth: 280,\n        resizable: true,\n        field: 'planName',\n        tooltipField: \"planName\"\n      }, {\n        headerName: 'IPA Name',\n        minWidth: 280,\n        resizable: true,\n        field: 'ipaName',\n        tooltipField: \"ipaName\"\n      }];\n      this.selectedIPACodes = '';\n      this.createForm();\n      this.subjectSubcribe();\n      this.subjectService.getSearchRenderringProviderInfo().subscribe(res => {\n        if (res) {\n          this.renderringSearchResult = res;\n          this.renderingProviderResult = res.result;\n          this.subjectService.resetSearchMembernfo();\n          this.patchValue();\n        }\n      });\n    }\n\n    ngOnInit() {\n      this.ipaCodeItems.forEach(ipa => {\n        this.allIPACode += ipa.ipaCode + \",\";\n      });\n      this.searchBillingGridOptions = {\n        rowHeight: 30,\n        getRowStyle: this.changeRowColor,\n        defaultColDef: {\n          lockVisible: true,\n          initialWidth: 100,\n          sortable: true,\n          filter: false,\n          floatingFilter: false,\n          suppressMenu: true,\n          wrapText: true,\n          autoHeight: true,\n          floatingFilterComponentParams: {\n            suppressFilterButton: true\n          }\n        },\n        style: {\n          width: '100%',\n          height: '100%',\n          flex: '1 1 auto'\n        },\n        pagination: {\n          enable: true,\n          size: 10\n        },\n        totalRowsCount: 0,\n        overlayLoadingTemplate: this.overlayLoadingTemplate,\n        overlayNoRowsTemplate: this.overlayNoRowsTemplate\n      }; // this.fillRenderringProviderr();\n    }\n\n    ngOnChanges() {\n      if (this.isShowGrid) {\n        this.renderringProviderInfo.reset();\n        this.fillRenderringProviderr();\n      }\n    }\n\n    fillRenderringProviderr() {\n      this.renderingFormRequestLoad(); // Show loading overlay\n\n      if (this.gridApi) {\n        this.gridApi.showLoadingOverlay();\n      } else {}\n\n      this.renderingProviderService.fetchRenderingProviderByDOS(this.renderingRequest).subscribe(res => {\n        this.renderingProviderResult = [];\n\n        if (res.length > 0) {\n          res?.forEach(element => {\n            if (element.ipaCode && this.allIPACode.includes(element.ipaCode)) {\n              if (this.renderringProviderInfo.controls['npi'].value != '' && this.renderringProviderInfo.controls['npi'].value != null) {\n                if (element.providerNPI && element.providerNPI.includes(this.renderringProviderInfo.controls['npi'].value)) {\n                  if (element.providerNPI.indexOf(this.renderringProviderInfo.controls['npi'].value) > -1) {\n                    this.renderingProviderResult.push(element);\n                  }\n                }\n              } else {\n                this.renderingProviderResult.push(element);\n              }\n            }\n          });\n        } // Hide loading overlay and show results\n\n\n        if (this.renderingProviderResult.length > 0) {\n          if (this.gridApi) {\n            this.gridApi.hideOverlay();\n          }\n\n          this.searchRenderringResult.emit({\n            result: this.renderingProviderResult,\n            searchRenderring: this.renderingRequest\n          });\n        } else {\n          if (this.gridApi) {\n            this.gridApi.showNoRowsOverlay();\n          }\n        }\n      });\n    }\n\n    renderingFormRequestLoad() {\n      let rendering = {\n        sortBy: 'ProviderNPI',\n        sortOrder: 'ASC',\n        limit: 10000,\n        index: 0,\n        ipaName: this.renderringProviderInfo.controls['ipa'].value === '' ? null : this.renderringProviderInfo.controls['ipa'].value,\n        ipaCode: this.allIPACode,\n        taxId: this.renderringProviderInfo.controls['tax'].value === '' ? null : this.renderringProviderInfo.controls['tax'].value,\n        address1: this.renderringProviderInfo.controls['address'].value === '' ? null : this.renderringProviderInfo.controls['address'].value,\n        firstName: this.renderringProviderInfo.controls['name'].value === '' ? null : this.renderringProviderInfo.controls['name'].value,\n        providerNPI: this.renderringProviderInfo.controls['npi'].value === '' ? null : this.renderringProviderInfo.controls['npi'].value,\n        startDate: new Date(this.claimFormData.profileMember.dOSFrom),\n        endDate: new Date(this.claimFormData.profileMember.dOSTo),\n        planName: this.renderringProviderInfo.controls['planName'].value === '' ? null : this.renderringProviderInfo.controls['planName'].value\n      };\n      this.renderingRequest = rendering;\n    }\n\n    changeRowColor(params) {\n      if (params.node.rowIndex % 2 === 0) {\n        return {\n          'background-color': '#f1f0f0'\n        };\n      } else {\n        return {\n          'background-color': 'white'\n        };\n      }\n    }\n\n    selectedIPACode(event) {\n      this.selectedIPACodes = '';\n      event.forEach(element => {\n        this.selectedIPACodes += this.selectedIPACodes + ',' + element.mdmCode;\n      });\n    }\n\n    onSelectionChanged(e) {\n      const selectedRows = this.gridApi.getSelectedRows();\n      selectedRows.forEach(element => {\n        this.selectedRenderringProvider.emit(element);\n        this.isShowGrid = false;\n        this.changeSearchGrid.emit(false);\n      });\n      this.renderringProviderInfo.reset();\n    }\n\n    searchRender() {\n      this.renderingFormRequestLoad(); // Show loading overlay\n\n      if (this.gridApi) {\n        this.gridApi.showLoadingOverlay();\n      }\n\n      this.renderingProviderService.fetchRenderingProviderSearch(this.renderingRequest).subscribe(res => {\n        this.renderingProviderResult = [];\n        res?.forEach(element => {\n          if (element.ipaCode && this.allIPACode.includes(element.ipaCode)) {\n            if (this.renderringProviderInfo.controls['npi'].value != '' && this.renderringProviderInfo.controls['npi'].value != null) {\n              if (element.providerNPI && element.providerNPI.includes(this.renderringProviderInfo.controls['npi'].value)) {\n                if (element.providerNPI.indexOf(this.renderringProviderInfo.controls['npi'].value) > -1) {\n                  this.renderingProviderResult.push(element);\n                }\n              }\n            } else {\n              this.renderingProviderResult.push(element);\n            }\n          }\n        }); // Hide loading overlay and show results\n\n        if (this.renderingProviderResult.length > 0) {\n          if (this.gridApi) {\n            this.gridApi.hideOverlay();\n          }\n\n          this.searchRenderringResult.emit({\n            result: this.renderingProviderResult,\n            searchRenderring: this.renderingRequest\n          });\n        } else {\n          if (this.gridApi) {\n            this.gridApi.showNoRowsOverlay();\n          }\n        }\n      });\n    }\n\n    onGridReady(params) {\n      this.gridApi = params.api; // Test the overlay immediately\n\n      setTimeout(() => {\n        if (this.gridApi) {\n          this.gridApi.showLoadingOverlay();\n          setTimeout(() => {\n            this.gridApi.hideOverlay();\n          }, 2000);\n        }\n      }, 100);\n    }\n\n    createForm() {\n      this.renderringProviderInfo = this.renderringForm.group({\n        address: new FormControl(''),\n        ipa: new FormControl(''),\n        tax: new FormControl(''),\n        name: new FormControl(''),\n        npi: new FormControl(''),\n        planName: new FormControl('')\n      });\n      return this.renderringProviderInfo;\n    }\n\n    patchValue() {\n      this.renderringProviderInfo.patchValue({\n        address: this.renderringSearchResult.searchRenderring.address1,\n        ipa: this.renderringSearchResult.searchRenderring.ipaName,\n        tax: this.renderringSearchResult.searchRenderring.taxId,\n        name: this.renderringSearchResult.searchRenderring.firstName,\n        npi: this.renderringSearchResult.searchRenderring.providerNPI\n      });\n    }\n\n    get f() {\n      return this.renderringProviderInfo.controls;\n    }\n\n    showGrid() {\n      this.isShowGrid = !this.isShowGrid;\n      this.changeSearchGrid.emit(this.isShowGrid);\n    }\n\n    subjectSubcribe() {\n      this.subjectService.getRender().pipe(first()).subscribe(res => {\n        if (res) {\n          this.providerInfoData = res;\n          this.isShowGrid = false;\n          this.changeSearchGrid.emit(false);\n          this.subjectService.resetRender();\n        }\n      });\n    }\n\n    getDisplayName(params) {\n      return (params?.data?.providerLastName === null ? '' : params?.data?.providerLastName) + ' ' + (params?.data?.providerFirstName === null ? '' : params?.data?.providerFirstName) + ' ' + (params?.data?.providerMiddleName === null ? '' : params?.data?.providerMiddleName);\n    }\n\n    onGridSizeChanged(e) {\n      this.searchBillingGridOptions.api.sizeColumnsToFit();\n    }\n\n  }\n\n  RenderProviderComponent.ɵfac = function RenderProviderComponent_Factory(t) {\n    return new (t || RenderProviderComponent)(i0.ɵɵdirectiveInject(i1.RenderingProviderService), i0.ɵɵdirectiveInject(i2.SubjectService), i0.ɵɵdirectiveInject(i3.FormBuilder));\n  };\n\n  RenderProviderComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RenderProviderComponent,\n    selectors: [[\"app-render-provider\"]],\n    inputs: {\n      ipaCodeItems: \"ipaCodeItems\",\n      claimFormData: \"claimFormData\",\n      isShowGrid: \"isShowGrid\"\n    },\n    outputs: {\n      selectedRenderringProvider: \"selectedRenderringProvider\",\n      searchRenderringResult: \"searchRenderringResult\",\n      changeSearchGrid: \"changeSearchGrid\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 3,\n    vars: 3,\n    consts: [[3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"row grid-deal-search mt-2\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"toggle-section\"], [1, \"detail-progress-bx\"], [1, \"row\", \"mt-25\"], [1, \"col-md-9\"], [1, \"row\", \"search-heading\"], [1, \"col-md-2\"], [1, \"dashboard-label\", \"ml-5\"], [\"type\", \"text\", \"placeholder\", \"NPI\", \"formControlName\", \"npi\", \"id\", \"npi\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Name\", \"formControlName\", \"name\", \"id\", \"name\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Address\", \"formControlName\", \"address\", \"id\", \"address\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"Tax ID\", \"formControlName\", \"tax\", \"id\", \"tax\", 1, \"form-control\", \"form-control-sm\"], [1, \"dashboard-label\", \"ml-5\", 2, \"width\", \"170px\"], [\"type\", \"text\", \"placeholder\", \"Plan Name\", \"formControlName\", \"planName\", \"id\", \"planName\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"IPA Name\", \"formControlName\", \"ipa\", \"id\", \"tax\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-md-3\", 2, \"text-align\", \"right\", \"align-self\", \"flex-end\"], [1, \"dashboard-label\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"form-control-sm\", \"flr\", \"primary-btn\"], [1, \"material-icons\", \"icon-margin\", \"icon-height\", \"icon-align\"], [1, \"btn\", \"btn-primary\", \"form-control-sm\", \"primary-btn\", 3, \"click\"], [1, \"fa\", \"fa-chevron-circle-left\", \"icon-margin\"], [1, \"row\", \"grid-deal-search\", \"mt-2\"], [\"id\", \"memberFacilityGrid\", 1, \"ag-theme-alpine\", \"ag-grid-view\", 3, \"rowData\", \"columnDefs\", \"gridOptions\", \"pagination\", \"accentedSort\", \"rowSelection\", \"paginationNumberFormatter\", \"enableCellTextSelection\", \"paginationPageSize\", \"overlayNoRowsTemplate\", \"overlayLoadingTemplate\", \"gridSizeChanged\", \"selectionChanged\", \"gridReady\"], [\"class\", \"row\", 4, \"ngIf\"], [\"class\", \"row mt-2\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-md-12\"], [1, \"col-md-3\", \"dashboard-label\", \"boldTxt\", \"sub-title-name-display\"], [1, \"col-md-3\"], [1, \"row\", \"mt-2\"]],\n    template: function RenderProviderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, RenderProviderComponent_form_0_Template, 41, 1, \"form\", 0);\n        i0.ɵɵtemplate(1, RenderProviderComponent_div_1_Template, 2, 11, \"div\", 1);\n        i0.ɵɵtemplate(2, RenderProviderComponent_div_2_Template, 6, 5, \"div\", 2);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isShowGrid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isShowGrid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isShowGrid);\n      }\n    },\n    dependencies: [i4.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i5.AgGridAngular, i3.FormGroupDirective, i3.FormControlName],\n    styles: [\".grid-deal-search[_ngcontent-%COMP%]{width:100%;flex:1 1 auto;--bs-gutter-x: 0rem!important;height:40vh}.title-name-display[_ngcontent-%COMP%]{font-size:16px;color:#617798;font-weight:800}.sub-title-name-display[_ngcontent-%COMP%]{font-size:12px;color:#617798;font-weight:800}.search-heading[_ngcontent-%COMP%]   .dashboard-label[_ngcontent-%COMP%]{margin-bottom:.25rem}.form-control[_ngcontent-%COMP%], .btn[_ngcontent-%COMP%]{height:2.2rem}.toggle-section[_ngcontent-%COMP%]{background:#fff;padding:15px;border:1px solid #ced4da;border-radius:5px;margin-top:.5rem}.detail-progress-bx[_ngcontent-%COMP%]{background:#f8f8f8;padding:15px;border:1px solid #ced4da;border-radius:5px}\"]\n  });\n  return RenderProviderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}