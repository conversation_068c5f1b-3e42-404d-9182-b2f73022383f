.body {
    background-color: white;
    border-radius: 8px;
    margin-top: 10px;
    height: 1000px;
    box-shadow: 0px 0px 8px #0000001a;
}

.heading {
    color: #0074BC;
    font-size: 22px;
    padding-left: 10px;
    padding-top: 20px;
    font-family: "Poppins-SemiBold"
}

table {
    margin: 10px;
    margin-top: 20px;
    border: 1px solid #CCCCCC;
    width: 98%;
    border-radius: 7px;
    border-collapse: inherit;
    border-spacing: 0px;
}

th {
    background-color: #E5E5E5;
    height: 60px;
    font-size: 16px;
    font-family: "Poppins-SemiBold";
}

tr {
    border-bottom: 1px solid #CCCCCC;
}

th, td {
    padding: 10px;
    font-family: "Poppins";
    border-bottom: 1px solid #CCCCCC;
} 

.left-padding {
    padding-left: 30px;
}

object {
    position: relative;
    z-index: -1!important;
}

.IconStyle {
    cursor: pointer;
    position: relative;
    z-index: 1!important;
}