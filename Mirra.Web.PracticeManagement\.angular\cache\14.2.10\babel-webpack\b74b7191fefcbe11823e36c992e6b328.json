{"ast": null, "code": "import { ElementRef, EventEmitter } from '@angular/core';\nimport { PayerInfoComponent } from './payer-info/payer-info.component';\nimport { DiagnosisNatureComponent } from './diagnosis-nature/diagnosis-nature.component';\nimport { CRCConditionasIndicatoeProfessional837s, ClaimListDetailsModel, ClaimsProfessional837Model, CreateClaimModel, DTPDateTimePeriodProfessional837, DependentProfessional837sModel, ICDCodesModel, OtherInfoProfessionalOtherInfoProfessionalModel, PerAdministrativeCommunicationContactProfessional837sModel, ServiceLineDetailsModel, ServiceLineProfessional837sModel, SubscriberkeyNavigationModel } from 'src/app/models/ClaimForm/claim.create.model';\nimport { PatientInfoComponent } from './patient-info/patient-info.component';\nimport { ServiceFacilityComponent } from './service-facility/service-facility.component';\nimport { SubscriberInfoComponent } from './subscriber-info/subscriber-info.component';\nimport { PatientDobComponent } from './patient-dob/patient-dob.component';\nimport { PatientAddressComponent } from './patient-address/patient-address.component';\nimport { PatientRelationshipComponent } from './patient-relationship/patient-relationship.component';\nimport { InsureInfoComponent } from './insure-info/insure-info.component';\nimport { InsureAddressComponent } from './insure-address/insure-address.component';\nimport { InsurePolicyGroupComponent } from './insure-policy-group/insure-policy-group.component';\nimport { PatientSignatureComponent } from './patient-signature/patient-signature.component';\nimport { OtherProviderComponent } from './other-provider/other-provider.component';\nimport { CurrentIllnessComponent } from './current-illness/current-illness.component';\nimport { OtherDateComponent } from './other-date/other-date.component';\nimport { InsureAuthorizedComponent } from './insure-authorized/insure-authorized.component';\nimport { OtherProviderPayerComponent } from './other-provider-payer/other-provider-payer.component';\nimport { OutsideLabComponent } from './outside-lab/outside-lab.component';\nimport { PatientCurrentOccuptionComponent } from './patient-current-occuption/patient-current-occuption.component';\nimport { HospitalizationServiceComponent } from './hospitalization-service/hospitalization-service.component';\nimport { ServiceLineClaimComponent } from './service-line-claim/service-line-claim.component';\nimport { BillingInfoComponent } from './billing-info/billing-info.component';\nimport { ProviderSignComponent } from './provider-sign/provider-sign.component';\nimport { FederalTaxComponent } from './federal-tax/federal-tax.component';\nimport { PatientAccountAcceptAssignmentComponent } from './patient-account-accept-assignment/patient-account-accept-assignment.component';\nimport { ChargesComponent } from './charges/charges.component';\nimport { ResubmissionAuthorizedNumComponent } from './resubmission-authorized-num/resubmission-authorized-num.component';\nimport { AdditionalClaimComponent } from './additional-claim/additional-claim.component';\nimport { PatientOtherInsureComponent } from './patient-other-insure/patient-other-insure.component';\nimport { PatientConditionComponent } from './patient-condition/patient-condition.component';\nimport { SaveModalComponent } from 'src/app/modals/save-modal/save-modal.component';\nimport { MediGroupComponent } from './medi-group/medi-group.component';\nimport { Validators } from '@angular/forms';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport { UpdateClaimComponent } from 'src/app/modals/update-claim/update-claim.component';\nimport { DublicateClaimComponent } from 'src/app/modals/dublicate-claim/dublicate-claim.component';\nimport { NavTabFromDetails, Tabs } from 'src/app/common/nav-constant';\nimport { CLAIM_TYPE, PREVILEGES, ValidationMsgs } from 'src/app/common/common-static';\nimport { ReasonToHoldComponent } from 'src/app/modals/reason-to-hold/reason-to-hold.component';\nimport { ResubmitClaimComponent } from 'src/app/components/popups/resubmit-claim/resubmit-claim.component';\nimport { RejectedInComponent } from 'src/app/components/popups/rejected-in/rejected-in.component';\nimport { PendingDetailsComponent } from 'src/app/components/popups/pending-details/pending-details.component';\nimport { LinkedClaimsComponent } from 'src/app/components/popups/linked-claims/linked-claims.component';\nimport { Observable, distinctUntilChanged, forkJoin, map, of, switchMap } from 'rxjs';\nimport { ValidateAddressComponent } from 'src/app/components/popups/validate-address/validate-address.component';\nimport { AddressMismatchComponent } from 'src/app/shared/components/address-mismatch/address-mismatch.component';\nimport * as moment from 'moment';\nimport { LocalStorageKey } from 'src/app/shared/constant/constatnt';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/ClaimForm/claim.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"src/app/shared/services/subject.service\";\nimport * as i4 from \"src/app/services/Notification/notification.service\";\nimport * as i5 from \"src/app/services/file/file.service\";\nimport * as i6 from \"src/app/shared/services/dateformat\";\nimport * as i7 from \"src/app/services/ProviderManagement/provider-management.service\";\nimport * as i8 from \"src/app/shared/services/global.service\";\nimport * as i9 from \"src/app/services/cache-service/cache.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/material/tooltip\";\nimport * as i12 from \"./payer-info/payer-info.component\";\nimport * as i13 from \"./medi-group/medi-group.component\";\nimport * as i14 from \"./patient-info/patient-info.component\";\nimport * as i15 from \"./patient-dob/patient-dob.component\";\nimport * as i16 from \"./patient-address/patient-address.component\";\nimport * as i17 from \"./patient-relationship/patient-relationship.component\";\nimport * as i18 from \"./patient-other-insure/patient-other-insure.component\";\nimport * as i19 from \"./patient-condition/patient-condition.component\";\nimport * as i20 from \"./patient-signature/patient-signature.component\";\nimport * as i21 from \"./current-illness/current-illness.component\";\nimport * as i22 from \"./other-date/other-date.component\";\nimport * as i23 from \"./other-provider/other-provider.component\";\nimport * as i24 from \"./other-provider-payer/other-provider-payer.component\";\nimport * as i25 from \"./additional-claim/additional-claim.component\";\nimport * as i26 from \"./diagnosis-nature/diagnosis-nature.component\";\nimport * as i27 from \"./federal-tax/federal-tax.component\";\nimport * as i28 from \"./patient-account-accept-assignment/patient-account-accept-assignment.component\";\nimport * as i29 from \"./provider-sign/provider-sign.component\";\nimport * as i30 from \"./service-facility/service-facility.component\";\nimport * as i31 from \"./insure-info/insure-info.component\";\nimport * as i32 from \"./insure-address/insure-address.component\";\nimport * as i33 from \"./insure-policy-group/insure-policy-group.component\";\nimport * as i34 from \"./insure-authorized/insure-authorized.component\";\nimport * as i35 from \"./patient-current-occuption/patient-current-occuption.component\";\nimport * as i36 from \"./hospitalization-service/hospitalization-service.component\";\nimport * as i37 from \"./outside-lab/outside-lab.component\";\nimport * as i38 from \"./resubmission-authorized-num/resubmission-authorized-num.component\";\nimport * as i39 from \"./charges/charges.component\";\nimport * as i40 from \"./billing-info/billing-info.component\";\nimport * as i41 from \"./service-line-claim/service-line-claim.component\";\nimport * as i42 from \"./subscriber-info/subscriber-info.component\";\nimport * as i43 from \"./notes/notes.component\";\n\nfunction CreateClaimComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onSubmit());\n    });\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.open(\"ON\"));\n    });\n    i0.ɵɵelement(2, \"i\", 38);\n    i0.ɵɵtext(3, \"Open\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.accept(\"AC\"));\n    });\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵtext(3, \"Accept\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.deactivateClaim());\n    });\n    i0.ɵɵelementStart(2, \"i\", 40);\n    i0.ɵɵtext(3, \"do_disturb_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \"Deactivate\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_4_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.onHold(\"OH\"));\n    });\n    i0.ɵɵelementStart(2, \"i\", 41);\n    i0.ɵɵtext(3, \"pause_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \"On Hold\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.displayNotes());\n    });\n    i0.ɵɵelementStart(2, \"i\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r21.notesLength() === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r21.showNotes ? \"speaker_notes_off\" : \"speaker_notes\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r21.showNotes ? \"Hide\" : \"Show\", \" Notes ( \", ctx_r21.notesLength(), \" )\");\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.saveAllNotes());\n    });\n    i0.ɵɵelement(2, \"i\", 44);\n    i0.ɵɵtext(3, \" Save All Notes \");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r22.saveAllNotesEnable);\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r40.onSubmit());\n    });\n    i0.ɵɵelementStart(2, \"i\", 40);\n    i0.ɵɵtext(3, \"save_as\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \"Update\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.resubmission());\n    });\n    i0.ɵɵelement(2, \"i\", 45);\n    i0.ɵɵtext(3, \"Resubmit\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_13_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.exportClaim());\n    });\n    i0.ɵɵelement(2, \"i\", 47);\n    i0.ɵɵtext(3, \"Export\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_14_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.viewClaimClose());\n    });\n    i0.ɵɵtext(2, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CreateClaimComponent_div_6_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_div_15_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.viewClaimClose());\n    });\n    i0.ɵɵtext(2, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CreateClaimComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, CreateClaimComponent_div_6_div_1_Template, 4, 0, \"div\", 9);\n    i0.ɵɵtemplate(2, CreateClaimComponent_div_6_div_2_Template, 4, 0, \"div\", 9);\n    i0.ɵɵtemplate(3, CreateClaimComponent_div_6_div_3_Template, 5, 0, \"div\", 9);\n    i0.ɵɵtemplate(4, CreateClaimComponent_div_6_div_4_Template, 5, 0, \"div\", 9);\n    i0.ɵɵtemplate(5, CreateClaimComponent_div_6_div_5_Template, 5, 4, \"div\", 9);\n    i0.ɵɵelementStart(6, \"div\")(7, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_6_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.addNote());\n    });\n    i0.ɵɵelement(8, \"i\", 37);\n    i0.ɵɵtext(9, \"Add Notes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, CreateClaimComponent_div_6_div_10_Template, 4, 1, \"div\", 9);\n    i0.ɵɵtemplate(11, CreateClaimComponent_div_6_div_11_Template, 5, 0, \"div\", 9);\n    i0.ɵɵtemplate(12, CreateClaimComponent_div_6_div_12_Template, 4, 0, \"div\", 9);\n    i0.ɵɵtemplate(13, CreateClaimComponent_div_6_div_13_Template, 4, 0, \"div\", 9);\n    i0.ɵɵtemplate(14, CreateClaimComponent_div_6_div_14_Template, 3, 0, \"div\", 9);\n    i0.ɵɵtemplate(15, CreateClaimComponent_div_6_div_15_Template, 3, 0, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isShowUpdateBtn && ((ctx_r1.claimFormData.claimViewModel == null ? null : ctx_r1.claimFormData.claimViewModel.claimFormStatusCode) === \"OH\" || (ctx_r1.claimFormData.claimViewModel == null ? null : ctx_r1.claimFormData.claimViewModel.claimFormStatusCode) === \"AC\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isAcceptedClaimBtnShow && ctx_r1.isShowUpdateBtn && ((ctx_r1.claimFormData.claimViewModel == null ? null : ctx_r1.claimFormData.claimViewModel.claimFormStatusCode) === \"OH\" || (ctx_r1.claimFormData.claimViewModel == null ? null : ctx_r1.claimFormData.claimViewModel.claimFormStatusCode) === \"ON\" || (ctx_r1.claimFormData.claimViewModel == null ? null : ctx_r1.claimFormData.claimViewModel.claimFormStatusCode) === \"NTR\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isDeactivateClaimBtnShow && ctx_r1.isShowUpdateBtn && ((ctx_r1.claimFormData.claimViewModel == null ? null : ctx_r1.claimFormData.claimViewModel.claimFormStatusCode) === \"OH\" || (ctx_r1.claimFormData.claimViewModel == null ? null : ctx_r1.claimFormData.claimViewModel.claimFormStatusCode) === \"ON\" || (ctx_r1.claimFormData.claimViewModel == null ? null : ctx_r1.claimFormData.claimViewModel.claimFormStatusCode) === \"NTR\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isOnHoldClainBtnShow && ctx_r1.isShowUpdateBtn && ((ctx_r1.claimFormData.claimViewModel == null ? null : ctx_r1.claimFormData.claimViewModel.claimFormStatusCode) === \"AC\" || (ctx_r1.claimFormData.claimViewModel == null ? null : ctx_r1.claimFormData.claimViewModel.claimFormStatusCode) === \"ON\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isShowUpdateBtn || ctx_r1.claimFormData.isViewClaim || ctx_r1.isNewClaim);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isShowUpdateBtn && ctx_r1.isUpdateClaimBtnShow);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.claimFormData.isViewClaim && ctx_r1.showResubmit());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isShowUpdateBtn || !ctx_r1.isNewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isShowUpdateBtn && !ctx_r1.isNewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isShowUpdateBtn);\n  }\n}\n\nfunction CreateClaimComponent_div_7_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_7_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r57.activateClaim());\n    });\n    i0.ɵɵelementStart(1, \"i\", 40);\n    i0.ɵɵtext(2, \"check_circle_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Activate\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_7_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.displayNotes());\n    });\n    i0.ɵɵelementStart(2, \"i\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r53.notesLength() === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r53.showNotes ? \"speaker_notes_off\" : \"speaker_notes\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r53.showNotes ? \"Hide\" : \"Show\", \" Notes ( \", ctx_r53.notesLength(), \" )\");\n  }\n}\n\nfunction CreateClaimComponent_div_7_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_7_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r61.saveAllNotes());\n    });\n    i0.ɵɵelement(2, \"i\", 44);\n    i0.ɵɵtext(3, \" Save All Notes \");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r54.saveAllNotesEnable);\n  }\n}\n\nfunction CreateClaimComponent_div_7_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_7_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r63.exportClaim());\n    });\n    i0.ɵɵelement(2, \"i\", 47);\n    i0.ɵɵtext(3, \"Export\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CreateClaimComponent_div_7_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_7_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.viewClaimClose());\n    });\n    i0.ɵɵtext(1, \"Close\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, CreateClaimComponent_div_7_button_1_Template, 4, 0, \"button\", 49);\n    i0.ɵɵtemplate(2, CreateClaimComponent_div_7_div_2_Template, 5, 4, \"div\", 9);\n    i0.ɵɵelementStart(3, \"div\")(4, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r67.addNote());\n    });\n    i0.ɵɵelement(5, \"i\", 37);\n    i0.ɵɵtext(6, \"Add Notes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, CreateClaimComponent_div_7_div_7_Template, 4, 1, \"div\", 9);\n    i0.ɵɵtemplate(8, CreateClaimComponent_div_7_div_8_Template, 4, 0, \"div\", 9);\n    i0.ɵɵtemplate(9, CreateClaimComponent_div_7_button_9_Template, 2, 0, \"button\", 50);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isShowUpdateBtn);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isShowUpdateBtn || ctx_r2.claimFormData.isViewClaim || ctx_r2.isNewClaim);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isShowUpdateBtn || ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isNewClaim);\n  }\n}\n\nfunction CreateClaimComponent_div_8_div_1_app_notes_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r73 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-notes\", 55);\n    i0.ɵɵlistener(\"dismiss\", function CreateClaimComponent_div_8_div_1_app_notes_1_Template_app_notes_dismiss_0_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r72 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r72.deleteNote($event));\n    })(\"focusout\", function CreateClaimComponent_div_8_div_1_app_notes_1_Template_app_notes_focusout_0_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r74 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r74.saveNote($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const note_r70 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"id\", note_r70 == null ? null : note_r70.notesPkId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(note_r70 == null ? null : note_r70.description);\n  }\n}\n\nfunction CreateClaimComponent_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CreateClaimComponent_div_8_div_1_app_notes_1_Template, 2, 2, \"app-notes\", 54);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const note_r70 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(note_r70 == null ? null : note_r70.isRemoved));\n  }\n}\n\nfunction CreateClaimComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵtemplate(1, CreateClaimComponent_div_8_div_1_Template, 2, 1, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.claimFormData.claimViewModel == null ? null : ctx_r3.claimFormData.claimViewModel.notes);\n  }\n}\n\nfunction CreateClaimComponent_div_11_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" (DEACTIVE) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_11_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"span\", 58);\n    i0.ɵɵtext(3, \" Parent ID:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 62);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_11_div_21_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.parentClaimNavigation());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r77 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r77.claimFormData.claimViewModel == null ? null : ctx_r77.claimFormData.claimViewModel.parentPatientCtrlNo, \"\");\n  }\n}\n\nfunction CreateClaimComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 56)(2, \"div\", 57)(3, \"span\", 58);\n    i0.ɵɵtext(4, \" Current Claim Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"uppercase\");\n    i0.ɵɵtemplate(7, CreateClaimComponent_div_11_div_7_Template, 2, 0, \"div\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 56)(9, \"div\", 57)(10, \"span\", 58);\n    i0.ɵɵtext(11, \" Created on:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 56)(15, \"div\", 57)(16, \"span\", 58);\n    i0.ɵɵtext(17, \" Created By:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"uppercase\");\n    i0.ɵɵpipe(20, \"uppercase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, CreateClaimComponent_div_11_div_21_Template, 6, 1, \"div\", 59);\n    i0.ɵɵelementStart(22, \"div\", 56)(23, \"div\", 60);\n    i0.ɵɵelement(24, \"label\", 61);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.changeStatusText(i0.ɵɵpipeBind1(6, 6, ctx_r4.claimFormData.claimViewModel == null ? null : ctx_r4.claimFormData.claimViewModel.claimFormStatusCode)), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r4.claimFormData.claimViewModel == null ? null : ctx_r4.claimFormData.claimViewModel.isActive));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(13, 8, ctx_r4.claimFormData.claimViewModel == null ? null : ctx_r4.claimFormData.claimViewModel.formCreatedDate, \"MM/dd/yyyy HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(19, 11, ctx_r4.claimFormData.claimViewModel == null ? null : ctx_r4.claimFormData.claimViewModel.userLastName), \" \", i0.ɵɵpipeBind1(20, 13, ctx_r4.claimFormData.claimViewModel == null ? null : ctx_r4.claimFormData.claimViewModel.userFirstName), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r4.claimFormData.claimViewModel == null ? null : ctx_r4.claimFormData.claimViewModel.parentPatientCtrlNo) != null && (ctx_r4.claimFormData.claimViewModel == null ? null : ctx_r4.claimFormData.claimViewModel.parentPatientCtrlNo) != (ctx_r4.claimFormData.isParentClaim ? ctx_r4.claimFormData.claimViewModel == null ? null : ctx_r4.claimFormData.claimViewModel.parentPatientCtrlNo : ctx_r4.claimFormData.claimViewModel == null ? null : ctx_r4.claimFormData.claimViewModel.patientCtrlNo));\n  }\n}\n\nfunction CreateClaimComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 56)(2, \"div\", 57)(3, \"span\", 58);\n    i0.ɵɵtext(4, \" Held On :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 56)(8, \"div\", 57)(9, \"span\", 58);\n    i0.ɵɵtext(10, \" Held By :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"uppercase\");\n    i0.ɵɵpipe(13, \"uppercase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 56)(15, \"div\", 57)(16, \"span\", 58);\n    i0.ɵɵtext(17, \" Reason for holding :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"uppercase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 56)(21, \"div\", 57)(22, \"span\", 58);\n    i0.ɵɵtext(23, \" Description :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"uppercase\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(6, 5, ctx_r5.claimFormData.claimViewModel == null ? null : ctx_r5.claimFormData.claimViewModel.lastModifiedDate, \"MM/dd/yyyy HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(12, 8, ctx_r5.claimFormData.claimViewModel == null ? null : ctx_r5.claimFormData.claimViewModel.lastModifiedByLastName), \" \", i0.ɵɵpipeBind1(13, 10, ctx_r5.claimFormData.claimViewModel == null ? null : ctx_r5.claimFormData.claimViewModel.lastModifiedByFirstName), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 12, ctx_r5.reasonHoldingDesc(ctx_r5.claimFormData.claimViewModel == null ? null : ctx_r5.claimFormData.claimViewModel.reasons)), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 14, ctx_r5.reasonDesc(ctx_r5.claimFormData.claimViewModel == null ? null : ctx_r5.claimFormData.claimViewModel.reasons)), \" \");\n  }\n}\n\nfunction CreateClaimComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 56)(2, \"div\", 57);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 56)(6, \"div\", 57);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"uppercase\");\n    i0.ɵɵpipe(9, \"uppercase\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Accepted On :\", i0.ɵɵpipeBind2(4, 3, ctx_r6.claimFormData.claimViewModel == null ? null : ctx_r6.claimFormData.claimViewModel.lastModifiedDate, \"MM/dd/yyyy HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" Accepted By :\", i0.ɵɵpipeBind1(8, 6, ctx_r6.claimFormData.claimViewModel == null ? null : ctx_r6.claimFormData.claimViewModel.lastModifiedByLastName), \" \", i0.ɵɵpipeBind1(9, 8, ctx_r6.claimFormData.claimViewModel == null ? null : ctx_r6.claimFormData.claimViewModel.lastModifiedByFirstName), \" \");\n  }\n}\n\nfunction CreateClaimComponent_div_15_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r81 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r81.claimFormData.claimViewModel == null ? null : ctx_r81.claimFormData.claimViewModel._999processedOnCh, \"MM/dd/yyyy\"));\n  }\n}\n\nfunction CreateClaimComponent_div_15_div_1_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r84 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 64);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_15_div_1_i_5_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r83 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r83.downloadLatestResponseFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"span\", 58);\n    i0.ɵɵtext(3, \"Acknowledged By Clearing House On: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CreateClaimComponent_div_15_div_1_span_4_Template, 3, 4, \"span\", 9);\n    i0.ɵɵtemplate(5, CreateClaimComponent_div_15_div_1_i_5_Template, 1, 0, \"i\", 63);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r80 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r80.claimFormData.claimViewModel == null ? null : ctx_r80.claimFormData.claimViewModel._999processedOnCh));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r80.claimFormData.claimViewModel == null ? null : ctx_r80.claimFormData.claimViewModel.incomingFileId) || !!(ctx_r80.claimFormData.claimViewModel == null ? null : ctx_r80.claimFormData.claimViewModel.responseFileClaimMapings[0].incomeFileLoggerId));\n  }\n}\n\nfunction CreateClaimComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CreateClaimComponent_div_15_div_1_Template, 6, 2, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (!!(ctx_r7.claimFormData.claimViewModel == null ? null : ctx_r7.claimFormData.claimViewModel._999processedOnCh) || !!(ctx_r7.claimFormData.claimViewModel == null ? null : ctx_r7.claimFormData.claimViewModel.incomingFileId)) && (ctx_r7.claimFormData.claimViewModel == null ? null : ctx_r7.claimFormData.claimViewModel.responseFileClaimMapings.length) > 0);\n  }\n}\n\nfunction CreateClaimComponent_div_16_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r86 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r86.claimFormData.claimViewModel == null ? null : ctx_r86.claimFormData.claimViewModel._999processedOnCh, \"MM/dd/yyyy\"));\n  }\n}\n\nfunction CreateClaimComponent_div_16_div_1_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 64);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_16_div_1_i_5_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r88 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r88.downloadLatestResponseFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"span\", 58);\n    i0.ɵɵtext(3, \"Accepted By Clearing House On: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CreateClaimComponent_div_16_div_1_span_4_Template, 3, 4, \"span\", 9);\n    i0.ɵɵtemplate(5, CreateClaimComponent_div_16_div_1_i_5_Template, 1, 0, \"i\", 63);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r85 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r85.claimFormData.claimViewModel == null ? null : ctx_r85.claimFormData.claimViewModel._277processedOnCh));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r85.claimFormData.claimViewModel == null ? null : ctx_r85.claimFormData.claimViewModel.incomingFileId));\n  }\n}\n\nfunction CreateClaimComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CreateClaimComponent_div_16_div_1_Template, 6, 2, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r8.claimFormData.claimViewModel == null ? null : ctx_r8.claimFormData.claimViewModel._277processedOnCh) || !!(ctx_r8.claimFormData.claimViewModel == null ? null : ctx_r8.claimFormData.claimViewModel.incomingFileId));\n  }\n}\n\nfunction CreateClaimComponent_div_17_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r92 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r92.claimFormData.claimViewModel == null ? null : ctx_r92.claimFormData.claimViewModel._835processedOnDate, \"MM/dd/yyyy\"));\n  }\n}\n\nfunction CreateClaimComponent_div_17_div_1_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r95 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 64);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_17_div_1_i_5_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r95);\n      const ctx_r94 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r94.downloadLatestResponseFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"span\", 58);\n    i0.ɵɵtext(3, \"EOB Received On: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CreateClaimComponent_div_17_div_1_span_4_Template, 3, 4, \"span\", 9);\n    i0.ɵɵtemplate(5, CreateClaimComponent_div_17_div_1_i_5_Template, 1, 0, \"i\", 63);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r90 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r90.claimFormData.claimViewModel == null ? null : ctx_r90.claimFormData.claimViewModel._835processedOnDate));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r90.claimFormData.claimViewModel == null ? null : ctx_r90.claimFormData.claimViewModel.incomingFileId));\n  }\n}\n\nfunction CreateClaimComponent_div_17_div_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r98 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_17_div_2_div_6_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r98);\n      const ctx_r97 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r97.openLinkedClaims());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r96 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"+\", (ctx_r96.claimFormData.claimViewModel == null ? null : ctx_r96.claimFormData.claimViewModel.lstLinkedClaimDetails.length) - 1, \" \");\n  }\n}\n\nfunction CreateClaimComponent_div_17_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r100 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"span\", 58);\n    i0.ɵɵtext(3, \"Linked Claims: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 62);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_17_div_2_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r100);\n      const ctx_r99 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r99.viewLinkedClaim(ctx_r99.claimFormData.claimViewModel == null ? null : ctx_r99.claimFormData.claimViewModel.lstLinkedClaimDetails[0]));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CreateClaimComponent_div_17_div_2_div_6_Template, 2, 1, \"div\", 65);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r91 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r91.claimFormData.claimViewModel == null ? null : ctx_r91.claimFormData.claimViewModel.lstLinkedClaimDetails[0].patientCtrlNo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r91.claimFormData.claimViewModel == null ? null : ctx_r91.claimFormData.claimViewModel.lstLinkedClaimDetails.length) > 1);\n  }\n}\n\nfunction CreateClaimComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CreateClaimComponent_div_17_div_1_Template, 6, 2, \"div\", 59);\n    i0.ɵɵtemplate(2, CreateClaimComponent_div_17_div_2_Template, 7, 2, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r9.claimFormData.claimViewModel == null ? null : ctx_r9.claimFormData.claimViewModel._835processedOnDate) || !!(ctx_r9.claimFormData.claimViewModel == null ? null : ctx_r9.claimFormData.claimViewModel.incomingFileId));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r9.claimFormData.claimViewModel == null ? null : ctx_r9.claimFormData.claimViewModel.lstLinkedClaimDetails) && (ctx_r9.claimFormData.claimViewModel == null ? null : ctx_r9.claimFormData.claimViewModel.lstLinkedClaimDetails.length) > 0);\n  }\n}\n\nfunction CreateClaimComponent_div_18_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r101 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Pending On :\", i0.ɵɵpipeBind2(2, 1, ctx_r101.claimFormData.claimViewModel == null ? null : ctx_r101.claimFormData.claimViewModel._277pprocessedOn, \"MM/dd/yyyy\"), \" \");\n  }\n}\n\nfunction CreateClaimComponent_div_18_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r104 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 64);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_18_i_4_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r104);\n      const ctx_r103 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r103.downloadLatestResponseFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r106 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 56)(2, \"div\", 57);\n    i0.ɵɵtemplate(3, CreateClaimComponent_div_18_span_3_Template, 3, 4, \"span\", 9);\n    i0.ɵɵtemplate(4, CreateClaimComponent_div_18_i_4_Template, 1, 0, \"i\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 56)(6, \"div\", 57)(7, \"a\", 62);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_18_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r106);\n      const ctx_r105 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r105.pendingDetails());\n    });\n    i0.ɵɵtext(8, \"Pending Details\");\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.claimFormData.claimViewModel == null ? null : ctx_r10.claimFormData.claimViewModel._277pprocessedOn);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r10.claimFormData.claimViewModel == null ? null : ctx_r10.claimFormData.claimViewModel.incomingFileId));\n  }\n}\n\nfunction CreateClaimComponent_div_19_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r110 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r110.claimFormData.claimViewModel == null ? null : ctx_r110.claimFormData.claimViewModel._999processedOnPayer, \"MM/dd/yyyy\"));\n  }\n}\n\nfunction CreateClaimComponent_div_19_div_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r111 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r111.claimFormData.claimViewModel == null ? null : ctx_r111.claimFormData.claimViewModel._277processedOnPayer, \"MM/dd/yyyy\"));\n  }\n}\n\nfunction CreateClaimComponent_div_19_div_1_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r114 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 64);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_19_div_1_i_6_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r114);\n      const ctx_r113 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r113.downloadLatestResponseFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"span\", 58);\n    i0.ɵɵtext(3, \"Rejected By Payer On: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CreateClaimComponent_div_19_div_1_span_4_Template, 3, 4, \"span\", 9);\n    i0.ɵɵtemplate(5, CreateClaimComponent_div_19_div_1_span_5_Template, 3, 4, \"span\", 9);\n    i0.ɵɵtemplate(6, CreateClaimComponent_div_19_div_1_i_6_Template, 1, 0, \"i\", 63);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r107 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r107.claimFormData.claimViewModel == null ? null : ctx_r107.claimFormData.claimViewModel._999processedOnPayer));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!!(ctx_r107.claimFormData.claimViewModel == null ? null : ctx_r107.claimFormData.claimViewModel._999processedOnPayer) && !!(ctx_r107.claimFormData.claimViewModel == null ? null : ctx_r107.claimFormData.claimViewModel._277processedOnPayer));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r107.claimFormData.claimViewModel == null ? null : ctx_r107.claimFormData.claimViewModel.incomingFileId));\n  }\n}\n\nfunction CreateClaimComponent_div_19_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r116 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 68);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_19_a_6_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r116);\n      const ctx_r115 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r115.rejectedReason());\n    });\n    i0.ɵɵtext(1, \"277\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_19_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"span\", 58);\n    i0.ɵɵtext(3, \"Reason for Ignore: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r109 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r109.claimFormData.claimViewModel == null ? null : ctx_r109.claimFormData.claimViewModel.ignoreReason, \" \");\n  }\n}\n\nfunction CreateClaimComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CreateClaimComponent_div_19_div_1_Template, 7, 3, \"div\", 59);\n    i0.ɵɵelementStart(2, \"div\", 56)(3, \"div\", 57)(4, \"span\", 58);\n    i0.ɵɵtext(5, \"Rejected In: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CreateClaimComponent_div_19_a_6_Template, 2, 0, \"a\", 67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, CreateClaimComponent_div_19_div_7_Template, 5, 1, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (!!(ctx_r11.claimFormData.claimViewModel == null ? null : ctx_r11.claimFormData.claimViewModel._277processedOnPayer) || !!(ctx_r11.claimFormData.claimViewModel == null ? null : ctx_r11.claimFormData.claimViewModel._999processedOnPayer) || !!(ctx_r11.claimFormData.claimViewModel == null ? null : ctx_r11.claimFormData.claimViewModel.incomingFileId)) && (ctx_r11.claimFormData.claimViewModel == null ? null : ctx_r11.claimFormData.claimViewModel.responseFileClaimMapings.length) > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.claimFormData.claimViewModel == null ? null : ctx_r11.claimFormData.claimViewModel._277processedOnPayer);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r11.claimFormData.claimViewModel == null ? null : ctx_r11.claimFormData.claimViewModel.ignoreReason));\n  }\n}\n\nfunction CreateClaimComponent_div_20_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r121 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r121.claimFormData.claimViewModel == null ? null : ctx_r121.claimFormData.claimViewModel._999processedOnCh, \"MM/dd/yyyy\"));\n  }\n}\n\nfunction CreateClaimComponent_div_20_div_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r122 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r122.claimFormData.claimViewModel == null ? null : ctx_r122.claimFormData.claimViewModel._277processedOnCh, \"MM/dd/yyyy\"));\n  }\n}\n\nfunction CreateClaimComponent_div_20_div_1_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r125 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 64);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_20_div_1_i_6_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r125);\n      const ctx_r124 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r124.downloadLatestResponseFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"span\", 58);\n    i0.ɵɵtext(3, \"Rejected By Clearing House On: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CreateClaimComponent_div_20_div_1_span_4_Template, 3, 4, \"span\", 9);\n    i0.ɵɵtemplate(5, CreateClaimComponent_div_20_div_1_span_5_Template, 3, 4, \"span\", 9);\n    i0.ɵɵtemplate(6, CreateClaimComponent_div_20_div_1_i_6_Template, 1, 0, \"i\", 63);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r117 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r117.claimFormData.claimViewModel == null ? null : ctx_r117.claimFormData.claimViewModel._999processedOnCh));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!!(ctx_r117.claimFormData.claimViewModel == null ? null : ctx_r117.claimFormData.claimViewModel._999processedOnCh) && !!(ctx_r117.claimFormData.claimViewModel == null ? null : ctx_r117.claimFormData.claimViewModel._277processedOnCh));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r117.claimFormData.claimViewModel == null ? null : ctx_r117.claimFormData.claimViewModel.incomingFileId));\n  }\n}\n\nfunction CreateClaimComponent_div_20_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r127 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 68);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_20_a_6_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r127);\n      const ctx_r126 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r126.rejectedReason());\n    });\n    i0.ɵɵtext(1, \"277\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_20_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"999\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_20_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"span\", 58);\n    i0.ɵɵtext(3, \"Reason for Ignore: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r120 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r120.claimFormData.claimViewModel == null ? null : ctx_r120.claimFormData.claimViewModel.ignoreReason, \" \");\n  }\n}\n\nfunction CreateClaimComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CreateClaimComponent_div_20_div_1_Template, 7, 3, \"div\", 59);\n    i0.ɵɵelementStart(2, \"div\", 56)(3, \"div\", 57)(4, \"span\", 58);\n    i0.ɵɵtext(5, \"Rejected In: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CreateClaimComponent_div_20_a_6_Template, 2, 0, \"a\", 67);\n    i0.ɵɵtemplate(7, CreateClaimComponent_div_20_span_7_Template, 2, 0, \"span\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, CreateClaimComponent_div_20_div_8_Template, 5, 1, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r12.claimFormData.claimViewModel == null ? null : ctx_r12.claimFormData.claimViewModel._277processedOnCh) || !!(ctx_r12.claimFormData.claimViewModel == null ? null : ctx_r12.claimFormData.claimViewModel._999processedOnCh) || !!(ctx_r12.claimFormData.claimViewModel == null ? null : ctx_r12.claimFormData.claimViewModel.incomingFileId));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r12.claimFormData.claimViewModel == null ? null : ctx_r12.claimFormData.claimViewModel.fileType) != \"005010X231A1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r12.claimFormData.claimViewModel == null ? null : ctx_r12.claimFormData.claimViewModel.fileType) === \"005010X231A1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r12.claimFormData.claimViewModel == null ? null : ctx_r12.claimFormData.claimViewModel.ignoreReason));\n  }\n}\n\nfunction CreateClaimComponent_div_21_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r128 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r128.claimFormData.claimViewModel == null ? null : ctx_r128.claimFormData.claimViewModel._277processedOnPayer, \"MM/dd/yyyy\"));\n  }\n}\n\nfunction CreateClaimComponent_div_21_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r131 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 64);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_21_i_6_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r131);\n      const ctx_r130 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r130.downloadLatestResponseFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 56)(2, \"div\", 57)(3, \"span\", 58);\n    i0.ɵɵtext(4, \"Accepted By Payer On: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CreateClaimComponent_div_21_span_5_Template, 3, 4, \"span\", 9);\n    i0.ɵɵtemplate(6, CreateClaimComponent_div_21_i_6_Template, 1, 0, \"i\", 63);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r13.claimFormData.claimViewModel == null ? null : ctx_r13.claimFormData.claimViewModel._277processedOnPayer));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r13.claimFormData.claimViewModel == null ? null : ctx_r13.claimFormData.claimViewModel.incomingFileId));\n  }\n}\n\nfunction CreateClaimComponent_div_22_div_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r133 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r133.claimFormData.claimViewModel == null ? null : ctx_r133.claimFormData.claimViewModel._835processedOnDate, \"MM/dd/yyyy\"));\n  }\n}\n\nfunction CreateClaimComponent_div_22_div_2_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r136 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 64);\n    i0.ɵɵlistener(\"click\", function CreateClaimComponent_div_22_div_2_i_4_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r136);\n      const ctx_r135 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r135.downloadLatestResponseFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CreateClaimComponent_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"span\", 58);\n    i0.ɵɵtext(2, \"Denied By Payer On: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CreateClaimComponent_div_22_div_2_span_3_Template, 3, 4, \"span\", 9);\n    i0.ɵɵtemplate(4, CreateClaimComponent_div_22_div_2_i_4_Template, 1, 0, \"i\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r132 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r132.claimFormData.claimViewModel == null ? null : ctx_r132.claimFormData.claimViewModel._835processedOnDate));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r132.claimFormData.claimViewModel == null ? null : ctx_r132.claimFormData.claimViewModel.incomingFileId));\n  }\n}\n\nfunction CreateClaimComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 56);\n    i0.ɵɵtemplate(2, CreateClaimComponent_div_22_div_2_Template, 5, 2, \"div\", 69);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.claimFormData.claimViewModel == null ? null : ctx_r14.claimFormData.claimViewModel.responseFileClaimMapings.length) > 0);\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"view-claim-component\": a0\n  };\n};\n\nexport let CreateClaimComponent = /*#__PURE__*/(() => {\n  class CreateClaimComponent {\n    constructor(el, claimService, dialog, subjectService, notificationService, fileService, dateFormatService, providerManagementService, globalService, cacheService) {\n      this.el = el;\n      this.claimService = claimService;\n      this.dialog = dialog;\n      this.subjectService = subjectService;\n      this.notificationService = notificationService;\n      this.fileService = fileService;\n      this.dateFormatService = dateFormatService;\n      this.providerManagementService = providerManagementService;\n      this.globalService = globalService;\n      this.cacheService = cacheService;\n      this.claimModel = [];\n      this.icdIdentifier = 'ICD10';\n      this.max_total_cpt = 13;\n      this.closeClaim = new EventEmitter();\n      this.isShowUpdateBtn = false;\n      this.isDeactivateClaimBtnShow = false;\n      this.isAcceptedClaimBtnShow = false;\n      this.isActivateClaimBtnShow = false;\n      this.isOnHoldClainBtnShow = false;\n      this.isUpdateClaimBtnShow = false;\n      this.isToastShowing = false;\n      this.isNewClaim = false;\n      this.saveAllNotesEnable = false;\n      this.showNotes = false;\n      this.isShowInsureDetails = {\n        isCopyInsure: false,\n        insureAddress: {\n          n301SubscriberAddr1: '',\n          n302SubscriberAddr2: '',\n          n401SubscriberCity: '',\n          n402SubscriberState: '',\n          n403SubscriberZip: '',\n          per04SubscriberPhoneNo: '',\n          nm104SubscriberFirst: '',\n          nm105SubscriberMiddle: '',\n          nm103SubscriberLastOrOrganizationName: '',\n          insuredDateOfBirth: '',\n          genderCode: ''\n        }\n      };\n      this.getPrivilegesByRole();\n      const {\n        webkitSpeechRecognition\n      } = window;\n      this.recognition = new webkitSpeechRecognition();\n\n      this.recognition.onresult = event => {\n        this.el.nativeElement.querySelectorAll(\".content\")[0].innerText = event.results[0][0].transcript;\n      };\n    }\n\n    ngOnInit() {\n      if (this.claimFormData.isEditClaim) {\n        this.isShowUpdateBtn = true;\n      }\n\n      this.claimFormCreation();\n      setTimeout(() => {\n        if (this.claimFormData.isEditClaim) {//this.initLoadForEditClaimSmaryStreetValidate();\n        }\n      }, 1000);\n\n      if (this.claimFormData.isAddClaim) {\n        this.isNewClaim = true; //this.initLoadSmartyStreetValidate();\n      }\n    }\n\n    timeout(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n\n    addNote() {\n      this.saveAllNotesEnable = true;\n      this.showNotes = true;\n      let max = 0;\n      this.claimFormData?.claimViewModel?.notes.forEach(element => {\n        max = +element.notesPkId;\n      });\n      this.claimFormData?.claimViewModel?.notes?.push({\n        notesPkId: max + 1,\n        content: '',\n        title: 'new',\n        isRemoved: false\n      });\n    }\n\n    saveNote(event) {\n      const id = event.srcElement.parentElement.parentElement.getAttribute('id');\n      const content = event.target.innerText;\n      event.target.innerText = content;\n      this.claimFormData?.claimViewModel?.notes.forEach(element => {\n        if (element.notesPkId == id) element.description = content;\n      });\n    }\n\n    deleteNote(event) {\n      const idAttr = event.target.closest('[id]') // find nearest ancestor with an “id” attribute\n      ?.getAttribute('id');\n      if (!idAttr) return;\n      const id = Number(idAttr);\n      const notes = this.claimFormData?.claimViewModel?.notes;\n      if (!notes) return;\n      const idx = notes.findIndex(n => n.notesPkId === id);\n      if (idx < 0) return;\n      notes[idx].isRemoved = !notes[idx].isRemoved;\n    }\n\n    displayNotes() {\n      this.saveAllNotesEnable = true;\n      this.showNotes = !this.showNotes;\n\n      if (this.claimFormData?.claimViewModel?.notes.length === 0) {\n        this.addNote();\n      }\n    }\n\n    record(event) {\n      this.recognition.start();\n      this.addNote();\n    }\n\n    getNotesToSave() {\n      this.claimFormData?.claimViewModel?.notes.forEach(element => {\n        if (element.title == 'new') {\n          element.notesPkId = 0;\n          element.title = '';\n        }\n      });\n      return this.claimFormData?.claimViewModel?.notes;\n    }\n\n    saveAllNotes() {\n      let data = {\n        insuranceCompany: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm109PayerIdCode'].value),\n        fromDos: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.claimFormData?.claimViewModel?.claimDosfrom,\n        claimDosfrom: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.claimFormData?.claimViewModel?.claimDosfrom,\n        claimDosto: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSTo) : this.claimFormData?.claimViewModel?.claimDosto,\n        claimsProfessional837: undefined,\n        paidAmount: this.ChargesInfo.chargesInfo.controls['paidAmount'].value == 0 ? null : this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['paidAmount'].value),\n        displayMemberFirstnameName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm104PatientFirst'].value),\n        displayMemberLastName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm103PatientLastOrOrganizationName'].value),\n        facilityId: null,\n        formCreatedDate: this.dateFormatService.formatDate(new Date()),\n        ipacode: this.claimFormData?.isAddClaim ? this.claimFormData?.profileMember?.ipaCode : this.claimFormData?.claimViewModel?.ipacode,\n        lastModifiedBy: JSON.parse(localStorage.getItem('email')),\n        lastModifiedByFirstName: JSON.parse(localStorage.getItem('userFirstName')),\n        lastModifiedByMiddleName: JSON.parse(localStorage.getItem('userMiddleName')) ? JSON.parse(localStorage.getItem('userMiddleName')) : null,\n        lastModifiedByLastName: JSON.parse(localStorage.getItem('userLastName')),\n        lastModifiedDate: null,\n        userName: JSON.parse(localStorage.getItem('email')),\n        claimFormStatusCode: this.claimFormData?.claimViewModel?.claimFormStatusCode ? this.claimFormData?.claimViewModel?.claimFormStatusCode : 'ON',\n        uuid: JSON.parse(localStorage.getItem('uuid')),\n        claimType: 'CAP',\n        totalCharges: this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['totalCharges'].value.toString()),\n        patientCtrlNo: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.patientCtrlNo,\n        isActive: true,\n        claimForm837Pid: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimForm837Pid,\n        claimsProfessional837id: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837id,\n        filename: null,\n        uploadExcelWarningMessage: null,\n        status: 'Active',\n        source: 'Gateway',\n        notes: this.getNotesToSave(),\n        userFirstName: JSON.parse(localStorage.getItem('userFirstName')),\n        userLastName: JSON.parse(localStorage.getItem('userLastName')),\n        userMiddleName: JSON.parse(localStorage.getItem('userMiddleName')) ? JSON.parse(localStorage.getItem('userMiddleName')) : null,\n        subscribeId: this.controlValueCheck(this.SubscriberInfo.subscriberInfo.controls['subscriberID'].value),\n        patientFirstName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm104PatientFirst'].value),\n        patientLastName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm103PatientLastOrOrganizationName'].value),\n        patientMiddleName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm105PatientMiddle'].value),\n        patientDob: this.controlValueCheck(this.PatientDOBInfo.patientdobForm.controls['dmg02PatientBirthDate'].value),\n        responseFileClaimMapings: this.claimFormData?.claimViewModel?.responseFileClaimMapings,\n        resubmissionTrackerId: this.claimFormData?.claimViewModel?.resubmissionTrackerId,\n        _277pprocessedOn: this.claimFormData?.claimViewModel?._277pprocessedOn,\n        _835processedOnDate: this.claimFormData?.claimViewModel?._835processedOnDate,\n        _999processedOnCh: this.claimFormData?.claimViewModel?._999processedOnCh,\n        isSkippeMemberAddress: false,\n        isSkippedDublicateClaim: false\n      };\n      this.claimModel.push(data);\n      this.claimService.updateNotes(this.claimModel).subscribe(res => {\n        if (res.statusCode == 200) {\n          this.notificationService.showSuccess('Notes Updated Successfully', ValidationMsgs.success, 4000);\n          this.showNotes = !this.showNotes;\n        }\n      });\n    }\n\n    onSubmit() {\n      if (this.showNotes) {\n        this.showNotes = false;\n      }\n\n      if (this.validateAllForms() && this.patientOrInsuredAddressIsDirtyCheck()) {\n        this.claimModel = [];\n        this.claimModelConvertion();\n        this.saveClaimData();\n      } // else if (this.validateAllForms()) {\n      //   this.fallback();\n      // }\n\n    }\n\n    patientOrInsuredAddressIsDirtyCheck() {\n      if (this.InsureAddressInfo.patientAddress.controls[\"n301SubscriberAddr1\"].dirty || this.InsureAddressInfo.patientAddress.controls[\"n401SubscriberCity\"].dirty || this.InsureAddressInfo.patientAddress.controls[\"n402SubscriberState\"].dirty || this.InsureAddressInfo.patientAddress.controls[\"n403SubscriberZip\"].dirty || this.PatientAddressInfo.patientAddressInfo.controls[\"address1\"].dirty || this.PatientAddressInfo.patientAddressInfo.controls[\"city\"].dirty || this.PatientAddressInfo.patientAddressInfo.controls[\"state\"].dirty || this.PatientAddressInfo.patientAddressInfo.controls[\"zip\"].dirty) {\n        return true;\n      } else {\n        return true;\n      }\n    }\n\n    isValidResponse(response) {\n      return !!response && !!response.content && response.content.isSmartAddressFound && !response.content.isAdderssCorrect;\n    }\n\n    handleResponse(response, changeStatus = \"\", status = \"\") {\n      const dialogRef = this.dialog.open(AddressMismatchComponent, {\n        width: '800px',\n        data: [{\n          entity: 'Patient & Insured',\n          address: response\n        }]\n      });\n      dialogRef.afterClosed().subscribe(confirmed => {\n        if (confirmed) {\n          this.InsureAddressInfo.f.n301SubscriberAddr1.setValue(response.addressLineOne);\n          this.InsureAddressInfo.f.n401SubscriberCity.setValue(response.city);\n          this.InsureAddressInfo.f.n402SubscriberState.setValue(response.state);\n          this.InsureAddressInfo.f.n403SubscriberZip.setValue(response.zipcode);\n          this.PatientAddressInfo.f.address1.setValue(response.addressLineOne);\n          this.PatientAddressInfo.f.city.setValue(response.city);\n          this.PatientAddressInfo.f.state.setValue(response.state);\n          this.PatientAddressInfo.f.zip.setValue(response.zipcode);\n          this.PatientAddressInfo.patientAddressInfo.updateValueAndValidity();\n          this.InsureAddressInfo.patientAddress.updateValueAndValidity(); // this.PayerInfoForm.payerInfo.updateValueAndValidity();\n\n          if (this.validateAllForms()) {\n            this.claimModel = [];\n            this.claimModelConvertion();\n            this.claimModel[0].isSkippeMemberAddress = true;\n            this.claimModel[0].isSkippedDublicateClaim = true;\n            this.saveClaimData(changeStatus, status);\n          }\n        }\n      }); //   this.providerManagementService.GetCorrectAddress(requestBody).subscribe((res) => {\n      //     if (!!res && !!res.content && res.content.isSmartAddressFound && !res.content.isAdderssCorrect) {\n      //       const dialogRef = this.dialog.open(AddressMismatchComponent, {\n      //         width: '800px',\n      //         data: [\n      //           {entity: 'Payer',\n      //           address: res.content}\n      //         ]\n      //       })\n      //       dialogRef.afterClosed().subscribe((confirmed: boolean) => {\n      //         if (confirmed) {\n      //           this.PayerInfoForm.f.n301PayerAddr1.setValue(res.content.addressLineOne);\n      //           this.PayerInfoForm.f.n401PayerCity.setValue(res.content.city);\n      //           this.PayerInfoForm.f.n402PayerState.setValue(res.content.state);\n      //           this.PayerInfoForm.f.n403PayerZip.setValue(res.content.zipcode);\n      //           this.PayerInfoForm.payerInfo.updateValueAndValidity();\n      //           if (this.validateAllForms()) {\n      //             this.claimModelConvertion();\n      //             this.saveClaimData();\n      //           }\n      //         }\n      //       });\n      //     } else {\n      //       this.claimModelConvertion();\n      //       this.saveClaimData();\n      //     }\n      //   }, (err)=> {\n      //     this.claimModelConvertion();\n      //     this.saveClaimData();\n      //   })\n      // }\n    }\n\n    fallback() {\n      this.claimModelConvertion();\n      this.saveClaimData();\n    }\n\n    saveClaimData(changeStatus = \"\", status = \"\") {\n      if (this.validateAllForms() && this.patientOrInsuredAddressIsDirtyCheck()) {\n        if (this.claimFormData?.isAddClaim) {\n          this.saveClaim();\n        } else {\n          this.updateClaimData(changeStatus, status);\n        }\n      }\n    }\n\n    validateCPTCode() {\n      for (let i = 0; i < this.ServiceLineClaimInfo.serviceLine.length; i++) {\n        for (let j = 1; j < 5; j++) {\n          if (!this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).errors && this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).errors?.required) this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).setErrors(null);\n        }\n      }\n      /**/\n\n\n      for (let i = 0; i < this.ServiceLineClaimInfo.serviceLine.length; i++) {\n        for (let h = 1; h <= this.ServiceLineClaimInfo.serviceLine.length - 1; h++) {\n          if (this.ServiceLineClaimInfo.serviceLine.at(i).get('cpt').value === this.ServiceLineClaimInfo.serviceLine.at(h).get('cpt').value) {\n            for (let j = 1; j <= 4; j++) {\n              for (let k = 1; k <= 4; k++) {\n                if (i != h && this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).value && this.ServiceLineClaimInfo.serviceLine.at(h).get('diagnosispointer' + k).value && this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).value === this.ServiceLineClaimInfo.serviceLine.at(h).get('diagnosispointer' + k).value && this.ServiceLineClaimInfo.serviceLine.at(i).get('cpt').value === this.ServiceLineClaimInfo.serviceLine.at(h).get('cpt').value) {\n                  this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).setErrors({\n                    dublicate: true\n                  });\n                  this.ServiceLineClaimInfo.serviceLine.at(h).get('diagnosispointer' + k).setErrors({\n                    dublicate: true\n                  });\n                }\n              }\n            }\n          }\n        }\n      }\n\n      let icDLength = 0;\n\n      for (let i = 1; i < this.max_total_cpt; i++) {\n        if (this.DiagnosisNature.icdInfo.controls['iCDInput' + i].value) {\n          icDLength = i + 1;\n        }\n      }\n\n      for (let j = 0; j <= this.ServiceLineClaimInfo.serviceLine.length - 1; j++) {\n        for (let i = 1; i <= 4; i++) {\n          if (this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer' + i).value && (Number.parseInt(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer' + i).value) >= icDLength || Number.parseInt(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer' + i).value) === 0)) {\n            this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer' + i).setErrors({\n              numberMax: true\n            });\n          }\n        }\n      }\n    }\n\n    validateAllForms() {\n      let mediGroupValidate = this.MediGroup.validateForm();\n      let payerValidate = this.PayerInfoForm.validateForm();\n      let SubscriberValidate = this.SubscriberInfo.validateForm();\n      let PatientDOBValidate = this.PatientDOBInfo.validateForm();\n      let InsurePolicyValidate = this.InsurePolicyGroupInfo.validateForm();\n      let FederalTaxValidate = this.FederalTaxInfo.validateForm();\n      let patientRelationShip = this.patientRelationshipComponent.validateForm();\n      let InsureAddressValidate = this.InsureAddressInfo.validateForm();\n      let BillingInfoValidate = this.BillingInfoInfo.validateForm();\n      let hospitalizationValidate = this.HospitalizationServiceInfo.setValiator();\n      let patientCurrentOccuptionValidate = this.PatientCurrentOccuptionInfo.setValiator();\n      this.validateDosFrom();\n\n      if (this.claimFormData.isAddClaim) {\n        this.validateCurrentIllnessDate();\n      }\n\n      this.validateCPTCode();\n      let ServiceLineValidate = this.ServiceLineClaimInfo.validateForm();\n      let otherDate = this.OtherDateInfo.validateControls();\n      let currentIllness = this.CurrentIllnessInfo.validateControls();\n      let otherProviderPayer = this.OtherProviderPayerInfo.validateForm();\n      let otherProviderValidate = this.OtherProviderInfo.validateForm();\n      let serviceFacility = this.ServiceFacilityForm.validateForm();\n      let patientAddress = this.PatientAddressInfo.validateForm();\n      let insureInfoValidate = this.InsureInfo.validateForm();\n      let patientSignatuew = this.PatientSignatureInfo.validateForm();\n      let resubmissionAuthorizedNumValidate = this.ResubmissionAuthorizedNumInfo.validateForm();\n      let hospitalization = this.HospitalizationServiceInfo.setValiator();\n      let currentOccupation = this.PatientCurrentOccuptionInfo.setValiator();\n      this.PatientOtherInsureInfo.validateRequired(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['anotherHealthBenefitPlan'].value);\n      let otherInsure = this.PatientOtherInsureInfo.validateControls(); /// If acciendent date was selected patient control need to validate \n\n      if (this.ProviderSignInfo.providerSignInfo.controls['accident'].value && this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].value === '') {\n        this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].setErrors({\n          require: true\n        });\n      } else {\n        this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].setErrors(null);\n      }\n\n      let PatientConditionInfoValidate = this.PatientConditionInfo.validateForm();\n\n      if (this.DiagnosisNature.icdInfo.controls.iCDInput1.value == null || this.DiagnosisNature.icdInfo.controls.iCDInput1.value == undefined) {\n        this.DiagnosisNature.icdInfo.controls.iCDInput1.setErrors({\n          required: true\n        });\n      }\n\n      let diagnosisNatureValidate = this.DiagnosisNature.validateForm();\n      this.validateAccidentDate();\n      let patientControl = this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].value === \"AA\";\n\n      if (patientControl) {\n        if (!this.ProviderSignInfo.providerSignInfo.controls['accident'].value) {\n          this.ProviderSignInfo.providerSignInfo.controls['accident'].setErrors({\n            require: true\n          });\n        } else {\n          if (this.ProviderSignInfo.providerSignInfo.controls.accident.hasError('require')) {\n            delete this.ProviderSignInfo.providerSignInfo.controls.accident.errors['require'];\n            this.ProviderSignInfo.providerSignInfo.controls.accident.updateValueAndValidity();\n          }\n        }\n      }\n\n      let ProviderSignValidate = this.ProviderSignInfo.validateForm();\n\n      if (patientRelationShip && otherInsure && currentOccupation && hospitalization && patientSignatuew && otherProviderValidate && diagnosisNatureValidate && insureInfoValidate && resubmissionAuthorizedNumValidate && serviceFacility && patientAddress && otherDate && currentIllness && payerValidate && SubscriberValidate && PatientDOBValidate && FederalTaxValidate && InsurePolicyValidate && InsureAddressValidate && BillingInfoValidate && hospitalizationValidate && patientCurrentOccuptionValidate && ProviderSignValidate && ServiceLineValidate && PatientConditionInfoValidate && otherProviderPayer && mediGroupValidate) {\n        return true;\n      }\n\n      if (!this.isToastShowing) {\n        this.isToastShowing = true;\n        this.notificationService.showWarning('', `Please fill the mandatory claim form fields`, 4000); // Reset flag after toast timeout\n\n        setTimeout(() => {\n          this.isToastShowing = false;\n        }, 4000);\n      }\n\n      return false;\n    }\n\n    dublicateClaimCheck() {\n      this.claimService.dublicateClaimCheck(this.claimModel).subscribe(res => {\n        if (res?.length === 0) {\n          this.saveClaim();\n        } else {\n          let claimIds = [];\n          res?.forEach(element => {\n            claimIds.push(element.patientCtrlNo);\n          });\n          this.dublicateModal({\n            isClaimCreated: false,\n            message: \"There are some claims found with the same information you have recently filled. Would you like to continue to create this claim? Here is the list of claim(s) found:\",\n            title: 'Duplicate Claim Found',\n            claimIds: claimIds\n          });\n        }\n      });\n    }\n\n    saveClaim() {\n      this.claimService.createClaim(this.claimModel).subscribe(res => {\n        if (res) {\n          if (res && res?.isDublicateClaim && !this.claimModel[0].isSkippedDublicateClaim) {\n            let claimIds = [];\n            res?.claimNo.forEach(element => {\n              claimIds.push(element.patientCtrlNo);\n            });\n            this.dublicateModal({\n              isClaimCreated: false,\n              message: \"There are some claims found with the same information you have recently filled. Would you like to continue to create this claim? Here is the list of claim(s) found:\",\n              title: 'Duplicate Claim Found',\n              claimIds: claimIds\n            });\n          } else if (res && res?.isMemberAddress && !this.claimModel[0].isSkippeMemberAddress) {\n            this.handleResponse(res?.claimNo);\n          } else {\n            this.openDialog({\n              isClaimCreated: true,\n              message: \"Claim Created Successfully!\",\n              title: 'Create Claim Confirmation',\n              claimId: res\n            });\n          }\n        }\n      }); // removing the selected icdcodes once created claim.\n\n      localStorage.removeItem(LocalStorageKey.selectedICDCodesForCreateClaim);\n    }\n\n    updateClaimData(changeStatus = \"\", status = \"\") {\n      this.updateModelMapping();\n      this.claimService.createClaim(this.claimModel).subscribe(res => {\n        if (res) {\n          if (changeStatus == \"accept\" || changeStatus == \"open\") {\n            this.changeClaimStatus(status);\n          } else {\n            this.updateClaimShowPopup({\n              isClaimCreated: true,\n              message: \"Claim updated Successfully !\",\n              title: 'Updated Claim Confirmation',\n              claimId: res\n            });\n          }\n\n          this.subjectService.setSearchClaimRefresh();\n          this.subjectService.setCloseTabRefresh('View Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);\n        }\n      });\n    }\n\n    updateClaimShowPopup(res) {\n      const dialogRef = this.dialog.open(UpdateClaimComponent, {\n        width: '450px',\n        data: {\n          res\n        }\n      });\n      dialogRef.afterClosed().subscribe(confirmed => {\n        if (res.isClaimCreated && confirmed) {\n          this.subjectService.setCloseTabRefresh('Edit Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);\n        }\n      });\n    }\n\n    dublicateModal(res) {\n      const dialogRef = this.dialog.open(DublicateClaimComponent, {\n        maxHeight: '100vh',\n        maxWidth: '60vw',\n        minWidth: '40vw',\n        data: {\n          res\n        }\n      });\n      dialogRef.afterClosed().subscribe(confirmed => {\n        if (confirmed) {\n          this.claimModel[0].isSkippedDublicateClaim = true;\n          this.saveClaim();\n        }\n      });\n    }\n\n    openDialog(res) {\n      const dialogRef = this.dialog.open(SaveModalComponent, {\n        maxHeight: '100vh',\n        maxWidth: '60vw',\n        minWidth: '40vw',\n        data: {\n          res\n        }\n      });\n      dialogRef.afterClosed().subscribe(confirmed => {\n        if (res.isClaimCreated && confirmed) {\n          this.claimFormData.provider.billingProvider.billingProviderNPI = this.BillingInfoInfo.billingInfo.controls['groupNPI'].value;\n\n          if (this.claimFormData.provider.referringProvider) {\n            this.claimFormData.provider.referringProvider.nPINumber = this.OtherProviderPayerInfo.otherPayerInfo.controls['npi'].value;\n          }\n\n          if (this.claimFormData.provider.supervisingProvider) {\n            this.claimFormData.provider.supervisingProvider.providerNPI = this.ProviderSignInfo.providerSignInfo.controls['supervisingNpi'].value;\n          }\n\n          this.claimFormData.provider.billingProvider.billingProviderZip = this.BillingInfoInfo.billingInfo.controls['n403BillingProviderZip'].value;\n          this.claimFormData.memberResult.dateOfBirth = this.InsurePolicyGroup.insurencePolicyInfo.controls['insuredDateOfBirth'].value;\n          this.claimFormData.memberResult.gender = this.InsurePolicyGroup.insurencePolicyInfo.controls['genderCode'].value;\n          this.claimFormData.memberResult.insuranceCompanyName = this.PayerInfoForm.payerInfo.controls['nm103PayerLastOrOrganizatioName'].value;\n          this.claimFormData.memberResult.addressLine1 = this.PayerInfoForm.payerInfo.controls['n301PayerAddr1'].value;\n          this.claimFormData.memberResult.city = this.PayerInfoForm.payerInfo.controls['n401PayerCity'].value;\n          this.claimFormData.memberResult.state = this.PayerInfoForm.payerInfo.controls['n402PayerState'].value;\n          this.claimFormData.memberResult.zipCode = this.PayerInfoForm.payerInfo.controls['n403PayerZip'].value;\n          this.claimFormData.provider.billingProvider.billingProviderTaxonomy = this.BillingInfoInfo.billingInfo.controls['providerSpecialty'].value; //this.claimFormData.claimViewModel.patientCtrlNo =null;\n          //this.claimFormData.claimViewModel.claimsProfessional837id =null;\n\n          this.closeClaim.emit(confirmed);\n        } else if (confirmed) {\n          this.saveClaim();\n        }\n      });\n    }\n\n    claimFormCreation() {\n      this.PayerInfoForm.createForm();\n      this.PatientInfo.createForm();\n      this.PatientDOBInfo.createForm();\n      this.InsureInfo.createForm();\n      this.PatientAddressInfo.createForm();\n      this.PatientRelationshipInfo.createForm();\n      this.PatientSignatureInfo.createForm();\n      this.InsureAddressInfo.createForm();\n      this.InsurePolicyGroupInfo.createForm();\n      this.PatientOtherInsureInfo.createForm();\n      this.PatientConditionInfo.createForm();\n      this.SubscriberInfo.createForm();\n      this.OtherProviderInfo.createForm();\n      this.ServiceFacilityForm.createForm();\n      this.CurrentIllnessInfo.createForm();\n      this.OtherDateInfo.createForm();\n      this.InsureAuthorizedInfo.createForm();\n      this.OtherProviderPayerInfo.createForm();\n      this.OutsideLabInfo.createForm();\n      this.PatientCurrentOccuptionInfo.createForm();\n      this.HospitalizationServiceInfo.createForm();\n      this.ServiceLineClaimInfo.createForm();\n      this.DiagnosisNature.createForm();\n      this.BillingInfoInfo.createForm();\n      this.ProviderSignInfo.createForm();\n      this.FederalTaxInfo.createForm();\n      this.PatientAccountAcceptAssignmentInfo.createForm();\n      this.ChargesInfo.createForm();\n      this.ResubmissionAuthorizedNumInfo.createForm();\n      this.AdditionalClaimInfo.createForm();\n      this.MediGroup.createForm();\n      this.InsurePolicyGroup.createForm();\n      this.OtherProviderInfo.otherProviderInfo.controls['referringProviderLastName'].valueChanges.pipe(distinctUntilChanged()).subscribe(referringProviderLastName => {\n        if (!!referringProviderLastName && referringProviderLastName.length > 0) {\n          this.OtherProviderPayerInfo.otherPayerInfo.controls.npi.addValidators(Validators.required);\n        } else {\n          this.OtherProviderPayerInfo.otherPayerInfo.controls.npi.removeValidators(Validators.required);\n        }\n\n        this.OtherProviderPayerInfo.otherPayerInfo.controls.npi.updateValueAndValidity();\n      });\n      this.OtherProviderPayerInfo.otherPayerInfo.controls['npi'].valueChanges.pipe(distinctUntilChanged()).subscribe(npi => {\n        if (!!npi && npi.length > 0) {\n          this.OtherProviderInfo.otherProviderInfo.controls.referringProviderLastName.addValidators(Validators.required);\n        } else {\n          this.OtherProviderInfo.otherProviderInfo.controls.referringProviderLastName.removeValidators(Validators.required);\n        }\n\n        this.OtherProviderInfo.otherProviderInfo.controls.referringProviderLastName.updateValueAndValidity();\n      });\n    }\n\n    setInsureData(e) {\n      let insureAddress = {\n        isCopyInsure: e,\n        n301SubscriberAddr1: this.InsureAddressInfo.patientAddress.controls['n301SubscriberAddr1'].value,\n        n302SubscriberAddr2: this.InsureAddressInfo.patientAddress.controls['n302SubscriberAddr2'].value,\n        n401SubscriberCity: this.InsureAddressInfo.patientAddress.controls['n401SubscriberCity'].value,\n        n402SubscriberState: this.InsureAddressInfo.patientAddress.controls['n402SubscriberState'].value,\n        n403SubscriberZip: this.InsureAddressInfo.patientAddress.controls['n403SubscriberZip'].value,\n        per04SubscriberPhoneNo: this.InsureAddressInfo.patientAddress.controls['per04SubscriberPhoneNo'].value\n      };\n      let insureInfo = {\n        isCopyInsure: e,\n        nm104SubscriberFirst: this.InsureInfo.insureInfo.controls['nm104SubscriberFirst'].value,\n        nm105SubscriberMiddle: this.InsureInfo.insureInfo.controls['nm105SubscriberMiddle'].value,\n        nm103SubscriberLastOrOrganizationName: this.InsureInfo.insureInfo.controls['nm103SubscriberLastOrOrganizationName'].value\n      };\n      let insureDob = {\n        isCopyInsure: e,\n        insuredDateOfBirth: this.InsurePolicyGroup.insurencePolicyInfo.controls['insuredDateOfBirth'].value,\n        genderCode: this.InsurePolicyGroup.insurencePolicyInfo.controls['genderCode'].value\n      };\n      this.subjectService.setInsureAddress(insureAddress);\n      this.subjectService.setInsureInfo(insureInfo);\n      this.subjectService.setInsureDob(insureDob);\n    }\n\n    claimModelConvertion() {\n      let data = {\n        insuranceCompany: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm109PayerIdCode'].value),\n        fromDos: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.claimFormData?.claimViewModel?.claimDosfrom,\n        claimDosfrom: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.claimFormData?.claimViewModel?.claimDosfrom,\n        claimDosto: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSTo) : this.claimFormData?.claimViewModel?.claimDosto,\n        claimsProfessional837: undefined,\n        paidAmount: this.ChargesInfo.chargesInfo.controls['paidAmount'].value == 0 ? null : this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['paidAmount'].value),\n        displayMemberFirstnameName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm104PatientFirst'].value),\n        displayMemberLastName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm103PatientLastOrOrganizationName'].value),\n        facilityId: null,\n        formCreatedDate: this.dateFormatService.formatDate(new Date()),\n        ipacode: this.claimFormData?.isAddClaim ? this.claimFormData?.profileMember?.ipaCode : this.claimFormData?.claimViewModel?.ipacode,\n        lastModifiedBy: JSON.parse(localStorage.getItem('email')),\n        lastModifiedByFirstName: JSON.parse(localStorage.getItem('userFirstName')),\n        lastModifiedByLastName: JSON.parse(localStorage.getItem('userLastName')),\n        lastModifiedByMiddleName: JSON.parse(localStorage.getItem('userMiddleName')) ? JSON.parse(localStorage.getItem('userMiddleName')) : null,\n        lastModifiedDate: null,\n        userName: JSON.parse(localStorage.getItem('email')),\n        claimFormStatusCode: this.claimFormData?.claimViewModel?.claimFormStatusCode ? this.claimFormData?.claimViewModel?.claimFormStatusCode : 'ON',\n        uuid: JSON.parse(localStorage.getItem('uuid')),\n        claimType: 'CAP',\n        totalCharges: this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['totalCharges'].value.toString()),\n        patientCtrlNo: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.patientCtrlNo,\n        isActive: true,\n        claimForm837Pid: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimForm837Pid,\n        claimsProfessional837id: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837id,\n        filename: null,\n        uploadExcelWarningMessage: null,\n        status: 'Active',\n        source: 'Gateway',\n        notes: this.getNotesToSave(),\n        userFirstName: JSON.parse(localStorage.getItem('userFirstName')),\n        userLastName: JSON.parse(localStorage.getItem('userLastName')),\n        userMiddleName: JSON.parse(localStorage.getItem('userMiddleName')) ? JSON.parse(localStorage.getItem('userMiddleName')) : null,\n        subscribeId: this.controlValueCheck(this.SubscriberInfo.subscriberInfo.controls['subscriberID'].value),\n        patientFirstName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm104PatientFirst'].value),\n        patientLastName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm103PatientLastOrOrganizationName'].value),\n        patientMiddleName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm105PatientMiddle'].value),\n        patientDob: this.controlValueCheck(this.PatientDOBInfo.patientdobForm.controls['dmg02PatientBirthDate'].value),\n        responseFileClaimMapings: this.claimFormData?.claimViewModel?.responseFileClaimMapings,\n        resubmissionTrackerId: this.claimFormData?.claimViewModel?.resubmissionTrackerId,\n        _277pprocessedOn: this.claimFormData?.claimViewModel?._277pprocessedOn,\n        _835processedOnDate: this.claimFormData?.claimViewModel?._835processedOnDate,\n        _999processedOnCh: this.claimFormData?.claimViewModel?._999processedOnCh,\n        isSkippedDublicateClaim: false,\n        isSkippeMemberAddress: false\n      };\n      data.claimsProfessional837 = this.claimProfessionalModel();\n      data.claimsProfessional837.claimListDetails = [];\n      data.claimsProfessional837.claimListDetails.push(this.claimListDetailsModel());\n      data.claimsProfessional837.subscriberkeyNavigation = this.subscriberkeyNavigationModel();\n      data.claimsProfessional837.serviceLineProfessional837s = this.servicelineProfession();\n      data.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional = this.otherInforProfressionalModel();\n      data.claimsProfessional837.otherSubscriberInfoProfessional837s = this.otherSubscriberInfoProfessional837s();\n      this.claimModel.push(data);\n    }\n\n    claimProfessionalModel() {\n      let claimsProfessional837Model = {\n        claimskey: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837?.claimskey,\n        ref02PayerClaimControlNumber: this.controlValueCheck(this.ResubmissionAuthorizedNumInfo.resubmissionInfo.controls['referenceNumber'].value),\n        ref02PriorAuthorizationNumber: this.controlValueCheck(this.ResubmissionAuthorizedNumInfo.resubmissionInfo.controls['priorAuthorisationNumber'].value),\n        subscriberkey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkey,\n        dtp03HospitalizationDischargeDate: this.controlValueCheck(this.HospitalizationServiceInfo.hospitalizationinfo.controls['dtp03HospitalizationDischargeDate'].value),\n        dtp03HospitalizationAdmissionDate: this.controlValueCheck(this.HospitalizationServiceInfo.hospitalizationinfo.controls['dtp03HospitalizationAdmissionDate'].value),\n        dtp03LastWorkedDate: this.controlValueCheck(this.PatientCurrentOccuptionInfo.patientOccupationInfo.controls['patientUnableToWorkInCurrentOccupationFrom'].value),\n        dtp03WorkReturnDate: this.controlValueCheck(this.PatientCurrentOccuptionInfo.patientOccupationInfo.controls['patientUnableToWorkInCurrentOccupationTo'].value),\n        nm107ReferringProviderLastSuffix: null,\n        nm105ReferringProviderLastMiddle: this.controlValueCheck(this.OtherProviderInfo.otherProviderInfo.controls['referringProviderMiddleName'].value),\n        nm104ReferringProviderLastFirst: this.controlValueCheck(this.OtherProviderInfo.otherProviderInfo.controls['referringProviderFirstName'].value),\n        nm103ReferringProviderLastName: this.controlValueCheck(this.OtherProviderInfo.otherProviderInfo.controls['referringProviderLastName'].value),\n        nm109ReferringProviderIdentifier: this.controlValueCheck(this.OtherProviderPayerInfo.otherPayerInfo.controls['npi'].value),\n        hcp14PolicyComplianceCode: null,\n        clm01PatientControlNo: this.claimFormData?.isAddClaim ? null : this.controlValueCheck(this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value),\n        dtp03OnsetofCurrentIllnessInjuryDate: this.controlValueCheck(this.CurrentIllnessInfo.currentillnessInfo.controls['dtp03OnsetofCurrentIllnessInjuryDate'].value),\n        clm1103RelatedCausesCode: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToOtherAccident'].value),\n        clm1102RelatedCausesCode: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].value),\n        clm1101RelatedCausesCode: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToEmp'].value),\n        clm1104AutoAccidentStateCode: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccidentState'].value),\n        clm0503ClaimFrequencyCode: this.controlValueCheck(this.ResubmissionAuthorizedNumInfo.resubmissionInfo.controls['resubmissionCode'].value),\n        clm06SupplierSignatureIndicator: this.controlValueCheck(this.PatientSignatureInfo.patientAuthorizedForm.controls['patientOrAuthorizedSignature'].value),\n        clm08BenefitsAssignmentCertIndicator: this.controlValueCheck(this.InsureAuthorizedInfo.insureAuthorizedForm.controls['authorizedPersonsSignature'].value),\n        nm109RenderingProviderIdentifier: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['providerNPI'].value),\n        nm104RenderingProviderFirst: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm104RenderingProviderFirst'].value),\n        nm105RenderingProviderMiddle: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm105RenderingProviderMiddle'].value),\n        nm103RenderingProviderLastOrOrganizationName: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm103RenderingProviderLastOrOrganizationName'].value),\n        nm109LabFacilityIdentifier: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['FacilityLocationNPI'].value),\n        nm103LabFacilityName: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['nm103LabFacilityName'].value),\n        n403LabFacilityZip: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['n403LabFacilityZip'].value),\n        n402LabFacilityState: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['n402LabFacilityState'].value),\n        n401LabFacilityCity: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['n401LabFacilityCity'].value),\n        n302LabFacilityAddress2: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['n302LabFacilityAddress2'].value),\n        n301LabFacilityAddress1: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['n301LabFacilityAddress1'].value),\n        clm0501PlaceOfServiceCode: this.claimFormData?.isAddClaim ? this.claimFormData?.placeOfServiceCode : this.ServiceLineClaimInfo.serviceLineInfo.controls['serviceLines'].value[0].locationOfService,\n        prv03ProviderTaxonomyCode: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['providerSpecialty'].value),\n        dtp03LastSeenDate: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['dateOrConsultation'].value),\n        dtp03InitialTreatmentDate: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['dateInitial'].value),\n        nm104SupervisingPhysicianFirst: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianFirstname'].value),\n        nm103SupervisingPhysicianLastName: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianLastname'].value),\n        nm105SupervisingPhysicianMiddle: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianMiddlename'].value),\n        nm109SupervisingPhysicianIdentifier: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingNpi'].value),\n        dtp03AccidentDate: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['accident'].value),\n        subscriberkeyNavigation: undefined,\n        serviceLineProfessional837s: [],\n        claimListDetails: undefined,\n        otherSubscriberInfoProfessional837s: [],\n        n301AmbulancePickupAddress1: null,\n        n302AmbulancePickupAddress2: null,\n        n401AmbulancePickupCity: null,\n        n402AmbulancePickupState: null,\n        n403AmbulancePickupZip: null,\n        n404AmbulancePickupCountryCode: null,\n        n407AmbulancePickupCountrySubdivisionCode: null,\n        n103AmbulanceDropOffLocation: null,\n        n301AmbulanceDropOffAddress1: null,\n        n302AmbulanceDropOffAddress2: null,\n        n401AmbulanceDropOffCity: null,\n        n402AmbulanceDropOffState: null,\n        n403AmbulanceDropOffZip: null,\n        n404AmbulanceDropOffCountryCode: null,\n        n407AmbulanceDropOffCountrySubdivisionCode: null,\n        otherInfoProfessionalOtherInfoProfessional: undefined,\n        amt02PatientAmountPaid: this.ChargesInfo.chargesInfo.controls['paidAmount'].value == 0 ? null : this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['paidAmount'].value),\n        ref02MammographyCertificationNumber: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['mammography'].value),\n        clm07PlanParticipationCode: this.controlValueCheck(this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['clm08BenefitsAssignmentCertIndicator'].value)\n      };\n      return claimsProfessional837Model;\n    }\n\n    otherSubscriberInfoProfessional837s() {\n      let otherInfoProfessionalOtherInfoProfessionalModel = {\n        claimskey: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837?.claimskey,\n        amt02NonCoveredChargedAmount: null,\n        amt02PayorAmountPaid: null,\n        amt02RemainingPatientLiability: null,\n        dtp03OtherPayerPaymentDate: null,\n        moa01ReimbursementRate: null,\n        moa02HcpcsPayableAmount: null,\n        moa03ClaimPaymentRemarkCode: null,\n        moa04ClaimPaymentRemarkCode2: null,\n        moa05ClaimPaymentRemarkCode3: null,\n        moa06ClaimPaymentRemarkCode4: null,\n        moa07ClaimPaymentRemarkCode5: null,\n        claimskeyNavigation: undefined,\n        moa08EndStageRenalDiseasePaymntAmnt: null,\n        moa09NonPayableProfessionComponentBill: null,\n        n301OtherInsuredAddress: null,\n        n301OtherPayerAddress1: null,\n        n302OtherInsuredAddress2: null,\n        n302OtherPayerAddress2: null,\n        n401OtherInsuredCity: null,\n        n401OtherPayerCity: null,\n        n402OtherInsuredState: null,\n        n402OtherPayerState: null,\n        n403OtherInsuredZip: null,\n        n403OtherPayerZip: null,\n        n404OtherInsuredCountryCode: null,\n        n404OtherPayerCountryCode: null,\n        n407OtherInsuredCountrySubdivision: null,\n        n407OtherPayerCountrySubdivision: null,\n        nm101OtherBillingProviderEntityIdCode: null,\n        nm101OtherProviderEntityIdCode: null,\n        nm101OtherRenderingProviderEntityIdCode: null,\n        nm101OtherServiceLocationEntityIdCode: null,\n        nm101OtherSupervisorEntityIdCode: null,\n        nm102OtherBillingProviderEntityTypeQlfr: null,\n        nm102OtherInsuredEntityTypeQlfr: null,\n        nm102OtherProviderEntityTypeQlfr: null,\n        nm102OtherRenderingProviderEntityTypeQlfr: null,\n        nm102OtherServiceLocationEntityTypeQlfr: null,\n        nm102OtherSupervisorEntityTypeQlfr: null,\n        nm103OtherPayerOrganizationName: null,\n        nm107OtherInsuredSuffix: null,\n        nm108OtherInsuredIdQlfr: null,\n        nm108OtherPayerCodeQlfr: null,\n        nm109OtherInsuredId: null,\n        nm109OtherPayerPrimaryId: null,\n        oi03BenefitsAssignmentCertIndicator: null,\n        oi04PatientSignatureSourceCode: null,\n        oi06ReleaseOfInformationCode: null,\n        otherSubscriberInfokey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.otherSubscriberInfoProfessional837s[0] === undefined ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.otherSubscriberInfoProfessional837s[0].otherSubscriberInfokey,\n        ref02OtherInsuredSecondaryId: null,\n        ref02OtherPayerClaimAdjustmentIndicator: null,\n        ref02OtherPayerClaimControlNo: null,\n        ref02OtherPayerPriorAuthorizationNo: null,\n        ref02OtherPayerReferralNo: null,\n        sbr01PayerResponsibSeqNoCode: null,\n        sbr02IndividualRelationshipCode: null,\n        sbr05InsuranceTypeCode: null,\n        sbr09ClaimFilingIndicatorCode: this.controlValueCheck(this.MediGroup.mediInfo.controls['insuranceType'].value),\n        sbr03ReferenceIdentification: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredPolicy'].value),\n        nm104OtherInsuredFirst: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredFirstName'].value),\n        nm105OtherInsuredMiddle: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredMiddleInitial'].value),\n        nm103OtherInsuredLastName: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredLastName'].value),\n        sbr04OtherInsuredGroupName: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['insurePlanProgram'].value)\n      };\n      let other = [];\n      other.push(otherInfoProfessionalOtherInfoProfessionalModel);\n      return other;\n    }\n\n    otherInforProfressionalModel() {\n      //10, 8,33,\n      let otherInfoProfessionalOtherInfoProfessionalModel = {\n        otherInfoProfessionalId: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.otherInfoProfessionalOtherInfoProfessional?.otherInfoProfessionalId,\n        billingGroupIdQlfr: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['idQual'].value),\n        billingGroupNumber: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['providerId'].value),\n        dateOfCurrentIllnessQlfr: this.controlValueCheck(this.CurrentIllnessInfo.currentillnessInfo.controls['dateOfCurrentIllnessQlfr'].value),\n        icdidentifier: this.icdIdentifier,\n        insurancePlanName: null,\n        insuredOtherHealthBenefitPlan: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['anotherHealthBenefitPlan'].value),\n        insuredSignature: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['anotherHealthBenefitPlan'].value),\n        outsideLab: this.controlValueCheck(this.OutsideLabInfo.otSideLabInfo.controls['outsideLab'].value),\n        patientConditionRelatedToAutoAccident: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].value),\n        patientConditionRelatedToAutoAccidentState: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccidentState'].value),\n        patientConditionRelatedToEmp: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToEmp'].value),\n        patientConditionRelatedToOtherAccident: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToOtherAccident'].value),\n        patientOrAuthorizedSignature: this.controlValueCheck(this.PatientSignatureInfo.patientAuthorizedForm.controls['patientOrAuthorizedSignature'].value),\n        patientOrAuthorizedSignatureDate: this.controlValueCheck(this.PatientSignatureInfo.patientAuthorizedForm.controls['patientOrAuthorizedSignatureDate'].value),\n        patientOtherDate: this.controlValueCheck(this.OtherDateInfo.otherInfo.controls['otherDateQual'].value),\n        renderingProviderPin: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['renderingProviderPin'].value),\n        patientOtherDateQlfr: this.controlValueCheck(this.OtherDateInfo.otherInfo.controls['otherQual'].value),\n        reservedNuccuse0801: this.controlValueCheck(this.PatientRelationshipInfo.patientRelationShip.controls['NUCC1'].value),\n        reservedNuccuse0802: this.controlValueCheck(this.PatientRelationshipInfo.patientRelationShip.controls['NUCC2'].value),\n        otherInsuredNuccuse01: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredNuccuse01'].value),\n        otherInsuredNuccuse02: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredNuccuse02'].value),\n        otherClaimIdNuccuse: null,\n        additionalClaimInfoNucc: this.controlValueCheck(this.AdditionalClaimInfo.additionalClaim.controls['additionalClaimInformation'].value),\n        patientConditionNuccuse: null,\n        claimsCodes: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['claimsCodes'].value),\n        federalTaxIdType: null,\n        physicianQlfr: this.controlValueCheck(this.OtherProviderPayerInfo.otherPayerInfo.controls['physicianQual'].value),\n        physicianDescription: this.controlValueCheck(this.OtherProviderPayerInfo.otherPayerInfo.controls['physicianDecription'].value),\n        resubmissionCode: this.controlValueCheck(this.ResubmissionAuthorizedNumInfo.resubmissionInfo.controls['resubmissionCode'].value),\n        reservedNuccuse30: this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['reservedForNUCC'].value)\n      };\n      return otherInfoProfessionalOtherInfoProfessionalModel;\n    }\n\n    servicelineProfession() {\n      let serviceLineProfessional837sModel = [];\n\n      for (let j = 0; j <= this.ServiceLineClaimInfo.serviceLine.controls.length - 1; j++) {\n        let subscriberkeyNavigationModel = {\n          dOSFrom: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceFrom').value),\n          dOSTo: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSTo) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceTo').value),\n          n301ServiceFacilityAddress1: null,\n          n302ServiceFacilityAddress2: null,\n          n401ServiceFacilityCity: null,\n          n402ServiceFacilityState: null,\n          n403ServiceFacilityZip: null,\n          nm103RenderingProviderNameLastOrg: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm103BillingProviderMiddle'].value),\n          nm109RenderingProviderId: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('jRenderingProviderId').value),\n          nm109ServiceFacilityId: null,\n          sv10101ProductServiceIdQualifier: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('proceduceC').value),\n          sv10102ProcedureCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('cpt').value),\n          sv10103ProcedureModifier1: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m1').value),\n          sv10104ProcedureModifier2: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m2').value),\n          sv10105ProcedureModifier3: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m3').value),\n          sv10106ProcedureModifier4: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m4').value),\n          sv102LineItemChargeAmount: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('unitCharges').value.toString()),\n          sv104ServiceUnitCount: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dayUnitChanges').value).toString(),\n          sv105PlaceOfServiceCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('locationOfService').value),\n          sv10701DiagnosisCodePointer1: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer1').value),\n          sv10702DiagnosisCodePointer2: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer2').value),\n          sv10703DiagnosisCodePointer3: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer3').value),\n          sv10704DiagnosisCodePointer4: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer4').value),\n          sv109EmergencyIndicator: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('emg').value),\n          sv111EpsdtIndicator: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ePSDT').value),\n          nm103OrderingProviderLastName: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingLastname'].value),\n          nmSupervisingProviderId2: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingId'].value),\n          claimskey: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837?.claimskey,\n          nm104OrderingProviderFirst: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingFirstname'].value),\n          nm105OrderingProviderMiddle: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingMiddlename'].value),\n          nm109OrderingProviderId: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['orderingNpi'].value),\n          nmOrderingProviderId2: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['orderingId'].value),\n          ref02ReferringCliaNumber: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['cLIA'].value),\n          svServiceFacilityId2: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['FacilityId'].value),\n          ref02MammographyCertificationNumber: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['mammography'].value),\n          nm103SupervisingProviderLastName: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianLastname'].value) : null,\n          nm105SupervisingProviderMiddle: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianMiddlename'].value) : null,\n          nm104SupervisingProviderFirst: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianFirstname'].value) : null,\n          nm109SupervisingProviderId: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingNpi'].value) : null,\n          DOSFrom: null,\n          DOSTo: null,\n          serviceLinekey: 0\n        };\n        subscriberkeyNavigationModel.dtp03ServiceDate = this.dateChangeToString(this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceFrom').value), this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceTo').value));\n        serviceLineProfessional837sModel.push(subscriberkeyNavigationModel);\n      }\n\n      return serviceLineProfessional837sModel;\n    }\n\n    subscriberkeyNavigationModel() {\n      let subscriberkeyNavigationModel = {\n        dmg02SubscriberBirthDate: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['insuredDateOfBirth'].value),\n        ref02PropertyCasualtyClaimNo: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['otherClaimID'].value),\n        dmg03SubscriberGenderCode: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['genderCode'].value),\n        n301PayerAddr1: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n301PayerAddr1'].value),\n        n301SubscriberAddr1: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['n301SubscriberAddr1'].value),\n        n302PayerAddr2: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n302PayerAddr2'].value),\n        n302SubscriberAddr2: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['n302SubscriberAddr2'].value),\n        n401PayerCity: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n401PayerCity'].value),\n        n401SubscriberCity: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['n401SubscriberCity'].value),\n        n402PayerState: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n402PayerState'].value),\n        n402SubscriberState: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['n402SubscriberState'].value),\n        n403PayerZip: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n403PayerZip'].value),\n        n403SubscriberZip: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['n403SubscriberZip'].value),\n        nm103PayerLastOrOrganizatioName: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm103PayerLastOrOrganizatioName'].value),\n        nm103SubscriberLastOrOrganizationName: this.controlValueCheck(this.InsureInfo.insureInfo.controls['nm103SubscriberLastOrOrganizationName'].value),\n        nm104SubscriberFirst: this.controlValueCheck(this.InsureInfo.insureInfo.controls['nm104SubscriberFirst'].value),\n        nm105SubscriberMiddle: this.controlValueCheck(this.InsureInfo.insureInfo.controls['nm105SubscriberMiddle'].value),\n        nm109PayerIdCode: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm109PayerIdCode'].value),\n        nm109SubscriberIdCode: this.controlValueCheck(this.SubscriberInfo.subscriberInfo.controls['subscriberID'].value),\n        per04SubscriberPhoneNo: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['per04SubscriberPhoneNo'].value),\n        sbr02IndividualRelationshipCode: null,\n        sbr04SubscriberGroupName: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['insurancePlanName'].value),\n        sbr09ClaimFilingIndicatorCode: this.controlValueCheck(this.MediGroup.mediInfo.controls['insuranceType'].value),\n        subscriberkey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkey,\n        sbr03SubscriberGroupPolicyNo: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['fECANumber'].value),\n        dependentProfessional837s: [],\n        infoSourcekeyNavigation: this.infoSourcekeyNavigationModel(),\n        crcConditionsIndicatorProfessional837s: []\n      };\n      let dependentProfessional837s = {\n        dmg02PatientBirthDate: this.controlValueCheck(this.PatientDOBInfo.patientdobForm.controls['dmg02PatientBirthDate'].value),\n        dmg03PatientGenderCode: this.controlValueCheck(this.PatientDOBInfo.patientdobForm.controls['dmg03PatientGenderCode'].value),\n        n301PatientAddr1: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['address1'].value),\n        n302PatientAddr2: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['address2'].value),\n        n401PatientCity: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['city'].value),\n        n402PatientState: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['state'].value),\n        n403PatientZip: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['zip'].value),\n        nm103PatientLastOrOrganizationName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm103PatientLastOrOrganizationName'].value),\n        nm104PatientFirst: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm104PatientFirst'].value),\n        nm105PatientMiddle: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm105PatientMiddle'].value),\n        pat01IndividualRelationshipCode: this.controlValueCheck(this.PatientRelationshipInfo.patientRelationShip.controls['sbr02IndividualRelationshipCode'].value),\n        per04PatientPhoneNo: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['telephone'].value),\n        dependentkey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.dependentkey\n      };\n\n      if (!this.claimFormData?.isAddClaim) {\n        dependentProfessional837s.subscriberProfessional837id = this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.subscriberkey.toString();\n      }\n\n      subscriberkeyNavigationModel.dependentProfessional837s.push(dependentProfessional837s);\n      subscriberkeyNavigationModel.crcConditionsIndicatorProfessional837s.push(this.crcConditionalIndicator());\n      return subscriberkeyNavigationModel;\n    }\n\n    crcConditionalIndicator() {\n      let subscriberkeyNavigationModel = {\n        dependentProfessional837s: {\n          dmg02PatientBirthDate: null,\n          dmg03PatientGenderCode: null,\n          n301PatientAddr1: null,\n          n302PatientAddr2: null,\n          n401PatientCity: null,\n          n402PatientState: null,\n          n403PatientZip: null,\n          nm103PatientLastOrOrganizationName: null,\n          nm104PatientFirst: null,\n          nm105PatientMiddle: null,\n          pat01IndividualRelationshipCode: null,\n          per04PatientPhoneNo: null\n        },\n        dtP_DateTimePeriod_Professional_837: []\n      };\n      let dtpDateTimePeriodProfessional837Model = {\n        serviceLinekeyNavigation: {\n          nm103SupervisingProviderLastName: null,\n          nm104SupervisingProviderFirst: null,\n          nm105SupervisingProviderMiddle: null,\n          nm108RenderingProviderIdqlfr: null,\n          sv10101ProductServiceIdQualifier: null,\n          ref02MammographyCertificationNumber: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['mammography'].value)\n        },\n        dependentkey: null\n      };\n      subscriberkeyNavigationModel.dtP_DateTimePeriod_Professional_837.push(dtpDateTimePeriodProfessional837Model);\n      return subscriberkeyNavigationModel;\n    }\n\n    infoSourcekeyNavigationModel() {\n      let subscriberkeyNavigationModel = {\n        infosourcekey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.infosourcekey,\n        n301BillingProviderAddr1: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['n301BillingProviderAddr1'].value),\n        n302BillingProviderAddr2: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['n302BillingProviderAddr2'].value),\n        n401BillingProviderCity: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['n401BillingProviderCity'].value),\n        n402BillingProviderState: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['n402BillingProviderState'].value),\n        n403BillingProviderZip: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['n403BillingProviderZip'].value),\n        nm103BillingProviderLastOrOrganizationName: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['billingProviderFullName'].value),\n        nm104BillingProviderFirst: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm104BillingProviderFirst'].value),\n        nm105BillingProviderMiddle: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm103BillingProviderMiddle'].value),\n        nm109BillingProviderIdCode: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['groupNPI'].value),\n        prv03BillingProviderIdCode: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['billingproviderSpecialty'].value),\n        perAdministrativeCommunicationContactProfessional837s: [],\n        ref02BillingProviderEmployerId: this.controlValueCheck(this.FederalTaxInfo.federalInfo.controls['federalTaxNumber'].value)\n      };\n      let perAdministrativeCommunicationContactProfessional837s = {\n        per02ContactName: this.claimFormData?.provider?.billingProvider?.billingContactPersonName,\n        per0xEmail: this.claimFormData?.provider?.billingProvider?.billingContactPersonEmailAddress,\n        per0xFaxNo: this.claimFormData?.provider?.billingProvider?.billingContactPersonPhoneNumber,\n        per0xPhoneNo: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['billingProviderTelephone'].value),\n        per0xPhoneExtNo: null,\n        perkey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.perAdministrativeCommunicationContactProfessional837s[0]?.perkey\n      };\n      subscriberkeyNavigationModel.perAdministrativeCommunicationContactProfessional837s.push(perAdministrativeCommunicationContactProfessional837s);\n      return subscriberkeyNavigationModel;\n    }\n\n    claimListDetailsModel() {\n      let claimListDetailsModel = {\n        iCDCodes: [],\n        serviceLineDetails: []\n      };\n\n      for (let i = 1; i <= 12; i++) {\n        let ICDCodesModel1 = {\n          iCD: this.controlValueCheck(this.DiagnosisNature.icdInfo.controls['iCDInput' + i].value)\n        };\n        claimListDetailsModel.iCDCodes.push(ICDCodesModel1);\n      }\n\n      for (let j = 0; j <= this.ServiceLineClaimInfo.serviceLine.controls.length - 1; j++) {\n        let serviceLineDetailsModel = {\n          claimskey: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837?.claimskey,\n          dOSFrom: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceFrom').value),\n          dOSTo: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSTo) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceTo').value),\n          DOSFrom: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceFrom').value),\n          DOSTo: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSTo) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceTo').value),\n          svServiceFacilityId2: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['FacilityId'].value),\n          n301ServiceFacilityAddress1: null,\n          n302ServiceFacilityAddress2: null,\n          n401ServiceFacilityCity: null,\n          n402ServiceFacilityState: null,\n          n403ServiceFacilityZip: null,\n          nm103RenderingProviderNameLastOrg: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['billingProviderFullName'].value),\n          nm109RenderingProviderId: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('jRenderingProviderId').value),\n          nm109ServiceFacilityId: null,\n          sv10102ProcedureCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('cpt').value),\n          sv10103ProcedureModifier1: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m1').value),\n          sv10104ProcedureModifier2: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m2').value),\n          sv10105ProcedureModifier3: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m3').value),\n          sv10106ProcedureModifier4: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m4').value),\n          sv102LineItemChargeAmount: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('unitCharges').value).toString(),\n          sv104ServiceUnitCount: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dayUnitChanges').value),\n          sv105PlaceOfServiceCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('locationOfService').value),\n          sv10701DiagnosisCodePointer1: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer1').value),\n          sv10702DiagnosisCodePointer2: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer2').value),\n          sv10703DiagnosisCodePointer3: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer3').value),\n          sv10704DiagnosisCodePointer4: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer4').value),\n          sv109EmergencyIndicator: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('emg').value),\n          sv111EpsdtIndicator: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ePSDT').value),\n          serviceLinekey: 0,\n          sv10101ProductServiceIdQualifier: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('proceduceC').value),\n          anesStart: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('anesStart').value),\n          anesStop: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('anesStop1').value),\n          lin03NationalDrugCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ndcCode').value),\n          lin02NationalDrugCodeQlfr: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ndcQual').value),\n          ctp04NationalDrugUnitCount: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ndcQty').value),\n          ctp0501UnitMeasurementCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ndcQtyQual').value),\n          nte02LineNoteText: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('lineNote').value),\n          cpt03NationalDrugUnitPrice: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ndcUnitPrice').value),\n          lx01AssignedNumber: (this.ServiceLineClaimInfo.serviceLine.controls.length + 1).toString(),\n          sv10107ServiceDescription: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('desc').value.value),\n          nmSupervisingProviderId2: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingId'].value),\n          nm103SupervisingProviderLastName: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianLastname'].value) : null,\n          nm105SupervisingProviderMiddle: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianMiddlename'].value) : null,\n          nm104SupervisingProviderFirst: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianFirstname'].value) : null,\n          nm109SupervisingProviderId: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingNpi'].value) : null\n        };\n        serviceLineDetailsModel.dtp03ServiceDate = this.dateChangeToString(this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceFrom').value), this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceTo').value));\n        claimListDetailsModel.serviceLineDetails.push(serviceLineDetailsModel);\n      }\n\n      return claimListDetailsModel;\n    }\n\n    controlValueCheck(data) {\n      if (data === undefined) return null;\n      if (data === null) return null;\n      if (data === '') return null;\n\n      if (moment.isMoment(data)) {\n        return moment(data).format('YYYY-MM-DD');\n      }\n\n      return data;\n    }\n\n    dateChangeToString(fromDate, toDate) {\n      if (fromDate === undefined && toDate === undefined) return null;\n      if (fromDate === null && toDate === null) return null;\n      if (fromDate === '' && toDate === null) return null;\n\n      if (typeof fromDate == 'string') {\n        return fromDate.substring(0, 4) + fromDate.substring(5, 7) + fromDate.substring(8, 10) + \"-\" + toDate.substring(0, 4) + toDate.substring(5, 7) + toDate.substring(8, 10);\n      } else {\n        return moment(fromDate).format('YYYYMMDD') + \"-\" + moment(toDate).format('YYYYMMDD');\n      }\n    }\n\n    changeStatusText(status) {\n      let statusText = '';\n\n      switch (!!status && status.toLocaleUpperCase()) {\n        case 'OH':\n          statusText = 'ON HOLD';\n          break;\n\n        case 'ON':\n          statusText = 'OPEN';\n          break;\n\n        case \"AC\":\n          statusText = 'ACCEPTED';\n          break;\n\n        case \"NTR\":\n          statusText = 'RESUBMISSION';\n          break;\n\n        case \"DP\":\n          statusText = 'DISPATCHED';\n          break;\n\n        case \"UACH\":\n          statusText = 'UNACKNOWLEDGED BY CLEARING HOUSE';\n          break;\n\n        case \"RCH\":\n          statusText = 'REJECTED BY CLEARING HOUSE';\n          break;\n\n        case \"ABCH\":\n          statusText = 'ACCEPTED BY CLEARING HOUSE';\n          break;\n\n        case \"ANCH\":\n          statusText = 'ACKNOWLEDGED BY CLEARING HOUSE';\n          break;\n\n        case \"ABP\":\n          statusText = 'ACCEPTED BY PAYER';\n          break;\n\n        case \"UAP\":\n          statusText = 'UNACKNOWLEDGED BY PAYER';\n          break;\n\n        case \"AP\":\n          statusText = 'ACKNOWLEDGED BY PAYER';\n          break;\n\n        case \"RP\":\n          statusText = 'REJECTED BY PAYER';\n          break;\n\n        case \"PEND\":\n          statusText = 'PENDING';\n          break;\n\n        case \"EOB\":\n          statusText = 'EOB RECIEVED';\n          break;\n\n        case \"DBP\":\n          statusText = 'DENIED BY PAYER';\n          break;\n      }\n\n      return statusText;\n    }\n\n    open(status) {\n      if (this.showNotes) {\n        this.showNotes = false;\n      }\n\n      if (this.validateAllForms()) {\n        this.claimModel = [];\n        this.claimModelConvertion();\n        this.updateModelMapping();\n        this.claimService.createClaim(this.claimModel).subscribe(res => {\n          if (res) {\n            if (res && res?.isMemberAddress && !this.claimModel[0].isSkippeMemberAddress) {\n              this.handleResponse(res?.claimNo, \"open\", status);\n            } else {\n              this.changeClaimStatus(status);\n            }\n          }\n        });\n      }\n    }\n\n    accept(status) {\n      if (this.showNotes) {\n        this.showNotes = false;\n      }\n\n      if (this.validateAllForms() && this.patientOrInsuredAddressIsDirtyCheck()) {\n        if (this.claimFormData?.claimViewModel?.source?.toLocaleLowerCase() != \"gateway\") {\n          this.validateAddress(status);\n        } else {\n          this.claimModelConvertion();\n          this.updateModelMapping();\n          this.claimService.createClaim(this.claimModel).subscribe(res => {\n            if (res) {\n              if (res && res?.isMemberAddress && !this.claimModel[0].isSkippeMemberAddress) {\n                this.handleResponse(res?.claimNo, \"accept\", status);\n              } else {\n                this.changeClaimStatus(status);\n              }\n            }\n          });\n        }\n      }\n    }\n\n    handleResponses(response) {\n      const dialogRef = this.dialog.open(AddressMismatchComponent, {\n        width: '800px',\n        data: [{\n          entity: 'Patient & Insured',\n          address: response.content\n        }]\n      });\n      return dialogRef.afterClosed().pipe(map(confirmed => {\n        if (confirmed) {\n          this.InsureAddressInfo.f.n301SubscriberAddr1.setValue(response.content.addressLineOne);\n          this.InsureAddressInfo.f.n401SubscriberCity.setValue(response.content.city);\n          this.InsureAddressInfo.f.n402SubscriberState.setValue(response.content.state);\n          this.InsureAddressInfo.f.n403SubscriberZip.setValue(response.content.zipcode);\n          this.PatientAddressInfo.f.address1.setValue(response.content.addressLineOne);\n          this.PatientAddressInfo.f.city.setValue(response.content.city);\n          this.PatientAddressInfo.f.state.setValue(response.content.state);\n          this.PatientAddressInfo.f.zip.setValue(response.content.zipcode);\n          this.PatientAddressInfo.patientAddressInfo.updateValueAndValidity();\n          this.InsureAddressInfo.patientAddress.updateValueAndValidity();\n          return true; // Address mapping successful\n        }\n\n        return false; // Address mapping not confirmed\n      }));\n    }\n\n    smartystreet() {\n      let requestBody = {\n        addressLineOne: this.InsureAddressInfo.f.n301SubscriberAddr1.value,\n        city: this.InsureAddressInfo.f.n401SubscriberCity.value,\n        state: this.InsureAddressInfo.f.n402SubscriberState.value,\n        givenZipcode: this.InsureAddressInfo.f.n403SubscriberZip.value\n      };\n      let patientrequestbody = {\n        addressLineOne: this.PatientAddressInfo.f.address1.value,\n        city: this.PatientAddressInfo.f.city.value,\n        state: this.PatientAddressInfo.f.state.value,\n        givenZipcode: this.PatientAddressInfo.f.zip.value\n      };\n      let initialRequest = this.providerManagementService.GetCorrectAddress(requestBody);\n      let alternativeRequest = this.providerManagementService.GetCorrectAddress(patientrequestbody);\n      return forkJoin([initialRequest, alternativeRequest]).pipe(switchMap(responses => {\n        let initialResponse = responses[0];\n        let alternativeResponse = responses[1];\n\n        if (this.isValidResponse(initialResponse)) {\n          return this.handleResponses(initialResponse);\n        } else if (this.isValidResponse(alternativeResponse)) {\n          return this.handleResponses(alternativeResponse);\n        } else {\n          return of(false); // Neither response is valid\n        }\n      }));\n    }\n\n    initLoadSmartyStreetValidate() {\n      let requestBody = {\n        addressLineOne: this.claimFormData?.memberResult?.member.addressLine1,\n        city: this.claimFormData?.memberResult?.member.city,\n        state: this.claimFormData?.memberResult?.member.stateSubdivisionCode,\n        givenZipcode: this.claimFormData?.memberResult?.member.zipCode\n      };\n      this.providerManagementService.GetCorrectAddress(requestBody).subscribe(response => {\n        if (!!response && !!response.content && response.content.isSmartAddressFound) {\n          this.patchValidSmartyAddress(response);\n        }\n      });\n    }\n\n    initLoadForEditClaimSmaryStreetValidate() {\n      let requestBody = {\n        addressLineOne: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n301PatientAddr1,\n        city: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n401PatientCity,\n        state: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n402PatientState,\n        givenZipcode: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n403PatientZip\n      };\n      this.providerManagementService.GetCorrectAddress(requestBody).subscribe(response => {\n        if (!!response && !!response.content && response.content.isSmartAddressFound) {\n          this.patchValidSmartyAddress(response);\n        }\n      });\n    }\n\n    patchValidSmartyAddress(response) {\n      // patient address\n      this.PatientAddressInfo.patientAddressInfo.patchValue({\n        address1: response.content.addressLineOne,\n        city: response.content.city,\n        state: response.content.state,\n        zip: response.content.zipcode == null ? response.content.givenZipcode : response.content.zipcode\n      }); // insured address.\n\n      this.InsureAddressInfo.patientAddress.patchValue({\n        n301SubscriberAddr1: response.content.addressLineOne,\n        n401SubscriberCity: response.content.city,\n        n402SubscriberState: response.content.state,\n        n403SubscriberZip: response.content.zipcode == null ? response.content.givenZipcode : response.content.zipcode\n      });\n    }\n\n    validateAddress(status) {\n      let validateAddress = {\n        claimDOSFrom: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(0).get('dateServiceFrom').value),\n        claimDOSTo: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(0).get('dateServiceTo').value),\n        dosFrom: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(0).get('dateServiceFrom').value),\n        claimId: this.claimFormData?.claimViewModel?.claimsProfessional837?.claimskey.toString(),\n        firstName: this.InsureInfo.insureInfo.controls['nm104SubscriberFirst'].value,\n        insuranceCompanyName: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm103PayerLastOrOrganizatioName'].value),\n        lastName: this.InsureInfo.insureInfo.controls['nm103SubscriberLastOrOrganizationName'].value,\n        memberDOB: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['insuredDateOfBirth'].value),\n        memberFirstName: this.controlValueCheck(this.InsureInfo.insureInfo.controls['nm104SubscriberFirst'].value),\n        memberid: this.controlValueCheck(this.SubscriberInfo.subscriberInfo.controls['subscriberID'].value),\n        memberLastName: this.controlValueCheck(this.InsureInfo.insureInfo.controls['nm103SubscriberLastOrOrganizationName'].value),\n        npi: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['providerNPI'].value),\n        payToAddressLine1: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n301PayerAddr1'].value),\n        payToCity: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n401PayerCity'].value),\n        payToName: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm103PayerLastOrOrganizatioName'].value),\n        payToState: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n402PayerState'].value),\n        payToZipCode: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n403PayerZip'].value),\n        taxIdOrSSN: this.controlValueCheck(this.FederalTaxInfo.federalInfo.controls['federalTaxNumber'].value),\n        email: JSON.parse(localStorage.getItem('email')),\n        ipacode: this.claimFormData?.claimViewModel?.ipacode,\n        uuid: JSON.parse(localStorage.getItem('uuid')),\n        mdmServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),\n        providerVillaServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),\n        memberHouseServiceBaseUrl: JSON.parse(localStorage.getItem('MemberService'))\n      };\n      this.claimService.validateAddress(validateAddress).subscribe(res => {\n        const dialogRef = this.dialog.open(ValidateAddressComponent, {\n          height: '530px',\n          width: '1100px',\n          panelClass: 'custom-dialog-containers',\n          data: res\n        });\n        dialogRef.afterClosed().subscribe(res => {\n          if (res) {\n            this.changeClaimStatus(status);\n          }\n        });\n      });\n    }\n\n    deactivate(status) {\n      this.changeClaimStatus(status);\n    }\n\n    onHold(status) {\n      if (this.showNotes) {\n        this.showNotes = false;\n      }\n\n      const dialogRef = this.dialog.open(ReasonToHoldComponent, {\n        minWidth: '40vw',\n        minHeight: '35vh',\n        autoFocus: false,\n        restoreFocus: false\n      });\n      dialogRef.afterClosed().subscribe(res => {\n        if (!!res && res.mode == 'changeStatus') {\n          if (this.validateAllForms()) {\n            this.onHoldUpdate(res, status);\n          }\n        }\n      });\n    }\n\n    onHoldUpdate(res, status) {\n      this.claimModelConvertion();\n      this.updateModelMapping();\n      let data = [];\n      data = this.claimModel;\n      data[0].isActive = true;\n      data[0].status = status;\n      data[0].onHoldCategory = res.data?.categoryDesc;\n      data[0].claimFormStatusCode = CLAIM_TYPE.onHold;\n      data[0].lastModifiedByFirstName = JSON.parse(localStorage.getItem('userFirstName'));\n      data[0].lastModifiedByLastName = JSON.parse(localStorage.getItem('userLastName'));\n      data[0].userFirstName = JSON.parse(localStorage.getItem('userFirstName'));\n      data[0].userLastName = JSON.parse(localStorage.getItem('userLastName'));\n      data[0].userMiddleName = JSON.parse(localStorage.getItem('userMiddleName')) ? JSON.parse(localStorage.getItem('userMiddleName')) : null;\n      data[0].reasons = [];\n      data[0].reasons.push({\n        categoryFkId: res.data.id,\n        description: res.data.description\n      });\n      this.claimService.createClaim(data).subscribe(res => {\n        this.notificationService.showSuccess(`Claim ${this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value} has been moved to ON HOLD bucket successfully.`, ValidationMsgs.success, 4000);\n        this.subjectService.setCloseTabRefresh('Edit Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);\n        this.subjectService.setIsClaimMovedOtherBucket();\n        this.subjectService.setSearchClaimRefresh();\n      });\n    }\n\n    changeClaimStatus(status) {\n      let changeClaimStatus = {\n        claimsStatus: [{\n          claimId: this.claimFormData?.claimViewModel?.claimForm837Pid,\n          patientControlNumber: this.claimFormData?.claimViewModel?.clm01PatientControlNo\n        }],\n        status: status,\n        lastModifiedFirstName: JSON.parse(localStorage.getItem('userFirstName')),\n        lastModifiedLastName: JSON.parse(localStorage.getItem('userLastName')),\n        lastModifiedMiddleName: JSON.parse(localStorage.getItem('userMiddleName')) ? JSON.parse(localStorage.getItem('userMiddleName')) : null\n      };\n      let statusText = this.changeStatusText(status);\n      this.claimService.claimStatusChange(changeClaimStatus).subscribe(res => {\n        if (res) {\n          this.notificationService.showSuccess(`Claim ${this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value} has been moved to ` + statusText + ' bucket successfully', ValidationMsgs.success, 4000);\n          this.subjectService.setCloseTabRefresh('Edit Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);\n          this.subjectService.setCloseTabRefresh('View Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);\n          this.subjectService.setIsClaimMovedOtherBucket();\n          this.subjectService.setSearchClaimRefresh();\n        }\n      });\n    }\n\n    validateAccidentDate() {\n      let otherDate = this.OtherDateInfo.otherInfo.controls.otherDateQual.value;\n      let otherQual = this.OtherDateInfo.otherInfo.controls.otherQual.value;\n      let accidentDate = this.ProviderSignInfo.providerSignInfo.controls.accident.value;\n\n      if (!!otherQual && otherQual == '439') {\n        if (!accidentDate) {\n          this.ProviderSignInfo.providerSignInfo.controls.accident.setErrors({\n            require: true\n          });\n        } else {\n          if (this.ProviderSignInfo.providerSignInfo.controls.accident.hasError('require')) {\n            delete this.ProviderSignInfo.providerSignInfo.controls.accident.errors['require'];\n            this.ProviderSignInfo.providerSignInfo.controls.accident.updateValueAndValidity();\n          }\n\n          if (!!otherDate && new Date(otherDate).getDate() + '' + new Date(otherDate).getMonth() + '' + new Date(otherDate).getFullYear() != new Date(accidentDate).getDate() + '' + new Date(accidentDate).getMonth() + '' + new Date(accidentDate).getFullYear()) {\n            this.ProviderSignInfo.providerSignInfo.controls.accident.setErrors({\n              isInvalid: true\n            });\n          } else {\n            if (this.ProviderSignInfo.providerSignInfo.controls.accident.hasError('isInvalid')) {\n              delete this.ProviderSignInfo.providerSignInfo.controls.accident.errors['isInvalid'];\n              this.ProviderSignInfo.providerSignInfo.controls.accident.updateValueAndValidity();\n            }\n          }\n        }\n      } else {\n        this.ProviderSignInfo.providerSignInfo.controls.accident.setErrors(null);\n      }\n    }\n\n    validateDosFrom() {\n      let currentIllnessDate = new Date(this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.value);\n\n      if (!!this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.value && !!currentIllnessDate) {\n        for (let i = 0; i <= this.ServiceLineClaimInfo.serviceLine.value.length - 1; i++) {\n          if (!!this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').value && !!new Date(this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').value)) {\n            if (new Date(this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').value).getTime() < currentIllnessDate.getTime()) {\n              this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').setErrors({\n                currentIllnessError: true\n              });\n            } else {\n              if (this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').hasError('currentIllnessError')) {\n                delete this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').errors['currentIllnessError'];\n                this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').updateValueAndValidity();\n              }\n            }\n          } else {\n            if (this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').hasError('currentIllnessError')) {\n              delete this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').errors['currentIllnessError'];\n              this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').updateValueAndValidity();\n            }\n          }\n        }\n\n        if (this.ServiceLineClaimInfo.serviceLineInfo.invalid) {\n          submitValidateAllFields.validateAllFields(this.ServiceLineClaimInfo.serviceLineInfo);\n          return;\n        }\n      }\n    }\n\n    validateCurrentIllnessDate() {\n      let currentIllnessDate = new Date(this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.value);\n\n      if (!!this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.value && !!currentIllnessDate) {\n        for (let i = 0; i <= this.ServiceLineClaimInfo.serviceLine.value.length - 1; i++) {\n          if (!!this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceTo').value && !!new Date(this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceTo').value) && !!this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').value && !!new Date(this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').value)) {\n            let dateServiceTo = moment(moment(this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceTo').value).format('MM/DD/YYYY'));\n            let currentIllnessDateTemp = moment(moment(currentIllnessDate).format('MM/DD/YYYY'));\n\n            if (moment(currentIllnessDateTemp).isAfter(dateServiceTo)) {\n              this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.setErrors({\n                currentIllnessError: true\n              });\n            } else {\n              if (this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.hasError('currentIllnessError')) {\n                delete this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').errors['currentIllnessError'];\n                this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').updateValueAndValidity();\n              }\n            }\n          } else {\n            if (this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.hasError('currentIllnessError')) {\n              delete this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.errors['currentIllnessError'];\n              this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.updateValueAndValidity();\n            }\n          }\n        }\n\n        if (this.CurrentIllnessInfo.currentillnessInfo.invalid) {\n          submitValidateAllFields.validateAllFields(this.CurrentIllnessInfo.currentillnessInfo);\n          return;\n        }\n      }\n    }\n\n    calculateTotal(e) {\n      let totalAmount = 0.0;\n\n      for (let j = 0; j <= this.ServiceLineClaimInfo.serviceLine.value.length - 1; j++) {\n        //const unitCharges = this.ServiceLineClaimInfo.serviceLine.at(j).get('unitCharges').value;\n        //const dayUnitChanges = Number(this.ServiceLineClaimInfo.serviceLine.at(j).get('dayUnitChanges').value) \n        const total = parseFloat(this.ServiceLineClaimInfo.serviceLine.at(j).get('total').value) || 0;\n        totalAmount += total;\n      }\n\n      this.ChargesInfo.chargesInfo.patchValue({\n        totalCharges: totalAmount.toFixed(2)\n      });\n    }\n\n    reasonDesc(reson) {\n      if (!!reson && reson.length > 0) return reson[reson.length - 1]?.description;\n      return '';\n    }\n\n    reasonHoldingDesc(reson) {\n      if (!!reson && reson.length > 0) return reson[reson.length - 1].categoryFk?.categoryDesc;\n      return '';\n    }\n\n    anotherHealthInsurenceValidate(e) {\n      this.PatientOtherInsureInfo.validateRequired(e);\n      this.PatientOtherInsureInfo.validateControls();\n    }\n\n    parentClaimNavigation() {\n      let nav = new NavTabFromDetails();\n      nav.name = Tabs.viewClaim;\n      nav.data.claimControlNumber = this.claimFormData?.claimViewModel?.parentPatientCtrlNo;\n      nav.data.claimId = this.claimFormData?.claimViewModel?.parentClaimForm837Pid;\n      this.subjectService.setViewClaim(this.claimFormData?.claimViewModel?.parentClaimForm837Pid);\n      localStorage.setItem('claimTapName', 'View Claim - #' + this.claimFormData?.claimViewModel?.parentPatientCtrlNo);\n      this.subjectService.passValue(nav);\n    }\n\n    activateClaim() {\n      this.claimModel = [];\n      this.claimModelConvertion();\n      this.updateModelMapping();\n      let data = [];\n      data = this.claimModel;\n      data[0].isActive = true;\n      data[0].status = \"Active\";\n      this.claimService.createClaim(data).subscribe(res => {\n        this.notificationService.showSuccess(`Claim ${this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value} activated  Successfully`, ValidationMsgs.success, 4000);\n        this.subjectService.setCloseTabRefresh('Edit Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);\n      });\n    }\n\n    deactivateClaim() {\n      this.claimModel = [];\n      this.claimModelConvertion();\n      this.updateModelMapping();\n      let data = [];\n      data = this.claimModel;\n      data[0].isActive = false;\n      data[0].status = \"InActive\";\n\n      if (this.showNotes) {\n        this.showNotes = false;\n      } // return;\n\n\n      this.claimService.createClaim(data).subscribe(res => {\n        this.notificationService.showSuccess(`Claim ${this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value} de-activated  Successfully`, ValidationMsgs.success, 4000);\n        this.subjectService.setCloseTabRefresh('Edit Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);\n        this.subjectService.setIsClaimMovedOtherBucket();\n        this.subjectService.setSearchClaimRefresh();\n      });\n    }\n\n    updateModelMapping() {\n      let data = this.claimModel;\n      data[0].claimFormStatusCode = this.claimFormData?.claimViewModel?.claimFormStatusCode;\n      data[0].parentClaimForm837Pid = this.claimFormData?.claimViewModel?.parentClaimForm837Pid;\n      data[0].parentPatientCtrlNo = this.claimFormData?.claimViewModel?.parentPatientCtrlNo;\n      data[0].formCreatedDate = this.claimFormData?.claimViewModel?.formCreatedDate;\n      data[0].onHoldCategory = this.claimFormData?.claimViewModel?.onHoldCategory;\n      data[0].notes = this.claimFormData?.claimViewModel?.notes;\n      data[0].formCreatedDate = this.claimFormData?.claimViewModel?.formCreatedDate;\n      data[0].source = this.claimFormData?.claimViewModel?.source;\n      data[0].userFirstName = JSON.parse(localStorage.getItem('userFirstName')), data[0].userLastName = JSON.parse(localStorage.getItem('userLastName')), data[0].userMiddleName = JSON.parse(localStorage.getItem('userMiddleName')) ? JSON.parse(localStorage.getItem('userMiddleName')) : null;\n      data[0].lastModifiedByFirstName = JSON.parse(localStorage.getItem('userFirstName'));\n      data[0].lastModifiedByLastName = JSON.parse(localStorage.getItem('userLastName'));\n      data[0].lastModifiedByMiddleName = JSON.parse(localStorage.getItem('userMiddleName')) ? JSON.parse(localStorage.getItem('userMiddleName')) : null;\n    }\n\n    resubmission() {\n      if (this.showNotes) {\n        this.showNotes = false;\n      }\n\n      const dialogRef = this.dialog.open(ResubmitClaimComponent, {\n        //height: '680px',\n        //width: '1400px',\n        minHeight: '240px',\n        maxHeight: '680px',\n        minWidth: '1200px',\n        maxWidth: '1400px',\n        panelClass: 'custom-dialog-containers',\n        data: {\n          claimControlNumber: this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value,\n          claimId: this.claimFormData?.claimViewModel?.parentClaimForm837Pid ? this.claimFormData?.claimViewModel?.parentClaimForm837Pid : this.claimFormData?.claimViewModel?.claimForm837Pid\n        }\n      });\n      dialogRef.afterClosed().subscribe(res => {\n        if (res) {\n          this.subjectService.setCloseTabRefresh('View Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);\n        }\n      });\n    }\n\n    showResubmit() {\n      const privielagesDetails = this.globalService.getPrivilegesByRole();\n\n      if (privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_Resubmission).length > 0) {\n        return this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.dispatched && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.acknolwedgedByCH && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.acceptedByCH && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.unAckByPayer && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.acceptedByPayer && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.acknolwedgedByPayer && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.rejectedByPayer && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.pending.toUpperCase() && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.rejectedByCH && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.eobReceived && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.deniedByPayer && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null ? true : false;\n      }\n\n      return false;\n    }\n\n    rejectedReason() {\n      this.dialog.open(RejectedInComponent, {\n        minHeight: '40vh',\n        minWidth: '70vw',\n        maxHeight: '72vh',\n        height: '-webkit-fill-available',\n        panelClass: 'custom-dialog-containers',\n        data: {\n          claimControlNumber: this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value,\n          claimId: this.claimFormData?.claimViewModel?.parentClaimForm837Pid\n        }\n      });\n    }\n\n    pendingDetails() {\n      this.dialog.open(PendingDetailsComponent, {\n        minHeight: '40vh',\n        minWidth: '70vw',\n        maxHeight: '72vh',\n        height: '-webkit-fill-available',\n        panelClass: 'custom-dialog-containers',\n        data: {\n          claimControlNumber: this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value\n        }\n      });\n    }\n\n    viewClaimClose() {\n      let tabName = Tabs.editClaim;\n      if (this.claimFormData?.isViewClaim) tabName = Tabs.viewClaim;\n      this.subjectService.setCloseTabRefresh(tabName + ' - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);\n    }\n\n    notesLength() {\n      return this.claimFormData?.claimViewModel?.notes?.filter(t => !t.isRemoved).length;\n    }\n\n    openLinkedClaims() {\n      this.dialog.open(LinkedClaimsComponent, {\n        minHeight: '40vh',\n        minWidth: '70vw',\n        maxHeight: '65vh',\n        height: '-webkit-fill-available',\n        panelClass: 'custom-dialog-containers',\n        data: {\n          claimControlNumber: this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value,\n          linkedClaims: this.claimFormData?.claimViewModel?.lstLinkedClaimDetails\n        }\n      });\n    }\n\n    viewLinkedClaim(data) {\n      let nav = new NavTabFromDetails();\n      nav.name = Tabs.viewClaim;\n      nav.data.claimControlNumber = data.patientCtrlNo;\n      this.subjectService.setViewClaim(data.claimPid);\n      localStorage.setItem('claimTapName', 'View Claim - #' + data.patientCtrlNo);\n      this.subjectService.passValue(nav);\n    }\n\n    downloadLatestResponseFile() {\n      let b64Data;\n      let fileId;\n\n      if (this.claimFormData.claimViewModel?.claimFormStatusCode === 'AP') {\n        fileId = this.claimFormData.claimViewModel?.incomingFileId;\n      } else {\n        fileId = this.claimFormData.claimViewModel?.responseFileClaimMapings?.[0]?.incomeFileLoggerId;\n      }\n\n      this.fileService.downloadLatestResponseFile(fileId).subscribe(data => {\n        if (data.statusCode == 404) {\n          this.notificationService.showError('', 'File Not Found.', 4000);\n        } else if (data.statusCode == 500) {\n          this.notificationService.showError('Error occured, Please try again later.', 'Error!', 4000);\n        } else {\n          if (!!data.content && !!data.content.ediFileContent) {\n            b64Data = data.content.ediFileContent;\n            this.notificationService.showSuccess('File Downloaded.', ValidationMsgs.success, 4000);\n            const blob1 = new Blob([atob(b64Data)], {\n              type: 'text/plain'\n            });\n            const a = document.createElement('a');\n            a.style = 'display: none';\n            document.body.appendChild(a);\n            const url = window.URL.createObjectURL(blob1);\n            a.href = url;\n            a.download = !!data.content.fileName ? data.content.fileName : 'Latest Response';\n            a.click();\n            window.URL.revokeObjectURL(url);\n          }\n        }\n      });\n    }\n\n    exportClaim() {\n      this.claimModel = [];\n      this.claimModelConvertion();\n      this.updateModelMapping();\n      this.claimModel[0].reasons = this.claimFormData?.claimViewModel?.reasons;\n      this.claimModel[0].lastModifiedByFirstName = this.claimFormData?.claimViewModel?.lastModifiedByFirstName;\n      this.claimModel[0].lastModifiedByLastName = this.claimFormData?.claimViewModel?.lastModifiedByLastName;\n      this.claimModel[0].lastModifiedDate = this.claimFormData?.claimViewModel?.lastModifiedDate;\n      this.claimModel[0].showNDC = this.ServiceLineClaimInfo.serviceLineInfo.controls[\"showNDC\"].value === \"No\" ? false : true;\n      this.claimModel[0]._277processedOnPayer = this.claimFormData?.claimViewModel._277processedOnPayer;\n      this.claimModel[0]._999processedOnPayer = this.claimFormData?.claimViewModel._999processedOnPayer;\n      this.claimModel[0]._999processedOnCh = this.claimFormData?.claimViewModel._999processedOnCh;\n      this.claimModel[0]._277processedOnCh = this.claimFormData?.claimViewModel._277processedOnCh;\n      this.claimModel[0].ignoreReason = this.claimFormData?.claimViewModel.ignoreReason;\n      this.claimModel[0].patientDob = this.claimFormData?.claimViewModel?.patientDob;\n      this.claimModel[0].isActive = this.claimFormData?.claimViewModel.isActive;\n\n      if (this.showNotes) {\n        this.showNotes = false;\n      }\n\n      this.fileService.exportCMSForm(this.claimModel[0]).subscribe(data => {\n        if (data.statusCode == 404) {\n          this.notificationService.showError('', 'File Not Found.', 4000);\n        } else if (data.statusCode == 500) {\n          this.notificationService.showError('Error occured, Please try again later.', 'Error!', 4000);\n        } else {\n          if (!!data.content) {\n            this.notificationService.showSuccess('File Downloaded.', ValidationMsgs.success, 4000);\n            const byteString = window.atob(data.content);\n            const arrayBuffer = new ArrayBuffer(byteString.length);\n            const int8Array = new Uint8Array(arrayBuffer);\n\n            for (let i = 0; i < byteString.length; i++) {\n              int8Array[i] = byteString.charCodeAt(i);\n            }\n\n            let fileName = this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value + \"_\" + this.formatDate(new Date());\n            const blob = new Blob([int8Array], {\n              type: 'application/pdf'\n            });\n            const file = blob ? new File([blob], this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value + '.pdf', {\n              type: 'application/pdf'\n            }) : null;\n            const url2 = file ? URL.createObjectURL(file) : null;\n            const a = document.createElement('a');\n            a.style = 'display: none';\n            document.body.appendChild(a);\n            a.href = url2;\n            a.download = fileName + '.pdf';\n            a.click();\n            window.URL.revokeObjectURL(url2);\n          }\n        }\n      });\n    }\n\n    formatDate(dateObj) {\n      var curr_date = dateObj.getDate();\n      var curr_month = dateObj.getMonth();\n      curr_month = curr_month + 1;\n      var curr_year = dateObj.getFullYear();\n      var curr_min = dateObj.getMinutes();\n      var curr_hr = dateObj.getHours();\n      var curr_sc = dateObj.getSeconds();\n      if (curr_month.toString().length == 1) curr_month = '0' + curr_month;\n      if (curr_date.toString().length == 1) curr_date = '0' + curr_date;\n      if (curr_hr.toString().length == 1) curr_hr = '0' + curr_hr;\n      if (curr_min.toString().length == 1) curr_min = '0' + curr_min;\n      return curr_month + curr_date + curr_year + curr_hr + curr_min + curr_sc;\n    }\n\n    validateDiagnosisPointerData(e) {\n      for (let i = 0; i <= this.ServiceLineClaimInfo.serviceLine.value.length - 1; i++) {\n        for (let j = 1; j <= 4; j++) {\n          if ((Number.parseInt(this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).value) === e.index || Number.parseInt(this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).value) === 0) && e.validationRequired) {\n            this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).setErrors({\n              numberMax: true\n            });\n            this.ServiceLineClaimInfo.serviceLine.updateValueAndValidity();\n          } else {\n            this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).setErrors(null);\n            this.ServiceLineClaimInfo.serviceLine.updateValueAndValidity();\n          }\n        }\n      }\n\n      this.ServiceLineClaimInfo.setValidator();\n    }\n\n    updatePayerItem(newPayerItem) {\n      this.claimFormData.claimViewModel.payerId = newPayerItem.payerId;\n      this.claimFormData.claimViewModel.insuranceCompany = newPayerItem.payerName;\n    }\n\n    getPrivilegesByRole() {\n      const privielagesDetails = this.globalService.getPrivilegesByRole();\n      this.isDeactivateClaimBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_DeactivateaClaim).length > 0 ? true : false;\n      this.isAcceptedClaimBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_AcceptAClaim).length > 0 ? true : false;\n      this.isActivateClaimBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_ActivateaClaim).length > 0 ? true : false;\n      this.isOnHoldClainBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_OnHoldAClaim).length > 0 ? true : false;\n      this.isUpdateClaimBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_UpdateaClaim).length > 0 ? true : false;\n    }\n\n  }\n\n  CreateClaimComponent.ɵfac = function CreateClaimComponent_Factory(t) {\n    return new (t || CreateClaimComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ClaimService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.SubjectService), i0.ɵɵdirectiveInject(i4.NotificationService), i0.ɵɵdirectiveInject(i5.FileService), i0.ɵɵdirectiveInject(i6.DateFormatService), i0.ɵɵdirectiveInject(i7.ProviderManagementService), i0.ɵɵdirectiveInject(i8.GlobalService), i0.ɵɵdirectiveInject(i9.CacheService));\n  };\n\n  CreateClaimComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CreateClaimComponent,\n    selectors: [[\"app-create-claim\"]],\n    viewQuery: function CreateClaimComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(PayerInfoComponent, 7);\n        i0.ɵɵviewQuery(MediGroupComponent, 7);\n        i0.ɵɵviewQuery(ServiceFacilityComponent, 7);\n        i0.ɵɵviewQuery(SubscriberInfoComponent, 7);\n        i0.ɵɵviewQuery(PatientInfoComponent, 7);\n        i0.ɵɵviewQuery(PatientDobComponent, 7);\n        i0.ɵɵviewQuery(PatientAddressComponent, 7);\n        i0.ɵɵviewQuery(PatientRelationshipComponent, 7);\n        i0.ɵɵviewQuery(PatientSignatureComponent, 7);\n        i0.ɵɵviewQuery(FederalTaxComponent, 7);\n        i0.ɵɵviewQuery(PatientCurrentOccuptionComponent, 7);\n        i0.ɵɵviewQuery(CurrentIllnessComponent, 7);\n        i0.ɵɵviewQuery(OtherDateComponent, 7);\n        i0.ɵɵviewQuery(InsureInfoComponent, 7);\n        i0.ɵɵviewQuery(InsurePolicyGroupComponent, 7);\n        i0.ɵɵviewQuery(InsureAddressComponent, 7);\n        i0.ɵɵviewQuery(InsurePolicyGroupComponent, 7);\n        i0.ɵɵviewQuery(InsureAuthorizedComponent, 7);\n        i0.ɵɵviewQuery(OtherProviderComponent, 7);\n        i0.ɵɵviewQuery(OtherProviderPayerComponent, 7);\n        i0.ɵɵviewQuery(OutsideLabComponent, 7);\n        i0.ɵɵviewQuery(HospitalizationServiceComponent, 7);\n        i0.ɵɵviewQuery(DiagnosisNatureComponent, 7);\n        i0.ɵɵviewQuery(ServiceLineClaimComponent, 7);\n        i0.ɵɵviewQuery(BillingInfoComponent, 7);\n        i0.ɵɵviewQuery(ProviderSignComponent, 7);\n        i0.ɵɵviewQuery(ChargesComponent, 7);\n        i0.ɵɵviewQuery(PatientAccountAcceptAssignmentComponent, 7);\n        i0.ɵɵviewQuery(ResubmissionAuthorizedNumComponent, 7);\n        i0.ɵɵviewQuery(AdditionalClaimComponent, 7);\n        i0.ɵɵviewQuery(PatientOtherInsureComponent, 7);\n        i0.ɵɵviewQuery(PatientConditionComponent, 7);\n        i0.ɵɵviewQuery(PatientRelationshipComponent, 7);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.PayerInfoForm = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.MediGroup = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ServiceFacilityForm = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.SubscriberInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.PatientInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.PatientDOBInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.PatientAddressInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.PatientRelationshipInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.PatientSignatureInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.FederalTaxInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.PatientCurrentOccuptionInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.CurrentIllnessInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.OtherDateInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.InsureInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.InsurePolicyGroup = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.InsureAddressInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.InsurePolicyGroupInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.InsureAuthorizedInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.OtherProviderInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.OtherProviderPayerInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.OutsideLabInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.HospitalizationServiceInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.DiagnosisNature = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ServiceLineClaimInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.BillingInfoInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ProviderSignInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ChargesInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.PatientAccountAcceptAssignmentInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ResubmissionAuthorizedNumInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.AdditionalClaimInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.PatientOtherInsureInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.PatientConditionInfo = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.patientRelationshipComponent = _t.first);\n      }\n    },\n    inputs: {\n      allPlaceOfServices: \"allPlaceOfServices\",\n      claimFormData: \"claimFormData\",\n      claimData: \"claimData\"\n    },\n    outputs: {\n      closeClaim: \"closeClaim\"\n    },\n    decls: 97,\n    vars: 85,\n    consts: [[1, \"container-fluid\", \"create-claim-component\", 3, \"ngClass\"], [1, \"mat-card\", \"mt-3\", \"create-claim-form-styles\"], [1, \"create-claim\", 2, \"display\", \"flow-root !important\"], [1, \"create-claim-title\", 2, \"margin-bottom\", \"-30px\"], [\"class\", \"btn btn-primary create-claim-action primary-btn btn-height\", \"style\", \"float: right; display: block;\", \"type\", \"button\", \"form\", \"claimForm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"create-claim-action\", 4, \"ngIf\"], [\"class\", \"row\", \"style\", \"position:absolute;top:120px;right:20px; z-index:999;text-align:center;\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-md-4\", \"form-border\"], [4, \"ngIf\"], [3, \"payerItem\", \"claimFormData\", \"allStateData\", \"payerItemChange\"], [1, \"col-md-8\", \"form-border\"], [3, \"payerItem\", \"claimFormData\"], [3, \"subscriberId\", \"claimFormData\"], [3, \"memberResult\", \"claimFormData\"], [3, \"insuredPerson\", \"claimFormData\"], [3, \"memberResult\", \"claimFormData\", \"allStateData\", \"getInsureData\"], [3, \"memberProfile\", \"claimFormData\"], [3, \"insuredPerson\", \"claimFormData\", \"allStateData\"], [3, \"claimFormData\"], [3, \"allStateData\", \"claimFormData\"], [3, \"insuredPerson\", \"claimFormData\", \"anotherHealthInsurenceValidate\"], [3, \"claimFormData\", \"qualifierDataByType\", \"currentIllnessDateChanged\"], [3, \"claimFormData\", \"qualifierDataByType\", \"changeOtherQual\"], [3, \"otherProvider\", \"claimFormData\", \"provider\"], [3, \"claimFormData\", \"serviceLineInfo\", \"formServiceLine\", \"claimData\", \"getICDCountData\"], [3, \"claimFormData\", \"resubmissionCode\"], [1, \"col-md-12\", \"form-border\"], [3, \"claimData\", \"allPlaceOfServices\", \"serviceLineInfos\", \"memberProfile\", \"claimFormData\", \"calculateTotal\", \"dosFromChanged\"], [3, \"serviceLineInfos\", \"claimFormData\"], [3, \"claimFormData\", \"supervisingProviderInfo\"], [3, \"claimFormData\", \"allStateData\", \"billingInfoForm\"], [3, \"claimFormData\", \"allStateData\", \"serviceFacilityForm\", \"federalTaxForm\", \"taxonomyCode\"], [\"type\", \"button\", \"form\", \"claimForm\", 1, \"btn\", \"btn-primary\", \"create-claim-action\", \"primary-btn\", \"btn-height\", 2, \"float\", \"right\", \"display\", \"block\", 3, \"click\"], [1, \"fa\", \"fa-floppy-o\", \"icon-margin\"], [1, \"create-claim-action\"], [\"type\", \"button\", \"form\", \"claimForm\", 1, \"btn\", \"btn-primary\", \"primary-btn\", 3, \"click\"], [1, \"fa\", \"fa-sticky-note-o\", \"icon-margin\"], [1, \"fa\", \"fa-unlock\", \"icon-margin\"], [1, \"fa\", \"fa-thumbs-o-up\", \"icon-margin\"], [1, \"material-icons\", \"icon-margin\"], [1, \"material-icons\", \"icon-margin\", 2, \"font-size\", \"13px !important\"], [\"type\", \"button\", \"form\", \"claimForm\", 1, \"btn\", \"btn-primary\", \"primary-btn\", 3, \"disabled\", \"click\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"data-placement\", \"top\", \"title\", \"Save All Notes\", 1, \"fa\", \"fa-save\"], [1, \"fa\", \"fa-retweet\", \"icon-margin\"], [\"type\", \"button\", \"form\", \"claimForm\", \"matTooltip\", \"Export CMS1500 Form\", \"matTooltipPosition\", \"above\", 1, \"btn\", \"btn-primary\", \"primary-btn\", 3, \"click\"], [1, \"fa\", \"fa-file-pdf-o\", \"icon-margin\"], [\"type\", \"button\", \"form\", \"claimForm\", 1, \"btn\", \"btn-common\", \"common-btn\", 3, \"click\"], [\"class\", \"btn btn-primary primary-btn\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-common common-btn\", \"type\", \"button\", \"form\", \"claimForm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"primary-btn\", 3, \"click\"], [1, \"row\", 2, \"position\", \"absolute\", \"top\", \"120px\", \"right\", \"20px\", \"z-index\", \"999\", \"text-align\", \"center\"], [4, \"ngFor\", \"ngForOf\"], [3, \"id\", \"dismiss\", \"focusout\", 4, \"ngIf\"], [3, \"id\", \"dismiss\", \"focusout\"], [1, \"row\", \"mt-2\"], [1, \"col-md\", \"claim-title\"], [1, \"sub-title-name-display\"], [\"class\", \"row mt-2\", 4, \"ngIf\"], [1, \"col-md\"], [\"for\", \"flexCheckDefault\", 1, \"\"], [1, \"hyper-link\", 3, \"click\"], [\"class\", \"fa fa-download download-icon\", \"matTooltip\", \"Download Latest Response File\", \"matTooltipPosition\", \"above\", \"matTooltipClass\", \"above\", 3, \"click\", 4, \"ngIf\"], [\"matTooltip\", \"Download Latest Response File\", \"matTooltipPosition\", \"above\", \"matTooltipClass\", \"above\", 1, \"fa\", \"fa-download\", \"download-icon\", 3, \"click\"], [\"class\", \"round-circle\", \"style\", \"display: inline-block;\", 3, \"click\", 4, \"ngIf\"], [1, \"round-circle\", 2, \"display\", \"inline-block\", 3, \"click\"], [\"class\", \"hyper-link\", \"matTooltip\", \"Details\", \"matTooltipPosition\", \"above\", \"matTooltipClass\", \"above\", 3, \"click\", 4, \"ngIf\"], [\"matTooltip\", \"Details\", \"matTooltipPosition\", \"above\", \"matTooltipClass\", \"above\", 1, \"hyper-link\", 3, \"click\"], [\"class\", \"col-md claim-title\", 4, \"ngIf\"]],\n    template: function CreateClaimComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, CreateClaimComponent_button_5_Template, 3, 0, \"button\", 4);\n        i0.ɵɵtemplate(6, CreateClaimComponent_div_6_Template, 16, 11, \"div\", 5);\n        i0.ɵɵtemplate(7, CreateClaimComponent_div_7_Template, 10, 5, \"div\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, CreateClaimComponent_div_8_Template, 2, 1, \"div\", 6);\n        i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8);\n        i0.ɵɵtemplate(11, CreateClaimComponent_div_11_Template, 25, 15, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 8);\n        i0.ɵɵtemplate(13, CreateClaimComponent_div_13_Template, 26, 16, \"div\", 9);\n        i0.ɵɵtemplate(14, CreateClaimComponent_div_14_Template, 10, 10, \"div\", 9);\n        i0.ɵɵtemplate(15, CreateClaimComponent_div_15_Template, 2, 1, \"div\", 9);\n        i0.ɵɵtemplate(16, CreateClaimComponent_div_16_Template, 2, 1, \"div\", 9);\n        i0.ɵɵtemplate(17, CreateClaimComponent_div_17_Template, 3, 2, \"div\", 9);\n        i0.ɵɵtemplate(18, CreateClaimComponent_div_18_Template, 9, 2, \"div\", 9);\n        i0.ɵɵtemplate(19, CreateClaimComponent_div_19_Template, 8, 3, \"div\", 9);\n        i0.ɵɵtemplate(20, CreateClaimComponent_div_20_Template, 9, 4, \"div\", 9);\n        i0.ɵɵtemplate(21, CreateClaimComponent_div_21_Template, 7, 2, \"div\", 9);\n        i0.ɵɵtemplate(22, CreateClaimComponent_div_22_Template, 3, 1, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"div\", 8)(24, \"app-payer-info\", 10);\n        i0.ɵɵlistener(\"payerItemChange\", function CreateClaimComponent_Template_app_payer_info_payerItemChange_24_listener($event) {\n          return ctx.updatePayerItem($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(25, \"div\", 7)(26, \"div\", 11);\n        i0.ɵɵelement(27, \"app-medi-group\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"div\", 8);\n        i0.ɵɵelement(29, \"app-subscriber-info\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"div\", 7)(31, \"div\", 8);\n        i0.ɵɵelement(32, \"app-patient-info\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"div\", 8);\n        i0.ɵɵelement(34, \"app-patient-dob\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"div\", 8);\n        i0.ɵɵelement(36, \"app-insure-info\", 15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(37, \"div\", 7)(38, \"div\", 8)(39, \"app-patient-address\", 16);\n        i0.ɵɵlistener(\"getInsureData\", function CreateClaimComponent_Template_app_patient_address_getInsureData_39_listener($event) {\n          return ctx.setInsureData($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"div\", 8);\n        i0.ɵɵelement(41, \"app-patient-relationship\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"div\", 8);\n        i0.ɵɵelement(43, \"app-insure-address\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(44, \"div\", 7)(45, \"div\", 8);\n        i0.ɵɵelement(46, \"app-patient-other-insure\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(47, \"div\", 8);\n        i0.ɵɵelement(48, \"app-patient-condition\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(49, \"div\", 8)(50, \"app-insure-policy-group\", 21);\n        i0.ɵɵlistener(\"anotherHealthInsurenceValidate\", function CreateClaimComponent_Template_app_insure_policy_group_anotherHealthInsurenceValidate_50_listener($event) {\n          return ctx.anotherHealthInsurenceValidate($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(51, \"div\", 7)(52, \"div\", 11);\n        i0.ɵɵelement(53, \"app-patient-signature\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"div\", 8);\n        i0.ɵɵelement(55, \"app-insure-authorized\", 19);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(56, \"div\", 7)(57, \"div\", 8)(58, \"app-current-illness\", 22);\n        i0.ɵɵlistener(\"currentIllnessDateChanged\", function CreateClaimComponent_Template_app_current_illness_currentIllnessDateChanged_58_listener() {\n          return ctx.validateCurrentIllnessDate();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(59, \"div\", 8)(60, \"app-other-date\", 23);\n        i0.ɵɵlistener(\"changeOtherQual\", function CreateClaimComponent_Template_app_other_date_changeOtherQual_60_listener() {\n          return ctx.validateAccidentDate();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(61, \"div\", 8);\n        i0.ɵɵelement(62, \"app-patient-current-occuption\", 19);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(63, \"div\", 7)(64, \"div\", 8);\n        i0.ɵɵelement(65, \"app-other-provider\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(66, \"div\", 8);\n        i0.ɵɵelement(67, \"app-other-provider-payer\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"div\", 8);\n        i0.ɵɵelement(69, \"app-hospitalization-service\", 19);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(70, \"div\", 7)(71, \"div\", 11);\n        i0.ɵɵelement(72, \"app-additional-claim\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(73, \"div\", 8);\n        i0.ɵɵelement(74, \"app-outside-lab\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(75, \"div\", 7)(76, \"div\", 11)(77, \"app-diagnosis-nature\", 25);\n        i0.ɵɵlistener(\"getICDCountData\", function CreateClaimComponent_Template_app_diagnosis_nature_getICDCountData_77_listener($event) {\n          return ctx.validateDiagnosisPointerData($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(78, \"div\", 8);\n        i0.ɵɵelement(79, \"app-resubmission-authorized-num\", 26);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(80, \"div\", 7)(81, \"div\", 27)(82, \"app-service-line-claim\", 28);\n        i0.ɵɵlistener(\"calculateTotal\", function CreateClaimComponent_Template_app_service_line_claim_calculateTotal_82_listener($event) {\n          return ctx.calculateTotal($event);\n        })(\"dosFromChanged\", function CreateClaimComponent_Template_app_service_line_claim_dosFromChanged_82_listener() {\n          return ctx.validateDosFrom();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(83, \"div\", 7)(84, \"div\", 8);\n        i0.ɵɵelement(85, \"app-federal-tax\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(86, \"div\", 8);\n        i0.ɵɵelement(87, \"app-patient-account-accept-assignment\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(88, \"div\", 8);\n        i0.ɵɵelement(89, \"app-charges\", 29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(90, \"div\", 7)(91, \"div\", 8);\n        i0.ɵɵelement(92, \"app-provider-sign\", 30);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(93, \"div\", 8);\n        i0.ɵɵelement(94, \"app-service-facility\", 31);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(95, \"div\", 8);\n        i0.ɵɵelement(96, \"app-billing-info\", 32);\n        i0.ɵɵelementEnd()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(83, _c0, !ctx.isShowUpdateBtn && !ctx.isNewClaim));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.isNewClaim ? \"Create\" : ctx.isShowUpdateBtn ? \"Edit\" : \"View\", \" Claim (CMS 1500) \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isNewClaim);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.isActive);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !(ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.isActive) && ctx.isActivateClaimBtnShow);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isShowUpdateBtn || ctx.claimFormData.isViewClaim || ctx.isNewClaim) && ctx.showNotes);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim && (ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.claimFormStatusCode) == \"OH\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim && (ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.claimFormStatusCode) == \"AC\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim && (ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.claimFormStatusCode) == \"ANCH\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim && (ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.claimFormStatusCode) == \"ABCH\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim && (ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.claimFormStatusCode) == \"EOB\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim && (ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.claimFormStatusCode) == \"Pend\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim && (ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.claimFormStatusCode) == \"RP\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim && (ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.claimFormStatusCode) == \"RCH\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim && (ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.claimFormStatusCode) == \"ABP\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.claimFormData.isAddClaim && (ctx.claimFormData.claimViewModel == null ? null : ctx.claimFormData.claimViewModel.claimFormStatusCode) == \"DBP\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"payerItem\", ctx.claimFormData == null ? null : ctx.claimFormData.payerItem)(\"claimFormData\", ctx.claimFormData)(\"allStateData\", ctx.claimFormData == null ? null : ctx.claimFormData.allStatesBySearchstring);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"payerItem\", ctx.claimFormData == null ? null : ctx.claimFormData.payerItem)(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"subscriberId\", ctx.claimFormData == null ? null : ctx.claimFormData.memberResult == null ? null : ctx.claimFormData.memberResult.subscriberID)(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"memberResult\", ctx.claimFormData == null ? null : ctx.claimFormData.memberResult == null ? null : ctx.claimFormData.memberResult.member)(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"memberResult\", ctx.claimFormData == null ? null : ctx.claimFormData.memberResult == null ? null : ctx.claimFormData.memberResult.member)(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"insuredPerson\", ctx.claimFormData == null ? null : ctx.claimFormData.memberResult == null ? null : ctx.claimFormData.memberResult.insuredPerson)(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"memberResult\", ctx.claimFormData == null ? null : ctx.claimFormData.memberResult == null ? null : ctx.claimFormData.memberResult.member)(\"claimFormData\", ctx.claimFormData)(\"allStateData\", ctx.claimFormData == null ? null : ctx.claimFormData.allStatesBySearchstring);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"memberProfile\", ctx.claimFormData == null ? null : ctx.claimFormData.memberResult)(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"insuredPerson\", ctx.claimFormData == null ? null : ctx.claimFormData.memberResult == null ? null : ctx.claimFormData.memberResult.insuredPerson)(\"claimFormData\", ctx.claimFormData)(\"allStateData\", ctx.claimFormData == null ? null : ctx.claimFormData.allStatesBySearchstring);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"allStateData\", ctx.claimFormData == null ? null : ctx.claimFormData.allStatesBySearchstring)(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"insuredPerson\", ctx.claimFormData == null ? null : ctx.claimFormData.memberResult == null ? null : ctx.claimFormData.memberResult.insuredPerson)(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData)(\"qualifierDataByType\", ctx.claimFormData == null ? null : ctx.claimFormData.qualifierDataByType);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData)(\"qualifierDataByType\", ctx.claimFormData == null ? null : ctx.claimFormData.qualifierDataByType);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"otherProvider\", ctx.claimFormData == null ? null : ctx.claimFormData.referringProviderInfo)(\"claimFormData\", ctx.claimFormData)(\"provider\", ctx.claimFormData == null ? null : ctx.claimFormData.provider);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"otherProvider\", ctx.claimFormData == null ? null : ctx.claimFormData.referringProviderInfo)(\"claimFormData\", ctx.claimFormData)(\"provider\", ctx.claimFormData == null ? null : ctx.claimFormData.provider);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData)(\"serviceLineInfo\", ctx.claimFormData == null ? null : ctx.claimFormData.serviceLineInfo)(\"formServiceLine\", ctx.ServiceLineClaimInfo)(\"claimData\", ctx.claimData);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData)(\"resubmissionCode\", ctx.claimFormData == null ? null : ctx.claimFormData.resubmissionCode);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"claimData\", ctx.claimData)(\"allPlaceOfServices\", ctx.allPlaceOfServices)(\"serviceLineInfos\", ctx.claimFormData == null ? null : ctx.claimFormData.serviceLineInfo)(\"memberProfile\", ctx.claimFormData == null ? null : ctx.claimFormData.profileMember)(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"serviceLineInfos\", ctx.claimFormData == null ? null : ctx.claimFormData.serviceLineInfo)(\"claimFormData\", ctx.claimFormData);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData)(\"supervisingProviderInfo\", ctx.claimFormData == null ? null : ctx.claimFormData.supervisingProviderInfo);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData)(\"allStateData\", ctx.claimFormData == null ? null : ctx.claimFormData.allStatesBySearchstring)(\"billingInfoForm\", ctx.BillingInfoInfo.f);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"claimFormData\", ctx.claimFormData)(\"allStateData\", ctx.claimFormData == null ? null : ctx.claimFormData.allStatesBySearchstring)(\"serviceFacilityForm\", ctx.ServiceFacilityForm.f)(\"federalTaxForm\", ctx.FederalTaxInfo.f)(\"taxonomyCode\", ctx.claimFormData == null ? null : ctx.claimFormData.taxonomyCode);\n      }\n    },\n    dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i11.MatTooltip, i12.PayerInfoComponent, i13.MediGroupComponent, i14.PatientInfoComponent, i15.PatientDobComponent, i16.PatientAddressComponent, i17.PatientRelationshipComponent, i18.PatientOtherInsureComponent, i19.PatientConditionComponent, i20.PatientSignatureComponent, i21.CurrentIllnessComponent, i22.OtherDateComponent, i23.OtherProviderComponent, i24.OtherProviderPayerComponent, i25.AdditionalClaimComponent, i26.DiagnosisNatureComponent, i27.FederalTaxComponent, i28.PatientAccountAcceptAssignmentComponent, i29.ProviderSignComponent, i30.ServiceFacilityComponent, i31.InsureInfoComponent, i32.InsureAddressComponent, i33.InsurePolicyGroupComponent, i34.InsureAuthorizedComponent, i35.PatientCurrentOccuptionComponent, i36.HospitalizationServiceComponent, i37.OutsideLabComponent, i38.ResubmissionAuthorizedNumComponent, i39.ChargesComponent, i40.BillingInfoComponent, i41.ServiceLineClaimComponent, i42.SubscriberInfoComponent, i43.NotesComponent, i10.UpperCasePipe, i10.DatePipe],\n    styles: [\".create-claims-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;color:#3b475a}.form-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:16px;color:#3b475a}.create-claim-form[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:24px;line-height:31px}.form-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.form-border[_ngcontent-%COMP%]{border:1px solid #DCE3EF;padding:12px}.claim-label[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px}.claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:16px;line-height:42px;color:#617798}.search-icon-alignment[_ngcontent-%COMP%]{position:relative}.search-icons[_ngcontent-%COMP%]{position:absolute;right:0%;top:20%;cursor:pointer;z-index:1000}.create-claim-title[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:600;font-size:20px}.create-claim[_ngcontent-%COMP%]{display:flex;justify-content:space-between}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:12px}.create-claim-radio-labels[_ngcontent-%COMP%]{font-family:IBM Plex Sans;font-style:normal;font-weight:400;font-size:14px;color:#617798}.table-flex[_ngcontent-%COMP%]{display:flex;gap:2px}.radio-flex[_ngcontent-%COMP%]{display:flex;gap:5px}.form-select-sm[_ngcontent-%COMP%]{font-size:12px}@media (min-width: 1300px){.create-claims-labels[_ngcontent-%COMP%]{font-size:12px}  .view-claim-component .form-control{font-size:12px}.form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:12px}.claim-title[_ngcontent-%COMP%]{font-size:14px}}@media (min-width: 1700px){.create-claims-labels[_ngcontent-%COMP%]{font-size:14px}  .view-claim-component .form-control{font-size:14px}.form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%]{font-size:14px}.claim-title[_ngcontent-%COMP%]{font-size:16px}}@media (min-width: 1900px){.create-claims-labels[_ngcontent-%COMP%]{font-size:16px}  .view-claim-component .form-control{font-size:16px}.form-title[_ngcontent-%COMP%], .create-claim-radio-labels[_ngcontent-%COMP%], .claim-title[_ngcontent-%COMP%]{font-size:16px}}  .mat-card{background:#fff;color:#000000de;padding:11px!important;border-radius:4px!important}.fieldError[_ngcontent-%COMP%]{border:1px solid #dc3545;border-radius:4px 6px}.sbt-btn[_ngcontent-%COMP%]{margin-right:-102px;float:right}.create-claim-action[_ngcontent-%COMP%]{justify-content:end!important;display:flex;margin-bottom:20px}.create-claim-component[_ngcontent-%COMP%]     .form-control{height:2rem!important;min-height:unset!important}.create-claim-component[_ngcontent-%COMP%]     .ng-select-container{height:100%!important}.view-claim-component[_ngcontent-%COMP%]     input::placeholder{color:transparent!important}.view-claim-component[_ngcontent-%COMP%]     select::placeholder{color:transparent!important}.view-claim-component[_ngcontent-%COMP%]     .ng-placeholder{color:transparent!important}.view-claim-component[_ngcontent-%COMP%]     ::placeholder{color:transparent!important}.view-claim-component[_ngcontent-%COMP%]     .form-control, .view-claim-component[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]{background-color:transparent!important;border:none!important;padding:0!important;font-style:italic!important;pointer-events:none!important;text-transform:uppercase;word-wrap:break-word;min-height:2rem!important;height:unset!important}.view-claim-component[_ngcontent-%COMP%]     .create-claims-labels{color:#617798;font-weight:600}.view-claim-component[_ngcontent-%COMP%]     .ng-select-disabled .ng-select-container{background-color:#e9ecef!important}  .ng-option{white-space:break-spaces!important}.sub-title-name-display[_ngcontent-%COMP%]{font-size:16px;color:#617798;font-weight:800}.desc[_ngcontent-%COMP%]{width:250px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.hyper-link[_ngcontent-%COMP%]{cursor:pointer;color:#0a0aff;font-weight:800}.round-circle[_ngcontent-%COMP%]{margin-left:.5rem;text-align:center;padding:0 3px;border-radius:45%;-webkit-border-radius:45%;-moz-border-radius:45%;font-size:12px;margin-right:-4px;cursor:pointer;border:1px solid #BDBDBD}.download-icon[_ngcontent-%COMP%]{cursor:pointer;margin-left:.5rem}.btn[_ngcontent-%COMP%]{height:26px}.material-icons[_ngcontent-%COMP%]{font-size:12px;padding:unset!important}.icon-margin[_ngcontent-%COMP%]{vertical-align:text-top}\"]\n  });\n  return CreateClaimComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}