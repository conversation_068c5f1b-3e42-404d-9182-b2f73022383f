.Form-Body {
    //box-shadow: 0px 0px 8px #0000001A;
    background-color: white;
    // padding: 5px;
    padding: 5px 7px 5px 5px;
}

.container {
    margin-left: 1%;
    margin-right: 1%;
    width: 98%;
}

.body {
    background-color: white;
    border-radius: 8px;
    margin-top: 10px;
    box-shadow: 0px 0px 8px #0000001a;
}

// basic styling for all tds in table
.TableTd {
    color: #272d3b;
    font-size: 12px;
    font-family: "Poppins";
}

// background effect on hover on table row
.TableTr:hover {
    background-color: #e3f2fd;
}

fieldset.scheduler-border {
    border: 1px solid #cccccc !important;
    padding: 0 1.4em 1.4em 1.4em !important;
    margin: 0 0 1.5em 0 !important;
    -webkit-box-shadow: 0px 0px 0px 0px #cccccc;
    box-shadow: 0px 0px 0px 0px #cccccc;
    margin-top: 30px !important;
    border-radius: 8px;
}

legend.scheduler-border {
    font-size: 14px !important;
    text-align: left !important;
    width: auto;
    padding: 0 10px;
    border-bottom: none;
    margin-top: -10px;
    background-color: white;
    color: #0074bc;
    font-family: "Poppins-SemiBold";
}

.leftMargin {
    margin-left: 16px;
}

select {
    height: 35px;
    border-radius: 8px;
    // box-shadow: 0px 0px 8px #0000001a;
}

.custom-select {
    // margin-left: 11px;
    // width: 250px;
    border-color: lightgray !important;
    color: #565656;
    height:30px;
    
    outline: none;

}

// style for checkbox and paginator of primeng
::ng-deep .p-checkbox-box {
    border-radius: 50% !important;
}
::ng-deep .p-paginator {
    margin-right: 1%;
    justify-content: flex-end !important;
}

/* basic setting for all input type */

input[type="text"],
input[type="number"] {
    border-radius: 6px;
    border: 1px solid #cccccc !important;
    height: 30px;
    outline: none;
    min-width: 40px;
    margin-left: 5px;
    // width: 150px;
    color: #757575;
    font-size: 12px;
    font-family: "Poppins";
    font-weight: bold;
    width:72%;
    padding-left: 5px !important;
}

.width {
    width: 120px !important;
}

// styling for input when on focus
input[type="text"]:focus,
input[type="number"]:focus {
    border: 1px solid #61abd4 !important;
}

// styling for table headers
th {
    padding-right: 0px !important;
    padding-left: 0px !important;
    background-color: white !important;
    border-radius: 8px;
}

/* for setting second heading box*/

.Form-Header-2 {
    font-family: "Poppins";
    background-color: white;
    padding: 10px;
    align-items: center;
    border-radius: 40px;
}

.Form-Header-2-Heading {
    color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 18px;
}

p-table {
    margin-left: 16px !important;
    border: 1px solid #cccccc;
    border-radius: 8px;
    width: 97% !important;
}

.TextRight {
    text-align: end;
}

.MarginRight {
    margin-right: 10px;
}

.MarginLeft {
    margin-left: 20px;
}

.btn {
    border: none;
    color: white;
    box-shadow: 0px 0px 8px #0000001a;
    border-radius: 8px;
    opacity: 1;
    background-color: #0074bc;
    font-family: "Poppins-SemiBold";
    font-size: 14px;
}

.btn:hover {
    background-color: #005488;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

::ng-deep .pi-sort-amount-down:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}
::ng-deep .pi-sort-amount-up-alt:before {
    content: "\F0DC" !important;
    font-family: "FontAwesome" !important;
}

:host ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: initial !important;
}

.ThBtn {
    font-family: "Poppins-Bold";
    width: 8%;
    color: #5d5d5d !important;
}

::ng-deep .p-datatable-resizable .p-datatable-tbody > tr > td,
.p-datatable-resizable .p-datatable-tfoot > tr > td,
.p-datatable-resizable .p-datatable-thead > tr > th {
    text-overflow: ellipsis !important;
}

.IconStyle {
    cursor: pointer;
    position: relative;
    z-index: 1 !important;
}

object {
    position: relative;
    z-index: -1 !important;
}

::ng-deep .p-datatable .p-sortable-column .p-sortable-column-icon {
    margin-left: 0rem;
}

.TextRight {
    text-align: end;
}

.col {
    font-family: "Poppins";
}

.inputFields {
    margin-left: 0px !important;
    background-color: white;
}

.marginLeft {
    margin-left: 20px;
}

input.ng-invalid.ng-touched {
    border: 1px solid red !important;
}

.backBtn {
    border-color: #0074bc !important;
    background-color: white;
    margin-right: 3%;
    color: #0074bc;
    border: 1px solid;
    font-size: 14px;
}

.backBtn:hover {
    border-color: #0074bc !important;
    background-color: white;
}

.paddingB {
    padding-bottom: 1px;
}

.buttons {
    padding: 0px 20px 10px 0px;
}

 

 


.LastTd{
    position: absolute;
    right: 0;
    background-color: white;
    height: 53px;
    width: 8%;
  }


.LastTh{
    border-bottom: 2px solid #e3f2fd;
    text-align: center!important;
    position: absolute;
    right: 0;
    background-color: white;
    height: 62.5px;
    width: 9%;
  }

  .IconsPadding{
      padding-top: 1%!important;
  }

  .updatedDate,.updatedDateWidth{
      width: 27%;
  }
  
  .updatedBy{
      width: 21%;
  }

  
.sftpHost,.sftpPort,.sftpDetailID,.clearingHouseName{
    width: 17%;
}

.sftpHost,.sftpPort{
    width: 23%;
}

.sftpHostWidth,.sftpPortWidth,.userNameWidth{
    width: 17%;

}

.sourceWidth,.updatedByWidth{
    width: 17%;
}

.widthCustom{
    width:100%;
}

.sftpBorder {
    font-size: 14px !important;
    text-align: left !important;
    width: auto;
    border-bottom: none;
    margin-top: 10px;
    background-color: white;
    color: #0074bc;
    font-family: "Poppins-SemiBold";
}



.heading837{
    margin-top: 2%;
}

.paddingIcon{
    padding-right: 3%;
    text-align: right;
}

.alignBtn{
    padding-left: 2%;
}

.textColor{
    font-size: 14px !important;
    color: #0074bc;
    font-family: "Poppins-SemiBold";
}