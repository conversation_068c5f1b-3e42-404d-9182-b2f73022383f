.MainRow{
    min-width: 500px;
}
.Medium {
    font-family: 'Poppins-Medium'!important;
}

.Regular {
    font-family: 'Poppins'!important;
}

.Bold {
    font-family: 'Poppins-Bold'!important;
}

.SemiBold {
    font-family: 'Poppins-SemiBold';
}

.Close-Icon-Position {
    margin-top: 0px;
}

.Close-Icon-Position svg{
  pointer-events: none;
}
.Modal-Close-Image{
    width:26px;
    margin-top: 5px;
}

.Header{
    // background-color: powderblue;
    margin-bottom: 10px;
}

input[type="radio"] {
    color: white !important;
    border-radius: 50%;
    appearance: none;
    border: 1px solid #d3d3d3;
    width: 16px;
    height: 16px;
    content: none;
    outline: none;
    margin: 0;
    padding: 0px!important;
    margin-right: 2px;
    box-shadow: none;
}

input[type="radio"]:checked {
    width: 16px;
    height: 16px;
    appearance: none;
    outline: none;
    padding: 0;
    content: none;
    border: none;
    box-shadow: none;
}


/* some more property of checked radio button */

input[type="radio"]:checked::before {
    position: absolute;
    background-color: #0074BC!important;
    color: white !important;
    // content: "\00A0\2713\00A0" !important;
    border: 1px solid #d3d3d3;
    font-weight: bold;
    font-size: 10px;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    box-shadow: none;
    border: none;
    padding: 0px;
    margin-left: -1px;
    margin-top: -1px;
    padding-left: 1px;
}


.reset{
    background-color: white;
    color: #0074bc;
    border: solid 1px #0074bc;
}

.CloseIconCol{
    width: 26px;
    cursor: pointer;
}
.save-modal-component {
    ::ng-deep .mat-dialog-actions {
        border-top: 1px solid #bdbdbd;
        padding-top: 0;
        padding-bottom: 0;
    }
    ::ng-deep .mat-dialog-content {
        max-height: 60vh !important;
    }
}